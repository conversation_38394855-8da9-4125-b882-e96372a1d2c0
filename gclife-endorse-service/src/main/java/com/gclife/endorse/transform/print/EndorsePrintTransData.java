package com.gclife.endorse.transform.print;

import com.alibaba.fastjson.JSON;
import com.gclife.agent.api.AgentApi;
import com.gclife.agent.model.response.AgentResponse;
import com.gclife.attachment.api.AttachmentPDFDocumentApi;
import com.gclife.attachment.model.config.AttachmentPdfEnum;
import com.gclife.attachment.model.policy.endorse.EndorsePrintAttachmentData;
import com.gclife.attachment.model.request.ElectronicPolicyGeneratorRequest;
import com.gclife.attachment.model.response.AttachmentResponse;
import com.gclife.common.model.ResultObject;
import com.gclife.common.model.base.Users;
import com.gclife.common.service.impl.BaseBusinessServiceImpl;
import com.gclife.common.util.AssertUtils;
import com.gclife.common.util.DateUtils;
import com.gclife.common.util.JackSonUtils;
import com.gclife.endorse.base.model.bo.EndorseAcceptBo;
import com.gclife.endorse.base.model.bo.EndorseThreadLocalBo;
import com.gclife.endorse.base.model.bo.GroupEndorsePrintBo;
import com.gclife.endorse.base.model.config.EndorseErrorConfigEnum;
import com.gclife.endorse.base.model.feign.attachment.EndorsePrintCommonReqFc;
import com.gclife.endorse.base.service.EndorseAcceptBaseService;
import com.gclife.endorse.base.service.EndorseItemBaseService;
import com.gclife.endorse.core.jooq.tables.pojos.EndorseAttachmentPo;
import com.gclife.endorse.core.jooq.tables.pojos.EndorseItemPo;
import com.gclife.endorse.model.response.group.GroupEndorsePrintPDFResponse;
import com.gclife.endorse.model.response.group.GroupEndorsePrintResponse;
import com.gclife.platform.api.PlatformBranchApi;
import com.gclife.platform.model.response.BranchResponse;
import com.gclife.policy.api.PolicyApi;
import com.gclife.policy.model.response.PolicyPartInfoResponse;
import com.gclife.policy.model.response.PolicyResponse;
import org.modelmapper.TypeToken;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

import static com.gclife.endorse.base.model.config.EndorseTermEnum.ENDORSE_PROJECT.ADD_SUBTRACT_INSURED;

/**
 * <AUTHOR>
 * @version v2.0
 * Description:
 * @date 19-1-13
 */
@Component
public class EndorsePrintTransData extends BaseBusinessServiceImpl {
    @Autowired
    private EndorseAcceptBaseService endorseAcceptBaseService;
    @Autowired
    private EndorseItemBaseService endorseItemBaseService;
    @Autowired
    private AttachmentPDFDocumentApi attachmentPDFDocumentApi;
    @Autowired
    private PolicyApi policyApi;
    @Autowired
    private AgentApi agentApi;
    @Autowired
    private PlatformBranchApi platformBranchApi;
    /**
     * 保全打印共有数据查询
     * @param endorseId 保全ID
     * @param languageCode 打印语言
     * @param endorsePrintCommonReqFc 保全打印共同数据
     * @return PolicyPartInfoRespFc
     */
    public PolicyPartInfoResponse queryEndorsePrintCommonData(String endorseId, String languageCode, EndorsePrintCommonReqFc endorsePrintCommonReqFc) {
        EndorseAcceptBo endorseAcceptBo = endorseAcceptBaseService.queryEndorseAcceptInfo(endorseId);
        AssertUtils.isNotNull(this.getLogger(), endorseAcceptBo, EndorseErrorConfigEnum.ENDORSE_BUSINESS_ENDORSE_ACCEPT_IS_NOT_FOUND_OBJECT);
        endorsePrintCommonReqFc.setAcceptNo(endorseAcceptBo.getAcceptNo());
        endorsePrintCommonReqFc.setPolicyNo(endorseAcceptBo.getApplyNo());
        endorsePrintCommonReqFc.setApplyDate(endorseAcceptBo.getApplyDate());
        endorsePrintCommonReqFc.setAcceptDate(endorseAcceptBo.getAcceptDate());
        endorsePrintCommonReqFc.setProjectCode(endorseAcceptBo.getProjectCode());

        // 生效日期
        EndorseItemPo endorseItemPo = endorseItemBaseService.queryEndorseItemById(endorseAcceptBo.getEndorseItemId());
        AssertUtils.isNotNull(this.getLogger(),endorseItemPo, EndorseErrorConfigEnum.ENDORSE_BUSINESS_PROJECT_CONFIG_IS_UNFINISHED);
        endorsePrintCommonReqFc.setEndorseFinishDate(endorseItemPo.getEndorseFinishDate());

        ResultObject<PolicyPartInfoResponse> policyPartInfoObject = policyApi.queryPolicyPartInfo(endorseAcceptBo.getApplyId());
        AssertUtils.isResultObjectError(this.getLogger(), policyPartInfoObject);
        PolicyPartInfoResponse policyPartInfo = policyPartInfoObject.getData();
        // 投保人
        endorsePrintCommonReqFc.setApplicantName(policyPartInfo.getApplicant().getName());
        // 被保人
        endorsePrintCommonReqFc.setInsuredName(policyPartInfo.getListInsured().get(0).getName());

        return policyPartInfo;
    }

    /**
     * 生成PDF
     * @param languageCode 语言
     * @param pdfType pdf类型
     *@param contentObject Object  @return
     */
    public String generatePdf(String languageCode, String pdfType, Object contentObject) {
        ElectronicPolicyGeneratorRequest electronicPolicyGeneratorRequest = new ElectronicPolicyGeneratorRequest();
        electronicPolicyGeneratorRequest.setPdfType(pdfType);
        electronicPolicyGeneratorRequest.setLanguage(languageCode);
        electronicPolicyGeneratorRequest.setContent(JackSonUtils.toJson(contentObject,languageCode));
        // 打印日志
        this.getLogger().info(JSON.toJSONString(electronicPolicyGeneratorRequest));
        ResultObject<List<AttachmentResponse>> responseResultObject = attachmentPDFDocumentApi.electronicPolicyGenerator(electronicPolicyGeneratorRequest);
        AssertUtils.isResultObjectDataNull(this.getLogger(), responseResultObject, EndorseErrorConfigEnum.ENDORSE_PRINT_GENERATE_IS_ERROR);
        return responseResultObject.getData().get(0).getMediaId();
    }

    /**
     * 生成PDF
     * @param languageCode 语言
     * @param pdfType pdf类型
     *@param endorsePrintAttachmentData Object  @return
     */
    public String generatePdfCompatible(String languageCode, String pdfType, EndorsePrintAttachmentData endorsePrintAttachmentData) {
        ElectronicPolicyGeneratorRequest electronicPolicyGeneratorRequest = new ElectronicPolicyGeneratorRequest();

        //29号产品增减员有单独的单证
        if (AssertUtils.isNotEmpty(endorsePrintAttachmentData.getCoverageBoList()) && AttachmentPdfEnum.ENDORSE_GROUP_INSURED_LIST.name().equals(pdfType)) {
            endorsePrintAttachmentData.getCoverageBoList().stream().filter(endorsePrintCoverageBo -> "PRO880000000000029".equals(endorsePrintCoverageBo.getProductId()))
                    .findFirst().ifPresent(s -> electronicPolicyGeneratorRequest.setProductId("PRO880000000000029"));
        }
        electronicPolicyGeneratorRequest.setPdfType(pdfType);
        electronicPolicyGeneratorRequest.setLanguage(languageCode);
        electronicPolicyGeneratorRequest.setContent(JackSonUtils.toJson(endorsePrintAttachmentData,languageCode));
        // 打印日志
        this.getLogger().info(JSON.toJSONString(electronicPolicyGeneratorRequest));
        ResultObject<List<AttachmentResponse>> responseResultObject = attachmentPDFDocumentApi.electronicPolicyGenerator(electronicPolicyGeneratorRequest);
        AssertUtils.isResultObjectDataNull(this.getLogger(), responseResultObject, EndorseErrorConfigEnum.ENDORSE_PRINT_GENERATE_IS_ERROR);
        return responseResultObject.getData().get(0).getMediaId();
    }
    /**
     * 团险打印列表数据转换
     *
     * @param groupEndorsePrintBos
     * @param users
     * @return
     */
    public List<GroupEndorsePrintResponse> groupEndorsePrintListTransData(List<GroupEndorsePrintBo> groupEndorsePrintBos, Users users) {
        List<GroupEndorsePrintResponse> groupEndorsePrintResponses = (List<GroupEndorsePrintResponse>) this.converterList(groupEndorsePrintBos, new TypeToken<List<GroupEndorsePrintResponse>>() {
        }.getType());
        if (AssertUtils.isNotEmpty(groupEndorsePrintResponses)) {
            groupEndorsePrintResponses.forEach(groupEndorsePrintResponse -> {
                //业务员信息
                if (AssertUtils.isNotEmpty(groupEndorsePrintResponse.getAgentId())) {
                    AgentResponse agentResponse = agentApi.agentByIdGet(groupEndorsePrintResponse.getAgentId()).getData();
                    if (AssertUtils.isNotNull(agentResponse)) {
                        groupEndorsePrintResponse.setAgentCode(agentResponse.getAgentCode());
                        groupEndorsePrintResponse.setAgentName(agentResponse.getAgentName());
                    }
                    //查询销售机构
                    PolicyResponse policyResponse = policyApi.queryOnePolicy(groupEndorsePrintResponse.getApplyId()).getData();
                    if (AssertUtils.isNotNull(policyResponse)) {
                        if (AssertUtils.isNotEmpty(policyResponse.getSalesBranchId())) {
                            BranchResponse branchResponse = platformBranchApi.branchGet(policyResponse.getSalesBranchId()).getData();
                            if (AssertUtils.isNotNull(branchResponse)) {
                                groupEndorsePrintResponse.setBranchName(branchResponse.getBranchName());
                            }
                        }
                    }
                }
            });
        }
        return groupEndorsePrintResponses;
    }
}
