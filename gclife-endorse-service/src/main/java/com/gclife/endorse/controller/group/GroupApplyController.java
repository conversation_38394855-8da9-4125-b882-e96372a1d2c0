package com.gclife.endorse.controller.group;

import com.gclife.common.annotation.ErrorTip;
import com.gclife.common.configuration.system.permissions.AutoCheckPermissions;
import com.gclife.common.configuration.system.version.ApiVersion;
import com.gclife.common.controller.base.BaseController;
import com.gclife.common.model.BasePageResponse;
import com.gclife.common.model.ResultObject;
import com.gclife.endorse.base.model.config.EndorseErrorConfigEnum;
import com.gclife.endorse.model.request.EndorseSubmitRequest;
import com.gclife.endorse.model.request.GroupEndorsePageRequest;
import com.gclife.endorse.model.request.group.GroupAcceptRequest;
import com.gclife.endorse.model.response.group.GroupEndorseAuditResponse;
import com.gclife.endorse.model.response.group.GroupEndorseResponse;
import com.gclife.endorse.service.group.GroupApplyService;
import com.gclife.policy.model.vo.PolicyVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiResponse;
import io.swagger.annotations.ApiResponses;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;
import springfox.documentation.annotations.ApiIgnore;

/**
 * <AUTHOR>
 * @version v1.0
 * Description: 团险保全申请
 * @date 19-9-10
 */
@Api(tags = "group_endorse_apply", description = "团险保全申请")
@Controller
@RequestMapping(value = "/v1/group/endorse/apply/")
public class GroupApplyController extends BaseController {
    @Autowired
    private GroupApplyService groupApplyService;

    @ApiOperation(value = "查询批单类型", notes = "查询批单类型")
    @ApiResponses({
            @ApiResponse(code = 200, message = "请求成功")
    })
    @ApiVersion(1)
    @AutoCheckPermissions
    @GetMapping(value = "project")
    public ResultObject queryGroupProjectList() {
        return groupApplyService.queryGroupProjectList();
    }

    @ApiOperation(value = "团险增员减员受理", notes = "团险增员减员受理")
    @ApiResponses({
            @ApiResponse(code = 200, message = "请求成功")
    })
    @ApiVersion(1)
    @AutoCheckPermissions
    @PostMapping(value = "accept")
    public ResultObject groupAccept(@RequestBody GroupAcceptRequest groupAcceptRequest) {
        return groupApplyService.groupAccept(groupAcceptRequest, getCurrentLoginUsers());
    }

    @ApiOperation(value = "团险批单申请提交", notes = "团险批单申请提交")
    @ApiResponses({
            @ApiResponse(code = 200, message = "请求成功")
    })
    @ApiVersion(1)
    @AutoCheckPermissions
    @PostMapping(value = "submit")
    public ResultObject submitGroupApply(@RequestParam(name = "endorseId") String endorseId) {
        return groupApplyService.submitGroupApply(endorseId, getCurrentLoginUsers());
    }


    @ApiOperation(value = "核心端团险增员减员受理", notes = "核心端团险增员减员受理")
    @ApiResponses({
            @ApiResponse(code = 200, message = "请求成功")
    })
    @ApiVersion(1)
    @AutoCheckPermissions
    @PostMapping(value = "bmp/accept")
    public ResultObject groupAcceptBmp(@RequestBody GroupAcceptRequest groupAcceptRequest) {
        return groupApplyService.groupAcceptBmp(groupAcceptRequest, getCurrentLoginUsers());
    }

    @ApiOperation(value = "核心端团险分页查询录入列表", notes = "核心端团险分页查询录入列表")
    @ApiResponses({
            @ApiResponse(code = 200, message = "请求成功")
    })
    @ApiVersion(1)
    @AutoCheckPermissions
    @GetMapping(value = "bmp/list")
    @ErrorTip(classType = EndorseErrorConfigEnum.class, key = "ENDORSE_QUERY_ACCEPT_LIST_IS_ERROR")
    public ResultObject<BasePageResponse<GroupEndorseResponse>> queryGroupApplyList(GroupEndorsePageRequest groupEndorsePageRequest) {
        return groupApplyService.queryGroupApplyList(this.getCurrentLoginUsers(), this.getAppRequestHandler(), groupEndorsePageRequest);
    }

    @ApiOperation(value = "核心端录入批单签收", notes = "核心端录入批单签收")
    @ApiResponses({
            @ApiResponse(code = 200, message = "请求成功")
    })
    @ApiVersion(1)
    @AutoCheckPermissions
    @GetMapping(value = "sign")
    public ResultObject signApplyEndorse(@RequestParam(name = "endorseId") String endorseId) {
        return groupApplyService.signApplyEndorse(this.getCurrentLoginUsers().getUserId(), endorseId);
    }


    @ApiOperation(value = "核心端团险保全申请提交", notes = "核心端团险保全申请提交")
    @ApiResponses({
            @ApiResponse(code = 200, message = "请求成功")
    })
    @ApiVersion(1)
    @AutoCheckPermissions
    @PostMapping(value = "bmp/submit")
    public ResultObject submitGroupApplyBmp(@RequestBody EndorseSubmitRequest endorseSubmitRequest) {
        return groupApplyService.submitGroupApplyBmp(endorseSubmitRequest, getCurrentLoginUsers());
    }


    @ApiOperation(value = "同步团险保单数据(仅测试用)", notes = "同步团险保单数据(仅测试用)")
    @ApiIgnore
    @PostMapping(value = "sync")
    public ResultObject syncGroupPolicyInfo(@RequestBody PolicyVo policyVo, @RequestParam("endorseItemId") String endorseItemId) {
        return groupApplyService.syncGroupPolicyInfo(policyVo, endorseItemId, getCurrentLoginUsers());
    }

}
