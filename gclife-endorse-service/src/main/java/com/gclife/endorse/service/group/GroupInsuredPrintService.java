package com.gclife.endorse.service.group;

import com.gclife.common.model.BasePageResponse;
import com.gclife.common.model.ResultObject;
import com.gclife.common.model.base.Users;
import com.gclife.common.model.pojo.AppRequestHeads;
import com.gclife.endorse.model.request.EndorsePageListRequest;
import com.gclife.endorse.model.request.GroupEndorsePageRequest;
import com.gclife.endorse.model.response.EndorseAcceptListResponse;
import com.gclife.endorse.model.response.group.GroupEndorsePrintResponse;

import javax.servlet.http.HttpServletResponse;

/**
 * <AUTHOR>
 * create 18-9-11
 * description:
 */
public interface GroupInsuredPrintService {
    /**
     * 查询团险保全审核通过后的打印列表
     * @param users
     * @param appRequestHandler
     * @param groupEndorsePageRequest
     * @return
     */
    ResultObject<BasePageResponse<GroupEndorsePrintResponse>> queryGroupEndorsePrintList(Users users, AppRequestHeads appRequestHandler, GroupEndorsePageRequest groupEndorsePageRequest);

    /**
     * 查询团险保全-被保人清单打印列表管理
     * @param users
     * @param appRequestHandler
     * @param groupEndorsePageRequest
     * @return
     */
    ResultObject<BasePageResponse<GroupEndorsePrintResponse>> queryGroupEndorsePrintManageList(Users users, AppRequestHeads appRequestHandler, GroupEndorsePageRequest groupEndorsePageRequest);

    /**
     * 打印保全团险详情
     * @param httpServletResponse
     * @param users
     * @param endorseId
     * @param languageCode
     * @return
     */
    ResultObject printGroupEndorseInfo(HttpServletResponse httpServletResponse, Users users, String endorseId, String languageCode);

    /**
     * 保全打印结束
     * @param httpServletResponse
     * @param users
     * @param endorseId
     * @param languageCode 语言
     * @return
     */
    ResultObject endPrintGroupEndorse(HttpServletResponse httpServletResponse, Users users, String endorseId, String languageCode);
}