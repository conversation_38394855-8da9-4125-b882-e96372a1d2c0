package com.gclife.endorse.service.group;

import com.gclife.apply.model.request.AttachmentDetailRequest;
import com.gclife.attachment.model.response.AttachmentResponse;
import com.gclife.common.model.ResultObject;
import com.gclife.common.model.base.Users;
import com.gclife.common.model.pojo.AppRequestHeads;
import com.gclife.endorse.model.request.EndorsePaymentNotifyRequest;
import com.gclife.endorse.model.request.GroupImmediatePaymentRequest;
import com.gclife.endorse.model.response.group.GroupPaymentEndorseResponse;
import com.gclife.endorse.model.response.group.PaymentMethResponse;

import java.util.List;

/**
 * <AUTHOR>
 * create 18-9-17
 * description:
 */
public interface GroupEndorsePaymentService {

    /**
     * 获取收费信息或付费信息
     * @param users
     * @param appRequestHandler
     * @param businessId
     * @return
     */
    ResultObject<GroupPaymentEndorseResponse> getGroupPaymentEndorseInfo(Users users, AppRequestHeads appRequestHandler, String businessId);

    /**
     * 立即支付
     *
     * @param users       当前用户
     * @param immediatePaymentRequest 支付参数
     * @param appRequestHandler 请求头
     * @return ResultObject
     */
    ResultObject postImmediatePayment(Users users, GroupImmediatePaymentRequest immediatePaymentRequest, AppRequestHeads appRequestHandler);

    /**
     * 团险保全支付回调
     * @param endorsePaymentNotifyRequest 支付通知
     * @param users       当前用户
     */
    ResultObject groupHandPaymentNotify(EndorsePaymentNotifyRequest endorsePaymentNotifyRequest, Users users);

    /**
     *根据保全id查询支付方式
     * @param endorseId
     * @param users
     * @param businessType
     * @return
     */
    ResultObject<List<PaymentMethResponse>> queryPaymentMethod(String endorseId, Users users, String businessType);

    /**
     * 保存支付指引
     * @param attachmentDetail
     * @param currentLoginUsers
     * @return
     */
    ResultObject saveApplyPaymentInstrument(AttachmentDetailRequest attachmentDetail, Users currentLoginUsers);

    /**
     * 查询保存附件
     * @param attachmentDetail
     * @param currentLoginUsers
     * @return
     */
    ResultObject<List<AttachmentResponse>> queryApplyPaymentInstrument(AttachmentDetailRequest attachmentDetail, Users currentLoginUsers);

    /**
     * 支付退回 保全支付凭证
     *
     * @param attachmentDetail
     * @param users
     * @return
     */
    ResultObject paymentReturnInstrument(AttachmentDetailRequest attachmentDetail, Users users);
}
