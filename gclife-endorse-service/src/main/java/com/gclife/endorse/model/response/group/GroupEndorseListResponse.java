package com.gclife.endorse.model.response.group;

import com.gclife.common.annotation.DateFormat;
import com.gclife.common.annotation.Internation;
import com.gclife.common.model.config.DateFormatPatternEnum;
import com.gclife.common.model.config.InternationCodeEnum;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class GroupEndorseListResponse {
    @ApiModelProperty(example = "保全id")
    private String endorseId;
    @ApiModelProperty(example = "保单id")
    private String applyId;
    @ApiModelProperty(example = "保单号")
    private String applyNo;
    @ApiModelProperty(example = "单位名称")
    private String companyName;
    @ApiModelProperty(example = "保单id")
    private String policyId;
    @ApiModelProperty(example = "客户号")
    private String customerNo;
    @ApiModelProperty(example = "客户id")
    private String customerId;
    @ApiModelProperty(example = "保单号")
    private String policyNo;
    @ApiModelProperty(example = "投保人代表姓名")
    private String delegateName;
    @ApiModelProperty(example = "投保人代表手机号码")
    private String delegateMobile;
    @ApiModelProperty(example = "保全明细id")
    private String endorseItemId;
    @ApiModelProperty(example = "产品名称")
    private String productName;
    @ApiModelProperty(example = "保全总费用")
    private String totalFee;
    @ApiModelProperty(example = "代理人id")
    private String agentId;
    @ApiModelProperty(example = "代理人编码")
    private String agentCode;
    @ApiModelProperty(example = "代理人姓名")
    private String agentName;
    @ApiModelProperty(example = "机构名称")
    private String branchName;
    @ApiModelProperty(example = "批单类型编码")
    private String projectCode;
    @ApiModelProperty(example = "批单类型名称")
    @Internation(codeType = "GROUP_ENDORSE_PROJECT", filed = "projectCode")
    private String projectCodeName;
    @ApiModelProperty(example = "申请时间")
    private Long applyDate;
    @ApiModelProperty(example = "转换后申请时间")
    @DateFormat(filed = "applyDate", pattern = DateFormatPatternEnum.FORMATE5)
    private String applyDateFormat;
    @ApiModelProperty(example = "申请日期")
    private Long applyTime;
    @ApiModelProperty(example = "申请日期")
    @DateFormat(filed = "applyTime", pattern = DateFormatPatternEnum.FORMATE6)
    private String applyDateTimeStr;
    @ApiModelProperty(example = "批单时间")
    private Long endorseFinishDate;
    @ApiModelProperty(example = "转换后批单时间")
    @DateFormat(filed = "endorseFinishDate", pattern = DateFormatPatternEnum.FORMATE5)
    private String endorseFinishDateFormat;
    @ApiModelProperty(example = "保全状态")
    private String endorseStatus;
    @ApiModelProperty(example = "保全状态名字")
    @Internation(codeType = "GROUP_ENDORSE_STATUS", filed = "endorseStatus")
    private String endorseStatusName;
    @ApiModelProperty(example = "退保原因")
    private String endorseReasonCode;
    @ApiModelProperty(example = "退保原因")
    @Internation(codeType = "ENDORSE_REASON", filed = "endorseReasonCode", type = InternationCodeEnum.syscode)
    private String endorseReasonCodeName;
    private String applyName;
    private String applyMobile;
    private String email;
    private String onlineRemark;
    //客户app展示状态
    private String showStatusCode;
    //客户app展示状态
    @Internation(codeType = "SHOW_STATUS", filed = "showStatusCode", type = InternationCodeEnum.syscode)
    private String showStatusName;
    // 保全类型
    private String feeType;
    // 团险保全批单下载id
    private String attachmentId;
    //保全生效日期
    private Long effectiveDate;
    @ApiModelProperty(example = "保全生效日期")
    @DateFormat(filed = "effectiveDate", pattern = DateFormatPatternEnum.FORMATE3)
    private String effectiveDateFormat;
    @ApiModelProperty(example = "保全通过日期")
    private String endorseFinishDateTimeFormat;
    //保全备注
    private String endorseReasonRemark;
}
