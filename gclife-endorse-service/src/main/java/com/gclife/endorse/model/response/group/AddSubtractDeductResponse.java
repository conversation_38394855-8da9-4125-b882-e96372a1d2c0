package com.gclife.endorse.model.response.group;

import com.gclife.endorse.model.response.input.EndorsePayInfoResponse;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @version v1.0
 * Description: 增减员抵扣信息
 * @date 20-3-6
 */
@Data
public class AddSubtractDeductResponse {
    @ApiModelProperty(example = "保全ID")
    private String endorseId;
    @ApiModelProperty(example = "保全明细ID")
    private String endorseItemId;
    @ApiModelProperty(example = "增员总保费")
    private BigDecimal totalPremium;
    @ApiModelProperty(example = "减员总退费金额")
    private BigDecimal refundAmount;
    @ApiModelProperty(example = "抵扣保费")
    private BigDecimal deductPremium;
    @ApiModelProperty(example = "抵扣退费金额")
    private BigDecimal deductRefundAmount;
    @ApiModelProperty(example = "抵扣笔数")
    private Integer deductNum;
    @ApiModelProperty(example = "总费用")
    private BigDecimal totalFee;
    @ApiModelProperty(example = "总费用类型")
    private String feeType;
    @ApiModelProperty(example = "币种单位")
    private String currencyCode;
    @ApiModelProperty(example = "币种名称")
    private String currencyName;
    @ApiModelProperty(example = "币种符号")
    private String currencySymbol;
    /**支付信息*/
    private EndorsePayInfoResponse endorsePayInfo;
    /**抵扣明细*/
    private List<AddSubtractDeductDetailResponse> listAddSubtractDeductDetail;
}
