package com.gclife.endorse.model.response.group;

import com.gclife.common.annotation.Internation;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * create 2022/12/14
 * description:
 */
@Data
public class GroupPolicyEndorseInfoResponse {
    @ApiModelProperty(example = "单位名称")
    private String companyName;
    @ApiModelProperty(example = "保单号(单位名称)")
    private String policyNoAndCompanyName;
    @ApiModelProperty(example = "保单id")
    private String policyId;
    @ApiModelProperty(example = "保单No")
    private String policyNo;
    @ApiModelProperty(example = "批单类型")
    private String projectCode;
    @ApiModelProperty(example = "批单类型国际化")
    @Internation(codeType = "GROUP_ENDORSE_PROJECT", filed = "projectCode")
    private String projectCodeName;
}