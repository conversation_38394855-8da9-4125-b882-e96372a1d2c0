package com.gclife.endorse.model.response;

import com.gclife.platform.model.response.SyscodeResponse;
import io.swagger.annotations.ApiModelProperty;

import java.util.List;

/**
 * <AUTHOR>
 * create 18-9-3
 * description:
 */
public class AcceptDicResponse {

    @ApiModelProperty(example = "保全类型")
    private List<SyscodeResponse> endorseType;

    @ApiModelProperty(example = "网销保全详细类型")
    private List<SyscodeResponse> endorseDetailType;

    @ApiModelProperty(example = "申请方式")
    private List<SyscodeResponse> applyMethod;

    public List<SyscodeResponse> getEndorseType() {
        return endorseType;
    }

    public void setEndorseType(List<SyscodeResponse> endorseType) {
        this.endorseType = endorseType;
    }

    public List<SyscodeResponse> getApplyMethod() {
        return applyMethod;
    }

    public void setApplyMethod(List<SyscodeResponse> applyMethod) {
        this.applyMethod = applyMethod;
    }

    public List<SyscodeResponse> getEndorseDetailType() {
        return endorseDetailType;
    }

    public void setEndorseDetailType(List<SyscodeResponse> endorseDetailType) {
        this.endorseDetailType = endorseDetailType;
    }
}
