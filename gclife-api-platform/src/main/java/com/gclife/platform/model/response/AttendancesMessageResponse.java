package com.gclife.platform.model.response;

import com.gclife.platform.model.request.AttendanceStatusRequest;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

/**
 * 获取当月的打卡信息  以及  累计签到天数
 * <AUTHOR>
 * create 17-11-8
 * description:
 */
@Getter
@Setter
public class AttendancesMessageResponse {

    @ApiModelProperty(example = "累计签到天数")
    private Long continuouSignDays;
    @ApiModelProperty(example = "用户总计积分")
    private String totalAmount;

    private List<AttendanceStatusRequest> daysStatus;
}