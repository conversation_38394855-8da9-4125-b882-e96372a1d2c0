package com.gclife.platform.model.request;

import com.gclife.common.model.BaseRequest;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

/**
 * <AUTHOR>
 * create 17-12-15
 * description:保存链接的请求
 */
@Getter
@Setter
@ApiModel(value = "SharedLinkSave",description = "链接保存请求")
public class SharedLinkSaveRequest extends BaseRequest {

    @ApiModelProperty(example = "链接类型")
    private String linkType;

    @ApiModelProperty(example = "链接参数")
    private String linkParameters;
}
