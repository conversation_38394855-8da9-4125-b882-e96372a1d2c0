package com.gclife.platform.model.response;

import com.gclife.common.model.BaseResponse;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

/**
 * \* Created with IntelliJ IDEA.
 * \* User: deep
 * \* Date: 17-9-18
 * \* Time: 下午4:32
 * \* To change this template use File | Settings | File Templates.
 * \* Description: 机构返回信息
 * <AUTHOR>
 */
@Getter
@Setter
@ApiModel(value = "Branch",description = "系统机构")
public class BranchParentResponse extends BaseResponse {

    @ApiModelProperty(example = "机构ID")
    private String branchId;
    /**父机构**/
    private List<BranchResponse> listParentBranch;

}