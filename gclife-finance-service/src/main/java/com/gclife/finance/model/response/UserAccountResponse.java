package com.gclife.finance.model.response;

import com.gclife.common.model.BaseResponse;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.math.BigDecimal;

@ApiModel(value = "UserAccountResponse",description = "客户账户")
public class UserAccountResponse extends BaseResponse {
    @ApiModelProperty(example = "账户ID")
    private String     accountId;
    @ApiModelProperty(example = "总金额")
    private BigDecimal totalAmount;
    @ApiModelProperty(example = "使用金额")
    private BigDecimal useAmount;
    @ApiModelProperty(example = "剩余金额")
    private BigDecimal residueAmount;
    @ApiModelProperty(example = "客户ID")
    private String     customerId;
    @ApiModelProperty(example = "冻结金额")
    private BigDecimal frozenAmount;
    @ApiModelProperty(example = "正在消费提取金额")
    private BigDecimal extractAmount;
    @ApiModelProperty(example = "币种")
    private String     currencyCode;
    @ApiModelProperty(example = "账户类型")
    private String     userAccountTypeCode;
    @ApiModelProperty(example = "账户状态")
    private String     accountStatus;


    public String getAccountId() {
        return accountId;
    }

    public void setAccountId(String accountId) {
        this.accountId = accountId;
    }

    public BigDecimal getTotalAmount() {
        return totalAmount;
    }

    public void setTotalAmount(BigDecimal totalAmount) {
        this.totalAmount = totalAmount;
    }

    public BigDecimal getUseAmount() {
        return useAmount;
    }

    public void setUseAmount(BigDecimal useAmount) {
        this.useAmount = useAmount;
    }

    public BigDecimal getResidueAmount() {
        return residueAmount;
    }

    public void setResidueAmount(BigDecimal residueAmount) {
        this.residueAmount = residueAmount;
    }

    public String getCustomerId() {
        return customerId;
    }

    public void setCustomerId(String customerId) {
        this.customerId = customerId;
    }

    public BigDecimal getFrozenAmount() {
        return frozenAmount;
    }

    public void setFrozenAmount(BigDecimal frozenAmount) {
        this.frozenAmount = frozenAmount;
    }

    public BigDecimal getExtractAmount() {
        return extractAmount;
    }

    public void setExtractAmount(BigDecimal extractAmount) {
        this.extractAmount = extractAmount;
    }

    public String getCurrencyCode() {
        return currencyCode;
    }

    public void setCurrencyCode(String currencyCode) {
        this.currencyCode = currencyCode;
    }

    public String getUserAccountTypeCode() {
        return userAccountTypeCode;
    }

    public void setUserAccountTypeCode(String userAccountTypeCode) {
        this.userAccountTypeCode = userAccountTypeCode;
    }

    public String getAccountStatus() {
        return accountStatus;
    }

    public void setAccountStatus(String accountStatus) {
        this.accountStatus = accountStatus;
    }
}
