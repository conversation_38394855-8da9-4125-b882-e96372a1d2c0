package com.gclife.finance.model.response;

import io.swagger.annotations.ApiModelProperty;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @version v1.0
 * Description: 回盘明细
 * @date 17-12-25
 */
public class CounterofferDetailResponse {
    @ApiModelProperty(example = "所属机构")
    private String     branchName;
    @ApiModelProperty(example = "业务员")
    private String     agentName;
    @ApiModelProperty(example = "职级")
    private String     agentLevelCodeName;
    @ApiModelProperty(example = "手机号")
    private String     mobile;
    @ApiModelProperty(example = "开户人员")
    private String     bankAccountName;
    @ApiModelProperty(example = "开户行编码")
    private String     bankCode;
    @ApiModelProperty(example = "开户行")
    private String     bankName;
    @ApiModelProperty(example = "银行卡卡号")
    private String     bankAccountNo;
    @ApiModelProperty(example = "账户余额")
    private BigDecimal residueAmount;
    @ApiModelProperty(example = "提现金额")
    private BigDecimal extractAmount;
    @ApiModelProperty(example = "回盘状态")
    private String     counterofferStatus;

    @ApiModelProperty(example = "财务提现ID")
    private String     financeExtractId;
    @ApiModelProperty(example = "所属机构ID")
    private String     branchId;
    @ApiModelProperty(example = "用户ID")
    private String     userId;
    @ApiModelProperty(example = "业务员ID")
    private String     agentId;
    @ApiModelProperty(example = "币种")
    private String     currencyCode;
    @ApiModelProperty(example = "申请时间")
    private Long       applyDate;
    @ApiModelProperty(example = "等待时长")
    private Long       waitTime;
    @ApiModelProperty(example = "回盘状态编码")
    private String     counterofferStatusCode;
    @ApiModelProperty(example = "批次明细表主键")
    private String     counterofferDetailId;

    public String getBranchName() {
        return branchName;
    }

    public void setBranchName(String branchName) {
        this.branchName = branchName;
    }

    public String getAgentName() {
        return agentName;
    }

    public void setAgentName(String agentName) {
        this.agentName = agentName;
    }

    public String getAgentLevelCodeName() {
        return agentLevelCodeName;
    }

    public void setAgentLevelCodeName(String agentLevelCodeName) {
        this.agentLevelCodeName = agentLevelCodeName;
    }

    public String getMobile() {
        return mobile;
    }

    public void setMobile(String mobile) {
        this.mobile = mobile;
    }

    public String getBankAccountName() {
        return bankAccountName;
    }

    public void setBankAccountName(String bankAccountName) {
        this.bankAccountName = bankAccountName;
    }

    public String getBankCode() {
        return bankCode;
    }

    public void setBankCode(String bankCode) {
        this.bankCode = bankCode;
    }

    public String getBankName() {
        return bankName;
    }

    public void setBankName(String bankName) {
        this.bankName = bankName;
    }

    public String getBankAccountNo() {
        return bankAccountNo;
    }

    public void setBankAccountNo(String bankAccountNo) {
        this.bankAccountNo = bankAccountNo;
    }

    public BigDecimal getResidueAmount() {
        return residueAmount;
    }

    public void setResidueAmount(BigDecimal residueAmount) {
        this.residueAmount = residueAmount;
    }

    public BigDecimal getExtractAmount() {
        return extractAmount;
    }

    public void setExtractAmount(BigDecimal extractAmount) {
        this.extractAmount = extractAmount;
    }

    public String getCounterofferStatus() {
        return counterofferStatus;
    }

    public void setCounterofferStatus(String counterofferStatus) {
        this.counterofferStatus = counterofferStatus;
    }

    public String getFinanceExtractId() {
        return financeExtractId;
    }

    public void setFinanceExtractId(String financeExtractId) {
        this.financeExtractId = financeExtractId;
    }

    public String getBranchId() {
        return branchId;
    }

    public void setBranchId(String branchId) {
        this.branchId = branchId;
    }

    public String getUserId() {
        return userId;
    }

    public void setUserId(String userId) {
        this.userId = userId;
    }

    public String getAgentId() {
        return agentId;
    }

    public void setAgentId(String agentId) {
        this.agentId = agentId;
    }

    public String getCurrencyCode() {
        return currencyCode;
    }

    public void setCurrencyCode(String currencyCode) {
        this.currencyCode = currencyCode;
    }

    public Long getApplyDate() {
        return applyDate;
    }

    public void setApplyDate(Long applyDate) {
        this.applyDate = applyDate;
    }

    public Long getWaitTime() {
        return waitTime;
    }

    public void setWaitTime(Long waitTime) {
        this.waitTime = waitTime;
    }

    public String getCounterofferStatusCode() {
        return counterofferStatusCode;
    }

    public void setCounterofferStatusCode(String counterofferStatusCode) {
        this.counterofferStatusCode = counterofferStatusCode;
    }

    public String getCounterofferDetailId() {
        return counterofferDetailId;
    }

    public void setCounterofferDetailId(String counterofferDetailId) {
        this.counterofferDetailId = counterofferDetailId;
    }
}
