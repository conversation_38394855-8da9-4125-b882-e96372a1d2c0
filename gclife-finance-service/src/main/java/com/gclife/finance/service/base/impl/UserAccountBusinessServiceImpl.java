package com.gclife.finance.service.base.impl;

import com.alibaba.fastjson.JSON;
import com.gclife.common.exception.RequestException;
import com.gclife.common.model.BaseResponse;
import com.gclife.common.model.ResultObject;
import com.gclife.common.service.impl.BaseExcelBusinessServiceImpl;
import com.gclife.common.util.AssertUtils;
import com.gclife.common.util.ClazzUtils;
import com.gclife.finance.base.model.bo.UserAccountDetailVo;
import com.gclife.finance.base.model.config.FinanceErrorConfigEnum;
import com.gclife.finance.base.model.config.FinanceTermEnum;
import com.gclife.finance.base.service.UserAccountBaseService;
import com.gclife.finance.core.jooq.tables.pojos.*;
import com.gclife.finance.model.excel.ExtractExceptionExcelBo;
import com.gclife.finance.model.request.UserAccountAddRequest;
import com.gclife.finance.model.request.UserAccountRequest;
import com.gclife.finance.model.request.UserAccountUpdateRequest;
import com.gclife.finance.model.response.UserAccountAddResponse;
import com.gclife.finance.model.response.UserAccountResponse;
import com.gclife.finance.service.base.UserAccountBusinessService;
import com.gclife.finance.validate.parameter.UserAccountParameterValidate;
import org.modelmapper.TypeToken;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * <AUTHOR>
 *         create 18-05-20
 *         description:　用户账户中台service
 */
@Service
public class UserAccountBusinessServiceImpl extends BaseExcelBusinessServiceImpl implements UserAccountBusinessService {

    @Autowired
    private UserAccountBaseService userAccountBaseService;

    @Autowired
    private UserAccountParameterValidate userAccountParameterValidate;

    /**
     * 查询用户账号
     * @param userAccountId 账号主键
     * @return  UserAccountPo
     */
    @Override
    public ResultObject<UserAccountResponse> queryOneAcountByAccountId(String userAccountId) {
        ResultObject<UserAccountResponse> resultObject = new ResultObject<>();
        try {
            AssertUtils.isNotEmpty(this.getLogger(),userAccountId,FinanceErrorConfigEnum.FINANCE_PARAMETER_ACCOUNT_ID_IS_NOT_NULL);
           UserAccountPo userAccountPo= userAccountBaseService.queryOneAcountByAccountId(userAccountId);
           AssertUtils.isNotNull(this.getLogger(),userAccountPo,FinanceErrorConfigEnum.FINANCE_BUSINESS_ACCOUNT_IS_NOT_FOUND_OBJECT);
            UserAccountResponse userAccountResponse = (UserAccountResponse)this.converterObject(userAccountPo,UserAccountResponse.class);
            resultObject.setData(userAccountResponse);
        } catch (Exception e) {
            this.setResultObjectException(this.getLogger(),resultObject,e,FinanceErrorConfigEnum.FINANCE_QUERY_ACCOUNT_IS_ERROR);
        }
        return resultObject;
    }
    /**
     *查询用户账号
     * @param userId 客户ID
     * @param accountTypeCode 账户类型
     * @return  UserAccountPo
     */
    @Override
    public ResultObject<UserAccountResponse> queryOneAcount(String userId, String accountTypeCode) {
        ResultObject<UserAccountResponse> resultObject = new ResultObject<>();
        try {
            AssertUtils.isNotEmpty(this.getLogger(),userId,FinanceErrorConfigEnum.FINANCE_PARAMETER_CUSTOMER_ID_IS_NOT_NULL);
            AssertUtils.isNotEmpty(this.getLogger(),accountTypeCode,FinanceErrorConfigEnum.FINANCE_PARAMETER_ACCOUNT_TYPE_IS_NOT_NULL);
            UserAccountPo userAccountPo= userAccountBaseService.queryOneAcount(userId,accountTypeCode);
            AssertUtils.isNotNull(this.getLogger(),userAccountPo,FinanceErrorConfigEnum.FINANCE_BUSINESS_ACCOUNT_IS_NOT_FOUND_OBJECT);
            UserAccountResponse userAccountResponse = (UserAccountResponse)this.converterObject(userAccountPo,UserAccountResponse.class);
            resultObject.setData(userAccountResponse);
        } catch (Exception e) {
            this.setResultObjectException(this.getLogger(),resultObject,e,FinanceErrorConfigEnum.FINANCE_QUERY_ACCOUNT_IS_ERROR);
        }
        return resultObject;
    }
    /**
     *查询用户账户集合
     * @param userId 客户ID
     * @return  UserAccountPo
     */
    @Override
    public ResultObject<List<UserAccountResponse>> queryAcountByCustomerId(String userId) {
        ResultObject<List<UserAccountResponse>> resultObject = new ResultObject<>();
        try {
            AssertUtils.isNotEmpty(this.getLogger(),userId,FinanceErrorConfigEnum.FINANCE_PARAMETER_CUSTOMER_ID_IS_NOT_NULL);
            List<UserAccountPo> userAccountPos= userAccountBaseService.queryAcountByCustomerId(userId);
            if(AssertUtils.isNotEmpty(userAccountPos)){
                List<UserAccountResponse> userAccountResponse = (List<UserAccountResponse>)this.converterList(userAccountPos,new TypeToken<List<UserAccountResponse>>() {}.getType());
                resultObject.setData(userAccountResponse);
            }
        } catch (Exception e) {
            this.setResultObjectException(this.getLogger(),resultObject,e,FinanceErrorConfigEnum.FINANCE_QUERY_ACCOUNT_IS_ERROR);
        }
        return resultObject;
    }


    /**
     *　新增　收入
     * @param userAccountRequest 对象
     * @return  ResultObject<String>
     */
    @Override
    @Transactional
    public ResultObject<UserAccountAddResponse> addUserAccountDetail(UserAccountAddRequest userAccountRequest) {
        ResultObject<UserAccountAddResponse> resultObject = new ResultObject<>();
        try {
            //1.数据校验
            userAccountParameterValidate.addUserAccountDetail(userAccountRequest);
            //2.数据转换
            UserAccountDetailVo userAccountDetailVo =(UserAccountDetailVo)this.converterObject(userAccountRequest,UserAccountDetailVo.class);
            //单独判断状态
            if(!FinanceTermEnum.ACCOUNT_DETAIL_STATUS.ACTIVITY.name().equals(userAccountDetailVo.getStatus()) &&
                    !FinanceTermEnum.ACCOUNT_DETAIL_STATUS.FROZEN.name().equals(userAccountDetailVo.getStatus())){
                throw  new RequestException(FinanceErrorConfigEnum.FINANCE_PARAMETER_ACCOUNT_STATUS_IS_ERROR);
            }
            //3.调用基础保存接口
            String accountDetailId = userAccountBaseService.saveUserAccountDetail(userAccountDetailVo);
            //返回
            resultObject.setData(new UserAccountAddResponse(accountDetailId));
        } catch (Exception e) {
            this.setTransactionalResultObjectException(this.getLogger(),resultObject,e,FinanceErrorConfigEnum.FINANCE_QUERY_ACCOUNT_IS_ERROR);
        }
        return resultObject;
    }
    /**
     *　新增　支出
     * @param userAccountRequest 对象
     * @return  ResultObject<String>
     */
    @Override
    @Transactional
    public ResultObject<UserAccountAddResponse> addOutUserAccountDetail(UserAccountAddRequest userAccountRequest) {
        ResultObject<UserAccountAddResponse> resultObject = new ResultObject<>();
        try {
            //1.数据校验
            userAccountParameterValidate.addUserAccountDetail(userAccountRequest);
            //2.数据转换
            UserAccountDetailVo userAccountDetailVo =(UserAccountDetailVo)this.converterObject(userAccountRequest,UserAccountDetailVo.class);
            if(!FinanceTermEnum.ACCOUNT_DETAIL_STATUS.WITHDRAWALING.name().equals(userAccountDetailVo.getStatus()) &&
                    !FinanceTermEnum.ACCOUNT_DETAIL_STATUS.WITHDRAWAL_SUCCESS.name().equals(userAccountDetailVo.getStatus())){
                throw  new RequestException(FinanceErrorConfigEnum.FINANCE_PARAMETER_ACCOUNT_STATUS_IS_ERROR);
            }
            //3.调用基础保存接口
            String accountDetailId = userAccountBaseService.saveUserAccountDetail(userAccountDetailVo);
            //返回
            resultObject.setData(new UserAccountAddResponse(accountDetailId));
        } catch (Exception e) {
            this.setTransactionalResultObjectException(this.getLogger(),resultObject,e,FinanceErrorConfigEnum.FINANCE_QUERY_ACCOUNT_IS_ERROR);
        }
        return resultObject;
    }

    /**
     *　　收入 解冻
     * @param userAccountRequest 对象
     * @return  ResultObject<String>
     */
    @Override
    @Transactional
    public ResultObject<BaseResponse> passInputUserAccountDetail(UserAccountUpdateRequest userAccountRequest) {
        ResultObject<BaseResponse> resultObject = new ResultObject<>();
        try {
            //1.数据校验
            userAccountParameterValidate.updateInputUserAccountDetail(userAccountRequest);
            //2.数据转换
            UserAccountDetailVo userAccountDetailVo =(UserAccountDetailVo)this.converterObject(userAccountRequest,UserAccountDetailVo.class);
            //3.调用基础保存接口
            userAccountBaseService.saveUserAccountDetail(userAccountDetailVo);

        } catch (Exception e) {
            this.setTransactionalResultObjectException(this.getLogger(),resultObject,e,FinanceErrorConfigEnum.FINANCE_QUERY_ACCOUNT_IS_ERROR);
        }
        return resultObject;
    }
    /**
     *　支出 申请审核通过
     * @param userAccountRequest 对象
     * @return  ResultObject<String>
     */
    @Override
    @Transactional
    public ResultObject<BaseResponse> updateOutUserAccountDetail(UserAccountUpdateRequest userAccountRequest) {
        ResultObject<BaseResponse> resultObject = new ResultObject<>();
        try {
            //1.数据校验
            userAccountParameterValidate.updateOutUserAccountDetail(userAccountRequest);
            //2.数据转换
            UserAccountDetailVo userAccountDetailVo =(UserAccountDetailVo)this.converterObject(userAccountRequest,UserAccountDetailVo.class);
            //3.调用基础保存接口
            userAccountBaseService.saveUserAccountDetail(userAccountDetailVo);

        } catch (Exception e) {
            this.setTransactionalResultObjectException(this.getLogger(),resultObject,e,FinanceErrorConfigEnum.FINANCE_QUERY_ACCOUNT_IS_ERROR);
        }
        return resultObject;
    }
    /**
     *　撤销已经　支出审核通过的流水
     * @param userAccountRequest 对象
     * @return  ResultObject<String>
     */
    @Override
    @Transactional
    public ResultObject<BaseResponse> revokeOutUserAccountDetail(UserAccountUpdateRequest userAccountRequest) {
        ResultObject<BaseResponse> resultObject = new ResultObject<>();
        try {
            //1.数据校验
            userAccountParameterValidate.revokeOutUserAccountDetail(userAccountRequest);
            //2.数据转换
            UserAccountDetailVo userAccountDetailVo =(UserAccountDetailVo)this.converterObject(userAccountRequest,UserAccountDetailVo.class);
            //3.调用基础保存接口
            userAccountBaseService.revokeOutUserAccountDetail(userAccountDetailVo);

        } catch (Exception e) {
            this.setTransactionalResultObjectException(this.getLogger(),resultObject,e,FinanceErrorConfigEnum.FINANCE_QUERY_ACCOUNT_IS_ERROR);
        }
        return resultObject;
    }

    /**
     * 保存用户账户
     * @param userAccountRequest 用户账户数据
     * @return ResultObject
     */
    @Override
    public ResultObject saveUserAccount(UserAccountRequest userAccountRequest) {
        ResultObject resultObject = new ResultObject();
        try {
            this.getLogger().error("userAccountRequest:"+ JSON.toJSONString(userAccountRequest));
            UserAccountPo userAccountPo = userAccountBaseService.queryOneAcount(userAccountRequest.getUserId(), userAccountRequest.getUserAccountTypeCode());
            if (!AssertUtils.isNotNull(userAccountPo)) {
                userAccountPo = new UserAccountPo();
            }
            ClazzUtils.copyPropertiesIgnoreNull(userAccountRequest, userAccountPo);
            userAccountBaseService.saveUserAccount(userAccountPo);
        } catch (Exception e) {
            e.printStackTrace();
            throwsTransactionalException(this.getLogger(), e, FinanceErrorConfigEnum.FINANCE_SAVE_USER_ACCOUNT_ERROR);
        }
        return resultObject;
    }
}