package com.gclife.finance.service.business.impl;

import com.gclife.agent.api.AgentExtApi;
import com.gclife.agent.model.response.AgentResponse;
import com.gclife.common.exception.RequestException;
import com.gclife.common.model.*;
import com.gclife.common.model.base.Users;
import com.gclife.common.model.config.InternationalTypeEnum;
import com.gclife.common.model.config.TerminologyTypeEnum;
import com.gclife.common.model.feign.SyscodeRespFc;
import com.gclife.common.service.impl.BaseExcelBusinessServiceImpl;
import com.gclife.common.util.AssertUtils;
import com.gclife.common.util.DateUtils;
import com.gclife.common.util.UUIDUtils;
import com.gclife.finance.core.jooq.tables.daos.FinanceExtractDao;
import com.gclife.finance.core.jooq.tables.pojos.CounterofferBatchPo;
import com.gclife.finance.core.jooq.tables.pojos.CounterofferDetailPo;
import com.gclife.finance.core.jooq.tables.pojos.FinanceExtractPo;
import com.gclife.finance.dao.FinanceExtractExtDao;
import com.gclife.finance.model.bo.FinanceExtractBo;
import com.gclife.finance.base.model.config.FinanceErrorConfigEnum;
import com.gclife.finance.base.model.config.FinanceTermEnum;
import com.gclife.finance.model.excel.FinanceExtractExcelBo;
import com.gclife.finance.base.model.feign.AgentRespFc;
import com.gclife.finance.base.model.feign.BranchRespFc;
import com.gclife.finance.model.request.ApplyExtractRequest;
import com.gclife.finance.model.request.FinanceExtractRequest;
import com.gclife.finance.model.response.FinanceExtractResponse;
import com.gclife.finance.service.business.FinanceExtractBusinessService;
import com.gclife.finance.base.service.data.FinanceService;
import com.gclife.finance.validate.parameter.FinanceExtractParameterValidate;
import com.gclife.finance.validate.transfer.FinanceExtractTransfer;
import com.gclife.platform.api.PlatformBranchApi;
import com.gclife.platform.api.PlatformInternationalBaseApi;
import com.gclife.platform.model.response.BranchResponse;
import com.gclife.platform.model.response.SyscodeResponse;
import org.apache.poi.ss.usermodel.Workbook;
import org.jeecgframework.poi.excel.ExcelExportUtil;
import org.jeecgframework.poi.excel.entity.ExportParams;
import org.modelmapper.TypeToken;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.interceptor.TransactionAspectSupport;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * \* Created with IntelliJ IDEA.
 * \* User: deep
 * \* Date: 17-9-20
 * \* Time: 下午4:38
 * \* To change this template use File | Settings | File Templates.
 * \* Description:财务提现service
 * <AUTHOR>
 */
@Service
public class FinanceExtractBusinessServiceImpl extends BaseExcelBusinessServiceImpl implements FinanceExtractBusinessService {

    @Autowired
    private FinanceExtractExtDao financeExtractExtDao;
    @Autowired
    private FinanceService financeService;
    @Autowired
    private FinanceExtractParameterValidate financeExtractParameterValidate;
    @Autowired
    private FinanceExtractTransfer financeExtractTransfer;
    @Autowired
    private AgentExtApi agentExtApi;
    @Autowired
    private PlatformInternationalBaseApi platformInternationalBaseApi;
    @Autowired
    private FinanceExtractDao financeExtractDao;
    @Autowired
    private PlatformBranchApi platformBranchApi;

    /**
     * 提现接口
     * @param users　当前用户
     * @param applyExtractRequest　申请提现对象
     * @return  ResultObject
     */
    @Override
    @Transactional
    public ResultObject financeExtract(Users users, ApplyExtractRequest applyExtractRequest) {
        ResultObject<BaseResponse> resultObject = new ResultObject<>();
        try {
            //参数校验
            financeExtractParameterValidate.validParameterExtract(applyExtractRequest);
            //数据转换
            FinanceExtractPo financeExtractPo=financeExtractTransfer.transferFinanceExtract(users,applyExtractRequest);
            //数据保存
            financeService.saveFinanceExtractPo(financeExtractPo);
        } catch (Exception e) {
            //事务回滚
            TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
            if (e instanceof RequestException) {
                RequestException error = (RequestException) e;
                //错误设置
                resultObject.setIenum(error.getiEnum());
            } else {
                resultObject.setIenum(FinanceErrorConfigEnum.FINANCE_FAIL);
            }
            e.printStackTrace();
        }
        return resultObject;
    }
    /**
     * 财务提现回滚
     * @param accountDetailId　账户流水ID
     * @return  返回
     */
    @Override
    @Transactional
    public ResultObject<BaseResponse> financeExtractRollback(String accountDetailId) {
        ResultObject<BaseResponse> resultObject = new ResultObject<>();
        try {
            AssertUtils.isNotNull(this.getLogger(),accountDetailId, FinanceErrorConfigEnum.FINANCE_PARAMETER_ACCOUNT_DETAIL_IS_NOT_FOUND_OBJECT);
            FinanceExtractPo financeExtractPo= financeExtractExtDao.loadFinanceExtractPoByAccountDetailId(accountDetailId);
            if(AssertUtils.isNotNull(financeExtractPo)){
                financeExtractDao.delete(financeExtractPo);
            }
        } catch (Exception e) {
            this.setTransactionalResultObjectException(this.getLogger(),resultObject,e,FinanceErrorConfigEnum.FINANCE_FAIL);
        }
        return resultObject;
    }

    /**
     * 提现导出(制盘)
     * @param users　当前用户
     * @param financeExtractIds　提现IDS
     * @return  ResultObject
     */
    @Override
    @Transactional
    public ResultObject<BaseExcelResponse> financeExtractExport(Users users, List<String> financeExtractIds) {
        ResultObject<BaseExcelResponse> resultObject = new ResultObject<>();
        BaseExcelResponse baseExcelResponse = new BaseExcelResponse();
        try {
            //校验
            AssertUtils.isNotEmpty(this.getLogger(),financeExtractIds,FinanceErrorConfigEnum.FINANCE_PARAMETER_EXTRACT_IDS_IS_NOT_NULL);
            //数据查询
            String extractStatus = FinanceTermEnum.EXTRACT_STATUS.WITHDRAWAL_APPLY.name();
            List<FinanceExtractPo> financeExtractPoList = financeExtractExtDao.loadFinanceExtractPoList(financeExtractIds, extractStatus);
            //数据转换
            List<FinanceExtractExcelBo> financeExtractExcelBos = (List<FinanceExtractExcelBo>)this.converterList(
                    financeExtractPoList,new TypeToken<List<FinanceExtractExcelBo>>() {}.getType()
            );

            if(AssertUtils.isNotEmpty(financeExtractExcelBos)) {
                // 币种
                List<SyscodeResponse> currencySyscodes =
                        platformInternationalBaseApi.queryInternational(TerminologyTypeEnum.CURRENCY.name(),null).getData();
                // 银行代码
                List<SyscodeResponse> bankCodeSyscodes =
                        platformInternationalBaseApi.queryInternational(InternationalTypeEnum.BANK.name(),null).getData();
                // 提现状态
                List<SyscodeResponse> withdrawalStatusCodeSyscodes =
                        platformInternationalBaseApi.queryInternational(TerminologyTypeEnum.WITHDRAWAL_STATUS.name(),null).getData();
                List<String> branchIds = financeExtractExcelBos.stream()
                        .map(FinanceExtractExcelBo::getBranchId).collect(Collectors.toList());

                List<BranchResponse> branchRespFcs = this.platformBranchApi.branchsPost(branchIds).getData();

                financeExtractExcelBos.forEach((excelBo -> {
                    // 币种
                    if (AssertUtils.isNotEmpty(currencySyscodes) && AssertUtils.isNotEmpty(excelBo.getCurrencyCode())) {
                        currencySyscodes.stream()
                                .filter(syscode -> syscode.getCodeKey().equals(excelBo.getCurrencyCode()))
                                .findFirst().ifPresent(syscode -> excelBo.setCurrencyCode(syscode.getSymbol()));
                    }
                    // 开户行
                    if (AssertUtils.isNotEmpty(bankCodeSyscodes) && AssertUtils.isNotEmpty(excelBo.getBankCode())) {
                        bankCodeSyscodes.stream()
                                .filter(syscode -> syscode.getCodeKey().equals(excelBo.getBankCode()))
                                .findFirst().ifPresent(syscode -> excelBo.setBankCode(syscode.getCodeName()));
                    }
                    // 提现状态
                    if (AssertUtils.isNotEmpty(withdrawalStatusCodeSyscodes) && AssertUtils.isNotEmpty(excelBo.getExtractStatusCode())) {
                        withdrawalStatusCodeSyscodes.stream()
                                .filter(syscode -> syscode.getCodeKey().equals(excelBo.getExtractStatusCode()))
                                .findFirst().ifPresent(syscode -> excelBo.setExtractStatusCode(syscode.getCodeName()));
                    }
                    // 机构名
                    if(AssertUtils.isNotEmpty(branchRespFcs) && AssertUtils.isNotEmpty(excelBo.getBranchId())) {
                        branchRespFcs.stream()
                                .filter(branchRespFc -> excelBo.getBranchId().equals(branchRespFc.getBranchId()))
                                .findFirst().ifPresent(branchRespFc -> excelBo.setBranchName(branchRespFc.getBranchName()));
                    }
                }));
            } else {
                financeExtractExcelBos = new ArrayList<>();
            }

            //创建excel
            Workbook workbook = ExcelExportUtil.exportExcel(new ExportParams(FinanceTermEnum.EXCEL_FILE_SHEET_TITLE.FINANCE_EXTRACT_EXPORT.name(),
                            FinanceTermEnum.EXCEL_FILE_SHEET_NAME.FINANCE_EXTRACT_EXPORT.name()),
                    FinanceExtractExcelBo.class, financeExtractExcelBos);
            baseExcelResponse.setFileName(FinanceTermEnum.EXCEL_FILE_NAME.FINANCE_EXTRACT_EXPORT.name());
            baseExcelResponse.setWorkbook(workbook);
            // 国际化
            this.excelInternationalization(baseExcelResponse, true);

            if(AssertUtils.isNotEmpty(financeExtractPoList)) {
                /**
                 * 1.改制盘表状态
                 */
                financeExtractPoList.forEach(financeExtractPo -> {
                    // 提现中
                    financeExtractPo.setExtractStatusCode(FinanceTermEnum.EXTRACT_STATUS.WITHDRAWALING.name());
                    financeExtractPo.setUpdatedUserId(users.getUserId());
                    financeExtractPo.setUpdatedDate(DateUtils.getCurrentTime());
                });
                // 更新制盘表提现状态
                financeService.updateFinanceExtractPo(financeExtractPoList);

                /**
                 * 2.存批次表
                 */
                String counterofferBatchId = UUIDUtils.getUUIDStr();

                // new一个po对象
                CounterofferBatchPo batchPo =
                        financeExtractTransfer.getCounterofferBatchPo(counterofferBatchId, financeExtractPoList);
                financeService.saveCounterofferBatchPo(batchPo);

                /**
                 * 3.存明细表
                 */
                List<CounterofferDetailPo> detailPos = new ArrayList<>();
                financeExtractPoList.stream().forEach(financeExtractPo -> {
                    // new一个po对象
                    CounterofferDetailPo detailPo =
                            financeExtractTransfer.getCounterofferDetailPo(users, counterofferBatchId, financeExtractPo);
                    detailPos.add(detailPo);
                });
                financeService.saveCounterofferDetailPo(detailPos);
            }

            //赋值到返回数据
            resultObject.setData(baseExcelResponse);
        } catch (Exception e) {
            //事务回滚
            TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
            if (e instanceof RequestException) {
                RequestException error = (RequestException) e;
                //错误设置
                resultObject.setIenum(error.getiEnum());
            } else {
                resultObject.setIenum(FinanceErrorConfigEnum.FINANCE_FAIL);
            }
        }
        return resultObject;
    }

    /**
     * 分页查询提现列表
     * @param financeExtractRequest 请求对象
     * @return 提现列表
     */
    @Override
    public ResultObject<BasePageResponse<FinanceExtractResponse>> getFinanceExtractList(FinanceExtractRequest financeExtractRequest) {
        if(!AssertUtils.isNotNull(financeExtractRequest)) {
            financeExtractRequest = new FinanceExtractRequest();
        }
        ResultObject<BasePageResponse<FinanceExtractResponse>> resultObject = new ResultObject<>();
        try {
            // 数据查询
            List<FinanceExtractBo> financeExtractBos = financeExtractExtDao.getFinanceExtractList(financeExtractRequest);

            List<FinanceExtractResponse> financeExtractResponseList = (List<FinanceExtractResponse>) this.converterList(
                    financeExtractBos, new TypeToken<List<FinanceExtractResponse>>() {
                    }.getType()
            );

            if (AssertUtils.isNotEmpty(financeExtractResponseList)) {
                // 币种
                List<SyscodeResponse> currencySyscodes =
                        platformInternationalBaseApi.queryInternational(TerminologyTypeEnum.CURRENCY.name(),null).getData();
                // 银行代码
                List<SyscodeResponse> bankCodeSyscodes =
                        platformInternationalBaseApi.queryInternational(InternationalTypeEnum.BANK.name(),null).getData();
                // 获取代理人信息
                List<String> userIds = financeExtractResponseList.stream().distinct().map(
                        financeExtract -> financeExtract.getUserId()).collect(Collectors.toList());
                List<AgentResponse> agentRespFcList = this.agentExtApi.agentsGetByUserId(userIds).getData();

                financeExtractResponseList.forEach((financeExtract -> {
                    // 代理人
                    if (AssertUtils.isNotEmpty(agentRespFcList)) {
                        agentRespFcList.stream()
                                .filter(agentRespFc -> financeExtract.getUserId().equals(agentRespFc.getUserId()))
                                .findFirst().ifPresent(agentRespFc -> {
                            financeExtract.setBranchName(agentRespFc.getBranchName());
                            financeExtract.setAgentName(agentRespFc.getAgentName());
                            financeExtract.setAgentLevelCodeName(agentRespFc.getAgentLevelCodeName());
                            financeExtract.setMobile(agentRespFc.getMobile());
                        });
                    }
                    // 等待时长
                    financeExtract.setWaitTime(DateUtils.getCurrentTime() - financeExtract.getApplyDate());
                    // 币种
                    if (AssertUtils.isNotEmpty(currencySyscodes) && AssertUtils.isNotEmpty(financeExtract.getCurrencyCode())) {
                        currencySyscodes.stream()
                                .filter(syscode -> syscode.getCodeKey().equals(financeExtract.getCurrencyCode()))
                                .findFirst().ifPresent(syscode -> financeExtract.setCurrencyCode(syscode.getSymbol()));
                    }
                    // 开户行
                    if (AssertUtils.isNotEmpty(bankCodeSyscodes) && AssertUtils.isNotEmpty(financeExtract.getBankCode())) {
                        bankCodeSyscodes.stream()
                                .filter(syscode -> syscode.getCodeKey().equals(financeExtract.getBankCode()))
                                .findFirst().ifPresent(syscode -> financeExtract.setBankName(syscode.getCodeName()));
                        // 未找到国际化数据
                        if(!AssertUtils.isNotEmpty(financeExtract.getBankName())) {
                            financeExtract.setBankName(financeExtract.getBankCode());
                        }
                    }
                }));
            }
            //获取总页数
            Integer totalLine = AssertUtils.isNotNull(financeExtractBos) ? financeExtractBos.get(0).getTotalLine() : null;
            BasePageResponse basePageResponse = BasePageResponse.getData(
                    financeExtractRequest.getCurrentPage(), financeExtractRequest.getPageSize(), totalLine, financeExtractResponseList);

            resultObject.setData(basePageResponse);

        } catch (Exception e) {
            if (e instanceof RequestException) {
                RequestException error = (RequestException) e;
                //错误设置
                resultObject.setIenum(error.getiEnum());
            } else {
                resultObject.setIenum(FinanceErrorConfigEnum.FINANCE_FAIL);
            }
        }

        return resultObject;
    }
}