package com.gclife.attachment.model.policy.policy.group;

import com.gclife.attachment.model.policy.policy.PolicyPaymentBo;
import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 *         create 17-10-20
 *         description:
 */
@Data
public class GroupAttachPolicyPremiumBo {

    private String     policyPremiumId;
    private String     policyId;
    private String     payStatus;
    private String     premiumSource;
    private String     nextChargingMethod;
    private String     frequency;
    private Long       premDueDate;
    private String     chargingMethod;
    private BigDecimal periodTotalPremium;
    private BigDecimal policyBalance;
    private BigDecimal actualPremium;
    private String     validFlag;
    private String     createdUserId;
    private Long       createdDate;
    private String     updatedUserId;
    private Long       updatedDate;
    private String     paymentNo;

    /**
     * 缴费信息
     */
    private PolicyPaymentBo policyPayment;



}
