package com.gclife.attachment.model.policy.endorse;

import com.gclife.common.annotation.Internation;
import lombok.Data;

/**
 * <AUTHOR>
 * @ClassName EndorseApplicantBo
 * @Description
 * @Date 2020/1/3 下午4:29
 * @Version 1.0
 */
@Data
public class EndorsePrintApplicantBo {

    private String endorseApplicantId;
    private String validFlag;
    private String createdUserId;
    private Long   createdDate;
    private String updatedUserId;
    private String applicantId;
    private Long   updatedDate;
    private String endorseItemId;
    private String policyId;
    private String applicantType;
    private String customerId;
    private String name;
    private String englishName;
    private String idType;
    private String sex;
    private Long   birthday;
    private String idNo;
    private String idCategory;
    private Long   idExpDate;
    private String idAttachId;
    private String postalAddress;
    private String zipCode;
    private String phone;
    private String mobile;
    private String fax;
    private String otherPhone;
    private String email;
    private String headAttachId;
    private String homeAreaCode;
    private String homeAddress;
    private String homeAddressWhole;
    private String homeZipCode;
    private String homePhone;
    private String homeFax;
    private String nationality;
    private String countryCode;
    private String mrzOne;
    private String mrzTwo;
    private String nations;
    private String registerAddress;
    private String birthPlace;
    private String issuePlace;
    private String issueDate;
    private String rfidMrz;
    private String ocrMrz;
    private String marriage;
    private String health;
    private String stature;
    private String avoirdupois;
    private String degree;
    private String creditGrade;
    private String smokeFlag;
    private String bmi;
    private String license;
    private String licenseType;
    private String occupationType;
    private String occupationCode;
    private String workType;
    private String pluralityType;
    private String salary;
    private String socialSecurity;
    private String belongsCompanyAreaCode;
    private String belongsCompanyAddress;
    private String belongsCompanyAddressWhole;
    private String belongsCompanyZipCode;
    private String belongsCompanyPhone;
    private String belongsCompanyFax;
    private Long   joinCompanyDate;
    private Long   startWorkDate;
    private String position;
    private String income;
    private String incomeSource;
    private String familyIncome;
    private String familyIncomeSource;
    private String bankCode;
    private String bankAccountNo;
    private String bankAccountName;
    private String companyName;
    private String companyIdNo;
    private String companyIdType;
    private String companyType;
    private String companyIndustry;
    private String companyAreaCode;
    private String companyAddress;
    private String companyAddressWhole;
    private String companyZipCode;
    private String companyPhone;
    private String companyFax;
    private String companyContractName;
    private String companyContractIdType;
    private String companyContractIdNo;
    private String companyContractMobile;
    private String companyContractAddress;
    private String companyContractPhone;
    private String companyLegalPersonName;
    private String companyLegalPersonIdType;
    private String companyLegalPersonIdNo;
    private String wechatNo;
    private String facebookNo;
    private String customerSource;
    private String surName;
    private String givenName;
    private String companyLegalPersonNationality;
    private Long   companyLegalPersonIdExpDate;
    private String companyContractDept;
    private String companyContractPosition;
    private String companyContractNationality;
    private Long   companyContractIdExpDate;
    private String companyContractEmail;
    private String delegateName;
    private String delegateMobile;
    private String delegateIdType;
    private String delegateIdNo;
    private String taxRegistrationNo;

    private String companyAreaName;
    private String companyIndustryName;
    @Internation(filed = "delegateIdType",codeType = "ID_TYPE")
    private String delegateIdTypeName;
    private Long delegateBirthday;


    @Internation(filed = "companyContractIdType",codeType = "ID_TYPE")
    private String companyContractIdTypeName;
    @Internation(codeType = "NATIONALITY", filed = "companyContractNationality")
    private String companyContractNationalityName;
}
