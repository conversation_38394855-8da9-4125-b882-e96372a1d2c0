package com.gclife.claim.dao.impl;

import com.gclife.claim.core.jooq.tables.records.ClaimReportAttachmentRecord;
import com.gclife.claim.dao.ReportAttachmentBaseDao;
import com.gclife.claim.model.bo.ReportAttachmentBo;
import com.gclife.common.TerminologyConfigEnum;
import com.gclife.common.dao.base.impl.BaseDaoImpl;
import com.gclife.common.util.AssertUtils;
import org.jooq.Condition;
import org.jooq.Record;
import org.jooq.SelectConditionStep;
import org.jooq.UpdateSetMoreStep;
import org.springframework.stereotype.Repository;

import java.util.List;

import static com.gclife.claim.core.jooq.Tables.CLAIM_REPORT_ATTACHMENT;

/**
 * <AUTHOR>
 * @description
 * @date 2019/9/23 2:42 下午
 */
@Repository
public class ReportAttachmentBaseDaoImpl extends BaseDaoImpl implements ReportAttachmentBaseDao {

    @Override
    public List<ReportAttachmentBo> queryByReportCustomerId(String reportCustomerId) {

        SelectConditionStep<Record> where = this.getDslContext()
                .select(CLAIM_REPORT_ATTACHMENT.fields())
                .from(CLAIM_REPORT_ATTACHMENT)
                .where(CLAIM_REPORT_ATTACHMENT.REPORT_CUSTOMER_ID.eq(reportCustomerId).and(CLAIM_REPORT_ATTACHMENT.VALID_FLAG.eq(TerminologyConfigEnum.VALID_FLAG.effective.name())));
        System.out.println(where.toString());
        return where.fetchInto(ReportAttachmentBo.class);
    }

    @Override
    public void delete(String reportCustomerId, String... attachmentTypeCodes) {
        UpdateSetMoreStep<ClaimReportAttachmentRecord> updateSetMoreStep = this.getDslContext()
                .update(CLAIM_REPORT_ATTACHMENT)
                .set(CLAIM_REPORT_ATTACHMENT.VALID_FLAG, TerminologyConfigEnum.VALID_FLAG.invalid.name());

        Condition condition = CLAIM_REPORT_ATTACHMENT.REPORT_CUSTOMER_ID.eq(reportCustomerId);
        if(AssertUtils.isNotNull(attachmentTypeCodes)){
            condition = condition.and(CLAIM_REPORT_ATTACHMENT.ATTACHMENT_TYPE_CODE.in(attachmentTypeCodes));
        }

        updateSetMoreStep.where(condition);

        updateSetMoreStep.execute();
    }

}
