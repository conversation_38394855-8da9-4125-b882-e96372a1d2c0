package com.gclife.claim.service.impl;

import com.gclife.claim.core.jooq.tables.daos.*;
import com.gclife.claim.core.jooq.tables.pojos.*;
import com.gclife.claim.dao.ClaimBaseDao;
import com.gclife.claim.dao.ReportBaseDao;
import com.gclife.claim.model.bo.*;
import com.gclife.claim.model.vo.CaseRevokeListVo;
import com.gclife.claim.model.vo.ClaimPreviousListVo;
import com.gclife.claim.service.CaseBaseService;
import com.gclife.claim.service.ClaimBaseService;
import com.gclife.claim.service.RegisterBaseService;
import com.gclife.claim.service.ReportBaseService;
import com.gclife.claim.transform.LanguageUtils;
import com.gclife.common.TerminologyTypeEnum;
import com.gclife.common.model.BasePageRequest;
import com.gclife.common.model.base.Users;
import com.gclife.common.model.config.TerminologyConfigEnum;
import com.gclife.common.service.impl.BaseBusinessServiceImpl;
import com.gclife.common.util.AssertUtils;
import com.gclife.common.util.DateUtils;
import com.gclife.common.util.UUIDUtils;
import com.gclife.platform.api.PlatformInternationalBaseApi;
import com.gclife.platform.model.response.SyscodeResponse;
import org.jooq.tools.StringUtils;
import org.modelmapper.TypeToken;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description
 * @date 2019/10/9 2:13 下午
 */
@Service
public class ClaimBaseServiceImpl extends BaseBusinessServiceImpl implements ClaimBaseService {
    private static String EFFECTIVE = TerminologyConfigEnum.VALID_FLAG.effective.name();

    @Autowired
    private ClaimDao claimDao;
    @Autowired
    private ReportBaseDao reportBaseDao;
    @Autowired
    private ClaimBaseDao claimBaseDao;
    @Autowired
    private ClaimInfoDao claimInfoDao;
    @Autowired
    private ClaimPolicyDao claimPolicyDao;
    @Autowired
    private ClaimPolicyFeeDao claimPolicyFeeDao;
    @Autowired
    private ClaimPolicyBeneficiaryInfoDao claimPolicyBeneficiaryInfoDao;
    @Autowired
    private ReportBaseService reportBaseService;
    @Autowired
    private CaseBaseService caseBaseService;
    @Autowired
    private ClaimPolicyCoverageDao claimPolicyCoverageDao;
    @Autowired
    private ClaimPolicyCoverageFeeDao claimPolicyCoverageFeeDao;
    @Autowired
    private ClaimPolicyDutyDao claimPolicyDutyDao;
    @Autowired
    private ClaimPolicyDutyFeeDao claimPolicyDutyFeeDao;
    @Autowired
    private ClaimPolicyDutyGetDao claimPolicyDutyGetDao;
    @Autowired
    private ClaimPolicyDutyGetFeeDao claimPolicyDutyGetFeeDao;
    @Autowired
    private ClaimPolicyRefundDao claimPolicyRefundDao;
    @Autowired
    private ClaimPolicyCoverageRefundDao claimPolicyCoverageRefundDao;
    @Autowired
    private RegisterBaseService registerBaseService;
    @Autowired
    private PlatformInternationalBaseApi platformInternationalBaseApi;

    /**
     * 查询理赔既往赔付列表
     *
     * @param claimPreviousListVo 参数
     * @return ClaimPreviousListBos
     */
    @Override
    public List<ClaimPreviousListBo> queryClaimPreviousListList(ClaimPreviousListVo claimPreviousListVo, Users users) {
        if (AssertUtils.isNotEmpty(claimPreviousListVo.getClaimId())) {
            List<ClaimCaseRelatedPolicyPo> claimCaseRelatedPolicyPos = caseBaseService.queryClaimRelatedPolicyByClaimId(claimPreviousListVo.getClaimId());
            if (AssertUtils.isNotEmpty(claimCaseRelatedPolicyPos)) {
                claimPreviousListVo.setPolicyIds(claimCaseRelatedPolicyPos.stream().map(ClaimCaseRelatedPolicyPo::getPolicyId).distinct().collect(Collectors.toList()));
            }
        }
        List<ClaimPreviousListBo> claimPreviousListBos = claimBaseDao.queryClaimPreviousList(claimPreviousListVo);

        List<SyscodeResponse> claimTypeSysCodes = platformInternationalBaseApi.queryInternational(TerminologyTypeEnum.DUTY_TYPE.name(), users.getLanguage()).getData();
        //获取理赔项目
        if (AssertUtils.isNotEmpty(claimPreviousListBos)) {
            List<String> caseIds = claimPreviousListBos.stream().map(ClaimPreviousListBo::getCaseId).distinct().collect(Collectors.toList());
            List<ClaimCaseProjectPo> claimCaseProjectPos = caseBaseService.queryCaseProjectByIds(caseIds);
            if (AssertUtils.isNotEmpty(claimCaseProjectPos)) {
                claimPreviousListBos.forEach(claimPreviousListBo -> {
                    List<ClaimCaseProjectPo> collect = claimCaseProjectPos.stream().filter(claimCaseProjectPo -> claimCaseProjectPo.getCaseId().equals(claimPreviousListBo.getCaseId()))
                            .collect(Collectors.toList());
                    claimPreviousListBo.setListCaseProject(collect);

                    List<String> claimTypeCodeTrans = new ArrayList<>();
                    List<String> claimTypeCodes = collect.stream().map(ClaimCaseProjectPo::getClaimTypeCode).distinct().collect(Collectors.toList());
                    if (AssertUtils.isNotEmpty(claimTypeCodes) && AssertUtils.isNotEmpty(claimTypeSysCodes)) {
                        claimTypeCodes.forEach(s -> claimTypeCodeTrans.add(LanguageUtils.getCodeName(claimTypeSysCodes, s)));
                        String claimTypeNames = StringUtils.join(claimTypeCodeTrans.toArray(), ",");
                        claimPreviousListBo.setClaimTypeName(claimTypeNames);
                    }
                });
            }
        }
        return claimPreviousListBos;
    }

    @Override
    public void saveClaimPo(ClaimPo claimPo, Users users) {
        Long currentTime = DateUtils.getCurrentTime();
        if (AssertUtils.isNotEmpty(claimPo.getClaimId())) {
            claimPo.setUpdatedUserId(users.getUserId());
            claimPo.setUpdatedDate(currentTime);
            claimDao.update(claimPo);
        } else {
            claimPo.setClaimId(UUIDUtils.getUUIDShort());
            claimPo.setCreatedUserId(users.getUserId());
            claimPo.setCreatedDate(currentTime);
            claimPo.setUpdatedUserId(users.getUserId());
            claimPo.setUpdatedDate(currentTime);
            claimPo.setValidFlag(EFFECTIVE);
            claimDao.insert(claimPo);
        }
    }

    /**
     * 查询理赔主表
     *
     * @param claimId 理赔ID
     * @return ClaimPo
     */
    @Override
    public ClaimPo queryClaimPo(String claimId) {
        return claimBaseDao.queryClaimPo(claimId);
    }

    /**
     * 查询赔案信息
     *
     * @param claimId 理赔ID
     * @return 赔案信息
     */
    @Override
    public ClaimInfoBo queryClaimInfoPo(String claimId) {
        ClaimInfoBo claimInfoBo = claimBaseDao.queryClaimInfoPo(claimId);
        if (AssertUtils.isNotNull(claimInfoBo)) {
            claimInfoBo.setClaimPolicyBos(this.queryClaimPolicyPo(claimInfoBo.getClaimInfoId()));
        }
        return claimInfoBo;
    }

    /**
     * 删除赔案信息
     *
     * @param claimId 理赔ID
     */
    @Override
    public void deleteClaimInfoBo(String claimId) {
        ClaimInfoBo claimInfoBo = this.queryClaimInfoPo(claimId);
        if (AssertUtils.isNotNull(claimInfoBo)) {
            List<ClaimPolicyBo> claimPolicyBos = claimInfoBo.getClaimPolicyBos();
            if (AssertUtils.isNotEmpty(claimPolicyBos)) {
                claimPolicyDao.delete((List<ClaimPolicyPo>) this.converterList(claimPolicyBos, new TypeToken<List<ClaimPolicyPo>>() {
                }.getType()));
            }
            claimInfoDao.delete(claimInfoBo);
        }
    }

    /**
     * 查询赔案信息
     *
     * @param claimId 理赔ID
     * @return 赔案信息
     */
    @Override
    public ClaimInfoPo queryClaimInfo(String claimId) {
        return claimBaseDao.queryClaimInfoPo(claimId);
    }

    /**
     * 保存赔案信息
     *
     * @param claimInfoPo 赔案信息
     * @param users       用户
     */
    @Override
    public void saveClaimInfoPo(ClaimInfoPo claimInfoPo, Users users) {
        Long currentTime = DateUtils.getCurrentTime();
        if (AssertUtils.isNotEmpty(claimInfoPo.getClaimInfoId())) {
            claimInfoPo.setUpdatedUserId(users.getUserId());
            claimInfoPo.setUpdatedDate(currentTime);
            claimInfoDao.update(claimInfoPo);
        } else {
            claimInfoPo.setClaimInfoId(UUIDUtils.getUUIDShort());
            claimInfoPo.setCreatedUserId(users.getUserId());
            claimInfoPo.setCreatedDate(currentTime);
            claimInfoPo.setUpdatedUserId(users.getUserId());
            claimInfoPo.setUpdatedDate(currentTime);
            claimInfoPo.setValidFlag(EFFECTIVE);
            claimInfoDao.insert(claimInfoPo);
        }
    }

    /**
     * 查询赔案保单信息
     *
     * @param claimInfoId 赔案信息ID
     * @return 赔案保单信息
     */
    @Override
    public List<ClaimPolicyBo> queryClaimPolicyPo(String claimInfoId) {
        List<ClaimPolicyBo> claimPolicyBos = claimBaseDao.queryClaimPolicyPo(claimInfoId);
        if (AssertUtils.isNotEmpty(claimPolicyBos)) {
            claimPolicyBos.forEach(claimPolicyBo -> {
                String claimPolicyId = claimPolicyBo.getClaimPolicyId();
                claimPolicyBo.setClaimPolicyFee(this.queryClaimPolicyFeePo(claimPolicyId));
                claimPolicyBo.setListClaimPolicyCoverage(this.queryClaimPolicyCoverage(claimPolicyId));
                claimPolicyBo.setListClaimPolicyBeneficiary(this.queryClaimPolicyBeneficiaryInfo(claimPolicyId));
                claimPolicyBo.setListClaimPolicyRefund(this.queryClaimPolicyRefund(claimPolicyId));
            });
        }
        return claimPolicyBos;
    }

    /**
     * 查询赔案保单信息
     *
     * @param claimPolicyId 赔案保单Id
     * @return ClaimPolicyBo
     */
    @Override
    public ClaimPolicyBo queryOneClaimPolicyPo(String claimPolicyId) {
        ClaimPolicyBo claimPolicyBo = claimBaseDao.queryOneClaimPolicyPo(claimPolicyId);
        if (AssertUtils.isNotNull(claimPolicyBo)) {
            claimPolicyBo.setClaimPolicyFee(this.queryClaimPolicyFeePo(claimPolicyId));
            claimPolicyBo.setListClaimPolicyBeneficiary(this.queryClaimPolicyBeneficiaryInfo(claimPolicyId));
            claimPolicyBo.setListClaimPolicyCoverage(this.queryClaimPolicyCoverage(claimPolicyId));
            claimPolicyBo.setListClaimPolicyRefund(this.queryClaimPolicyRefund(claimPolicyId));
        }
        return claimPolicyBo;
    }

    /**
     * 保存赔案保单信息
     *
     * @param claimPolicyPo 赔案保单信息
     * @param users         用户
     */
    @Override
    public void saveClaimPolicyPo(ClaimPolicyPo claimPolicyPo, Users users) {
        Long currentTime = DateUtils.getCurrentTime();
        if (AssertUtils.isNotEmpty(claimPolicyPo.getClaimPolicyId())) {
            claimPolicyPo.setUpdatedUserId(users.getUserId());
            claimPolicyPo.setUpdatedDate(currentTime);
            claimPolicyDao.update(claimPolicyPo);
        } else {
            claimPolicyPo.setClaimPolicyId(UUIDUtils.getUUIDShort());
            claimPolicyPo.setCreatedUserId(users.getUserId());
            claimPolicyPo.setCreatedDate(currentTime);
            claimPolicyPo.setUpdatedUserId(users.getUserId());
            claimPolicyPo.setUpdatedDate(currentTime);
            claimPolicyPo.setValidFlag(EFFECTIVE);
            claimPolicyDao.insert(claimPolicyPo);
        }
    }

    /**
     * 查询赔案保单费用明细
     *
     * @param claimPolicyId 赔案保单ID
     * @return 赔案保单费用明细
     */
    @Override
    public ClaimPolicyFeePo queryClaimPolicyFeePo(String claimPolicyId) {
        return claimBaseDao.queryClaimPolicyFeePo(claimPolicyId);
    }

    /**
     * 保存赔案保单费用明细
     *
     * @param claimPolicyFeePo 赔案保单费用明细
     * @param users            用户
     */
    @Override
    public void saveClaimPolicyFeePo(ClaimPolicyFeePo claimPolicyFeePo, Users users) {
        Long currentTime = DateUtils.getCurrentTime();
        if (AssertUtils.isNotEmpty(claimPolicyFeePo.getClaimPolicyFeeId())) {
            claimPolicyFeePo.setUpdatedUserId(users.getUserId());
            claimPolicyFeePo.setUpdatedDate(currentTime);
            claimPolicyFeeDao.update(claimPolicyFeePo);
        } else {
            claimPolicyFeePo.setClaimPolicyFeeId(UUIDUtils.getUUIDShort());
            claimPolicyFeePo.setCreatedUserId(users.getUserId());
            claimPolicyFeePo.setCreatedDate(currentTime);
            claimPolicyFeePo.setUpdatedUserId(users.getUserId());
            claimPolicyFeePo.setUpdatedDate(currentTime);
            claimPolicyFeePo.setValidFlag(EFFECTIVE);
            claimPolicyFeeDao.insert(claimPolicyFeePo);
        }
    }

    /**
     * 查询赔案保单受益人
     *
     * @param claimPolicyId 赔案信息ID
     * @return 赔案保单受益人
     */
    @Override
    public List<ClaimPolicyBeneficiaryInfoPo> queryClaimPolicyBeneficiaryInfo(String claimPolicyId) {
        return claimBaseDao.queryClaimPolicyBeneficiaryInfo(claimPolicyId);
    }

    /**
     * 查询赔案保单受益人
     *
     * @param claimPolicyBeneficiaryId 赔案保单受益人ID
     * @return 赔案保单受益人
     */
    @Override
    public ClaimPolicyBeneficiaryInfoPo queryOneClaimPolicyBeneficiaryInfo(String claimPolicyBeneficiaryId) {
        return claimBaseDao.queryOneClaimPolicyBeneficiaryInfo(claimPolicyBeneficiaryId);
    }

    /**
     * 保存赔案保单受益人
     *
     * @param claimPolicyBeneficiaryInfoPo 赔案保单受益人
     * @param users                        用户
     */
    @Override
    public void saveClaimPolicyBeneficiaryInfoPo(ClaimPolicyBeneficiaryInfoPo claimPolicyBeneficiaryInfoPo, Users users) {
        Long currentTime = DateUtils.getCurrentTime();
        if (AssertUtils.isNotEmpty(claimPolicyBeneficiaryInfoPo.getClaimPolicyBeneficiaryId())) {
            claimPolicyBeneficiaryInfoPo.setUpdatedUserId(users.getUserId());
            claimPolicyBeneficiaryInfoPo.setUpdatedDate(currentTime);
            claimPolicyBeneficiaryInfoDao.update(claimPolicyBeneficiaryInfoPo);
        } else {
            claimPolicyBeneficiaryInfoPo.setClaimPolicyBeneficiaryId(UUIDUtils.getUUIDShort());
            claimPolicyBeneficiaryInfoPo.setCreatedUserId(users.getUserId());
            claimPolicyBeneficiaryInfoPo.setCreatedDate(currentTime);
            claimPolicyBeneficiaryInfoPo.setUpdatedUserId(users.getUserId());
            claimPolicyBeneficiaryInfoPo.setUpdatedDate(currentTime);
            claimPolicyBeneficiaryInfoPo.setValidFlag(EFFECTIVE);
            claimPolicyBeneficiaryInfoDao.insert(claimPolicyBeneficiaryInfoPo);
        }
    }

    /**
     * 批量保存赔案保单受益人
     *
     * @param claimPolicyBeneficiaryInfoPos 赔案保单受益人
     * @param users                         用户
     */
    @Override
    public void saveClaimPolicyBeneficiaryInfoPo(List<ClaimPolicyBeneficiaryInfoPo> claimPolicyBeneficiaryInfoPos, Users users) {
        if (AssertUtils.isNotEmpty(claimPolicyBeneficiaryInfoPos)) {
            claimPolicyBeneficiaryInfoPos.forEach(claimPolicyBeneficiaryInfoPo -> this.saveClaimPolicyBeneficiaryInfoPo(claimPolicyBeneficiaryInfoPo, users));
        }
    }

    @Override
    public void updateStatus(String claimId, String claimStatusName, Users users) {
        String userId = users.getUserId();
        ClaimPo claimPo = claimDao.fetchOneByClaimId(claimId);
        if (!AssertUtils.isNotNull(claimPo)) {
            return;
        }
        claimPo.setClaimStatus(claimStatusName);
        this.saveClaimPo(claimPo, users);

        ClaimReportPo claimReportPo = reportBaseDao.queryByClaimId(claimId);
        if (AssertUtils.isNotNull(claimReportPo)) {
            claimReportPo.setReportStatus(claimStatusName);
            reportBaseService.insertOrUpdate(claimReportPo, users);
        }

        ClaimRegisterPo claimRegisterPo = registerBaseService.queryClaimRegisterPo(null, claimId);
        if (AssertUtils.isNotNull(claimRegisterPo)) {
            claimRegisterPo.setRegisterStatus(claimStatusName);
            registerBaseService.saveClaimRegister(userId, claimRegisterPo);

            ClaimCasePo claimCase = caseBaseService.queryClaimCasePo(claimRegisterPo.getRegisterId());
            if (AssertUtils.isNotNull(claimCase)) {
                claimCase.setCaseStatus(claimStatusName);
                caseBaseService.saveClaimCase(claimCase, userId);
            }
        }
    }

    /**
     * 查询保单险种赔付明细
     *
     * @param claimPolicyId 保单赔付ID
     * @return ClaimPolicyCoverageBos
     */
    @Override
    public List<ClaimPolicyCoverageBo> queryClaimPolicyCoverage(String claimPolicyId) {
        List<ClaimPolicyCoverageBo> claimPolicyCoverageBos = claimBaseDao.queryClaimPolicyCoverage(claimPolicyId);
        if (AssertUtils.isNotEmpty(claimPolicyCoverageBos)) {
            claimPolicyCoverageBos.forEach(claimPolicyCoverageBo -> {
                String claimPolicyCoverageId = claimPolicyCoverageBo.getClaimPolicyCoverageId();
                claimPolicyCoverageBo.setClaimPolicyCoverageFee(this.queryClaimPolicyCoverageFee(claimPolicyCoverageId));
                claimPolicyCoverageBo.setListClaimPolicyDuty(this.queryClaimPolicyDuty(claimPolicyCoverageId));
            });
        }
        return claimPolicyCoverageBos;
    }

    /**
     * 查询保单险种赔付费用明细
     *
     * @param claimPolicyCoverageId 保单险种赔付明细ID
     * @return ClaimPolicyCoverageFeePo
     */
    @Override
    public ClaimPolicyCoverageFeePo queryClaimPolicyCoverageFee(String claimPolicyCoverageId) {
        return claimBaseDao.queryClaimPolicyCoverageFee(claimPolicyCoverageId);
    }

    /**
     * 查询保单险种责任赔付明细
     *
     * @param claimPolicyCoverageId 保单险种赔付明细ID
     * @return ClaimPolicyDutyBos
     */
    @Override
    public List<ClaimPolicyDutyBo> queryClaimPolicyDuty(String claimPolicyCoverageId) {
        List<ClaimPolicyDutyBo> claimPolicyDutyBos = claimBaseDao.queryClaimPolicyDuty(claimPolicyCoverageId);
        if (AssertUtils.isNotEmpty(claimPolicyDutyBos)) {
            claimPolicyDutyBos.forEach(claimPolicyDutyBo -> {
                String claimPolicyDutyId = claimPolicyDutyBo.getClaimPolicyDutyId();

                claimPolicyDutyBo.setClaimPolicyDutyFee(this.queryClaimPolicyDutyFee(claimPolicyDutyId));
                claimPolicyDutyBo.setListClaimPolicyDutyGet(this.queryClaimPolicyDutyGet(claimPolicyDutyId));
            });
        }
        return claimPolicyDutyBos;
    }

    /**
     * 查询保单险种责任费用明细
     *
     * @param claimPolicyDutyId 保单险种责任赔付明细ID
     * @return ClaimPolicyDutyFeePo
     */
    @Override
    public ClaimPolicyDutyFeePo queryClaimPolicyDutyFee(String claimPolicyDutyId) {
        return claimBaseDao.queryClaimPolicyDutyFee(claimPolicyDutyId);
    }

    /**
     * 查询保单险种给付责任赔付明细
     *
     * @param claimPolicyDutyId 保单险种责任赔付明细ID
     * @return ClaimPolicyDutyGetBos
     */
    @Override
    public List<ClaimPolicyDutyGetBo> queryClaimPolicyDutyGet(String claimPolicyDutyId) {
        List<ClaimPolicyDutyGetBo> claimPolicyDutyGetBos = claimBaseDao.queryClaimPolicyDutyGet(claimPolicyDutyId);
        if (AssertUtils.isNotEmpty(claimPolicyDutyGetBos)) {
            claimPolicyDutyGetBos.forEach(claimPolicyDutyGetBo -> claimPolicyDutyGetBo.setClaimPolicyDutyGetFee(this.queryClaimPolicyDutyGetFee(claimPolicyDutyGetBo.getClaimPolicyDutyGetId())));
        }
        return claimPolicyDutyGetBos;
    }

    /**
     * 保查询单险种给付责任赔付费用明细
     *
     * @param claimPolicyDutyGetId 保单险种给付责任赔付明细ID
     * @return ClaimPolicyDutyGetFeePo
     */
    @Override
    public ClaimPolicyDutyGetFeePo queryClaimPolicyDutyGetFee(String claimPolicyDutyGetId) {
        return claimBaseDao.queryClaimPolicyDutyGetFee(claimPolicyDutyGetId);
    }

    /**
     * 删除赔付保单险种信息
     *
     * @param claimPolicyId 赔付保单ID
     */
    @Override
    public void deleteClaimPolicyCoverageBo(String claimPolicyId) {
        List<ClaimPolicyRefundPo> claimPolicyRefundPos = this.queryClaimPolicyRefund(claimPolicyId);
        if (AssertUtils.isNotEmpty(claimPolicyRefundPos)) {
            claimPolicyRefundDao.delete(claimPolicyRefundPos);
        }
        List<ClaimPolicyCoverageRefundPo> claimPolicyCoverageRefundPos = this.queryClaimPolicyCoverageRefund(claimPolicyId);
        if (AssertUtils.isNotEmpty(claimPolicyCoverageRefundPos)) {
            claimPolicyCoverageRefundDao.delete(claimPolicyCoverageRefundPos);
        }
        ClaimPolicyFeePo claimPolicyFeePo = this.queryClaimPolicyFeePo(claimPolicyId);
        if (AssertUtils.isNotNull(claimPolicyFeePo)) {
            claimPolicyFeeDao.delete(claimPolicyFeePo);
        }
        List<ClaimPolicyCoverageBo> claimPolicyCoverageBos = this.queryClaimPolicyCoverage(claimPolicyId);
        if (!AssertUtils.isNotEmpty(claimPolicyCoverageBos)) {
            return;
        }
        claimPolicyCoverageBos.forEach(claimPolicyCoverageBo -> {
            claimPolicyCoverageFeeDao.delete(claimPolicyCoverageBo.getClaimPolicyCoverageFee());

            List<ClaimPolicyDutyBo> listClaimPolicyDuty = claimPolicyCoverageBo.getListClaimPolicyDuty();
            if (!AssertUtils.isNotEmpty(listClaimPolicyDuty)) {
                return;
            }
            listClaimPolicyDuty.forEach(claimPolicyDutyBo -> {
                claimPolicyDutyFeeDao.delete(claimPolicyDutyBo.getClaimPolicyDutyFee());

                List<ClaimPolicyDutyGetBo> listClaimPolicyDutyGet = claimPolicyDutyBo.getListClaimPolicyDutyGet();
                if (!AssertUtils.isNotEmpty(listClaimPolicyDutyGet)) {
                    return;
                }
                listClaimPolicyDutyGet.forEach(claimPolicyDutyGetBo -> {
                    claimPolicyDutyGetFeeDao.delete(claimPolicyDutyGetBo.getClaimPolicyDutyGetFee());
                });
                claimPolicyDutyGetDao.delete((List<ClaimPolicyDutyGetPo>) this.converterList(listClaimPolicyDutyGet, new TypeToken<List<ClaimPolicyDutyGetPo>>() {
                }.getType()));
            });
            claimPolicyDutyDao.delete((List<ClaimPolicyDutyPo>) this.converterList(listClaimPolicyDuty, new TypeToken<List<ClaimPolicyDutyPo>>() {
            }.getType()));
        });
        claimPolicyCoverageDao.delete((List<ClaimPolicyCoveragePo>) this.converterList(claimPolicyCoverageBos, new TypeToken<List<ClaimPolicyCoveragePo>>() {
        }.getType()));
    }

    @Override
    public List<CaseRevokeListBo> queryCaseRevokeList(CaseRevokeListVo registerListVo) {

        return claimBaseDao.queryCaseRevokeList(registerListVo);
    }

    /**
     * 保存赔付保单险种
     *
     * @param claimPolicyCoverageBos 赔付保单险种
     */
    @Override
    public void saveClaimPolicyCoverage(List<ClaimPolicyCoverageBo> claimPolicyCoverageBos) {
        if (!AssertUtils.isNotEmpty(claimPolicyCoverageBos)) {
            return;
        }
        claimPolicyCoverageDao.insert((List<ClaimPolicyCoveragePo>) this.converterList(claimPolicyCoverageBos, new TypeToken<List<ClaimPolicyCoveragePo>>() {
        }.getType()));
    }

    /**
     * 保存赔付保单险种费用明细
     *
     * @param claimPolicyCoverageFeePo 赔付保单险种费用明细
     * @param userId                   用户
     */
    @Override
    public void saveClaimPolicyCoverageFee(ClaimPolicyCoverageFeePo claimPolicyCoverageFeePo, String userId) {
        Long currentTime = DateUtils.getCurrentTime();
        if (AssertUtils.isNotEmpty(claimPolicyCoverageFeePo.getClaimPolicyCoverageFeeId())) {
            claimPolicyCoverageFeePo.setUpdatedUserId(userId);
            claimPolicyCoverageFeePo.setUpdatedDate(currentTime);
            claimPolicyCoverageFeeDao.update(claimPolicyCoverageFeePo);
        } else {
            claimPolicyCoverageFeePo.setClaimPolicyCoverageFeeId(UUIDUtils.getUUIDShort());
            claimPolicyCoverageFeePo.setCreatedUserId(userId);
            claimPolicyCoverageFeePo.setCreatedDate(currentTime);
            claimPolicyCoverageFeePo.setUpdatedUserId(userId);
            claimPolicyCoverageFeePo.setUpdatedDate(currentTime);
            claimPolicyCoverageFeePo.setValidFlag(EFFECTIVE);
            claimPolicyCoverageFeeDao.insert(claimPolicyCoverageFeePo);
        }
    }

    /**
     * 保存赔付保单险种责任明细
     *
     * @param claimPolicyDutyBos 赔付保单险种责任明细
     */
    @Override
    public void saveClaimPolicyDuty(List<ClaimPolicyDutyBo> claimPolicyDutyBos) {
        if (!AssertUtils.isNotEmpty(claimPolicyDutyBos)) {
            return;
        }
        claimPolicyDutyDao.insert((List<ClaimPolicyDutyPo>) this.converterList(claimPolicyDutyBos, new TypeToken<List<ClaimPolicyDutyPo>>() {
        }.getType()));
    }

    /**
     * 保存赔付保单险种责任费用明细
     *
     * @param claimPolicyDutyFeePo 赔付保单险种责任费用明细
     * @param userId               用户
     */
    @Override
    public void saveClaimPolicyDutyFee(ClaimPolicyDutyFeePo claimPolicyDutyFeePo, String userId) {
        Long currentTime = DateUtils.getCurrentTime();
        if (AssertUtils.isNotEmpty(claimPolicyDutyFeePo.getClaimPolicyDutyFeeId())) {
            claimPolicyDutyFeePo.setUpdatedUserId(userId);
            claimPolicyDutyFeePo.setUpdatedDate(currentTime);
            claimPolicyDutyFeeDao.update(claimPolicyDutyFeePo);
        } else {
            claimPolicyDutyFeePo.setClaimPolicyDutyFeeId(UUIDUtils.getUUIDShort());
            claimPolicyDutyFeePo.setCreatedUserId(userId);
            claimPolicyDutyFeePo.setCreatedDate(currentTime);
            claimPolicyDutyFeePo.setUpdatedUserId(userId);
            claimPolicyDutyFeePo.setUpdatedDate(currentTime);
            claimPolicyDutyFeePo.setValidFlag(EFFECTIVE);
            claimPolicyDutyFeeDao.insert(claimPolicyDutyFeePo);
        }
    }

    /**
     * 保存赔付保单险种给付责任明细
     *
     * @param claimPolicyDutyGetBos 赔付保单险种给付责任明细
     */
    @Override
    public void saveClaimPolicyDutyGet(List<ClaimPolicyDutyGetBo> claimPolicyDutyGetBos) {
        if (!AssertUtils.isNotEmpty(claimPolicyDutyGetBos)) {
            return;
        }
        claimPolicyDutyGetDao.insert((List<ClaimPolicyDutyGetPo>) this.converterList(claimPolicyDutyGetBos, new TypeToken<List<ClaimPolicyDutyGetPo>>() {
        }.getType()));
    }

    /**
     * 保存赔付保单险种给付责任费用明细
     *
     * @param claimPolicyDutyGetFeePos 赔付保单险种给付责任费用明细
     */
    @Override
    public void saveClaimPolicyDutyGetFee(List<ClaimPolicyDutyGetFeePo> claimPolicyDutyGetFeePos) {
        if (!AssertUtils.isNotEmpty(claimPolicyDutyGetFeePos)) {
            return;
        }
        claimPolicyDutyGetFeeDao.insert((List<ClaimPolicyDutyGetFeePo>) this.converterList(claimPolicyDutyGetFeePos, new TypeToken<List<ClaimPolicyDutyGetFeePo>>() {
        }.getType()));
    }

    @Override
    public InstrumentPanelBo queryInstrumentPanel() {

        return claimBaseDao.queryInstrumentPanel();
    }

    /**
     * 理赔保单退费信息
     *
     * @param claimPolicyId 理赔保单ID
     * @return ClaimPolicyRefundBos
     */
    @Override
    public List<ClaimPolicyRefundPo> queryClaimPolicyRefund(String claimPolicyId) {
        return claimBaseDao.queryClaimPolicyRefund(claimPolicyId);
    }

    /**
     * 保存赔案保单退费明细
     *
     * @param claimPolicyRefundPos 赔案保单退费明细
     * @param userId               用户ID
     */
    @Override
    public void saveClaimPolicyRefundPo(List<ClaimPolicyRefundPo> claimPolicyRefundPos, String userId) {
        if (!AssertUtils.isNotEmpty(claimPolicyRefundPos)) {
            return;
        }
        Long currentTime = DateUtils.getCurrentTime();
        List<ClaimPolicyRefundPo> insertData = new ArrayList<>();
        List<ClaimPolicyRefundPo> updateData = new ArrayList<>();
        claimPolicyRefundPos.forEach(claimPolicyRefundPo -> {
            if (AssertUtils.isNotEmpty(claimPolicyRefundPo.getClaimPolicyRefundId())) {
                claimPolicyRefundPo.setUpdatedDate(currentTime);
                claimPolicyRefundPo.setUpdatedUserId(userId);
                updateData.add(claimPolicyRefundPo);
            } else {
                claimPolicyRefundPo.setClaimPolicyRefundId(UUIDUtils.getUUIDShort());
                claimPolicyRefundPo.setCreatedDate(currentTime);
                claimPolicyRefundPo.setCreatedUserId(userId);
                claimPolicyRefundPo.setValidFlag(EFFECTIVE);
                insertData.add(claimPolicyRefundPo);
            }
        });
        claimPolicyRefundDao.insert(insertData);
        claimPolicyRefundDao.update(updateData);
    }

    /**
     * 理赔保单险种退费信息
     *
     * @param claimPolicyId 理赔保单ID
     * @return ClaimPolicyRefundBos
     */
    @Override
    public List<ClaimPolicyCoverageRefundPo> queryClaimPolicyCoverageRefund(String claimPolicyId) {
        return claimBaseDao.queryClaimPolicyCoverageRefund(claimPolicyId);
    }

    /**
     * 保存赔案保单险种退费明细
     *
     * @param claimPolicyCoverageRefundPos 赔案保单险种退费明细
     * @param userId                       用户ID
     */
    @Override
    public void saveClaimPolicyCoverageRefundPo(List<ClaimPolicyCoverageRefundPo> claimPolicyCoverageRefundPos, String userId) {
        if (!AssertUtils.isNotEmpty(claimPolicyCoverageRefundPos)) {
            return;
        }
        Long currentTime = DateUtils.getCurrentTime();
        List<ClaimPolicyCoverageRefundPo> insertData = new ArrayList<>();
        List<ClaimPolicyCoverageRefundPo> updateData = new ArrayList<>();
        claimPolicyCoverageRefundPos.forEach(claimPolicyCoverageRefundPo -> {
            if (AssertUtils.isNotEmpty(claimPolicyCoverageRefundPo.getCpcrId())) {
                claimPolicyCoverageRefundPo.setUpdatedDate(currentTime);
                claimPolicyCoverageRefundPo.setUpdatedUserId(userId);
                updateData.add(claimPolicyCoverageRefundPo);
            } else {
                claimPolicyCoverageRefundPo.setCpcrId(UUIDUtils.getUUIDShort());
                claimPolicyCoverageRefundPo.setCreatedDate(currentTime);
                claimPolicyCoverageRefundPo.setCreatedUserId(userId);
                claimPolicyCoverageRefundPo.setValidFlag(EFFECTIVE);
                insertData.add(claimPolicyCoverageRefundPo);
            }
        });
        claimPolicyCoverageRefundDao.insert(insertData);
        claimPolicyCoverageRefundDao.update(updateData);
    }

    /**
     * 模糊查询医院列表
     *
     * @param keyword 关键字
     * @return HospitalPos
     */
    @Override
    public List<HospitalPo> queryHospitalFuzzy(String keyword) {
        return claimBaseDao.queryHospitalFuzzy(keyword);
    }

    /**
     * 查询理赔报表
     *
     * @param basePageRequest 分页参数
     * @param startDate       开始日期
     * @return ClaimReportBos
     */
    @Override
    public List<ClaimReportBo> queryListClaimReportBo(BasePageRequest basePageRequest, String startDate) {
        return claimBaseDao.queryListClaimReportBo(basePageRequest,startDate);
    }

    @Override
    public ClaimBo queryClaimInformation(String claimId) {

        return claimBaseDao.queryClaimInformation(claimId);
    }

    @Override
    public List<ClaimPolicyBo> queryClaimPolicyBo(List<String> claimInfoIdList) {

        return claimBaseDao.queryClaimPolicyBo(claimInfoIdList);
    }

    @Override
    public List<ClaimPreviousListBo> queryCustomerClaimHistory(String customerId) {
        return claimBaseDao.queryCustomerClaimHistory(customerId);
    }

    @Override
    public List<ClaimPreviousListBo> queryCustomerAppClaimHistory(String customerId) {
        return claimBaseDao.queryCustomerAppClaimHistory(customerId);
    }
}
