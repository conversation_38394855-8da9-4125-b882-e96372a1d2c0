package com.gclife.claim.model.bo;

import lombok.Data;

/**
 * Author luobin
 * Create 2020/12/18 上午9:42
 * Description:
 */
public class PolicyHookBo {
    private boolean groupFlag;
    private String claimId;
    private String policyId;
    private String customerId;

    public PolicyHookBo() {
    }

    public PolicyHookBo(boolean groupFlag, String policyId, String customerId) {
        this.groupFlag = groupFlag;
        this.policyId = policyId;
        this.customerId = customerId;
    }

    public boolean isGroupFlag() {
        return groupFlag;
    }

    public void setGroupFlag(boolean groupFlag) {
        this.groupFlag = groupFlag;
    }

    public String getClaimId() {
        return claimId;
    }

    public void setClaimId(String claimId) {
        this.claimId = claimId;
    }

    public String getPolicyId() {
        return policyId;
    }

    public void setPolicyId(String policyId) {
        this.policyId = policyId;
    }

    public String getCustomerId() {
        return customerId;
    }

    public void setCustomerId(String customerId) {
        this.customerId = customerId;
    }
}
