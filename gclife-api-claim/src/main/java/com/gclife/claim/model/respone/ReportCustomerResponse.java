package com.gclife.claim.model.respone;

import com.gclife.common.annotation.DateFormat;
import com.gclife.common.annotation.Internation;
import com.gclife.common.model.config.DateFormatPatternEnum;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

@Data
public class ReportCustomerResponse {
    @ApiModelProperty(example = "理赔号")
    private String claimId;
    @ApiModelProperty(example = "报案ID")
    private String reportId;
    @ApiModelProperty(example = "报案号")
    private String reportNo;
    @ApiModelProperty(example = "客户ID")
    private String customerId;
    @ApiModelProperty(example = "保单ID")
    private List<String> policyId;
    @ApiModelProperty(example = "保单No")
    private List<String> policyNo;
    @ApiModelProperty(example = "客户号码")
    private String customerNo;
    @ApiModelProperty(example = "客户姓名")
    private String name;
    @ApiModelProperty(example = "性别名称")
    private String sex;
    @ApiModelProperty(example = "性别名字")
    @Internation(filed = "sex", codeType = "GENDER")
    private String sexName;
    @ApiModelProperty(example = "证件号码")
    private String idNo;
    @ApiModelProperty(example = "证件类型")
    private String idType;
    @ApiModelProperty(example = "Id类型")
    @Internation(filed = "idType", codeType = "ID_TYPE")
    private String idTypeName;
    @ApiModelProperty(example = "生日")
    private Long birthday;
    @ApiModelProperty(example = "生日格式化")
    @DateFormat(filed = "birthday", pattern = DateFormatPatternEnum.FORMATE3)
    private String birthdayFormat;
    @ApiModelProperty(example = "手机号码")
    private String mobile;
    @ApiModelProperty(example = "客户主键ID")
    private String email;
    @ApiModelProperty(example = "家庭地址地区编码")
    private String homeAreaCode;
    @ApiModelProperty(example = "家庭地址地区名称")
    private String homeAreaName;
    @ApiModelProperty(example = "家庭地址")
    private String homeAddress;
}
