package com.gclife.workflow.api;

import com.gclife.common.model.ResultObject;
import com.gclife.workflow.model.request.*;
import com.gclife.workflow.model.response.WorkItemResponse;
import com.gclife.workflow.model.response.WorkItemTrackResponse;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

/**
 * @Auther: chenjinrong
 * @Date: 19-7-2 14:43
 * @Description:
 */
@FeignClient(name = "gclife-workflow-service")
public interface WorkFlowApi {
    /**
     * 开启流程
     * @param startProcessRequest
     * @return
     */
    @PostMapping("/v1/workflow/start")
    ResultObject startProcess(@RequestBody StartProcessRequest startProcessRequest);

    /**
     * 查询代办任务列表
     * @param waitingTaskRequest
     * @return
     */
    @PostMapping("/v1/workflow/tasks")
    ResultObject<List<WorkItemResponse>> queryWaitingTasks(@RequestBody WaitingTaskRequest waitingTaskRequest);

    /**
     * 签收任务
     *
     * @param userId
     * @param businessId
     * @param workflowItemType
     * @param isCompleteFlag
     * @return
     */
    @GetMapping("/v1/workflow/task/claim")
    ResultObject claimTask(@RequestParam(name = "userId") String userId,
                             @RequestParam(name = "businessId") String businessId,
                             @RequestParam(name = "workflowItemType") String workflowItemType,
                             @RequestParam(name = "isCompleteFlag") String isCompleteFlag);

    /**
     * 完成任务
     *
     * @param completeTaskParam
     * @return
     */
    @PostMapping("/v1/workflow/task/complete")
    ResultObject completeTask(@RequestBody CompleteTaskParam completeTaskParam);

    /**
     * 查询流程轨迹
     * @param businessId 业务单ID
     * @return
     */
    @GetMapping("/v1/workflow/task/track/{businessId}")
    ResultObject<List<WorkItemTrackResponse>> businessTrack(@RequestParam(name = "businessId") String businessId);

    @GetMapping("/v1/workflow/task/track/condition")
    ResultObject<List<WorkItemTrackResponse>> businessTrackByCondition(@RequestParam(name = "businessId") String businessId,@RequestParam(name = "workflowItemType") String workflowItemType);

    /**
     * 支付成功调用工作流
     *
     * @param businessId
     * @param workflowType
     * @return
     */
    @GetMapping("/v1/workflow/base/notify/payment/{businessId}")
    ResultObject paymentNotify(@RequestParam(name = "businessId") String businessId, @RequestParam(name = "workflowType") String workflowType);

    /**
     * 流程回退
     * @param rejectTaskRequest
     * @return
     */
    @PostMapping("/v1/workflow/task/reject")
    ResultObject rejectTask(@RequestBody RejectTaskRequest rejectTaskRequest);

    /**
     * 获取上个节点的用户
     * @param businessId
     * @param workflowType
     * @return
     */
    @GetMapping("/v1/workflow/task/last/{businessId}")
    ResultObject<WorkItemResponse> getLastTaskInfo(@RequestParam(name = "businessId") String businessId, @RequestParam(name = "workflowType") String workflowType);

    /**
     * 流程终止
     * @param terminationTaskRequest
     * @return
     */
    @PostMapping("/v1/workflow/base/termination")
    ResultObject terminationTask(@RequestBody TerminationTaskRequest terminationTaskRequest);
}
