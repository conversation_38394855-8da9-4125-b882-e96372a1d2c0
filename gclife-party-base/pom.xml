<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">

    <parent>
        <groupId>com.gclife</groupId>
        <artifactId>gclife-business-core</artifactId>
        <version>0.0.1-SNAPSHOT</version>
    </parent>

    <modelVersion>4.0.0</modelVersion>
    <groupId>com.gclife</groupId>
    <artifactId>gclife-party-base</artifactId>
    <version>0.0.1-SNAPSHOT</version>
    <packaging>jar</packaging>
    <name>${project.artifactId}</name>
    <description>保单服务层</description>
    <organization>
        <name>大中华人寿保险股份有限公司</name>
        <url>http://www.gc-life.com</url>
    </organization>


    <developers>
        <developer>
            <name>caoqinghua</name>
            <email><EMAIL></email>
        </developer>
    </developers>

    <dependencies>

        <dependency>
            <groupId>com.gclife</groupId>
            <artifactId>gclife-api-platform</artifactId>
        </dependency>

        <dependency>
            <groupId>com.gclife</groupId>
            <artifactId>gclife-api-thirdparty</artifactId>
        </dependency>

        <dependency>
            <groupId>com.gclife</groupId>
            <artifactId>gclife-api-message</artifactId>
        </dependency>

        <dependency>
            <groupId>com.gclife</groupId>
            <artifactId>gclife-api-agent</artifactId>
        </dependency>

        <dependency>
            <groupId>com.gclife</groupId>
            <artifactId>gclife-api-attachment</artifactId>
        </dependency>

        <dependency>
            <groupId>com.gclife</groupId>
            <artifactId>gclife-api-finance</artifactId>
        </dependency>

        <dependency>
            <groupId>com.gclife</groupId>
            <artifactId>gclife-party-core</artifactId>
            <version>0.0.1-SNAPSHOT</version>
        </dependency>

    </dependencies>


</project>
