package com.gclife.party.dao.impl;

import com.gclife.common.dao.base.impl.BaseDaoImpl;
import com.gclife.party.core.jooq.tables.pojos.ReturnVisitAttachmentPo;
import com.gclife.party.core.jooq.tables.records.ReturnVisitAttachmentRecord;
import com.gclife.party.dao.ReturnVisitAttachmentBaseDao;
import com.gclife.party.model.config.PartyTermEnum;
import org.jooq.Record;
import org.jooq.SelectConditionStep;
import org.jooq.UpdateConditionStep;
import org.springframework.stereotype.Repository;

import java.util.List;

import static com.gclife.party.core.jooq.Tables.RETURN_VISIT_ATTACHMENT;

/**
 * @Auther: chenjinrong
 * @Date: 19-10-26 16:22
 * @Description:
 */
@Repository
public class ReturnVisitAttachmentBaseDaoImpl extends BaseDaoImpl implements ReturnVisitAttachmentBaseDao {

    @Override
    public List<ReturnVisitAttachmentPo> getReturnVisitAttachmentByReturnVisitId(String returnVisitId) {
        SelectConditionStep<Record> selectConditionStep = this.getDslContext().select(RETURN_VISIT_ATTACHMENT.fields())
                .from(RETURN_VISIT_ATTACHMENT)
                .where(RETURN_VISIT_ATTACHMENT.RETURN_VISIT_ID.eq(returnVisitId).and(RETURN_VISIT_ATTACHMENT.VALID_FLAG.eq(PartyTermEnum.VALID_FLAG.effective.name())));
        System.out.println(selectConditionStep.toString());
        return selectConditionStep.fetchInto(ReturnVisitAttachmentPo.class);
    }

    @Override
    public void updateInvalidById(String policyReturnVisitId) {
        UpdateConditionStep<ReturnVisitAttachmentRecord> updateConditionStep = this.getDslContext()
                .update(RETURN_VISIT_ATTACHMENT)
                .set(RETURN_VISIT_ATTACHMENT.VALID_FLAG, PartyTermEnum.VALID_FLAG.invalid.name())
                .where(RETURN_VISIT_ATTACHMENT.RETURN_VISIT_ID.eq(policyReturnVisitId).and(RETURN_VISIT_ATTACHMENT.VALID_FLAG.eq(PartyTermEnum.VALID_FLAG.effective.name())));

        int execute = updateConditionStep.execute();
    }
}
