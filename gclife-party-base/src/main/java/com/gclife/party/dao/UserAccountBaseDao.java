package com.gclife.party.dao;

import com.gclife.common.dao.base.BaseDao;
import com.gclife.party.core.jooq.tables.pojos.*;
import com.gclife.party.model.bo.AccountCashWithdrawalBatchBo;
import com.gclife.party.model.bo.AccountOperateListBo;
import com.gclife.party.model.bo.FundAccountOverviewBo;
import com.gclife.party.model.bo.UserAccountBo;
import com.gclife.party.model.vo.AccountDetailVo;
import com.gclife.party.model.vo.AccountOperateListVo;
import com.gclife.party.model.vo.AccountVo;

import java.util.List;

/**
 * <AUTHOR>
 * create 19-6-15
 * description:
 */
public interface UserAccountBaseDao extends BaseDao {

    /**
     * 获取用户下某个类型的账户
     *
     * @param userId              用户ID
     * @param userAccountTypeCode 账户类型
     * @return UserAccountBo
     */
    UserAccountBo getUserAccountBo(String userId, String userAccountTypeCode);

    /**
     * 账户流水
     *
     * @param userAccountId    用户账户ID
     * @param businessTypeCode 业务类型编码  可为空
     * @return UserAccountTransactionRecordPos
     */
    List<UserAccountTransactionRecordPo> getAccountTransactionRecord(String userAccountId, String businessTypeCode);

    /**
     * 账户不可用流水
     *
     * @param userAccountId    用户账户ID
     * @param businessTypeCode 业务类型编码  可为空
     * @return UserAccountTransactionRecordPos
     */
    List<UserAccountDisableRecordPo> getAccountDisableRecord(String userAccountId, String businessTypeCode);

    /**
     * 获取当前用户账户
     *
     * @param userId
     * @return
     */
    List<UserAccountBo> getUserAccountBo(String userId);

    /**
     * 获取当前用户账户
     *
     * @param userId
     * @return
     */
    List<UserAccountPo> getUserAccountPo(String userId);

    /**
     * 查询用户账号流水
     *
     * @param bizId      业务ID
     * @param streamCode
     * @return UserAccountTransactionRecordPo
     */
    UserAccountTransactionRecordPo getAccountTransactionRecordById(String bizId, String streamCode);

    /**
     * 查询用户不可用收入流水
     *
     * @param bizId            业务ID
     * @param businessTypeCode 业务类型编码
     * @return UserAccountDisableRecordPo
     */
    UserAccountDisableRecordPo getAccountDisableRecordById(String bizId, String businessTypeCode);

    /**
     * 查询用户不可用收入流水
     *
     * @param bizId            业务ID
     * @return UserAccountDisableRecordPo
     */
    List<UserAccountDisableRecordPo> getAccountDisableRecordById(String bizId);


    /**
     * 根据用户ID、账户类型、流水类型获取用户账户信息
     *
     * @param userId             用户
     * @param accountDetailVo 请求参数
     * @return List<UserAccountTransactionRecordPo>
     */
    List<UserAccountTransactionRecordPo> loadAccountDetail(String userId, AccountDetailVo accountDetailVo);

    /**
     * 获取用户账户
     *
     * @param userAccountId 主键
     * @return UserAccountBo
     */
    UserAccountBo getUserAccountBoById(String userAccountId);

    /**
     * 主键获取提现记录
     *
     * @param bizId 主键
     * @return CashWithdrawalRecordPo
     */
    CashWithdrawalRecordPo getCashWithdrawalRecordPoById(String bizId);

    /**
     * 获取账户解冻/冻结操作列表
     *
     * @param accountOperateListVo 列表参数
     * @return AccountOperateListBos
     */
    List<AccountOperateListBo> getAccountOperateList(AccountOperateListVo accountOperateListVo);

    /**
     * 统计账户中心数据
     *
     * @return
     */
    FundAccountOverviewBo fundAccountOverview();

    /**
     * 账户中心查询
     *
     * @param accountVo
     * @return
     */
    List<UserAccountBo> queryAccount(AccountVo accountVo);


    /**
     * 批量提现查询
     *
     * @param accountVo
     * @return
     */
    List<UserAccountBo> queryAccountCashWithdrawalList(AccountVo accountVo);


    /**
     * 获取最新一条账户审批记录
     *
     * @param userAccountId 账户ID
     * @param operateType   账户操作类型(冻结/解冻)
     * @return UserAccountOperateReviewPos
     */
    UserAccountOperateReviewPo getUserAccountOperateReview(String userAccountId, String operateType);

    /**
     * 获取账户解冻冻结审批操作列表
     *
     * @param accountOperateListVo 列表参数
     * @return AccountOperateListBos
     */
    List<AccountOperateListBo> getAccountOperateReviewList(AccountOperateListVo accountOperateListVo);

    /**
     * 查询账户信息
     *
     * @param userAccountId
     * @return
     */
    UserAccountBo queryAccountById(String userAccountId);

    List<AccountCashWithdrawalBatchBo> getCashWithdrawalBatch();


    List<AccountCashWithdrawalBatchBo>  postCashWithdrawalBatchIds(List<String> userAccountids);

    /**
     * 查询账户黑名单
     *
     * @param userId              用户
     * @param userAccountTypeCode 账户类型
     * @return UserAccountBlackPo
     */
    UserAccountBlackPo queryUserAccountBlack(String userId, String userAccountTypeCode);
}
