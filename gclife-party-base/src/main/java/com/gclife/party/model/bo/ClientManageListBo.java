package com.gclife.party.model.bo;

import com.gclife.party.core.jooq.tables.pojos.CustomerAgentPo;
import io.swagger.annotations.ApiModelProperty;

/**
 * <AUTHOR>
 * create 2022/8/10 10:03
 * description:
 */
public class ClientManageListBo extends CustomerAgentPo {
    @ApiModelProperty(example = "保障顾问ID")
    private String serviceAgentId;
    @ApiModelProperty(example = "注册时间")
    private Long registerDate;
    @ApiModelProperty(example = "认证状态")
    private String status;

    public String getServiceAgentId() {
        return serviceAgentId;
    }

    public void setServiceAgentId(String serviceAgentId) {
        this.serviceAgentId = serviceAgentId;
    }

    public Long getRegisterDate() {
        return registerDate;
    }

    public void setRegisterDate(Long registerDate) {
        this.registerDate = registerDate;
    }

    @Override
    public String getStatus() {
        return status;
    }

    @Override
    public void setStatus(String status) {
        this.status = status;
    }
}
