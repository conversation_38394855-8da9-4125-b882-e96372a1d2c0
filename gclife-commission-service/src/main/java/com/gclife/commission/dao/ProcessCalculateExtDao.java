package com.gclife.commission.dao;

import com.gclife.commission.core.jooq.tables.ProcessCalculate;
import com.gclife.commission.core.jooq.tables.pojos.ProcessCalculatePo;
import com.gclife.commission.core.jooq.tables.pojos.ProcessPolicyPo;
import com.gclife.common.dao.base.BaseDao;

import java.util.List;

/**
 * <AUTHOR>
 *         create 17-11-12
 *         description:
 */
public interface ProcessCalculateExtDao extends BaseDao {

    /**
     *获取发佣开始流程
     * @param yearMonth 年月
     * @param branchIds　机构ids
     * @return
     */
    public List<ProcessCalculatePo> getProcessCalculateList(String yearMonth, List<String> branchIds);

    /**
     *保单筛选流程count
     * @param yearMonth 年月
     * @param branchIds　机构ids
     * @return
     */
    public long getProcessCalculateListCount(String yearMonth, List<String> branchIds);
}