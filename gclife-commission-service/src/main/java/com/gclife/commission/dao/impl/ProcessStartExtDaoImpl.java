package com.gclife.commission.dao.impl;

import com.gclife.commission.core.jooq.tables.pojos.ProcessStartPo;
import com.gclife.commission.dao.ProcessStartExtDao;
import com.gclife.commission.model.config.CommissionErrorConfigEnum;
import com.gclife.commission.model.config.CommissionTermEnum;
import com.gclife.common.dao.base.impl.BaseDaoImpl;
import com.gclife.common.exception.RequestException;
import org.jooq.SelectConditionStep;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Optional;

import static com.gclife.commission.core.jooq.tables.ProcessAgent.PROCESS_AGENT;
import static com.gclife.commission.core.jooq.tables.ProcessStart.PROCESS_START;

/**
 * <AUTHOR>
 *         create 17-11-12
 *         description: 流程开始
 */
@Component
public class ProcessStartExtDaoImpl extends BaseDaoImpl implements ProcessStartExtDao {

    /**
     *获取发佣开始流程
     * @param yearMonth 年月
     * @param branchIds　机构ids
     * @return
     */
    @Override
    public List<ProcessStartPo> getProcessStartList(String yearMonth, List<String> branchIds) {
        List<ProcessStartPo> processStartPos = null;
        try {
            processStartPos = this.getDslContext()
                    .select(PROCESS_START.fields())
                    .from(PROCESS_START)
                    .where(PROCESS_START.VALID_FLAG.eq(CommissionTermEnum.VALID_FLAG.effective.name()))
                    .and(PROCESS_START.BIZ_YEAR_MONTH.eq(yearMonth))
                    .and(PROCESS_START.BRANCH_ID.in(branchIds))
                    .and(PROCESS_START.START_STATUS.eq(CommissionTermEnum.PROCESS_STATUS.COMPLETE.name()))
                    .fetchInto(ProcessStartPo.class);
            return processStartPos;
        } catch (Exception e) {
            this.getLogger().error(CommissionErrorConfigEnum.COMMISSION_QUERY_PROCESS_FAILD.getValue());
            new RequestException(CommissionErrorConfigEnum.COMMISSION_QUERY_PROCESS_FAILD);
        }
        return null;
    }


    @Override
    public long getProcessStartListCount(String yearMonth, List<String> branchIds) {
        long count=0;
        try {
            Long  processCount= this.getDslContext()
                    .selectCount()
                    .from(PROCESS_START)
                    .where(PROCESS_START.VALID_FLAG.eq(CommissionTermEnum.VALID_FLAG.effective.name()))
                    .and(PROCESS_START.BIZ_YEAR_MONTH.eq(yearMonth))
                    .and(PROCESS_START.BRANCH_ID.in(branchIds))
                    .fetchOneInto(Long.class);
            if(Optional.ofNullable(processCount).isPresent()){
                count=processCount.longValue();
            }
            return count;
        } catch (Exception e) {
            this.getLogger().error(CommissionErrorConfigEnum.COMMISSION_QUERY_PROCESS_FAILD.getValue());
            new RequestException(CommissionErrorConfigEnum.COMMISSION_QUERY_PROCESS_FAILD);
        }
        return 0;
    }
}