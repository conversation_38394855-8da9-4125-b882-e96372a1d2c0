package com.gclife.commission.model.feign;


import com.gclife.commission.core.jooq.tables.CommissionPolicy;
import com.gclife.commission.core.jooq.tables.pojos.CommissionPolicyPo;
import com.gclife.common.model.BaseRequest;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 *         create 17-10-20
 *         description:
 */
@ApiModel(description = "保单直佣计算")
public class PolicyCommissionRequest extends BaseRequest {

    @ApiModelProperty(value = "保单ID",example = "1510312336000",required = false)
    private String     policyId;
    @ApiModelProperty(value = "业务日期",example = "1510312336000",required = false)
    private Long       bizDate;
    @ApiModelProperty(value = "业绩归属机构",example = "100100S1000000",required = false)
    private String     bizBranchId;
    @ApiModelProperty(value = "期缴保费",example = "10000",required = false)
    private BigDecimal periodTotalPremium;
    @ApiModelProperty(value = "期缴标准保费",example = "15000",required = false)
    private BigDecimal periodStandardPremium;
    @ApiModelProperty(value = "价值保费",example = "15000",required = false)
    private BigDecimal valuePremium;
    @ApiModelProperty(value = "签单日期",example = "1510312336000",required = false)
    private Long       signDate;
    @ApiModelProperty(value = "扫描日期",example = "1510312336000",required = false)
    private Long       scanDate;
    @ApiModelProperty(value = "客户回执日期",example = "1510312336000",required = false)
    private Long       receiptDate;
    @ApiModelProperty(value = "回执录入系统日期",example = "1510312336000",required = false)
    private Long       receiptInputDate;
    @ApiModelProperty(value = "对账日期",example = "1510312336000",required = false)
    private Long       reconciliationDate;
    @ApiModelProperty(value = "代理人ID",example = "wcl",required = false)
    private String     agentId;

    //保单险种
    List<PolicyCoverageCommissionRequest> listPolicyCoverage=new ArrayList<>();

    public String getPolicyId() {
        return policyId;
    }

    public void setPolicyId(String policyId) {
        this.policyId = policyId;
    }

    public Long getBizDate() {
        return bizDate;
    }

    public void setBizDate(Long bizDate) {
        this.bizDate = bizDate;
    }

    public String getBizBranchId() {
        return bizBranchId;
    }

    public void setBizBranchId(String bizBranchId) {
        this.bizBranchId = bizBranchId;
    }

    public BigDecimal getPeriodTotalPremium() {
        return periodTotalPremium;
    }

    public void setPeriodTotalPremium(BigDecimal periodTotalPremium) {
        this.periodTotalPremium = periodTotalPremium;
    }

    public BigDecimal getPeriodStandardPremium() {
        return periodStandardPremium;
    }

    public void setPeriodStandardPremium(BigDecimal periodStandardPremium) {
        this.periodStandardPremium = periodStandardPremium;
    }

    public BigDecimal getValuePremium() {
        return valuePremium;
    }

    public void setValuePremium(BigDecimal valuePremium) {
        this.valuePremium = valuePremium;
    }

    public Long getSignDate() {
        return signDate;
    }

    public void setSignDate(Long signDate) {
        this.signDate = signDate;
    }

    public Long getScanDate() {
        return scanDate;
    }

    public void setScanDate(Long scanDate) {
        this.scanDate = scanDate;
    }

    public Long getReceiptDate() {
        return receiptDate;
    }

    public void setReceiptDate(Long receiptDate) {
        this.receiptDate = receiptDate;
    }

    public Long getReceiptInputDate() {
        return receiptInputDate;
    }

    public void setReceiptInputDate(Long receiptInputDate) {
        this.receiptInputDate = receiptInputDate;
    }

    public Long getReconciliationDate() {
        return reconciliationDate;
    }

    public void setReconciliationDate(Long reconciliationDate) {
        this.reconciliationDate = reconciliationDate;
    }

    public String getAgentId() {
        return agentId;
    }

    public void setAgentId(String agentId) {
        this.agentId = agentId;
    }

    public List<PolicyCoverageCommissionRequest> getListPolicyCoverage() {
        return listPolicyCoverage;
    }

    public void setListPolicyCoverage(List<PolicyCoverageCommissionRequest> listPolicyCoverage) {
        this.listPolicyCoverage = listPolicyCoverage;
    }
}
