package com.gclife.claim.model.response.report;

import com.gclife.common.annotation.DateFormat;
import com.gclife.common.annotation.Internation;
import com.gclife.common.model.config.DateFormatPatternEnum;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @description
 * @date 2019/9/11 4:26 下午
 */
@Data
public class ReportClaimDetailResponse {
    @ApiModelProperty(example = "理赔ID")
    private String claimId;
    @ApiModelProperty(example = "报案Id")
    private String reportNo;
    @ApiModelProperty(example = "报案Id")
    private String reportId;
    @ApiModelProperty(example = "报案日期")
    private Long reportDate;
    @ApiModelProperty(example = "报案日期")
    @DateFormat(filed = "reportDate", pattern = DateFormatPatternEnum.FORMATE3)
    private String reportDateFormat;
    @ApiModelProperty(example = "报案时间")
    private Long reportTime;
    @ApiModelProperty(example = "报案时间")
    @DateFormat(filed = "reportTime", pattern = DateFormatPatternEnum.FORMATE19)
    private String reportTimeFormat;
    @ApiModelProperty(example = "报案日期")
    private Long accidentDate;
    @ApiModelProperty(example = "出险日期")
    @DateFormat(filed = "accidentDate", pattern = DateFormatPatternEnum.FORMATE3)
    private String accidentDateFormat;
    @ApiModelProperty(example = "出险日期")
    @DateFormat(filed = "accidentDate", pattern = DateFormatPatternEnum.FORMATE18)
    private String accidentTimeFormat;
    @ApiModelProperty(example = "出险地点")
    private String accidentArea;
    @ApiModelProperty(example = "出险原因")
    private String accidentTypeCode;
    @ApiModelProperty(example = "出险原因名称")
    @Internation(filed = "accidentTypeCode", codeType = "ACCIDENT_TYPE")
    private String accidentTypeCodeName;
    @ApiModelProperty(example = "意外原因")
    private String accidentReasonCode;
    @ApiModelProperty(example = "意外原因名称")
    @Internation(filed = "accidentReasonCode", codeType = "ACCIDENT_REASON")
    private String accidentReasonCodeName;
    @ApiModelProperty(example = "具体意外原因")
    private String accidentReasonSpecific;
    @ApiModelProperty(example = "就诊医院ID")
    private String hospitalId;
    @ApiModelProperty(example = "就诊医院")
    private String hospitalName;
    @ApiModelProperty(example = "具体就诊医院")
    private String hospitalSpecific;
    @ApiModelProperty(example = "治疗情况")
    private String cureSituationCode;
    @ApiModelProperty(example = "治疗情况")
    @Internation(filed = "cureSituationCode", codeType = "CURE_SITUATION")
    private String cureSituationCodeName;
    @ApiModelProperty(example = "客户Id")
    private String customerId;
    @ApiModelProperty(example = "客户状态")
    private String customerStatus;
    @ApiModelProperty(example = "客户状态")
    @Internation(filed = "customerStatus", codeType = "CUSTOMER_STATUS")
    private String customerStatusName;
    @ApiModelProperty(example = "残疾等级")
    private String deformityLevelCode;
    @ApiModelProperty(example = "残疾等级名称")
    @Internation(filed = "deformityLevelCode", codeType = "DEFORMITY_LEVEL")
    private String deformityLevelCodeName;
    @ApiModelProperty(example = "伤残项Id")
    private String deformityId;
    @ApiModelProperty(example = "伤残项编码")
    private String deformityCode;
    @ApiModelProperty(example = "伤残项名称")
    private String deformityName;
    @ApiModelProperty(example = "具体残疾项目")
    private String deformitySpecific;
    @ApiModelProperty(example = "事故描述")
    private String accidentDesc;
    @ApiModelProperty(example = "客户ID")
    private String reportCustomerId;
    @ApiModelProperty(example = "诊断细节")
    private String cureDesc;

    List<ReportProjectResponse> reportProjectList;
}
