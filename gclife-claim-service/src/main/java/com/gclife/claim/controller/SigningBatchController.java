package com.gclife.claim.controller;

import com.gclife.claim.model.request.ClaimListRequest;
import com.gclife.claim.model.response.ClaimListResponse;
import com.gclife.claim.service.business.SigningBatchBusinessService;
import com.gclife.common.controller.base.BaseController;
import com.gclife.common.model.BasePageResponse;
import com.gclife.common.model.ResultObject;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiResponse;
import io.swagger.annotations.ApiResponses;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;

/**
 * <AUTHOR>
 * create 19-10-29
 * description:签批
 */
@Api(tags = "签批", description = "签批")
@Controller
@RequestMapping(value = "/v1/signing/batch/")
public class SigningBatchController extends BaseController {
    @Autowired
    private SigningBatchBusinessService signingBatchBusinessService;

    @ApiOperation(value = "签批列表", notes = "签批列表")
    @ApiResponses({
            @ApiResponse(code = 200, message = "请求成功")
    })
    @GetMapping(value = "list")
    public ResultObject<BasePageResponse<ClaimListResponse>> querySigningBatchList(ClaimListRequest claimListRequest) {
        return signingBatchBusinessService.querySigningBatchList(claimListRequest, this.getCurrentLoginUsers());
    }

    @ApiOperation(value = "sign", notes = "签批任务签收")
    @ApiResponses({
            @ApiResponse(code = 200, message = "请求成功")
    })
    @PutMapping(value = "sign")
    public ResultObject signingBatchSign(String claimId) {
        return signingBatchBusinessService.signingBatchSign(this.getCurrentLoginUsers(), claimId);
    }

    @ApiOperation(value = "签批此案件", notes = "签批此案件")
    @ApiResponses({
            @ApiResponse(code = 200, message = "请求成功")
    })
    @PostMapping(value = "submit")
    public ResultObject submitSigningBatch(@RequestParam String claimId) {
        return signingBatchBusinessService.submitSigningBatch(claimId, this.getCurrentLoginUsers(), this.getAppRequestHandler());
    }

}
