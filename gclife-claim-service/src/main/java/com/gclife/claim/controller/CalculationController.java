package com.gclife.claim.controller;

import com.gclife.claim.model.request.ClaimListRequest;
import com.gclife.claim.model.request.calculation.ClaimPolicyBeneficiaryRequest;
import com.gclife.claim.model.request.calculation.ClaimPolicyDetailRequest;
import com.gclife.claim.model.response.ClaimListResponse;
import com.gclife.claim.model.response.calculation.ClaimInfoResponse;
import com.gclife.claim.model.response.calculation.ClaimPolicyBeneficiaryResponse;
import com.gclife.claim.model.response.calculation.ClaimPolicyDetailResponse;
import com.gclife.claim.service.business.CalculationBusinessService;
import com.gclife.common.controller.base.BaseController;
import com.gclife.common.model.BasePageResponse;
import com.gclife.common.model.ResultObject;
import io.swagger.annotations.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * <AUTHOR>
 * create 19-10-29
 * description:理算
 */
@Api(tags = "理算", description = "理算")
@Controller
@RequestMapping(value = "/v1/calculation/")
public class CalculationController extends BaseController {
    @Autowired
    private CalculationBusinessService calculationBusinessService;

    @ApiOperation(value = "理算列表", notes = "理算列表")
    @ApiResponses({
            @ApiResponse(code = 200, message = "请求成功")
    })
    @GetMapping(value = "list")
    public ResultObject<BasePageResponse<ClaimListResponse>> queryClaimCalculationList(ClaimListRequest claimListRequest) {
        return calculationBusinessService.queryClaimCalculationList(claimListRequest, this.getCurrentLoginUsers());
    }

    @ApiOperation(value = "sign", notes = "理算任务签收")
    @ApiResponses({
            @ApiResponse(code = 200, message = "请求成功")
    })
    @PutMapping(value = "sign")
    public ResultObject claimCalculationSign(String claimId) {
        return calculationBusinessService.claimCalculationSign(this.getCurrentLoginUsers(), claimId);
    }

    /***********************************************************赔付信息***********************************************************/

    @ApiOperation(value = "理算详情/赔付信息", notes = "理算详情/赔付信息")
    @ApiResponses({
            @ApiResponse(code = 200, message = "请求成功")
    })
    @GetMapping(value = "detail")
    public ResultObject<List<ClaimInfoResponse>> queryClaimCalculationDetail(String claimId) {
        return calculationBusinessService.queryClaimCalculationDetail(claimId, this.getCurrentLoginUsers());
    }

    @ApiOperation(value = "计算理赔受益金", notes = "计算理赔受益金")
    @ApiResponses({
            @ApiResponse(code = 200, message = "请求成功")
    })
    @GetMapping(value = "claim/benefit")
    public ResultObject<List<ClaimPolicyBeneficiaryResponse>> calculationClaimBenefit(String claimPolicyId) {
        return calculationBusinessService.calculationClaimBenefit(claimPolicyId, this.getCurrentLoginUsers());
    }

    @ApiOperation(value = "查询理赔保单受益人信息", notes = "查询理赔保单受益人信息")
    @ApiResponses({
            @ApiResponse(code = 200, message = "请求成功")
    })
    @ApiImplicitParams({
            @ApiImplicitParam(name = "claimPolicyBeneficiaryId", value = "赔案保单受益人信息Id"),
    })
    @GetMapping(value = "beneficiary/detail")
    public ResultObject<ClaimPolicyBeneficiaryResponse> queryClaimPolicyBeneficiaryDetail(@RequestParam(name = "claimPolicyId") String claimPolicyId, String claimPolicyBeneficiaryId) {
        return calculationBusinessService.queryClaimPolicyBeneficiaryDetail(claimPolicyId, claimPolicyBeneficiaryId, this.getCurrentLoginUsers());
    }


    @ApiOperation(value = "保存理赔保单受益人信息", notes = "保存理赔保单受益人信息")
    @ApiResponses({
            @ApiResponse(code = 200, message = "请求成功")
    })
    @PostMapping(value = "beneficiary/detail")
    public ResultObject saveClaimPolicyBeneficiaryDetail(@RequestBody ClaimPolicyBeneficiaryRequest claimPolicyBeneficiaryRequest) {
        return calculationBusinessService.saveClaimPolicyBeneficiaryDetail(claimPolicyBeneficiaryRequest, this.getCurrentLoginUsers());
    }

    @ApiOperation(value = "删除理赔保单受益人", notes = "删除理赔保单受益人")
    @ApiResponses({
            @ApiResponse(code = 200, message = "请求成功")
    })
    @ApiImplicitParams({
            @ApiImplicitParam(name = "claimPolicyBeneficiaryId", value = "赔案保单受益人信息Id"),
    })
    @GetMapping(value = "delete/beneficiary")
    public ResultObject deleteClaimPolicyBeneficiary(String claimId, String claimPolicyBeneficiaryId) {
        return calculationBusinessService.deleteClaimPolicyBeneficiary(claimId, claimPolicyBeneficiaryId, this.getCurrentLoginUsers());
    }

    /***********************************************************保单赔付详情信息***********************************************************/

    @ApiOperation(value = "查询理赔保单赔付信息", notes = "查询理赔保单赔付信息")
    @ApiResponses({
            @ApiResponse(code = 200, message = "请求成功")
    })
    @ApiImplicitParams({
            @ApiImplicitParam(name = "claimPolicyId", value = "赔案保单Id"),
    })
    @GetMapping(value = "claim/policy/detail")
    public ResultObject<ClaimPolicyDetailResponse> queryClaimPolicyDetail(@RequestParam(name = "claimPolicyId") String claimPolicyId) {
        return calculationBusinessService.queryClaimPolicyDetail(claimPolicyId, this.getCurrentLoginUsers());
    }


    @ApiOperation(value = "保存理赔保单赔付信息", notes = "保存理赔保单赔付信息")
    @ApiResponses({
            @ApiResponse(code = 200, message = "请求成功")
    })
    @PostMapping(value = "claim/policy/detail")
    public ResultObject saveClaimPolicyDetail(@RequestBody ClaimPolicyDetailRequest claimPolicyDetailRequest) {
        return calculationBusinessService.saveClaimPolicyDetail(claimPolicyDetailRequest, this.getCurrentLoginUsers());
    }

    /***********************************************************提交理算信息***********************************************************/

    @ApiOperation(value = "提交理算信息", notes = "提交理算信息")
    @ApiResponses({
            @ApiResponse(code = 200, message = "请求成功")
    })
    @PostMapping(value = "submit/claim/calculation")
    public ResultObject submitClaimPolicyCalculation(@RequestParam String claimId) {
        return calculationBusinessService.submitClaimPolicyCalculation(claimId, this.getCurrentLoginUsers());
    }


}
