package com.gclife.certify.controller;

import com.gclife.certify.model.request.CertifyRequest;
import com.gclife.certify.model.response.CertifyNumberResponse;
import com.gclife.certify.model.response.CertifyResponse;
import com.gclife.certify.service.business.CertifyBusinessService;
import com.gclife.common.configuration.system.permissions.AutoCheckPermissions;
import com.gclife.common.configuration.system.version.ApiVersion;
import com.gclife.common.controller.base.BaseController;
import com.gclife.common.model.BaseRequest;
import com.gclife.common.model.BaseResponse;
import com.gclife.common.model.ResultObject;
import com.gclife.common.model.base.Users;
import io.swagger.annotations.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * \* Created with IntelliJ IDEA.
 * \* User: deep
 * \* Date: 17-9-20
 * \* Time: 上午10:02
 * \* To change this template use File | Settings | File Templates.
 * \* Description: 职员信息控制器
 *
 * <AUTHOR>
 */

@Api(tags = "Certify", description = "单证API")
@RefreshScope
@RestController
@RequestMapping("v1")
public class CertifyApplyController extends BaseController {

    @Autowired
    CertifyBusinessService certifyBusinessService;

    @ApiOperation(value = "certify/{code}", notes = "查询单证信息")
    @ApiResponses({
            @ApiResponse(code = 200, message = "请求成功")
    })
    @ApiVersion(1)
    @AutoCheckPermissions
    @GetMapping(value = "certify/{code}")
    public ResultObject<CertifyResponse> certifyGet(@ApiParam(value = "单证编码", name = "code", example = "20056546")
                                                    @PathVariable(value = "code") String code) {
        Users users = this.getCurrentLoginUsers();
        return certifyBusinessService.loadCertifyInfo(users, code);
    }


    @ApiOperation(value = "certify/codes/{code}", notes = "匹配单证编码集合")
    @ApiResponses({
            @ApiResponse(code = 200, message = "请求成功")
    })
    @ApiVersion(1)
    @AutoCheckPermissions
    @GetMapping(value = "certify/codes/{code}")
    public ResultObject<List<String>> certifyCodesGet(@ApiParam(value = "单证编码", name = "code", example = "2005654")
                                                      @PathVariable(value = "code") String code) {
        Users users = this.getCurrentLoginUsers();
        return certifyBusinessService.loadCertifyCodes(users, code);
    }


    @ApiOperation(value = "certify", notes = "单证信息修改")
    @ApiResponses({
            @ApiResponse(code = 200, message = "请求成功")
    })
    @ApiVersion(1)
    @AutoCheckPermissions
    @PutMapping(value = "certify")
    public ResultObject<BaseResponse> certifyPut(@RequestBody CertifyRequest request) {
        Users users = this.getCurrentLoginUsers();
        return certifyBusinessService.updateCertify(users, request);
    }

    @ApiOperation(value = "certify/number", notes = "生成单证号码")
    @ApiResponses({
            @ApiResponse(code = 200, message = "请求成功")
    })
    @ApiImplicitParams({
            @ApiImplicitParam(name = "channelTypeCode", value = "渠道类型", example = "AGENT", paramType = "query", required = true),
            @ApiImplicitParam(name = "certifySpeciesCode", value = "单证种类(保单，投保单)", example = "POLICY", paramType = "query", required = true),
            @ApiImplicitParam(name = "certifyTypeCode", value = "单证类型(个险，团险)", example = "PERSONAL", paramType = "query"),
    })
    @ApiVersion(1)
    @AutoCheckPermissions
    @GetMapping(value = "certify/number")
    public ResultObject<CertifyNumberResponse> certifyNumberGet(String channelTypeCode, String certifySpeciesCode, String certifyTypeCode, String bankCode, String branchId) {
        return certifyBusinessService.getCertifyNumber(channelTypeCode, certifySpeciesCode, certifyTypeCode, bankCode, branchId);
    }

    @ApiOperation(value = "certify/number/new", notes = "生成单证号码(新版)")
    @ApiResponses({
            @ApiResponse(code = 200, message = "请求成功")
    })
    @ApiImplicitParams({
            @ApiImplicitParam(name = "channelTypeCode", value = "渠道类型", example = "AGENT", paramType = "query", required = true),
            @ApiImplicitParam(name = "certifySpeciesCode", value = "单证种类(保单，投保单)", example = "POLICY", paramType = "query", required = true),
            @ApiImplicitParam(name = "certifyTypeCode", value = "单证类型(个险，团险)", example = "PERSONAL", paramType = "query"),
    })
    @ApiVersion(1)
    @AutoCheckPermissions
    @GetMapping(value = "certify/number/new")
    public ResultObject<CertifyNumberResponse> certifyNumberGetNew(String channelTypeCode, String certifySpeciesCode, String certifyTypeCode, String bankCode, String branchId,String policyNo,Integer policyYear,String approveDate) {
        return certifyBusinessService.getCertifyNumberNew(channelTypeCode, certifySpeciesCode, certifyTypeCode, bankCode, branchId,policyNo,policyYear,approveDate);
        //return certifyBusinessService.getCertifyNumber(channelTypeCode, certifySpeciesCode, certifyTypeCode, bankCode, branchId);
    }
}