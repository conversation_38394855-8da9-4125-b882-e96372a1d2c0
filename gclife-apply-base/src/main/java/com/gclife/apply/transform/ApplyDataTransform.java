package com.gclife.apply.transform;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.gclife.agent.api.AgentBaseAgentApi;
import com.gclife.agent.model.request.UserEventRecordRequest;
import com.gclife.apply.core.jooq.tables.pojos.*;
import com.gclife.apply.dao.ApplyBaseDao;
import com.gclife.apply.model.bo.*;
import com.gclife.apply.model.bo.app.ApplyPlanBo;
import com.gclife.apply.model.config.ApplyErrorConfigEnum;
import com.gclife.apply.model.config.ApplyTermEnum;
import com.gclife.apply.model.config.PayNotifyTermEnum;
import com.gclife.apply.model.response.ApplyDiscountPremiumResponse;
import com.gclife.apply.service.*;
import com.gclife.common.TerminologyConfigEnum;
import com.gclife.common.exception.RequestException;
import com.gclife.common.model.ResultObject;
import com.gclife.common.service.impl.BaseBusinessServiceImpl;
import com.gclife.common.util.AssertUtils;
import com.gclife.common.util.ClazzUtils;
import com.gclife.common.util.DateUtils;
import com.gclife.common.util.JackSonUtils;
import com.gclife.payment.api.PaymentApi;
import com.gclife.platform.api.PlatformAccountApi;
import com.gclife.platform.api.PlatformInternationalBaseApi;
import com.gclife.platform.model.request.AccountRequest;
import com.gclife.platform.model.response.SyscodeResponse;
import com.gclife.policy.api.PolicyApi;
import com.gclife.policy.model.vo.*;
import com.gclife.product.api.ProductDutyApi;
import com.gclife.product.api.ProductRateApi;
import com.gclife.product.api.ProductSalesApi;
import com.gclife.product.model.config.ProductTermEnum;
import com.gclife.product.model.request.insurance.policy.PolicyPaymentRequest;
import com.gclife.product.model.response.duty.DutyResponse;
import com.gclife.product.model.response.insurnce.policy.PolicyPaymentResponse;
import com.gclife.product.model.response.sales.ProductDiscountActivityResponse;
import com.gclife.workflow.api.WorkFlowApi;
import com.gclife.workflow.model.request.TerminationTaskRequest;
import lombok.extern.slf4j.Slf4j;
import org.modelmapper.TypeToken;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

import static com.gclife.apply.model.config.ApplyTermEnum.COMMISSION_DISCOUNT_TYPE.BEFORE_DISCOUNT;
import static java.lang.StrictMath.random;

@Component
@Slf4j
public class ApplyDataTransform extends BaseBusinessServiceImpl {
    @Autowired
    ApplyBaseDao applyBaseDao;
    @Autowired
    private ApplyBaseService applyBaseService;
    @Autowired
    private PlatformAccountApi platformAccountApi;
    @Autowired
    private ProductRateApi productRateApi;
    @Autowired
    private AgentBaseAgentApi baseAgentApi;
    @Autowired
    private ApplyCoverageBaseService applyCoverageBaseService;
    @Autowired
    private ProductDutyApi productDutyApi;
    @Autowired
    private WorkFlowApi workFlowApi;
    @Autowired
    private ApplyPaymentTransactionBaseService applyPaymentTransactionBaseService;
    @Autowired
    private PaymentApi paymentApi;
    @Autowired
    private ApplyUnderwriteBaseService applyUnderwriteBaseService;
    @Autowired
    private ProductSalesApi productSalesApi;
    @Autowired
    private ApplyPlanBaseService applyPlanBaseService;
    @Autowired
    private PolicyApi policyApi;
    @Autowired
    private ApplyInsuredBaseService applyInsuredBaseService;
    @Autowired
    private PlatformInternationalBaseApi platformInternationalBaseApi;

    public ApplyDiscountPremiumResponse transApplyDiscountPremium(ApplyBo applyBo) {
        String[] productId = {""};
        if (AssertUtils.isNotEmpty(applyBo.getListInsured()) && AssertUtils.isNotEmpty(applyBo.getListInsured().get(0).getListCoverage())) {
            applyBo.getListInsured().get(0).getListCoverage().stream().filter(applyCoverageResponse -> ApplyTermEnum.PRODUCT_PRIMARY_FLAG.MAIN.name().equals(applyCoverageResponse.getPrimaryFlag()))
                    .findFirst().ifPresent(applyCoveragePo -> productId[0] = applyCoveragePo.getProductId());
        }
        List<ApplyCoverageAcceptPo> applyCoverageAcceptPos = applyCoverageBaseService.listApplyCoverageAccept(applyBo.getApplyId());
        if (!AssertUtils.isNotEmpty(productId[0]) && AssertUtils.isNotEmpty(applyCoverageAcceptPos)) {
            applyCoverageAcceptPos.stream().filter(applyCoverageAcceptBo -> ApplyTermEnum.PRODUCT_PRIMARY_FLAG.MAIN.name().equals(applyCoverageAcceptBo.getPrimaryFlag()))
                    .findFirst().ifPresent(applyCoverageAcceptBo -> productId[0] = applyCoverageAcceptBo.getProductId());
        }
        Long discountDate = AssertUtils.isNotNull(applyBo.getAppSubmitUnderwritingDate()) ? applyBo.getAppSubmitUnderwritingDate() : DateUtils.getCurrentTime();
        ResultObject<List<ProductDiscountActivityResponse>> productSimpleInfo = productSalesApi.queryProductDiscountActivity(productId[0], applyBo.getSalesBranchId(), discountDate);
        ApplyPlanBo applyPlanBo = applyPlanBaseService.queryApplyPlan(applyBo.getApplyId());
        ApplyPremiumBo applyPremium = applyBo.getApplyPremiumBo();
        if (!AssertUtils.isNotNull(applyPlanBo) ||
                !AssertUtils.isNotNull(applyPlanBo.getDiscountModel()) ||
                !AssertUtils.isNotNull(applyPremium) ||
                !AssertUtils.isNotEmpty(applyPremium.getApplyId())
        ) {
            return null;
        }
//        boolean contains = Arrays.asList("APPLY_STATUS_ACCEPT", "APPLY_STATUS_ACCEPT_COMPLETE", "APPLY_STATUS_IMAGE_UPLOAD", "APPLY_STATUS_IMAGE_UPLOAD_COMPLETE", "APPLY_STATUS_INPUT").contains(applyBo.getApplyStatus());
        BigDecimal hundred = new BigDecimal("100");


        ApplyDiscountPremiumResponse applyPremiumResponse = new ApplyDiscountPremiumResponse();
        List<ProductDiscountActivityResponse> productDiscountActivityResponses = productSimpleInfo.getData();
        if (AssertUtils.isNotEmpty(productDiscountActivityResponses)) {
            productDiscountActivityResponses.stream().filter(productDiscountActivityResponse -> applyPlanBo.getDiscountModel().equals(productDiscountActivityResponse.getDiscountModel())
                    && (!AssertUtils.isNotEmpty(applyPlanBo.getPromotionType()) || applyPlanBo.getPromotionType().equals(productDiscountActivityResponse.getPromotionType()))
            ).findFirst().ifPresent(productDiscountActivityResponse -> {
                if (AssertUtils.isNotNull(applyPremium) && AssertUtils.isNotNull(applyPlanBo.getDiscountModel())) {
                    //PERCENTAGE:百分比
                    if (ProductTermEnum.DISCOUNT_MODEL.PERCENTAGE.name().equals(productDiscountActivityResponse.getDiscountModel())) {
                        //反算折扣前的实缴保费
                        BigDecimal premiumBeforeDiscount = applyPremium.getActualPremium()
                                .divide(new BigDecimal("1").subtract(applyPlanBo.getSpecialDiscount().divide(hundred, 2, BigDecimal.ROUND_HALF_UP)), 2, BigDecimal.ROUND_HALF_UP);
                        if (applyPremium.getTotalPremium().subtract(premiumBeforeDiscount).abs().compareTo(new BigDecimal("0.03")) < 0) {
                            premiumBeforeDiscount = applyPremium.getTotalPremium();
                        }
                        applyPremiumResponse.setTotalPremium(premiumBeforeDiscount);
                        applyPremiumResponse.setReceivablePremium(applyPremium.getActualPremium());
                        applyPremiumResponse.setSpecialDiscount(applyPlanBo.getSpecialDiscount().stripTrailingZeros());
                    }
                    //FIXED_AMOUNT:固定数额
                    if (ProductTermEnum.DISCOUNT_MODEL.FIXED_AMOUNT.name().equals(productDiscountActivityResponse.getDiscountModel())) {
                        //反算折扣前的实缴保费
                        if (applyBo.isCalFlag()) {
                            BigDecimal premiumBeforeDiscount = AssertUtils.isNotNull(applyPremium.getPremiumBeforeDiscount()) && applyPremium.getPremiumBeforeDiscount().compareTo(applyPremium.getActualPremium()) == 0 ? applyPremium.getPremiumBeforeDiscount() : applyPremium.getActualPremium();
                            applyPremiumResponse.setTotalPremium(premiumBeforeDiscount);
                            applyPremiumResponse.setSpecialDiscount(applyPlanBo.getSpecialDiscount().stripTrailingZeros());
                            applyPremiumResponse.setReceivablePremium(premiumBeforeDiscount.subtract(applyPlanBo.getSpecialDiscount()));
                        } else {
                            applyPremiumResponse.setTotalPremium(applyPremium.getPremiumBeforeDiscount());
                            applyPremiumResponse.setSpecialDiscount(applyPlanBo.getSpecialDiscount().stripTrailingZeros());
                            applyPremiumResponse.setReceivablePremium(applyPremium.getActualPremium());
                        }
                    }
                }
            });
        }
        //无折扣活动，但是有折扣系数的，重新赋值
        if (!AssertUtils.isNotNull(applyPremiumResponse.getSpecialDiscount())) {
            this.transDiscount(applyPlanBo, applyPremium, hundred, applyPremiumResponse, applyBo.isCalFlag());
        }
        applyPremiumResponse.setDiscountType(applyPlanBo.getDiscountType());
        applyPremiumResponse.setPromotionType(applyPlanBo.getPromotionType());
        applyPremiumResponse.setDiscountModel(applyPlanBo.getDiscountModel());
        applyPremiumResponse.setMemberCompanyName(applyPlanBo.getMemberCompanyName());
        //网销20A优惠码信息
        applyPremiumResponse.setPromotionalCode(applyPlanBo.getPromotionalCode());
        applyPremiumResponse.setPromotionalPremium(applyPlanBo.getPromotionalPremium());
        log.info("====================================applyDiscountPremiumResponse:{}", JackSonUtils.toJson(applyPremiumResponse));
//        if (!AssertUtils.isNotNull(applyPremiumResponse) || !AssertUtils.isNotNull(applyPremiumResponse.getSpecialDiscount())) {
//            return null;
//        }
        return applyPremiumResponse;
    }

    private void transDiscount(ApplyPlanBo applyPlanBo, ApplyPremiumBo applyPremium, BigDecimal hundred, ApplyDiscountPremiumResponse applyPremiumResponse, boolean calFlag) {
        //PERCENTAGE:百分比
        if (ProductTermEnum.DISCOUNT_MODEL.PERCENTAGE.name().equals(applyPlanBo.getDiscountModel())) {
            //反算折扣前的实缴保费
            BigDecimal premiumBeforeDiscount = applyPremium.getActualPremium()
                    .divide(new BigDecimal("1").subtract(applyPlanBo.getSpecialDiscount().divide(hundred, 2, BigDecimal.ROUND_HALF_UP)), 2, BigDecimal.ROUND_HALF_UP);
            if (applyPremium.getTotalPremium().subtract(premiumBeforeDiscount).abs().compareTo(new BigDecimal("0.03")) < 0) {
                premiumBeforeDiscount = applyPremium.getTotalPremium();
            }
            applyPremiumResponse.setTotalPremium(premiumBeforeDiscount);
            applyPremiumResponse.setReceivablePremium(applyPremium.getActualPremium());
            applyPremiumResponse.setSpecialDiscount(applyPlanBo.getSpecialDiscount().stripTrailingZeros());
        }
        //FIXED_AMOUNT:固定数额
        if (ProductTermEnum.DISCOUNT_MODEL.FIXED_AMOUNT.name().equals(applyPlanBo.getDiscountModel())) {
            if (calFlag) {
                BigDecimal premiumBeforeDiscount = AssertUtils.isNotNull(applyPremium.getPremiumBeforeDiscount()) && applyPremium.getPremiumBeforeDiscount().compareTo(applyPremium.getActualPremium()) == 0 ? applyPremium.getPremiumBeforeDiscount() : applyPremium.getActualPremium();
                applyPremiumResponse.setTotalPremium(premiumBeforeDiscount);
                applyPremiumResponse.setSpecialDiscount(applyPlanBo.getSpecialDiscount().stripTrailingZeros());
                applyPremiumResponse.setReceivablePremium(premiumBeforeDiscount.subtract(applyPlanBo.getSpecialDiscount()));
            } else {
                applyPremiumResponse.setTotalPremium(applyPremium.getPremiumBeforeDiscount());
                applyPremiumResponse.setSpecialDiscount(applyPlanBo.getSpecialDiscount().stripTrailingZeros());
                applyPremiumResponse.setReceivablePremium(applyPremium.getActualPremium());
            }
        }
    }

    public void transformApplyPremium(ApplyPremiumBo applyPremiumBo, String applyId, String premiumStatus) {
        applyPremiumBo.setApplyId(applyId);
        applyPremiumBo.setPolicyYear("1");
        applyPremiumBo.setPolicyPeriod("1");
        //查询投保单
        ApplyBo applyBo = applyBaseService.queryApply(applyId);
        AssertUtils.isNotNull(getLogger(), applyBo, ApplyErrorConfigEnum.APPLY_BASE_BUSINESS_APPLY_IS_NOT_FOUND_OBJECT);
        applyPremiumBo.setCurrencyCode(applyBo.getCurrencyCode());
        //查询险种
        List<ApplyCoveragePo> listApplyCoveragePo = applyCoverageBaseService.listApplyCoverageOfInsured(applyId);
        AssertUtils.isNotEmpty(getLogger(), listApplyCoveragePo, ApplyErrorConfigEnum.APPLY_BASE_BUSINESS_APPLY_COVERAGE_IS_NOT_FOUND_OBJECT);
        BigDecimal[] originPremium = new BigDecimal[1];
        originPremium[0] = new BigDecimal(0);
        BigDecimal[] totalPremium = new BigDecimal[1];
        totalPremium[0] = new BigDecimal(0);
        BigDecimal[] totalActualPremium = {new BigDecimal("0.00")};

        listApplyCoveragePo.forEach(applyCoveragePo -> {
            if (AssertUtils.isNotNull(applyCoveragePo.getOriginalPremium())) {
                originPremium[0] = originPremium[0].add(applyCoveragePo.getOriginalPremium());
            }
            if (AssertUtils.isNotNull(applyCoveragePo.getTotalPremium())) {
                totalPremium[0] = totalPremium[0].add(applyCoveragePo.getTotalPremium());
            }
            if (AssertUtils.isNotNull(applyCoveragePo.getActualPremium())) {
                totalActualPremium[0] = totalActualPremium[0].add(applyCoveragePo.getActualPremium());
            }
        });
        applyPremiumBo.setOriginalPremium(originPremium[0]);
        applyPremiumBo.setPeriodOriginalPremium(originPremium[0]);
        //查询加费
        BigDecimal[] addPremium = new BigDecimal[1];
        addPremium[0] = new BigDecimal(0);
        List<ApplyAddPremiumPo> listApplyAddPremiumPo = applyBaseDao.getApplyAddPremium(applyId);
        if (AssertUtils.isNotEmpty(listApplyAddPremiumPo)) {
            listApplyAddPremiumPo.forEach(applyAddPremiumPo -> {
                if (AssertUtils.isNotNull(applyAddPremiumPo.getTotalAddPremium())) {
                    addPremium[0] = addPremium[0].add(applyAddPremiumPo.getTotalAddPremium());
                }
            });
        }
        applyPremiumBo.setAddPremium(addPremium[0]);
        applyPremiumBo.setTotalPremium(totalPremium[0]);
        applyPremiumBo.setPeriodTotalPremium(totalPremium[0]);
        applyPremiumBo.setReceivablePremium(totalActualPremium[0]);
        applyPremiumBo.setTotalActualPremium(totalActualPremium[0]);
        applyPremiumBo.setActualPremium(totalActualPremium[0]);
        if (!AssertUtils.isNotEmpty(applyPremiumBo.getPremiumStatus()) || !ApplyTermEnum.PAYMENT_STATUS.PAYMENT_INITIAL.name().equals(premiumStatus)) {
            applyPremiumBo.setPremiumStatus(premiumStatus);
        }
        if (!AssertUtils.isNotNull(applyBo.getApplyPremiumBo()) || ProductTermEnum.DISCOUNT_MODEL.FIXED_AMOUNT.name().equals(applyPremiumBo.getDiscountModel())) {
            applyBo.setApplyPremiumBo(applyPremiumBo);
        }
        applyBo.setCalFlag(true);
        ApplyDiscountPremiumResponse applyDiscountPremiumResponse = this.transApplyDiscountPremium(applyBo);
        if (AssertUtils.isNotNull(applyDiscountPremiumResponse) && AssertUtils.isNotNull(applyDiscountPremiumResponse.getSpecialDiscount())) {
            applyPremiumBo.setDiscountType(applyDiscountPremiumResponse.getDiscountType());
            applyPremiumBo.setDiscountModel(applyDiscountPremiumResponse.getDiscountModel());
            applyPremiumBo.setPromotionType(applyDiscountPremiumResponse.getPromotionType());
            applyPremiumBo.setMemberCompanyName(applyDiscountPremiumResponse.getMemberCompanyName());
            //网销20A优惠码信息
            applyPremiumBo.setPromotionalCode(applyDiscountPremiumResponse.getPromotionalCode());
            applyPremiumBo.setPromotionalPremium(applyDiscountPremiumResponse.getPromotionalPremium());
            if (!AssertUtils.isNotEmpty(applyPremiumBo.getDiscountModel()) || ProductTermEnum.DISCOUNT_MODEL.PERCENTAGE.name().equals(applyPremiumBo.getDiscountModel())) {
                BigDecimal specialDiscount = applyDiscountPremiumResponse.getSpecialDiscount().divide(new BigDecimal("100"), 6, BigDecimal.ROUND_HALF_UP);
                applyPremiumBo.setSpecialDiscount(specialDiscount);
                //反算折扣前的实缴保费
                BigDecimal premiumBeforeDiscount = applyPremiumBo.getActualPremium()
                        .divide(new BigDecimal("1").subtract(specialDiscount), 2, BigDecimal.ROUND_HALF_UP);
                if (applyPremiumBo.getTotalPremium().subtract(premiumBeforeDiscount).abs().compareTo(new BigDecimal("0.03")) < 0) {
                    premiumBeforeDiscount = applyPremiumBo.getTotalPremium();
                }
                applyPremiumBo.setPremiumBeforeDiscount(premiumBeforeDiscount);
            } else if (ProductTermEnum.DISCOUNT_MODEL.FIXED_AMOUNT.name().equals(applyPremiumBo.getDiscountModel())) {
                BigDecimal actualPremium = applyDiscountPremiumResponse.getReceivablePremium();
                applyPremiumBo.setReceivablePremium(actualPremium);
                applyPremiumBo.setTotalActualPremium(actualPremium);
                applyPremiumBo.setActualPremium(actualPremium);
                applyPremiumBo.setSpecialDiscount(applyDiscountPremiumResponse.getSpecialDiscount());
                applyPremiumBo.setPremiumBeforeDiscount(actualPremium.add(applyDiscountPremiumResponse.getSpecialDiscount()));
            }
        } else if (ApplyTermEnum.APPLY_TYPE.LIFE_INSURANCE_PERSONAL.name().equals(applyBo.getApplyType())) {
            applyPremiumBo.setPremiumBeforeDiscount(null);
            applyPremiumBo.setSpecialDiscount(null);
            applyPremiumBo.setDiscountType(null);
            applyPremiumBo.setPromotionType(null);
            applyPremiumBo.setDiscountModel(null);
        }
    }

    /**
     * 投保单数据转保单数据
     *
     * @param applyBo       投保单数据
     * @param actualPayDate 财务实收时间
     * @return 保单数据
     */
    public PolicyVo transApplyDataToPolicyData(ApplyBo applyBo, String actualPayDate) {

        //投保单数据校验
//        paymentParameterValidate.validParameterApplyToPolicyTransData(applyBo);
        //初始化保单对象
        PolicyVo policyVo = new PolicyVo();
        //转换保单基础信息
        transferBasePolicyData(applyBo, policyVo);
        //转换保单账户
        transferPolicyAccountData(applyBo, policyVo);
        //代理人
        transferPolicyAgentData(applyBo, policyVo);
        //投保人
        transferPolicyApplicantData(applyBo, policyVo);
        //保单联系方式
        transferPolicyContactData(applyBo, policyVo);
        //保单附件
        transferPolicyAttachment(applyBo, policyVo);
        //保单保费
        transferPolicyPremium(applyBo, policyVo, actualPayDate);
        // 保单加费
        transferPolicyAddPremium(applyBo, policyVo);
        // 特别约定
        transferPolicySpecialContract(applyBo, policyVo);
        //保单打印
        transferPolicyPrintData(policyVo);
        // 转换公共险种
        transApplyCoverage(applyBo, policyVo);
        //被保人
        transferPolicyInsuredData(applyBo, policyVo);
        // 被保人统计
        transferPolicyInsuredCollectData(applyBo, policyVo);
        //调用产品计算费率
        transferPolicyPaymentRateData(applyBo, policyVo);

        //推荐信息
        if (AssertUtils.isNotNull(applyBo.getReferralInfo())) {
            PolicyReferralInfoVo policyReferralInfoVo = new PolicyReferralInfoVo();
            ClazzUtils.copyPropertiesIgnoreNull(applyBo.getReferralInfo(), policyReferralInfoVo);
            policyVo.setReferralInfo(policyReferralInfoVo);
        }

        return policyVo;
    }

    /**
     * 投保单数据转保单数据
     */
    public void transferPreUnderwritingApplyToPolicy(String applyId, String userId, String preUnderwritingFlag) {
        try {
            getLogger().info("transferApplyToPolicy start time :" + DateUtils.timeStrToString(System.currentTimeMillis(), "yyyy-MM-dd HH:mm:ss"));

            AssertUtils.isNotEmpty(log, applyId, ApplyErrorConfigEnum.APPLY_BASE_PARAMETER_APPLY_ID_IS_NOT_NULL);
            // 查询投保单数据
            ApplyBo applyBo = applyBaseService.queryApply(applyId);
            AssertUtils.isNotNull(log, applyBo, ApplyErrorConfigEnum.APPLY_BASE_BUSINESS_APPLY_IS_NOT_FOUND_OBJECT);

            // 查询险种档次
            List<ApplyCoverageLevelPo> applyCoverageLevelPos = applyCoverageBaseService.listApplyCoverageLevel(applyId);
            if (AssertUtils.isNotEmpty(applyCoverageLevelPos)) {
                // 按险种ID分组
                Map<String, List<ApplyCoverageLevelPo>> applyCoverageLevelPoMap =
                        applyCoverageLevelPos.parallelStream().collect(Collectors.groupingBy(ApplyCoverageLevelPo::getCoverageId));
                // 设置险种档次数据
                applyBo.getListInsured().forEach(applyInsuredBo -> {
                    applyInsuredBo.getListCoverage().forEach(applyCoverageBo -> {
                        applyCoverageBo.setListCoverageLevel(applyCoverageLevelPoMap.get(applyCoverageBo.getCoverageId()));
                    });
                });
            }

            //数据转换
            PolicyVo policyVo = this.transApplyDataToPolicyData(applyBo, null);
            policyVo.setPreUnderwritingFlag(preUnderwritingFlag);
            policyVo.setPolicyStatus(ApplyTermEnum.APPLY_STATUS_FLAG.POLICY_EFFECTIVE_HC.name());
            log.info("transferApplyToPolicy policyReqFc :{}", JSONObject.toJSONString(policyVo));

            //投保单转保单
            ResultObject<PolicyVo> resPolicyResult = policyApi.applyTransToPolicyBase(policyVo);
            AssertUtils.isResultObjectError(resPolicyResult);
            log.info("resPolicyResult==========================" + resPolicyResult.getData());
            //判断是否转换成功
            AssertUtils.isResultObjectDataNull(log, resPolicyResult, ApplyErrorConfigEnum.APPLY_BASE_BUSINESS_SAVE_POLICY_DATA_ERROR);
            //保存保单号
            PolicyVo returnPolicyVo = resPolicyResult.getData();
            applyBo.setPolicyNo(returnPolicyVo.getPolicyNo());
            applyBo.setApplyStatus(ApplyTermEnum.APPLY_STATUS_FLAG.APPROVED_HC.name());
            applyBo.setApproveDate(returnPolicyVo.getApproveDate());
            applyBo.setEffectiveDate(returnPolicyVo.getEffectiveDate());

            //保存被保人扩展
            if (AssertUtils.isNotNull(applyBo.getListInsured())) {
                List<ApplyInsuredExtendPo> applyInsuredExtendPos = (List<ApplyInsuredExtendPo>) this.converterList(applyBo.getListInsured(), new TypeToken<List<ApplyInsuredExtendPo>>() {
                }.getType());
                List<ApplyInsuredExtendPo> applyInsuredExtendPoTemps = null;
                if (AssertUtils.isNotNull(applyBo.getEffectiveDate()) && AssertUtils.isNotNull(applyInsuredExtendPos)) {
                    applyInsuredExtendPoTemps = applyInsuredExtendPos.stream().filter(applyInsuredExtendPo -> AssertUtils.isNotNull(applyInsuredExtendPo.getInsuredExtendId())).collect(Collectors.toList());
                    if (AssertUtils.isNotNull(applyInsuredExtendPoTemps)) {
                        applyInsuredExtendPoTemps.forEach(applyInsuredExtendPo -> {
                            applyInsuredExtendPo.setEffectiveDate(applyBo.getEffectiveDate());
                        });
                        //保存被保人扩展
                        applyInsuredBaseService.saveApplyInsuredExtend(userId, applyInsuredExtendPoTemps);
                    }
                }
            }
            applyBaseService.saveApply(userId, applyBo);

            getLogger().info("transferApplyToPolicy end time :" + DateUtils.timeStrToString(System.currentTimeMillis(), "yyyy-MM-dd HH:mm:ss"));
        } catch (Exception e) {
            e.printStackTrace();
            throwsTransactionalException(log, e, ApplyErrorConfigEnum.APPLY_BASE_BUSINESS_SAVE_POLICY_DATA_ERROR);
        }
    }

    /**
     * 转换保单基础信息
     *
     * @param applyBo  　投保单对象
     * @param policyVo 　保单请求对象
     */
    private void transferBasePolicyData(ApplyBo applyBo, PolicyVo policyVo) {
        // 复制对象属性
        ClazzUtils.copyPropertiesIgnoreNull(applyBo, policyVo);


        policyVo.setPolicyType(applyBo.getApplyType());

        //投保单支付事务
        ApplyPaymentTransactionBo applyPaymentTransactionBo = applyBo.getApplyPaymentTransactionBo();

        if (AssertUtils.isNotNull(applyBo.getEffectiveDate())) {
            policyVo.setEffectiveDate(applyBo.getEffectiveDate());
        } else if (AssertUtils.isNotNull(applyPaymentTransactionBo) && AssertUtils.isNotNull(applyPaymentTransactionBo.getArrivalDate())) {
            policyVo.setEffectiveDate(Long.valueOf(DateUtils.addStringDays(String.valueOf(applyPaymentTransactionBo.getArrivalDate()), 1)));
        } else {
            policyVo.setEffectiveDate(DateUtils.getCurrentTime());
        }
        if (AssertUtils.isNotNull(applyBo.getBizDate())) {
            policyVo.setBizDate(applyBo.getBizDate());
        } else {
            policyVo.setBizDate(DateUtils.getCurrentDateToTime());
        }
        if (AssertUtils.isNotNull(applyBo.getApproveDate())) {
            policyVo.setApproveDate(applyBo.getApproveDate());
        } else {
            policyVo.setApproveDate(DateUtils.getCurrentTime());
        }
        if (AssertUtils.isNotEmpty(applyBo.getVerifyNo())) {
            policyVo.setVerifyNo(applyBo.getVerifyNo());
        } else {
            policyVo.setVerifyNo((int) Math.floor(******** + random() * ******** + 1) + "");
        }

        //mustReturnFlag
        policyVo.setMustReturnFlag(this.calMustReturnFlag(applyBo));
    }

    /**
     * 转换保单账户数据
     *
     * @param policyVo 　保单对象
     * @param applyBo  投保单业务对象
     */
    private void transferPolicyAccountData(ApplyBo applyBo, PolicyVo policyVo) {
        List<ApplyAccountBo> listApplyAccountBo = applyBo.getListApplyAccount();
        if (AssertUtils.isNotEmpty(listApplyAccountBo)) {
            List<PolicyAccountVo> policyAccountVos = new ArrayList<>();
            listApplyAccountBo.forEach(applyAccountBo -> {
                AccountRequest accountRequest = (AccountRequest) this.converterObject(applyAccountBo, AccountRequest.class);
                accountRequest.setCity(applyAccountBo.getAreaCode());
                platformAccountApi.accountInfoPost(accountRequest);

                PolicyAccountVo policyAccountVo = (PolicyAccountVo) this.converterObject(applyAccountBo, PolicyAccountVo.class);
                policyAccountVo.setCity(applyAccountBo.getAreaCode());
                policyAccountVos.add(policyAccountVo);
            });
            policyVo.setListPolicyAccount(policyAccountVos);
        }
    }

    /**
     * 转换保单代理人
     *
     * @param applyBo  　投保单业务数据
     * @param policyVo 　保单对象
     */
    private void transferPolicyAgentData(ApplyBo applyBo, PolicyVo policyVo) {
        if (AssertUtils.isNotNull(applyBo.getApplyAgentBo())) {
            PolicyAgentVo policyAgentVo = (PolicyAgentVo) this.converterObject(applyBo.getApplyAgentBo(), PolicyAgentVo.class);
            policyVo.setPolicyAgent(policyAgentVo);
        }
    }

    /**
     * 转换保单投保人
     *
     * @param applyBo  　投保单业务对象
     * @param policyVo 　保单对象
     */
    private void transferPolicyApplicantData(ApplyBo applyBo, PolicyVo policyVo) {
        if (AssertUtils.isNotNull(applyBo.getApplicant())) {
            PolicyApplicantVo policyApplicantVo = (PolicyApplicantVo) this.converterObject(applyBo.getApplicant(), PolicyApplicantVo.class);
            policyApplicantVo.setApplicantId(null);
            policyVo.setPolicyApplicant(policyApplicantVo);
        }
    }

    /**
     * 转换保单联系信息
     *
     * @param applyBo  　投保单业务对象
     * @param policyVo 　保单对象
     */
    private void transferPolicyContactData(ApplyBo applyBo, PolicyVo policyVo) {
        ApplyContactInfoBo applyContactInfoBo = applyBo.getApplyContact();
        if (AssertUtils.isNotNull(applyContactInfoBo)) {
            PolicyContactInfoVo policyContactInfoVo = (PolicyContactInfoVo) this.converterObject(applyContactInfoBo, PolicyContactInfoVo.class);
            policyContactInfoVo.setContractAddress(applyContactInfoBo.getSendAddrContact());
            policyVo.setPolicyContactInfo(policyContactInfoVo);
        }
    }

    /**
     * 转换保单附件信息
     *
     * @param applyBo  　投保单业务对象
     * @param policyVo 保单对象
     */
    private void transferPolicyAttachment(ApplyBo applyBo, PolicyVo policyVo) {
        if (AssertUtils.isNotEmpty(applyBo.getListAttachment())) {
            List<PolicyAttachmentVo> policyAttachmentVos = (List<PolicyAttachmentVo>) this.converterList(
                    applyBo.getListAttachment(), new TypeToken<List<PolicyAttachmentVo>>() {
                    }.getType()
            );
            policyVo.setListPolicyAttachment(policyAttachmentVos);
        }
    }

    /**
     * 保单保费
     *
     * @param applyBo       投保单业务对象
     * @param policyVo      保单对象
     * @param actualPayDate 财务实收时间
     */
    private void transferPolicyPremium(ApplyBo applyBo, PolicyVo policyVo, String actualPayDate) {
        PolicyPremiumVo policyPremiumVo = new PolicyPremiumVo();
        List<ApplyCoverageBo> applyCoverageBos = applyBo.getListInsuredCoverage();
        applyCoverageBos.stream()
                .filter(applyCoverageBo -> ApplyTermEnum.PRODUCT_PRIMARY_FLAG.MAIN.name().equals(applyCoverageBo.getPrimaryFlag()))
                .findFirst().ifPresent(applyCoverageBo -> {
            policyPremiumVo.setPremiumFrequency(applyCoverageBo.getPremiumFrequency());
            policyPremiumVo.setPremiumPeriod(applyCoverageBo.getPremiumPeriod());
            policyPremiumVo.setPremiumPeriodUnit(applyCoverageBo.getPremiumPeriodUnit());
            policyPremiumVo.setPaymentCompleteDate(applyCoverageBo.getCoveragePeriodEndDate());
        });
        policyPremiumVo.setCurrencyCode(applyBo.getCurrencyCode());

        ApplyPremiumBo applyPremiumBo = applyBo.getApplyPremiumBo();
        if (AssertUtils.isNotNull(applyPremiumBo)) {
            //新增保费字段
            policyPremiumVo.setPeriodOriginalPremium(applyPremiumBo.getPeriodOriginalPremium());
            policyPremiumVo.setPeriodTotalPremium(applyPremiumBo.getPeriodTotalPremium());

            policyPremiumVo.setTotalRefundAmount(BigDecimal.ZERO);
            policyPremiumVo.setTotalDeductPremium(BigDecimal.ZERO);
            policyPremiumVo.setTotalDeductRefundAmount(BigDecimal.ZERO);

//            policyPremiumVo.setTotalOriginalPremium(applyPremiumBo.getOriginalPremium());
            policyPremiumVo.setReceivableDate(applyPremiumBo.getArrivalDate());
            policyPremiumVo.setPremiumStatus(applyPremiumBo.getPremiumStatus());
            policyPremiumVo.setPaymentNo(applyPremiumBo.getPaymentId());
            //反算折扣前的实缴保费
            if (AssertUtils.isNotNull(applyPremiumBo.getSpecialDiscount())
                    && AssertUtils.isNotNull(applyPremiumBo.getPremiumBeforeDiscount())
                    && AssertUtils.isNotEmpty(applyPremiumBo.getDiscountType())) {
                policyPremiumVo.setPremiumBeforeDiscount(applyPremiumBo.getPremiumBeforeDiscount());
                policyPremiumVo.setSpecialDiscount(applyPremiumBo.getSpecialDiscount());
                policyPremiumVo.setCompanyDiscount(applyPremiumBo.getCompanyDiscount());
                policyPremiumVo.setAgentDiscount(applyPremiumBo.getAgentDiscount());
                policyPremiumVo.setDiscountType(applyPremiumBo.getDiscountType());
                policyPremiumVo.setPromotionType(applyPremiumBo.getPromotionType());
                policyPremiumVo.setDiscountModel(applyPremiumBo.getDiscountModel());
                policyPremiumVo.setTotalActualPremium(applyPremiumBo.getPremiumBeforeDiscount());
                policyPremiumVo.setActualPremium(applyPremiumBo.getPremiumBeforeDiscount());
            } else {
                policyPremiumVo.setTotalActualPremium(applyPremiumBo.getTotalActualPremium());
                policyPremiumVo.setActualPremium(applyPremiumBo.getActualPremium());
            }
        }
        // 设置保单支付信息
        transferPolicyPaymentData(applyBo, policyVo, policyPremiumVo, actualPayDate);

        policyVo.setPolicyPremium(policyPremiumVo);
    }

    /**
     * 转换保单支付信息
     *
     * @param applyBo         投保单业务对象
     * @param policyVo        保单对象
     * @param policyPremiumVo 保单保费对象
     * @param actualPayDate   财务实收时间
     */
    private void transferPolicyPaymentData(ApplyBo applyBo, PolicyVo policyVo, PolicyPremiumVo policyPremiumVo, String actualPayDate) {
        PolicyPaymentVo policyPaymentVo = new PolicyPaymentVo();
        policyPaymentVo.setApplyDate(applyBo.getApplyDate());
        policyPaymentVo.setBizYearMonth(DateUtils.getCurrentYearMonth());
        if (AssertUtils.isNotNull(applyBo.getBizDate())) {
            policyPaymentVo.setBizDate(applyBo.getBizDate());
        } else {
            policyPaymentVo.setBizDate(DateUtils.getCurrentDateToTime());
        }
        policyPaymentVo.setBizBranchId(applyBo.getSalesBranchId());
        policyPaymentVo.setProviderId(applyBo.getProviderId());
        policyPaymentVo.setPolicyYear(1L);
        policyPaymentVo.setPremiumPeriodUnit(policyPremiumVo.getPremiumPeriodUnit());
        policyPaymentVo.setPremiumPeriod(policyPremiumVo.getPremiumPeriod());
        policyPaymentVo.setPremiumFrequency(policyPremiumVo.getPremiumFrequency());
        policyPaymentVo.setPaymentCompleteDate(policyPremiumVo.getPaymentCompleteDate());
        policyPaymentVo.setFrequency(1L);
        policyPaymentVo.setCurrencyCode(applyBo.getCurrencyCode());
        policyPaymentVo.setTotalOriginalPremium(policyPremiumVo.getTotalOriginalPremium());

        //新增保费字段
        policyPaymentVo.setPeriodOriginalPremium(policyPremiumVo.getPeriodOriginalPremium());
        policyPaymentVo.setPeriodActualPremium(policyPremiumVo.getTotalActualPremium());

        // 设置加费金额
        List<ApplyAddPremiumPo> applyAddPremiumPos = applyBo.getListApplyAddPremiumPo();
        if (AssertUtils.isNotEmpty(applyAddPremiumPos)) {
            applyAddPremiumPos.forEach(applyAddPremiumPo -> {
                if (AssertUtils.isNotNull(applyAddPremiumPo.getPeriodCareerAddPremium())) {
                    policyPaymentVo.setCareerAddPremium(policyPaymentVo.getCareerAddPremium().add(applyAddPremiumPo.getPeriodCareerAddPremium()));
                }
                if (AssertUtils.isNotNull(applyAddPremiumPo.getPeriodWeakAddPremium())) {
                    policyPaymentVo.setWeakAddPremium(policyPaymentVo.getWeakAddPremium().add(applyAddPremiumPo.getPeriodWeakAddPremium()));
                }
                if (AssertUtils.isNotNull(applyAddPremiumPo.getPeriodHealthAddPremium())) {
                    policyPaymentVo.setHealthAddPremium(policyPaymentVo.getHealthAddPremium().add(applyAddPremiumPo.getPeriodHealthAddPremium()));
                }
                if (AssertUtils.isNotNull(applyAddPremiumPo.getPeriodOtherAddPremium())) {
                    policyPaymentVo.setOtherAddPremium(policyPaymentVo.getOtherAddPremium().add(applyAddPremiumPo.getPeriodOtherAddPremium()));
                }
                if (AssertUtils.isNotNull(applyAddPremiumPo.getTotalAddPremium())) {
                    policyPaymentVo.setTotalAddPremium(policyPaymentVo.getTotalAddPremium().add(applyAddPremiumPo.getTotalAddPremium()));
                }
            });
        }
        policyPaymentVo.setTotalPremium(policyPaymentVo.getPeriodActualPremium().add(policyPaymentVo.getTotalAddPremium()));
        policyPaymentVo.setActualPremium(policyPaymentVo.getPeriodActualPremium());

        policyPaymentVo.setCommissionFee(new BigDecimal(0));
        if (AssertUtils.isNotNull(applyBo.getApplyPaymentTransactionBo())) {
            policyPaymentVo.setPaymentStatusCode(applyBo.getApplyPaymentTransactionBo().getPaymentStatus());
            if (AssertUtils.isNotEmpty(applyBo.getApplyPaymentTransactionBo().getApplyPaymentTransactionItemBos())) {
                policyPaymentVo.setPaymentModeCode(applyBo.getApplyPaymentTransactionBo().getApplyPaymentTransactionItemBos().get(0).getPaymentMethodCode());
            }
        }
        if (AssertUtils.isNotNull(actualPayDate)) {
            // 将到账日期从当前时间改为财务实收时间
            policyPaymentVo.setGainedDate(Long.valueOf(actualPayDate));
        } else {
            policyPaymentVo.setGainedDate(DateUtils.getCurrentDateToTime());
        }
        policyPaymentVo.setPremiumSource(applyBo.getApplySource());
        policyPaymentVo.setReceivableDate(policyVo.getApproveDate());
        policyPaymentVo.setStatusCode(ApplyTermEnum.SETTLEMENT_STATUS.RECEIVABLE.name());

        policyPremiumVo.setPolicyPayment(policyPaymentVo);
    }

    /**
     * 转换保单加费信息
     *
     * @param applyBo  　投保单号
     * @param policyVo 　保单请求对象
     */
    private void transferPolicyAddPremium(ApplyBo applyBo, PolicyVo policyVo) {
        if (AssertUtils.isNotEmpty(applyBo.getListApplyAddPremiumPo())) {
            List<PolicyAddPremiumVo> policyAddPremiumVos = (List<PolicyAddPremiumVo>) this.converterList(
                    applyBo.getListApplyAddPremiumPo(), new TypeToken<List<PolicyAddPremiumVo>>() {
                    }.getType()
            );
            policyVo.setListPolicyAddPremium(policyAddPremiumVos);
        }
    }

    /**
     * 转换保单特别约定信息
     *
     * @param applyBo  　投保单号
     * @param policyVo 　保单请求对象
     */
    private void transferPolicySpecialContract(ApplyBo applyBo, PolicyVo policyVo) {
        if (AssertUtils.isNotEmpty(applyBo.getListPolicySpecialContract())) {
            List<PolicySpecialContractVo> policySpecialContractVos = (List<PolicySpecialContractVo>) this.converterList(
                    applyBo.getListPolicySpecialContract(), new TypeToken<List<PolicySpecialContractVo>>() {
                    }.getType()
            );
            policyVo.setListPolicySpecialContract(policySpecialContractVos);
        }
    }

    /**
     * 转换被保人统计信息
     *
     * @param applyBo  投保单对象
     * @param policyVo 保单对象
     */
    private void transferPolicyInsuredCollectData(ApplyBo applyBo, PolicyVo policyVo) {
        if (AssertUtils.isNotNull(applyBo.getApplyInsuredCollect())) {
            PolicyInsuredCollectVo policyInsuredCollectVo =
                    (PolicyInsuredCollectVo) this.converterObject(applyBo.getApplyInsuredCollect(), PolicyInsuredCollectVo.class);
            policyVo.setPolicyInsuredCollect(policyInsuredCollectVo);
        }
    }

    /**
     * 保单打印
     *
     * @param policyVo 　保单对象
     */
    private void transferPolicyPrintData(PolicyVo policyVo) {
        PolicyPrintInfoVo policyPrintInfoVo = new PolicyPrintInfoVo();
        policyPrintInfoVo.setNeedPrint(ApplyTermEnum.YES_NO.YES.name());
        policyVo.setPolicyPrintInfo(policyPrintInfoVo);
    }

    /**
     * 转换公共险种(团险)
     *
     * @param applyBo
     * @param policyVo
     */
    private void transApplyCoverage(ApplyBo applyBo, PolicyVo policyVo) {
        List<ApplyCoverageBo> listCoverage = applyBo.getListCoverage();
        if (AssertUtils.isNotEmpty(listCoverage)) {
            List<PolicyCoverageVo> policyCoverageVos = (List<PolicyCoverageVo>) this.converterList(
                    listCoverage, new TypeToken<List<PolicyCoverageVo>>() {
                    }.getType()
            );
            policyCoverageVos.forEach(policyCoverageVo -> policyCoverageVo.setCoverageId(null));
            policyVo.setListCoverage(policyCoverageVos);
        }
    }

    /**
     * 转换被保人信息
     *
     * @param applyBo  　投保人业务对象
     * @param policyVo 　保单对象
     */
    private void transferPolicyInsuredData(ApplyBo applyBo, PolicyVo policyVo) {
        //获取投保单被保人，支付对象
        List<ApplyInsuredBo> listApplyInsuredBo = applyBo.getListInsured();
        //初始化保单被保人
        List<PolicyInsuredVo> policyInsuredVos = new ArrayList<>();

        listApplyInsuredBo.forEach(applyInsuredBo -> {
            //被保人基础信息
            PolicyInsuredVo policyInsuredVo = (PolicyInsuredVo) this.converterObject(applyInsuredBo, PolicyInsuredVo.class);
            policyInsuredVo.setInsuredId(null);

            //受益人，险种信息
            List<ApplyBeneficiaryInfoBo> listApplyBeneficiaryInfoBo = applyInsuredBo.getListBeneficiary();
            //转换受益人信息
            transferPolicyBeneficiaryData(policyInsuredVo, listApplyBeneficiaryInfoBo);

            //转换险种信息
            transferPolicyCoverageData(applyBo, policyInsuredVo, applyInsuredBo, policyVo);

            policyInsuredVos.add(policyInsuredVo);
        });

        policyVo.setListPolicyInsured(policyInsuredVos);

        List<PolicyCoveragePaymentVo> listPolicyCoveragePayment = new ArrayList<>();
        if (AssertUtils.isNotEmpty(policyInsuredVos)) {
            policyInsuredVos.forEach(policyInsuredReqFc -> {
                if (AssertUtils.isNotEmpty(policyInsuredReqFc.getListPolicyCoverage())) {
                    policyInsuredReqFc.getListPolicyCoverage().forEach(policyCoverageReqFc -> {
                        if (AssertUtils.isNotNull(policyCoverageReqFc.getPolicyCoveragePremium())) {
                            listPolicyCoveragePayment.add(policyCoverageReqFc.getPolicyCoveragePremium().getPolicyCoveragePayment());
                        }
                    });
                }
            });
        }
        policyVo.getPolicyPremium().getPolicyPayment().setListPolicyCoveragePayment(listPolicyCoveragePayment);
    }

    /**
     * 转换受益人信息
     *
     * @param policyInsuredVo            　保单被保人对象
     * @param listApplyBeneficiaryInfoBo 　投保单受益人集合
     */
    public void transferPolicyBeneficiaryData(PolicyInsuredVo
                                                      policyInsuredVo, List<ApplyBeneficiaryInfoBo> listApplyBeneficiaryInfoBo) {
        //受益人
        if (AssertUtils.isNotNull(listApplyBeneficiaryInfoBo)) {
            List<PolicyBeneficiaryInfoVo> listPolicyBeneficiaryInfo = new ArrayList<>();
            listApplyBeneficiaryInfoBo.forEach(applyBeneficiaryInfoBo -> {
                PolicyBeneficiaryInfoVo policyBeneficiaryInfoVo = (PolicyBeneficiaryInfoVo) this.converterObject(applyBeneficiaryInfoBo, PolicyBeneficiaryInfoVo.class);
                PolicyBeneficiaryVo policyBeneficiaryVo = (PolicyBeneficiaryVo) this.converterObject(applyBeneficiaryInfoBo.getApplyBeneficiaryBo(), PolicyBeneficiaryVo.class);
                policyBeneficiaryVo.setBeneficiaryId(null);
                policyBeneficiaryInfoVo.setPolicyBeneficiary(policyBeneficiaryVo);
                List<PolicyBeneficiaryAttachmentVo> policyBeneficiaryAttachmentReqFcs = new ArrayList<>();
                if (AssertUtils.isNotEmpty(applyBeneficiaryInfoBo.getListBeneficiaryAttachment())) {
                    applyBeneficiaryInfoBo.getListBeneficiaryAttachment().forEach(applyBeneficiaryAttachmentBo -> {
                        PolicyBeneficiaryAttachmentVo policyBeneficiaryAttachmentVo = new PolicyBeneficiaryAttachmentVo();
                        policyBeneficiaryAttachmentVo.setAttachmentId(applyBeneficiaryAttachmentBo.getAttachmentId());
                        policyBeneficiaryAttachmentVo.setPolicyBeneficiaryAttachmentId(null);
                        policyBeneficiaryAttachmentVo.setCustomerId(
                                AssertUtils.isNotNull(applyBeneficiaryInfoBo.getApplyBeneficiaryBo()) ? applyBeneficiaryInfoBo.getApplyBeneficiaryBo().getCustomerId() : null
                        );
                        policyBeneficiaryAttachmentReqFcs.add(policyBeneficiaryAttachmentVo);
                    });
                }
                policyBeneficiaryInfoVo.setListBeneficiaryAttachment(policyBeneficiaryAttachmentReqFcs);
                listPolicyBeneficiaryInfo.add(policyBeneficiaryInfoVo);
            });
            policyInsuredVo.setListPolicyBeneficiary(listPolicyBeneficiaryInfo);
        }
    }

    /**
     * 保单险种信息转换
     *
     * @param policyInsuredVo 保单被保人对象
     * @param applyInsuredBo  投保单被保人对象
     * @param policyVo
     */
    public void transferPolicyCoverageData(ApplyBo applyBo, PolicyInsuredVo policyInsuredVo, ApplyInsuredBo applyInsuredBo, PolicyVo policyVo) {
        List<PolicyCoverageVo> policyCoverageVos = new ArrayList<>();
        List<ApplyCoverageBo> listApplyCoverageBo = applyInsuredBo.getListCoverage();
        //险种循环
        listApplyCoverageBo.forEach(applyCoverageBo -> {
            PolicyCoverageVo policyCoverageVo = new PolicyCoverageVo();
            //转换险种基本信息
            ClazzUtils.copyPropertiesIgnoreNull(applyCoverageBo, policyCoverageVo);
            if (AssertUtils.isNotEmpty(applyCoverageBo.getAmount())) {
                policyCoverageVo.setAmount(new BigDecimal(applyCoverageBo.getAmount()));
            }
            policyCoverageVo.setCoverageId(applyCoverageBo.getCoverageId());
            policyCoverageVo.setBonusSumAmount(applyCoverageBo.getDividendAmount());
            policyCoverageVo.setEffectiveDate(policyVo.getApproveDate());
            policyCoverageVo.setMaturityDate(applyCoverageBo.getCoveragePeriodEndDate());

            policyCoverageVo.setDeductPremium(BigDecimal.ZERO);
            policyCoverageVo.setRefundAmount(BigDecimal.ZERO);
            policyCoverageVo.setDeductRefundAmount(BigDecimal.ZERO);

            ApplyPremiumBo applyPremiumBo = applyBo.getApplyPremiumBo();
            //反算折扣前的实缴保费
            if (AssertUtils.isNotNull(applyPremiumBo.getSpecialDiscount())
                    && AssertUtils.isNotNull(applyPremiumBo.getPremiumBeforeDiscount())
                    && AssertUtils.isNotEmpty(applyPremiumBo.getDiscountType())) {
                //反算折扣前的实缴保费
                BigDecimal premiumBeforeDiscount = applyCoverageBo.getTotalPremium();
                applyCoverageBo.setActualPremium(premiumBeforeDiscount);
                policyCoverageVo.setActualPremium(premiumBeforeDiscount);
            }
            //险种缴费
            transferPolicyCoveragePremiumData(applyBo, applyInsuredBo, applyCoverageBo, policyCoverageVo);

            // 险种档次
            transferPolicyCoverageLevelData(applyCoverageBo, policyCoverageVo);

            if (ApplyTermEnum.PRODUCT_PRIMARY_FLAG.MAIN.name().equals(applyCoverageBo.getPrimaryFlag())) {
                policyVo.getPolicyPremium().setPaymentCompleteDate(policyCoverageVo.getPolicyCoveragePremium().getPaymentCompleteDate());
            }

            //险种责任
            transferPolicyCoverageDuty(applyCoverageBo, policyCoverageVo);

            //添加到集合
            policyCoverageVos.add(policyCoverageVo);
        });
        //设置险种
        policyInsuredVo.setListPolicyCoverage(policyCoverageVos);
    }

    /**
     * 转换险种档次数据
     *
     * @param applyCoverageBo  投保单险种
     * @param policyCoverageVo 保单险种
     */
    private void transferPolicyCoverageLevelData(ApplyCoverageBo applyCoverageBo, PolicyCoverageVo policyCoverageVo) {
        List<ApplyCoverageLevelPo> applyCoverageLevelPos = applyCoverageBo.getListCoverageLevel();
        if (AssertUtils.isNotEmpty(applyCoverageLevelPos)) {
            List<PolicyCoverageLevelVo> policyCoverageLevelVos = new ArrayList<>();
            applyCoverageLevelPos.forEach(applyCoverageLevelPo -> {
                PolicyCoverageLevelVo policyCoverageLevelVo = new PolicyCoverageLevelVo();
                ClazzUtils.copyPropertiesIgnoreNull(applyCoverageLevelPo, policyCoverageLevelVo);

                // 保单的实收保费设置为折扣前保费
                policyCoverageLevelVo.setActualPremium(applyCoverageLevelPo.getTotalPremium());
                policyCoverageLevelVo.setDeductPremium(BigDecimal.ZERO);
                policyCoverageLevelVo.setRefundAmount(BigDecimal.ZERO);
                policyCoverageLevelVo.setDeductRefundAmount(BigDecimal.ZERO);

                policyCoverageLevelVo.setCoverageId(null);
                policyCoverageLevelVos.add(policyCoverageLevelVo);
            });
            policyCoverageVo.setListCoverageLevel(policyCoverageLevelVos);
        }
    }

    /**
     * 转换险种责任
     *
     * @param applyCoverageBo  　　投保单险种对象
     * @param policyCoverageVo 　保单险种对象
     */
    private void transferPolicyCoverageDuty(ApplyCoverageBo applyCoverageBo, PolicyCoverageVo policyCoverageVo) {
        List<ApplyCoverageDutyBo> listCoverageDuty = applyCoverageBo.getListCoverageDuty();
        //生存给付责任
        List<PolicyCoverageSurvivalVo> policyCoverageSurvivalVos = new ArrayList<>();
        //责任
        List<PolicyCoverageDutyVo> policyCoverageDutyVos = new ArrayList<>();

        //险种有存责任则取险种责任，不然取产品责任
        if (AssertUtils.isNotEmpty(listCoverageDuty)) {
            listCoverageDuty.forEach(coverageDutyBo -> {
//                //生存给付责任特殊处理
//                listProductDutyGetRespFc.getData().stream().filter(dutyResponse -> dutyResponse.getDutyId().equals(coverageDutyBo.getDutyId()))
//                        .findFirst().ifPresent(dutyResponse -> {
//                    this.transferPolicySurvivalDuty(applyCoverageBo, policyCoverageSurvivalVos, dutyResponse);
//                });

                //责任处理
                PolicyCoverageDutyVo policyCoverageDuty = new PolicyCoverageDutyVo();
                ClazzUtils.copyPropertiesIgnoreNull(coverageDutyBo, policyCoverageDuty);
                policyCoverageDutyVos.add(policyCoverageDuty);
            });
        }
        if (AssertUtils.isNotEmpty(policyCoverageSurvivalVos)) {
            policyCoverageVo.setListPolicyCoverageSurvival(policyCoverageSurvivalVos);
        }
        if (AssertUtils.isNotEmpty(policyCoverageDutyVos)) {
            policyCoverageVo.setListPolicyCoverageDuty(policyCoverageDutyVos);
        }
    }

    private Long getInterval(String periodType) {
        long interval = 0L;
        long dayData = 24 * 60 * 60 * 1000;
        if (AssertUtils.isNotEmpty(periodType)) {
            if (periodType.equals(ApplyTermEnum.PRODUCT_PREMIUM_PERIOD_UNIT.YEAR.name())) {
                interval = 365;
            } else if (periodType.equals(ApplyTermEnum.PRODUCT_PREMIUM_PERIOD_UNIT.MONTH.name())) {
                interval = 30;
            } else if (periodType.equals(ApplyTermEnum.PRODUCT_PREMIUM_PERIOD_UNIT.DAY.name())) {
                interval = 1;
            }
        }

        return interval * dayData;
    }

    private Long getPayPeriod(ApplyCoverageBo applyCoverageBo) {
        long payPeriodTotal = 0L;
        String payPeriod = applyCoverageBo.getPremiumPeriod();
        String payPeriodType = applyCoverageBo.getPremiumPeriodUnit();
        String payFrequency = applyCoverageBo.getPremiumFrequency();
        if (AssertUtils.isNotEmpty(payPeriod) && AssertUtils.isNotEmpty(payPeriodType) && AssertUtils.isNotEmpty(payFrequency)) {
            if (payFrequency.equals(ApplyTermEnum.PRODUCT_PAY_FREQUENCY.SINGLE.name())) {
                payPeriodTotal = 1;
            } else if (payFrequency.equals(ApplyTermEnum.PRODUCT_PAY_FREQUENCY.YEAR.name())) {
                if (payPeriodType.equals(ApplyTermEnum.PRODUCT_PREMIUM_PERIOD_UNIT.SINGLE.name())) {
                    payPeriodTotal = 1;
                } else if (payPeriodType.equals(ApplyTermEnum.PRODUCT_PREMIUM_PERIOD_UNIT.YEAR.name())) {
                    payPeriodTotal = Long.valueOf(payPeriod);
                } else if (payPeriodType.equals(ApplyTermEnum.PRODUCT_PREMIUM_PERIOD_UNIT.MONTH.name())) {
                    payPeriodTotal = Long.valueOf(payPeriod) / 12;
                }
            } else if (payFrequency.equals(ApplyTermEnum.PRODUCT_PAY_FREQUENCY.MONTH.name())) {
                if (payPeriodType.equals(ApplyTermEnum.PRODUCT_PREMIUM_PERIOD_UNIT.YEAR.name())) {
                    payPeriodTotal = Long.valueOf(payPeriod) * 12;
                } else if (payPeriodType.equals(ApplyTermEnum.PRODUCT_PREMIUM_PERIOD_UNIT.MONTH.name())) {
                    payPeriodTotal = Long.valueOf(payPeriod);
                }
            }
        }

        return payPeriodTotal;
    }

    /**
     * 责任转换
     *
     * @param listPolicyCoverageDuty 　保单责任列表
     * @param dutyId                 　产品责任
     */
    private void transferPolicyDuty(List<PolicyCoverageDutyVo> listPolicyCoverageDuty, String dutyId) {
        PolicyCoverageDutyVo policyCoverageDuty = new PolicyCoverageDutyVo();
        policyCoverageDuty.setDutyId(dutyId);
        listPolicyCoverageDuty.add(policyCoverageDuty);
    }

    /**
     * 转换给付责任
     *
     * @param applyCoverageBo            　投保单险种
     * @param listPolicyCoverageSurvival 　给付险种列表
     * @param dutyResponse               　产品责任对象
     */
    public void transferPolicySurvivalDuty(ApplyCoverageBo applyCoverageBo, List<PolicyCoverageSurvivalVo> listPolicyCoverageSurvival,
                                           DutyResponse dutyResponse) {
        if (AssertUtils.isNotEmpty(dutyResponse.getDutyGets())) {
            dutyResponse.getDutyGets().forEach(dutyGetRespFc -> {
                PolicyCoverageSurvivalVo policyCoverageSurvival = new PolicyCoverageSurvivalVo();
                policyCoverageSurvival.setGetDutyId(dutyGetRespFc.getDutyGetId());
                if (AssertUtils.isNotNull(policyCoverageSurvival.getFirstPayDate())) {
                    policyCoverageSurvival.setNextPayDate(policyCoverageSurvival.getFirstPayDate() + getInterval(applyCoverageBo.getPremiumFrequency()));
                }
                policyCoverageSurvival.setDutyStatus(ApplyTermEnum.DUTY_STATUS.UNSTART.name());
                policyCoverageSurvival.setPayPeriod(getPayPeriod(applyCoverageBo));
                policyCoverageSurvival.setFrequency(0L);
                listPolicyCoverageSurvival.add(policyCoverageSurvival);
            });
        }
    }

    /**
     * 转换险种缴费信息
     *
     * @param applyInsuredBo   　投保单被保人对象
     * @param applyCoverageBo  　投保单险种对象
     * @param policyCoverageVo 　保单险种对象
     */
    private void transferPolicyCoveragePremiumData(ApplyBo applyBo, ApplyInsuredBo applyInsuredBo, ApplyCoverageBo applyCoverageBo, PolicyCoverageVo policyCoverageVo) {
        PolicyCoveragePremiumVo policyCoveragePremiumVo =
                (PolicyCoveragePremiumVo) this.converterObject(applyCoverageBo, PolicyCoveragePremiumVo.class);
        if (AssertUtils.isNotNull(applyInsuredBo.getBirthday())) {
            policyCoveragePremiumVo.setPaymentCompleteDate(getPayToDate(applyInsuredBo.getBirthday(), applyCoverageBo.getPremiumPeriod(), applyCoverageBo.getPremiumPeriodUnit(), applyCoverageBo.getPremiumFrequency(), DateUtils.getCurrentDateToTime()));
        }
        //新增保费字段
        policyCoveragePremiumVo.setPeriodTotalPremium(applyCoverageBo.getTotalPremium());
        policyCoveragePremiumVo.setPeriodOriginalPremium(applyCoverageBo.getOriginalPremium());
        policyCoveragePremiumVo.setTotalActualPremium(applyCoverageBo.getActualPremium());
        policyCoveragePremiumVo.setRefundAmount(BigDecimal.ZERO);
        policyCoveragePremiumVo.setActualPremium(applyCoverageBo.getActualPremium());

//        policyCoveragePremiumVo.setTotalOriginalPremium(applyCoverageBo.getOriginalPremium());
        // 设置险种应缴日期
        policyCoveragePremiumVo.setReceivableDate(applyBo.getApplyPremiumBo().getReceivableDate());
        //险种支付记录
        transferPolicyCoveragePaymentData(applyBo, applyCoverageBo, policyCoveragePremiumVo);
        //设置保单缴费
        policyCoverageVo.setPolicyCoveragePremium(policyCoveragePremiumVo);
    }

    /**
     * 转换保单支付信息
     *
     * @param applyCoverageBo         　投保单险种对象
     * @param policyCoveragePremiumVo 　保单险种缴费对象
     */
    private void transferPolicyCoveragePaymentData(ApplyBo applyBo, ApplyCoverageBo applyCoverageBo, PolicyCoveragePremiumVo policyCoveragePremiumVo) {
        PolicyCoveragePaymentVo policyCoveragePaymentVo = new PolicyCoveragePaymentVo();
        policyCoveragePaymentVo.setCoverageId(applyCoverageBo.getCoverageId());
        policyCoveragePaymentVo.setPaymentCompleteDate(policyCoveragePremiumVo.getPaymentCompleteDate());
        policyCoveragePaymentVo.setProductId(applyCoverageBo.getProductId());
        policyCoveragePaymentVo.setProductCode(applyCoverageBo.getProductCode());
        policyCoveragePaymentVo.setBasePremium(applyCoverageBo.getBasePremium());

        //新增保费字段
        policyCoveragePaymentVo.setPeriodOriginalPremium(applyCoverageBo.getOriginalPremium());
        policyCoveragePaymentVo.setPeriodActualPremium(applyCoverageBo.getActualPremium());

//        policyCoveragePaymentVo.setPeriodTotalPremium(applyCoverageBo.getOriginalPremium());
//        policyCoveragePaymentVo.setTotalOriginalPremium(applyCoverageBo.getOriginalPremium());

        policyCoveragePaymentVo.setPeriodStandardPremium(new BigDecimal(0));
        policyCoveragePaymentVo.setPeriodStandardRate(new BigDecimal(0));
        policyCoveragePaymentVo.setValuePremium(new BigDecimal(0));
        policyCoveragePaymentVo.setValuePremiumRate(new BigDecimal(0));
        policyCoveragePaymentVo.setAnnOccuAddPremium(new BigDecimal(0));
        policyCoveragePaymentVo.setAnnStandardPremium(new BigDecimal(0));
        policyCoveragePaymentVo.setAnnTotalPremium(new BigDecimal(0));
        policyCoveragePaymentVo.setAnnWeakAddPremium(new BigDecimal(0));
        policyCoveragePaymentVo.setGovStandardPremium(new BigDecimal(0));
        policyCoveragePaymentVo.setGovStandardRate(new BigDecimal(0));
        policyCoveragePaymentVo.setPeriodOccuAddPremium(new BigDecimal(0));
        policyCoveragePaymentVo.setPeriodWeakAddPremium(new BigDecimal(0));
        policyCoveragePaymentVo.setAgencyFee(new BigDecimal(0));
        policyCoveragePaymentVo.setAgencyFeeRate(new BigDecimal(0));
        policyCoveragePaymentVo.setCommissionFee(new BigDecimal(0));
        policyCoveragePaymentVo.setCommissionFeeRate(new BigDecimal(0));
        policyCoveragePaymentVo.setBasePremium(applyCoverageBo.getTotalPremium());
        policyCoveragePaymentVo.setExtraPremium(new BigDecimal(0));
        policyCoveragePaymentVo.setAddPremiumTerm(new BigDecimal(0));
        policyCoveragePaymentVo.setAddPremiumStartDate(0L);
        policyCoveragePaymentVo.setPremiumPeriod(applyCoverageBo.getPremiumPeriod());
        policyCoveragePaymentVo.setPremiumPeriodUnit(applyCoverageBo.getPremiumPeriodUnit());
        policyCoveragePaymentVo.setPremiumFrequency(applyCoverageBo.getPremiumFrequency());
        policyCoveragePaymentVo.setFrequency(1L);
        policyCoveragePaymentVo.setCoveragePeriod(applyCoverageBo.getCoveragePeriod());
        policyCoveragePaymentVo.setCoveragePeriodUnit(applyCoverageBo.getCoveragePeriodUnit());
        policyCoveragePaymentVo.setPrimaryFlag(applyCoverageBo.getPrimaryFlag());
        // 设置加费金额
        List<ApplyAddPremiumPo> applyAddPremiumPos = applyBo.getListApplyAddPremiumPo();
        if (AssertUtils.isNotEmpty(applyAddPremiumPos)) {
            applyAddPremiumPos.stream()
                    .filter(applyAddPremiumPo -> applyCoverageBo.getCoverageId().equals(applyAddPremiumPo.getCoverageId()))
                    .findFirst().ifPresent(applyAddPremiumPo -> {
                if (AssertUtils.isNotNull(applyAddPremiumPo.getPeriodCareerAddPremium())) {
                    policyCoveragePaymentVo.setCareerAddPremium(policyCoveragePaymentVo.getCareerAddPremium().add(applyAddPremiumPo.getPeriodCareerAddPremium()));
                }
                if (AssertUtils.isNotNull(applyAddPremiumPo.getPeriodWeakAddPremium())) {
                    policyCoveragePaymentVo.setWeakAddPremium(policyCoveragePaymentVo.getWeakAddPremium().add(applyAddPremiumPo.getPeriodWeakAddPremium()));
                }
                if (AssertUtils.isNotNull(applyAddPremiumPo.getPeriodOtherAddPremium())) {
                    policyCoveragePaymentVo.setOtherAddPremium(policyCoveragePaymentVo.getOtherAddPremium().add(applyAddPremiumPo.getPeriodOtherAddPremium()));
                }
                if (AssertUtils.isNotNull(applyAddPremiumPo.getTotalAddPremium())) {
                    policyCoveragePaymentVo.setTotalAddPremium(policyCoveragePaymentVo.getTotalAddPremium().add(applyAddPremiumPo.getTotalAddPremium()));
                }
            });
        }
        policyCoveragePaymentVo.setTotalPremium(applyCoverageBo.getTotalPremium().add(policyCoveragePaymentVo.getTotalAddPremium()));
        //实际缴费(当期实缴费用) = period_actual_premium + total_add_premium
        policyCoveragePaymentVo.setActualPremium(policyCoveragePaymentVo.getPeriodActualPremium().add(policyCoveragePaymentVo.getTotalAddPremium()));

        policyCoveragePremiumVo.setPolicyCoveragePayment(policyCoveragePaymentVo);
    }

    /**
     * 调用产品计算费率
     *
     * @param applyBo
     * @param policyVo 保单对象
     */
    private void transferPolicyPaymentRateData(ApplyBo applyBo, PolicyVo policyVo) {
        ApplyPremiumBo applyPremiumBo = applyBo.getApplyPremiumBo();
        PolicyPaymentVo policyPaymentVo = policyVo.getPolicyPremium().getPolicyPayment();
        log.info("product_cal:" + JSON.toJSONString(policyPaymentVo));
        PolicyPaymentRequest policyPaymentRequest = (PolicyPaymentRequest) this.converterObject(policyPaymentVo, PolicyPaymentRequest.class);
        //调用
        if (AssertUtils.isNotNull(applyPremiumBo.getDiscountType())) {
            policyPaymentRequest.setCommissionDiscountType(BEFORE_DISCOUNT.name());
        }
        ResultObject<PolicyPaymentResponse> reqFcResultObject = productRateApi.rateCalculationRate(policyPaymentRequest);
        log.info("product2:" + JSON.toJSONString(reqFcResultObject));
        PolicyPaymentResponse policyPaymentResponse = reqFcResultObject.getData();
        if (AssertUtils.isNotNull(policyPaymentResponse)) {
            //数据匹配
            PolicyPaymentVo policyPayment = policyVo.getPolicyPremium().getPolicyPayment();
            policyPayment.setPeriodStandardPremium(policyPaymentResponse.getPeriodStandardPremium());
            policyPayment.setValuePremium(policyPaymentResponse.getValuePremium());
            policyPayment.setCommissionFee(policyPaymentResponse.getCommissionFee());
            policyPayment.setServiceChargeFee(policyPaymentResponse.getServiceChargeFee());
            policyPayment.setServiceChargeRate(policyPaymentResponse.getServiceChargeRate());
            //险种
            policyVo.getListPolicyInsured().forEach(policyInsuredReqFc -> {
                policyInsuredReqFc.getListPolicyCoverage().forEach(policyCoverageReqFc -> {
                    policyPaymentResponse.getListPolicyCoveragePayment().stream()
                            .filter(policyCoveragePaymentReqFc -> policyCoveragePaymentReqFc.getCoverageId().equals(policyCoverageReqFc.getCoverageId()))
                            .findFirst().ifPresent(policyCoveragePaymentReqFc -> {
                        PolicyCoveragePaymentVo policyCoveragePaymentVo = policyCoverageReqFc.getPolicyCoveragePremium().getPolicyCoveragePayment();

                        ClazzUtils.copyPropertiesIgnoreNull(policyCoveragePaymentReqFc, policyCoveragePaymentVo);
                    });
                });
            });
        }
    }

    public Long getPayToDate(long birthday, String period, String periodType, String premiumFrequency, long approveDate) {
        long payToDate = 0L;
        int age = getAgeByBirthday(birthday);
        if (AssertUtils.isNotEmpty(periodType) && AssertUtils.isNotEmpty(period)) {
            if (periodType.equals(ApplyTermEnum.PRODUCT_PREMIUM_PERIOD_UNIT.LIFELONG.name())) {
                payToDate = DateUtils.addStringYearsRT(DateUtils.timeToTimeLow(approveDate), 120 - age);
                //缴费周期为年缴
                if (ApplyTermEnum.PRODUCT_PAY_FREQUENCY.YEAR.name().equals(premiumFrequency)) {

                } else if (ApplyTermEnum.PRODUCT_PAY_FREQUENCY.SEMIANNUAL.name().equals(premiumFrequency)) {
                    //半年缴费-加6个月
                    payToDate = DateUtils.addStringMonthRT(payToDate, 6);
                } else if (ApplyTermEnum.PRODUCT_PAY_FREQUENCY.SEASON.name().equals(premiumFrequency)) {
                    //季度缴费　加9个月
                    payToDate = DateUtils.addStringMonthRT(payToDate, 9);
                } else if (ApplyTermEnum.PRODUCT_PAY_FREQUENCY.MONTH.name().equals(premiumFrequency)) {
                    //月度缴费　加11个月
                    payToDate = DateUtils.addStringMonthRT(payToDate, 11);
                }

            } else if (periodType.equals(ApplyTermEnum.PRODUCT_PREMIUM_PERIOD_UNIT.YEAR.name())) {
                payToDate = DateUtils.addStringYearsRT(DateUtils.timeToTimeLow(approveDate), Integer.valueOf(period) - 1);
                //缴费周期为年缴
                if (ApplyTermEnum.PRODUCT_PAY_FREQUENCY.YEAR.name().equals(premiumFrequency)) {

                } else if (ApplyTermEnum.PRODUCT_PAY_FREQUENCY.SEMIANNUAL.name().equals(premiumFrequency)) {
                    //半年缴费-加6个月
                    payToDate = DateUtils.addStringMonthRT(payToDate, 6);
                } else if (ApplyTermEnum.PRODUCT_PAY_FREQUENCY.SEASON.name().equals(premiumFrequency)) {
                    //季度缴费　加9个月
                    payToDate = DateUtils.addStringMonthRT(payToDate, 9);
                } else if (ApplyTermEnum.PRODUCT_PAY_FREQUENCY.MONTH.name().equals(premiumFrequency)) {
                    //月度缴费　加11个月
                    payToDate = DateUtils.addStringMonthRT(payToDate, 11);
                }
            } else if (periodType.equals(ApplyTermEnum.PRODUCT_PREMIUM_PERIOD_UNIT.AGE.name())) {
                payToDate = DateUtils.addStringYearsRT(DateUtils.timeToTimeLow(approveDate), Integer.valueOf(period) - age);
                //缴费周期为年缴
                if (ApplyTermEnum.PRODUCT_PAY_FREQUENCY.YEAR.name().equals(premiumFrequency)) {

                } else if (ApplyTermEnum.PRODUCT_PAY_FREQUENCY.SEMIANNUAL.name().equals(premiumFrequency)) {
                    //半年缴费-加6个月
                    payToDate = DateUtils.addStringMonthRT(payToDate, 6);
                } else if (ApplyTermEnum.PRODUCT_PAY_FREQUENCY.SEASON.name().equals(premiumFrequency)) {
                    //季度缴费　加9个月
                    payToDate = DateUtils.addStringMonthRT(payToDate, 9);
                } else if (ApplyTermEnum.PRODUCT_PAY_FREQUENCY.MONTH.name().equals(premiumFrequency)) {
                    //月度缴费　加11个月
                    payToDate = DateUtils.addStringMonthRT(payToDate, 11);
                }
            } else if (periodType.equals(ApplyTermEnum.PRODUCT_PREMIUM_PERIOD_UNIT.MONTH.name())) {
                payToDate = DateUtils.addStringMonthRT(DateUtils.timeToTimeLow(approveDate), Integer.valueOf(period) - 1);
            } else if (periodType.equals(ApplyTermEnum.PRODUCT_PREMIUM_PERIOD_UNIT.DAY.name())) {
                payToDate = DateUtils.addStringDayRT(DateUtils.timeToTimeLow(approveDate), Integer.valueOf(period) - 1);
            }
        }
        return payToDate;
    }

    public int getAgeByBirthday(Long ageDate) {
        int age = 0;
        try {
            SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            Date date = format.parse(format.format(ageDate));
            age = DateUtils.getAgeYear(date);

        } catch (Exception e) {
            throw new RequestException(ApplyErrorConfigEnum.APPLY_APP_TRANS_BIRTHDAY_TO_AGE_ERROR);
        }
        return age;
    }

    @Async
    public void saveUserEventRecordApply(ApplyBo applyBo) {
        List<UserEventRecordRequest> userEventRecordReqFcs = new ArrayList<>();
        UserEventRecordRequest userEventRecordReqFc = new UserEventRecordRequest();
        userEventRecordReqFc.setType(ApplyTermEnum.EVENT_RECORD_TYPE.POLICY_BOOK_NUMBER.name());
        userEventRecordReqFc.setValue(new BigDecimal(1));
        userEventRecordReqFc.setEventDate(DateUtils.getCurrentTime());
        userEventRecordReqFc.setUserId(applyBo.getApplyAgentBo().getAgentId());
        userEventRecordReqFcs.add(userEventRecordReqFc);

        UserEventRecordRequest userEventRecordReqFc1 = new UserEventRecordRequest();
        userEventRecordReqFc1.setType(ApplyTermEnum.EVENT_RECORD_TYPE.TOTAL_PREMIUM.name());
        userEventRecordReqFc1.setValue(applyBo.getReceivablePremium());
        userEventRecordReqFc1.setEventDate(DateUtils.getCurrentTime());
        userEventRecordReqFc1.setUserId(applyBo.getApplyAgentBo().getAgentId());
        userEventRecordReqFcs.add(userEventRecordReqFc1);
        baseAgentApi.saveUserEventRecord(userEventRecordReqFcs.toArray(new UserEventRecordRequest[0]));
    }

    /**
     * 将险种按档次分组，并计算保费和份数
     *
     * @param listApplyCoverage
     * @return
     */
    public List<ApplyCoverageBo> transSummaryCoverage(List<ApplyCoverageBo> listApplyCoverage) {
        // 将险种按产品分组
        Map<String, List<ApplyCoverageBo>> productMap =
                listApplyCoverage.stream().collect(Collectors.groupingBy(ApplyCoverageBo::getProductId));
        // 将险种按产品、档次分组 key:产品ID|档次 例：PRO88000000000002|A
        Map<String, List<ApplyCoverageBo>> productLevelMap = new HashMap<>();
        for (String productId : productMap.keySet()) {
            for (ApplyCoverageBo applyCoverageBo : productMap.get(productId)) {
                List<ApplyCoverageLevelPo> listCoverageLevel = applyCoverageBo.getListCoverageLevel();
                if (TerminologyConfigEnum.WHETHER.YES.name().equals(applyCoverageBo.getDutyChooseFlag())) {
                    // 11号产品
                    // 将险种按产品、责任、档次分组 key:产品ID|责任ID|档次
                    for (ApplyCoverageDutyBo coverageDutyBo : applyCoverageBo.getListCoverageDuty()) {
                        for (ApplyCoverageLevelPo coverageLevelPo : coverageDutyBo.getListCoverageLevel()) {
                            // 组装新的险种数据
                            // 档次
                            List<ApplyCoverageLevelPo> coverageLevelPos = new ArrayList<>();
                            coverageLevelPos.add(coverageLevelPo);
                            // 责任
                            List<ApplyCoverageDutyBo> coverageDutyBos = new ArrayList<>();
                            ApplyCoverageDutyBo dutyBo = new ApplyCoverageDutyBo();
                            ClazzUtils.copyPropertiesIgnoreNull(coverageDutyBo, dutyBo);
                            dutyBo.setListCoverageLevel(coverageLevelPos);
                            coverageDutyBos.add(dutyBo);
                            // 险种
                            ApplyCoverageBo coverageBo = new ApplyCoverageBo();
                            ClazzUtils.copyPropertiesIgnoreNull(applyCoverageBo, coverageBo);
                            coverageBo.setListCoverageDuty(coverageDutyBos);
                            coverageBo.setProductLevel(coverageLevelPo.getProductLevel());
                            coverageBo.setMult(coverageLevelPo.getMult());
                            coverageBo.setTotalPremium(coverageLevelPo.getTotalPremium());
                            coverageBo.setPremium(coverageLevelPo.getPremium());
                            coverageBo.setOriginalPremium(coverageLevelPo.getOriginalPremium());
                            coverageBo.setActualPremium(coverageLevelPo.getActualPremium());
                            coverageBo.setInsuredNum(1L);
                            coverageBo.setDutyId(coverageDutyBo.getDutyId());

                            // 将险种暂存map
                            putMap(productLevelMap, productId + "|" + coverageDutyBo.getDutyId() + "|" + coverageLevelPo.getProductLevel(), coverageBo);
                        }
                    }
                } else if (AssertUtils.isNotEmpty(listCoverageLevel)) {
                    // 团险
                    for (ApplyCoverageLevelPo coverageLevelPo : listCoverageLevel) {
                        // 组装新的险种数据
                        ApplyCoverageBo coverageBo = new ApplyCoverageBo();
                        ClazzUtils.copyPropertiesIgnoreNull(applyCoverageBo, coverageBo);
                        coverageBo.setProductLevel(coverageLevelPo.getProductLevel());
                        coverageBo.setMult(coverageLevelPo.getMult());
                        coverageBo.setTotalPremium(coverageLevelPo.getTotalPremium());
                        coverageBo.setPremium(coverageLevelPo.getPremium());
                        coverageBo.setOriginalPremium(coverageLevelPo.getOriginalPremium());
                        coverageBo.setActualPremium(coverageLevelPo.getActualPremium());
                        if (AssertUtils.isNotEmpty(coverageLevelPo.getPremiumFrequency())) {
                            coverageBo.setPremiumFrequency(coverageLevelPo.getPremiumFrequency());
                        }
                        if (AssertUtils.isNotNull(coverageLevelPo.getAmount())) {
                            coverageBo.setAmount(coverageLevelPo.getAmount() + "");
                        }
                        coverageBo.setInsuredNum(1L);

                        // 将险种暂存map
                        putMap(productLevelMap, productId + "|" + coverageLevelPo.getProductLevel(), coverageBo);
                    }
                } else {
                    // 个险
                    applyCoverageBo.setInsuredNum(1L);
                    // 将险种暂存map
                    putMap(productLevelMap, productId + "|" + applyCoverageBo.getProductLevel(), applyCoverageBo);
                }
            }
        }

        // 统计
        List<ApplyCoverageBo> applyCoverageBos = new ArrayList<>();
        for (String productLevel : productLevelMap.keySet()) {
            List<ApplyCoverageBo> coverageBos = productLevelMap.get(productLevel);
            int mult = 0;
            Long insuredNum = 0L;
            BigDecimal totalPremium = BigDecimal.ZERO;
            BigDecimal actualPremium = BigDecimal.ZERO;
            BigDecimal amount = BigDecimal.ZERO;
            for (ApplyCoverageBo applyCoverageBo : coverageBos) {
                if (AssertUtils.isNotNull(applyCoverageBo.getAmount())) {
                    amount = amount.add(new BigDecimal(applyCoverageBo.getAmount()));
                }
                if (AssertUtils.isNotNull(applyCoverageBo.getTotalPremium())) {
                    totalPremium = totalPremium.add(applyCoverageBo.getTotalPremium());
                }
                if (AssertUtils.isNotNull(applyCoverageBo.getActualPremium())) {
                    actualPremium = actualPremium.add(applyCoverageBo.getActualPremium());
                }
                if (AssertUtils.isNotEmpty(applyCoverageBo.getMult())) {
                    mult = mult + (Integer.valueOf(applyCoverageBo.getMult()));
                }
                if (AssertUtils.isNotNull(applyCoverageBo.getInsuredNum())) {
                    insuredNum += applyCoverageBo.getInsuredNum();
                }
            }
            ApplyCoverageBo applyCoverageBo = new ApplyCoverageBo();
            ClazzUtils.copyPropertiesIgnoreNull(coverageBos.get(0), applyCoverageBo);
            applyCoverageBo.setTotalPremium(totalPremium);
            applyCoverageBo.setActualPremium(actualPremium);
            applyCoverageBo.setMult(mult + "");
            applyCoverageBo.setInsuredNum(insuredNum);
            applyCoverageBo.setAmount(amount.compareTo(BigDecimal.ZERO) > 0 ? amount.toString() : null);
            if (TerminologyConfigEnum.WHETHER.YES.name().equals(coverageBos.get(0).getDutyChooseFlag())) {
                // 11号产品
                List<ApplyCoverageDutyBo> coverageDutyBos = new ArrayList<>();
                coverageDutyBos.addAll(coverageBos.get(0).getListCoverageDuty());

                List<ApplyCoverageLevelPo> coverageLevelPos = coverageDutyBos.get(0).getListCoverageLevel();
                if (AssertUtils.isNotEmpty(coverageLevelPos)) {
                    coverageLevelPos.get(0).setTotalPremium(totalPremium);
                    coverageLevelPos.get(0).setActualPremium(actualPremium);
                    coverageLevelPos.get(0).setMult(mult + "");
                }

                applyCoverageBo.setListCoverageDuty(coverageDutyBos);
            }
            applyCoverageBos.add(applyCoverageBo);
        }
        //排序
        if (AssertUtils.isNotEmpty(applyCoverageBos)) {
            applyCoverageBos.sort(Comparator.comparing(ApplyCoverageBo::getPrimaryFlag, Comparator.nullsLast(String::compareTo)).reversed()
                    .thenComparing(ApplyCoverageBo::getProductId, Comparator.nullsLast(String::compareTo))
                    .thenComparing(ApplyCoverageBo::getDutyId, Comparator.nullsLast(String::compareTo))
                    .thenComparing(ApplyCoverageBo::getProductLevel, Comparator.nullsLast(String::compareTo)));
        }
        return applyCoverageBos;
    }

    /**
     * 将险种暂存map
     *
     * @param map             map
     * @param key             key
     * @param applyCoverageBo value
     */
    private void putMap(Map<String, List<ApplyCoverageBo>> map, String key, ApplyCoverageBo applyCoverageBo) {
        List<ApplyCoverageBo> coverageBos = map.get(key);
        if (AssertUtils.isNotEmpty(coverageBos)) {
            coverageBos.add(applyCoverageBo);
        } else {
            coverageBos = new ArrayList<>();
            coverageBos.add(applyCoverageBo);
            map.put(key, coverageBos);
        }
    }

    /**
     * 必须回访标识
     *
     * @param applyBo applyBo
     * @return 必须回访标识
     */
    public String calMustReturnFlag(ApplyBo applyBo) {
        /*
         * sprint-v3.7.6.20201225
         * 1. 保单承保后，回访提交时需把必须回访标识传给回访中心，必须回访标识达成条件为：
         * a.个险保单年缴保费≥500元，团体保单≥2000元；
         * b.非标件
         */

        String applyId = applyBo.getApplyId();
        ApplyUnderwriteDecisionPo applyUnderwriteDecisionPo = applyUnderwriteBaseService.queryApplyUnderwriteDecisionPo(applyId);
        if (!AssertUtils.isNotNull(applyBo) || !AssertUtils.isNotNull(applyUnderwriteDecisionPo)) {
            log.info("return=========>applyId:{}", applyId);
            return TerminologyConfigEnum.WHETHER.YES.name();
        }

        final boolean[] notStandardFlag = {false};
        List<BaseUnderwriteDecisionPo> baseUnderwriteDecisionPos = applyUnderwriteBaseService.queryBaseUnderwriteDecisionPo();
        baseUnderwriteDecisionPos.stream().filter(baseUnderwriteDecisionPo -> baseUnderwriteDecisionPo.getUnderwriteDecisionId().equals(applyUnderwriteDecisionPo.getUnderwriteDecisionId()))
                .findAny().ifPresent(baseUnderwriteDecisionPo -> {
            //非标件
            if (!ApplyTermEnum.DECISION_TYPE.STANDARD.name().equalsIgnoreCase(baseUnderwriteDecisionPo.getUnderwriteDecisionCode())) {
                log.info("非标件=========>applyId:{}", applyId);
                notStandardFlag[0] = true;
            }
        });

        String mustReturnFlag = TerminologyConfigEnum.WHETHER.NO.name();

        if (ApplyTermEnum.APPLY_TYPE.LIFE_INSURANCE_PERSONAL.name().equals(applyBo.getApplyType())) {
            BigDecimal totalYearPremium = BigDecimal.ZERO;
            for (ApplyCoverageBo applyCoverageBo : applyBo.getListInsuredCoverage()) {
                String premiumFrequency = applyCoverageBo.getPremiumFrequency();
                if (ApplyTermEnum.PRODUCT_PAY_FREQUENCY.MONTH.name().equals(premiumFrequency)) {
                    BigDecimal bigDecimal = applyCoverageBo.getTotalPremium().divide(new BigDecimal("0.09"), 6, BigDecimal.ROUND_UP);
                    totalYearPremium = totalYearPremium.add(bigDecimal);
                } else if (ApplyTermEnum.PRODUCT_PAY_FREQUENCY.SEASON.name().equals(premiumFrequency)) {
                    BigDecimal bigDecimal = applyCoverageBo.getTotalPremium().divide(new BigDecimal("0.27"), 6, BigDecimal.ROUND_UP);
                    totalYearPremium = totalYearPremium.add(bigDecimal);
                } else if (ApplyTermEnum.PRODUCT_PAY_FREQUENCY.SEMIANNUAL.name().equals(premiumFrequency)) {
                    BigDecimal bigDecimal = applyCoverageBo.getTotalPremium().divide(new BigDecimal("0.52"), 6, BigDecimal.ROUND_UP);
                    totalYearPremium = totalYearPremium.add(bigDecimal);
                } else if (ApplyTermEnum.PRODUCT_PAY_FREQUENCY.YEAR.name().equals(premiumFrequency)) {
                    totalYearPremium = totalYearPremium.add(applyCoverageBo.getTotalPremium());
                } else if (ApplyTermEnum.PRODUCT_PAY_FREQUENCY.SINGLE.name().equals(premiumFrequency)) {
                    totalYearPremium = totalYearPremium.add(applyCoverageBo.getTotalPremium());
                }
            }
            log.info("年化保费applyId:{},totalYearPremium:{}", applyId, totalYearPremium.toString());
            //个险保单年化保费≥500元
            if (notStandardFlag[0] || totalYearPremium.compareTo(new BigDecimal(500)) >= 0) {
                mustReturnFlag = TerminologyConfigEnum.WHETHER.YES.name();
            }
        } else if (ApplyTermEnum.APPLY_TYPE.LIFE_INSURANCE_GROUP.name().equals(applyBo.getApplyType())) {
            //团体保单≥2000元
            log.info("团体保单保费:{}", applyBo.getApplyPremiumBo().getReceivablePremium().toString());
            if (notStandardFlag[0] || applyBo.getApplyPremiumBo().getReceivablePremium().compareTo(new BigDecimal(2000)) >= 0) {
                mustReturnFlag = TerminologyConfigEnum.WHETHER.YES.name();
            }
        }
        log.info("return2=========>applyId:{},mustReturnFlag:{}", applyId, mustReturnFlag);
        return mustReturnFlag;
    }

    /**
     * 判断是否通过个险智能核保
     *
     * @param applyId
     * @return
     */
    public boolean doWhetherThroughAutoUW(String applyId, String applyType) {
        List<ApplyUnderwriteProblemPo> applyUnderwriteProblemPos = applyUnderwriteBaseService.queryApplyUnderwriteProblemPo(applyId);
        ApplyUnderwriteDecisionPo applyUnderwriteDecisionPo = applyUnderwriteBaseService.queryApplyUnderwriteDecisionPo(applyId);

        return ApplyTermEnum.APPLY_TYPE.LIFE_INSURANCE_PERSONAL.name().equals(applyType)
                && !AssertUtils.isNotEmpty(applyUnderwriteProblemPos)
                && AssertUtils.isNotNull(applyUnderwriteDecisionPo)
                && "Auto Underwriting Passed".equals(applyUnderwriteDecisionPo.getRemarks());
    }

    public String getMainProductId(String applyId) {
        List<ApplyCoveragePo> listApplyCoveragePo = applyCoverageBaseService.listApplyCoverageOfInsured(applyId);
        if (!AssertUtils.isNotEmpty(listApplyCoveragePo)) {
            return null;
        }
        Optional<ApplyCoveragePo> first = listApplyCoveragePo.stream().filter(applyCoveragePo -> ApplyTermEnum.PRODUCT_PRIMARY_FLAG.MAIN.name().equals(applyCoveragePo.getPrimaryFlag()))
                .findFirst();
        return first.map(ApplyCoveragePo::getProductId).orElse(null);
    }

    /**
     * 针对20A网销产品国籍国际化特殊处理
     *
     * @param applyId
     * @param codeKey
     * @param language
     * @return
     */
    public String getNationalityCodeTypeName(String applyId, String codeKey, String language) {
        if (!AssertUtils.isNotEmpty(codeKey)) {
            return null;
        }
        String mainProductId = this.getMainProductId(applyId);
        //针对20A网销产品国籍国际化特殊处理
        String nationalityCodeType = "NATIONALITY";
        if (ProductTermEnum.PRODUCT.PRODUCT_20A.id().equals(mainProductId)) {
            nationalityCodeType = "NATIONALITY_TYPE";
        }
        SyscodeResponse syscodeResponse = platformInternationalBaseApi.queryOneInternational(nationalityCodeType, codeKey, language).getData();
        if (AssertUtils.isNotNull(syscodeResponse)) {
            return syscodeResponse.getCodeName();
        }
        return null;
    }

    /**
     * 相同客户的网销投保单只能发起支付一个，作废其他
     *
     * @param originApplyId
     * @param customerId
     */
    public void doRepeatOnlineApply(String originApplyId, String customerId) {
        List<ApplyPo> repeatOnlineApply = applyBaseService.getRepeatOnlineApply(originApplyId, customerId);
        if (!AssertUtils.isNotEmpty(repeatOnlineApply)) {
            return;
        }
        log.info("相同客户的网销投保单:{}", JSON.toJSONString(repeatOnlineApply));
        repeatOnlineApply.forEach(applyPo -> {
            String applyId = applyPo.getApplyId();
            Long abandonedDate = DateUtils.getCurrentTime();

            ApplyAbandonedPo applyAbandonedPo = applyBaseService.queryApplyAbandoned(applyId);
            if (!AssertUtils.isNotNull(applyAbandonedPo)) {
                applyAbandonedPo = new ApplyAbandonedPo();
            }
            applyAbandonedPo.setApplyId(applyId);
            applyAbandonedPo.setApplyDate(applyPo.getApplyDate());
            applyAbandonedPo.setApplyNo(applyPo.getApplyNo());
            if (AssertUtils.isNotNull(applyPo.getApplyDate())) {
                applyAbandonedPo.setApplyDateStr(DateUtils.timeStrToString(applyPo.getApplyDate(), DateUtils.FORMATE6));
            }
            String abandonedDateStr = DateUtils.timeStrToString(abandonedDate, DateUtils.FORMATE6);
            log.info("正在作废投保单,时间{}", abandonedDateStr);
            applyAbandonedPo.setInvalidDate(abandonedDate);
            applyAbandonedPo.setInvalidDateStr(abandonedDateStr);
            applyAbandonedPo.setAbandonedDate(abandonedDate);
            applyAbandonedPo.setAbandonedRemark("已经存在其余网销投保单，作废此单");
            applyAbandonedPo.setAbandonedType(ApplyTermEnum.ABANDONED_TYPE.AUTO.name());
            String userId = applyPo.getCreatedUserId();
            applyBaseService.saveApplyAbandoned(userId, applyAbandonedPo);

            applyPo.setApplyStatus(ApplyTermEnum.APPLY_STATUS_FLAG.APPLY_STATUS_ABANDONED.name());
            applyPo.setInvalidDate(DateUtils.getCurrentTime());
            applyBaseService.saveApply(userId, applyPo);

            if (!ApplyTermEnum.APPLY_STATUS_FLAG.APPLY_STATUS_INITIAL.name().equals(applyPo.getApplyStatus())) {
                TerminationTaskRequest terminationTaskRequest = new TerminationTaskRequest();
                terminationTaskRequest.setBusinessNo(applyPo.getApplyId());
                terminationTaskRequest.setTerminationMessage(null);
                log.info("正在结束工作流,时间{}", abandonedDateStr);
                ResultObject resultObject1 = workFlowApi.terminationTask(terminationTaskRequest);
//            AssertUtils.isResultObjectError(log, resultObject1);
            }
            // 作废财务支付记录
            List<ApplyPaymentTransactionBo> applyPaymentTransactionBos = applyPaymentTransactionBaseService.queryApplyPaymentTransactions(applyId);
            if (AssertUtils.isNotEmpty(applyPaymentTransactionBos)) {
                List<String> paymentTypes = Arrays.asList(ApplyTermEnum.CHARGE_TYPE.NORMAL_PAYMENT.name(), ApplyTermEnum.CHARGE_TYPE.PREPAID_PREMIUM.name());
                applyPaymentTransactionBos.stream().filter(applyPaymentTransactionBo -> paymentTypes.contains(applyPaymentTransactionBo.getPaymentType())
                                && Arrays.asList(PayNotifyTermEnum.PAY_NOTIFY_STATUS.PAYMENT_WAITTING.name(), PayNotifyTermEnum.PAY_NOTIFY_STATUS.PAYMENT_FINISHED.name()).contains(applyPaymentTransactionBo.getPaymentStatus()))
                        .forEach(applyPaymentTransactionBo -> {
                            ResultObject<Void> voidResultObject = paymentApi.updatePaymentStatus(applyPaymentTransactionBo.getPaymentId(), ApplyTermEnum.PAYMENT_STATUS.PAYMENT_INVALID.name());
                            log.info("作废发起支付的支付记录：{}", JackSonUtils.toJson(voidResultObject));
                            applyPaymentTransactionBo.setPaymentStatus(ApplyTermEnum.PAYMENT_STATUS.PAYMENT_INVALID.name());
                            applyPaymentTransactionBo.setValidFlag(com.gclife.common.model.config.TerminologyConfigEnum.VALID_FLAG.invalid.name());
                            applyPaymentTransactionBo.getApplyPaymentTransactionItemBos().forEach(applyPaymentTransactionItemBo -> {
                                applyPaymentTransactionItemBo.setPaymentStatus(ApplyTermEnum.PAYMENT_STATUS.PAYMENT_INVALID.name());
                                applyPaymentTransactionItemBo.setValidFlag(com.gclife.common.model.config.TerminologyConfigEnum.VALID_FLAG.invalid.name());
                            });
                            applyPaymentTransactionBaseService.saveApplyPaymentTransaction(userId, applyPaymentTransactionBo);
                        });
            }
        });
    }

    public String transPremiumPaymentTerm (String premiumFrequency, String premiumPeriod) {
        // 网销缴费年期特殊处理
        String premiumPaymentTerm = ProductTermEnum.PREMIUM_PAYMENT_TERM.SINGLE.name();
        if (ApplyTermEnum.PRODUCT_PAY_FREQUENCY.YEAR.name().equals(premiumFrequency)) {
            if ("2".equals(premiumPeriod)) {
                premiumPaymentTerm = ProductTermEnum.PREMIUM_PAYMENT_TERM.TWO_TIMES.name();
            } else if ("3".equals(premiumPeriod)) {
                premiumPaymentTerm = ProductTermEnum.PREMIUM_PAYMENT_TERM.THIRD_TIMES.name();
            }
        }
        return premiumPaymentTerm;
    }
}
