package com.gclife.apply.model.bo;

import com.gclife.apply.core.jooq.tables.pojos.ApplyApplicantPo;
import com.gclife.apply.core.jooq.tables.pojos.ApplyOccupationNaturePo;
import com.gclife.common.annotation.Internation;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * create 17-9-18
 * description
 */
@Data
public class ApplyApplicantBo extends ApplyApplicantPo{
    @ApiModelProperty(value = "刮刮卡健康告知标识", example = "YES")
    private String healthNoticeFlag = "YES";
    @ApiModelProperty(example = "影像附件ID")
    private String attachmentId;
    @ApiModelProperty(example = "护照资料影像附件ID")
    private String passwordAttachmentId;
    @ApiModelProperty(example = "护照资料影像附件ID")
    private String passwordVisaAttachmentId;
    /***************投保单打印国际化字段*********************/
    // 国籍 名
    private String nationalityName;
    // 职业名
    private String occupationName;
    // 兼职
    private String pluralityName;
    // 证件类型
    private String idTypeName;
    // 完整地址
    private String fullAddress;
    private String degreeName;
    private String socialSecurityName;
    private String companyAreaName;
    @ApiModelProperty(value = "性别名称")
    @Internation(filed = "sex", codeType = "GENDER")
    private String sexName;
    private String doctorAreaCodeName;
    //收入国际化
    private String incomeName;

    @ApiModelProperty(example = "回溯日期")
    private Long backTrackDate;

    @ApiModelProperty(example = "回溯日期格")
    private String backTrackDateFormat;
    @ApiModelProperty(example = "预期保费来源集合")
    private List<String> listExpectedPremiumSources;
    /**
     * 职业性质
     */
    private ApplyOccupationNaturePo occupationNature;

    @ApiModelProperty(value = "客户appUserId", name = "客户appUserId", example = "1")
    private String clientUserId;
    @ApiModelProperty(example = "网销计划书缓存数据")
    private String cacheData;
    @ApiModelProperty(example = "网销保额")
    private BigDecimal amount;
}
