package com.gclife.apply.model.config;

/**
 * <AUTHOR>
 * create 17-9-18
 * description
 */
public class ApplyTermEnum {

    private ApplyTermEnum() {
        throw new AssertionError();
    }

    public interface ConstantType {
    }

    public enum GENDER implements ConstantType {
        /**
         * 性别
         */
        FEMALE("Female", "女"),
        MALE("Male", "男");

        private String code;
        private String desc;

        private GENDER(String code, String desc) {
            this.code = code;
            this.desc = desc;
        }

        public String code() {
            return this.code;
        }

        public String desc() {
            return this.desc;
        }
    }

    public enum ABANDONED_TYPE implements ConstantType {
        //废弃类型(AUTO:自动,MANUAL:手动)
        PRE_PAYMENT_REFUND("预交撤单"),
        AUTO("自动"),
        MANUAL("手动");
        private String desc;

        ABANDONED_TYPE(String desc) {
            this.desc = desc;
        }

        public String desc() {
            return desc;
        }
    }


    public enum SCRATCH_CARD_NO_BUSINESS_CODE implements ConstantType {
        //刮刮卡号码业务类型
        PARTNER("合作伙伴代码"),
        REFERENCE("参考号"),
        ;
        private String desc;

        SCRATCH_CARD_NO_BUSINESS_CODE(String desc) {
            this.desc = desc;
        }

        public String desc() {
            return desc;
        }
    }

    public enum SCRATCH_CARD_STATUS implements ConstantType {
        //刮刮卡号码业务类型
        PENDING_ASSIGN("待分配"),
        PENDING_ACTIVATION("待激活"),
        ACTIVATED("已激活"),
        EXPIRED("失效"),
        RETURNED("退回"),
        LOST("丢失"),
        ;
        private String desc;

        SCRATCH_CARD_STATUS(String desc) {
            this.desc = desc;
        }
        public String desc() {
            return desc;
        }
    }

    public enum SCRATCH_CARD_PAYMENT_TYPE implements ConstantType {
        //刮刮卡支付方式 (预付费：PRE_PAID，后付费：POST_PAID)
        PRE_PAID("预付费"),
        POST_PAID("后付费"),
        ;
        private String desc;

        SCRATCH_CARD_PAYMENT_TYPE(String desc) {
            this.desc = desc;
        }
        public String desc() {
            return desc;
        }
    }

    public enum ABANDONED_REMARK implements ConstantType {
        //废弃类型(AUTO:自动,MANUAL:手动)
        ARTIFICIAL_ABANDONED("人工失效"),
        OVERDUE_PAYMENT("逾期未支付"),
        PRE_PAYMENT_REFUND("预交撤单");
        private String desc;

        ABANDONED_REMARK(String desc) {
            this.desc = desc;
        }

        public String desc() {
            return desc;
        }
    }

    /**
     * 流程类型
     */
    public enum WORK_FLOW_TYPE implements ConstantType {
        NEW_CONTRACT("新契约"),
        NEW_CONTRACT_GROUP("新契约团险");
        private String desc;

        WORK_FLOW_TYPE(String desc) {
            this.desc = desc;
        }

        public String desc() {
            return desc;
        }
    }

    /**
     * 身份认证状态
     */
    public enum CERTIFICATION_STATUS implements ConstantType {
        CERTIFIED("已认证"),
        CERTIFIED_FAILED("认证失败"),
        NOT_CERTIFIED("未认证"),
        UNDER_REVIEW("认证中");

        private String code;
        private String desc;

        private CERTIFICATION_STATUS(String desc) {
            this.code = code;
            this.desc = desc;
        }

        public String code() {
            return code;
        }

        public String desc() {
            return desc;
        }
    }


    /**
     * 身份认证状态
     */
    public enum ID_TYPE implements ConstantType {
        ID("身份证"),
        MILITARY_ID("军官证"),
        OTHER("其他"),
        BIRTH_CERTIFICATE("出生证"),
        HOUSEHOLD_REGISTER("户口本"),
        PASSPORT("护照"),
        HONG_KONG_MACAU_LAISSEZ_PASSER("港澳通行证");

        private String code;
        private String desc;

        private ID_TYPE(String desc) {
            this.code = code;
            this.desc = desc;
        }

        public String code() {
            return code;
        }

        public String desc() {
            return desc;
        }
    }

    public enum AGENT_TYPE implements ConstantType {
        /**
         * 代理人类型
         */
        EMPLOYEE("员工"),
        REGISTERED_USER("注册用户"),
        AGENT("业务员"),
        LIFE_CONSULTANT("寿险顾问");

        private String code;
        private String desc;

        private AGENT_TYPE(String desc) {
            this.code = this.code;
            this.desc = desc;
        }

        public String code() {
            return this.code;
        }

        public String desc() {
            return this.desc;
        }
    }

    public enum MSG_BUSINESS_TYPE implements ConstantType {
        /**
         * 消息类型
         */
        APP_APPLY_INSURE_SUBMIT_CP("app投保申请完成"),
        APPLY_ACCEPT_INPUT_CP("受理录入完成"),
        APPLY_ACCEPT_FOR_AGENT_IN("受理录入中消息"),
        APPLY_ACCEPT_DRAW_CARD_FOR_AGENT_CP("受理录入完成代理人电子贺卡领取通知"),
        APPLY_QUESTION_RETURN_CP("投保单问题件回退"),
        APPLY_IMAGE_UPLOAD_CP("影像件上传完成"),
        APPLY_INPUT_CP("投保录入完成"),
        APPLY_REVIEW_CP("投保复核完成"),
        APPLY_UNDERWRITING_CP("投保单核保完成"),
        APPLY_UNDERWRITING_CUSTOMER_CP("投保单核保完成"),
        APPLY_UNDERWRITING_RF("投保单拒保"),
        APPLY_PAY_CP("投保单支付成功"),
        APPLY_PAY_CP_RECOMMEND("投保单支付成功(推荐人)"),
        APPLY_TRANFER_POLICY_CP("保单出单成功"),
        APPLY_UNDERWRITE_FOR_AGENT_CP("投保出单成功发送业务员"),
        APPLY_UNDERWRITE_DRAW_CARD_FOR_AGENT_CP("承保完成电子贺卡领取通知(业务员)"),
        APPLY_TRANFER_POLICY_APPLICANT_CP("保单出单成功发送投保人"),
        POLICY_UNDERWRITTEN_TO_CUSTOMER("保单承保后发送客户"),
        POLICY_UNDERWRITTEN_TO_CUSTOMER_ONLINE("保单承保后发送客户(网销版)"),
        POLICY_UNDERWRITTEN_TO_CUSTOMER_ONLINE_MULTI_YEAR_PAYMENT("保单承保后发送客户(网销多年期缴费版)"),
        APPLY_TRANFER_POLICY_RECOMMEND_CP("保单出单成功推荐人"),
        APPLY_TRANFER_POLICY_FAIL("保单出单失败"),
        APPLY_PAY_AUDIT_PASS("支付申请审核通过"),
        APPLY_PAY_AUDIT_NOPASS("支付申请审核不通过"),
        APPLY_PLAN_SHARE_TICKLING_CP("计划书分享反馈通知"),
        PRODUCTION_PLAN_NOTICE("制作计划书提醒"),
        TWO_DAYS_NOTICE_BEFORE_THE_INSURANCE_POLICY_IS_VOIDED("投保单作废前两天提醒"),
        TWO_DAYS_NOTICE_BEFORE_THE_INSURANCE_POLICY_IS_VOIDED_RECOMMEND("投保单作废前两天提醒推荐人"),
        REMIND_TO_PAY_BEFORE_THE_PAYMENT_GUIDE_EXPIRES_FOR_AGENT("支付指引失效前提醒业务员"),
        REMIND_TO_PAY_BEFORE_THE_PAYMENT_GUIDE_EXPIRES_FOR_CUSTOMER("支付指引失效前提醒客户"),
        NOTICE_WHEN_THE_INSURANCE_POLICY_IS_INVALID("投保单作废时提醒"),
        NOTICE_WHEN_THE_INSURANCE_POLICY_IS_INVALID_RECOMMEND("投保单作废时提醒推荐人"),
        INFORM_CUSTOMER_OF_ABA_PAYMENT_APPLICATION_FOR_INDIVIDUAL_INSURANCE("ABA支付申请通知客户"),
        INFORM_AGENT_OF_ABA_PAYMENT_APPLICATION_FOR_INDIVIDUAL_INSURANCE("ABA支付申请通知业务员"),
        PASSED_THE_UNDERWRITING_OF_THE_NEW_SINGLE_INSURANCE_POLICY("个险新单核保通过"),
        NOTICE_OF_ADVANCE_PAYMENT_OF_FEES("预交加费通知"),
        NOTICE_OF_ADVANCE_PAYMENT_OF_FEES_CUSTOMER("预交加费通知客户"),
        NOTICE_OF_ADVANCE_PAYMENT_OF_FEES_DISCOUNT_CUSTOMER("预交加费通知客户(折扣版)"),
        NOTICE_AFTER_COMPLETION_OF_PREPAYMENT_OF_PREMIUM("完成预交保费后的暂保通知"),
        NOTICE_AFTER_COMPLETION_OF_PREPAYMENT_OF_PREMIUM_CUSTOMER("完成预交保费后的暂保通知客户"),
        TOTAL_PREMIUMS_PAID_IN_ADVANCE("预交总保费"),
        TOTAL_PREMIUMS_PAID_IN_ADVANCE_CUSTOMER("预交总保费客户"),
        TOTAL_PREMIUMS_PAID_IN_ADVANCE_DISCOUNT_CUSTOMER("预交总保费(折扣版)客户"),
        PASSED_THE_UNDERWRITING_OF_THE_NEW_SINGLE_INSURANCE_POLICY_CUSTOMER("个险新单核保通过发送客户"),
        PASSED_THE_UNDERWRITING_OF_THE_NEW_SINGLE_INSURANCE_POLICY_DISCOUNT_CUSTOMER("个险新单核保通过(折扣版)发送客户"),
        PASSED_THE_UNDERWRITING_OF_THE_NEW_SINGLE_INSURANCE_POLICY_ONLINE_CUSTOMER("个险新单核保通过(网销版)发送客户"),
        SMART_UNDERWRITING_NOT_PASS_TO_CUSTOMERS("智能核保未通过预交保费发送客户"),
        SMART_UNDERWRITING_NOT_PASS_TO_CUSTOMERS_DISCOUNT("智能核保未通过预交保费(折扣版)发送客户"),
        SMART_UNDERWRITING_NOT_PASS_TO_SALEMAN("智能核保未通过预交保费发送业务员"),
        SIGNATURE_CUSTOMER_COMPLETED_TO_AGENT_REMIND_APP("投保人签名完成提醒APP"),
        SIGNATURE_INSURED_COMPLETED_TO_AGENT_REMIND_APP("被保人签名完成提醒APP"),
        SIGNATURE_ALL_COMPLETED_TO_AGENT_REMIND_APP("投被保人签名完成提醒APP"),
        INSURANCE_ESIGNATURE_TO_CUSTOMER("保险电子签名投保人发送客户"),
        INSURANCE_ESIGNATURE_TO_INSURED("保险电子签名被保人发送客户"),
        APPLY_CHANGE_APPROVAL_NOTIFICATION("投保单变更审核通过通知"),
        APPLY_CHANGE_APPROVAL_NOTIFICATION_AGENT("投保单变更审核通过通知业务员"),
        SCRATCH_CARD_ACTIVATED_NOTIFY_PARTNERS("刮刮卡激活后通知合作伙伴"),

        //团险消息
        GROUP_APPLY_REVIEW_REMINDER("团险投保单复核提醒"),
        GROUP_APPLY_UNDERWRITING_REMINDER("团险投保单核保提醒"),
        GROUP_APPLY_INVOICE_PRINT_REMINDER("团单发票打印提醒"),
        GROUP_APPLY_PAYMENT_REMINDER_AGENT("团单支付提醒业务员"),
        GROUP_APPLY_PAYMENT_REMINDER_CUSTOMER("团单支付提醒客户"),
        GROUP_APPLY_REFUSAL_REMINDER_AGENT("团单拒保提醒业务员"),
        GROUP_APPLY_REFUSAL_REMINDER_CUSTOMER("团单拒保提醒客户"),
        GROUP_APPLY_APPROVE_REMINDER_CUSTOMER("团单承保提醒客户"),
        GROUP_APPLY_APPROVE_REMINDER_AGENT("团单承保提醒业务员"),
        GROUP_INSURANCE_NEW_SINGLE_UNDERWRITING_PASSED("团险新单核保通过"),
        GROUP_INSURANCE_NEW_SINGLE_UNDERWRITING_PASSED_CUSTOMER("团险新单核保通过客户"),
        GROUP_INSURANCE_NEW_SINGLE_UNDERWRITING_PASSED_DISCOUNT_CUSTOMER("团险新单核保通过(折扣版)客户"),
        PRE_UNDERWRITING_TO_CUSTOMER("暂予承保申请通过通知客户"),
        PRE_UNDERWRITING_TO_APP("暂予承保申请通过通知APP"),
        PRE_UNDERWRITING_ALL("暂予承保申请通过通知钉钉微信")
        ;
        private String desc;

        MSG_BUSINESS_TYPE(String desc) {
            this.desc = desc;
        }

        public String desc() {
            return desc;
        }
    }

    /**
     * 影像件上传状态
     */
    public enum UPLOAD_STATUS implements ConstantType {
        UPLOAD_STATUS("上传状态"),
        UNDONE("未上传"),
        UPLOADING("上传中"),
        COMPLETE("已上传");

        private String code;
        private String desc;

        private UPLOAD_STATUS(String desc) {
            this.code = code;
            this.desc = desc;
        }

        public String code() {
            return code;
        }

        public String desc() {
            return desc;
        }
    }

    /**
     * 签约状态
     */
    public enum SIGN_STATUS implements ConstantType {
        SIGN_COMPLETE("签约完成");

        private String code;
        private String desc;

        private SIGN_STATUS(String desc) {
            this.code = code;
            this.desc = desc;
        }

        public String code() {
            return code;
        }

        public String desc() {
            return desc;
        }
    }

    /**
     * 资源
     */
    public enum RESOURCE implements ConstantType {

        RESOURCE_APPLY_APPLYLIST("受理列表"),
        RESOURCE_APPLY_IMAGEPARTSLIST("影像上传列表"),
        RESOURCE_APPLY_PENDINGENTRYLIST("投保单录入"),
        RESOURCE_APPLY_UNDERWRITELIST("人工核保"),
        RESOURCE_APPLY_POLICYPRINT("保单打印"),
        RESOURCE_APPLY_REVIEWPOLICYRECEIPT("保单回执复核"),
        AGENT_CONFIRM_TASK("问题件工作流"),
        IMAGE_SCANNING_TASK("影像件上传"),
        RECEIVE_TASK("受理"),
        RETURN_RECEIPT_TASK("回执回销"),
        REVIEW_TASK("复核"),
        ARTIFICIAL_UNDERWRITING_TASK("人工核保"),
        POLICY_PRINT_TASK("保单打印"),
        NEW_ENTRY_TASK("新单录入"),
        CUSTOMER_SIGN_TASK("客户签收"),
        RECEIPT_REVIEW_TASK("回执复核"),
        ;

        private String code;
        private String desc;

        private RESOURCE(String desc) {
            this.code = code;
            this.desc = desc;
        }

        public String code() {
            return code;
        }

        public String desc() {
            return desc;
        }
    }

    /**
     * 消息模板
     */
    public enum MESSAGE_TEMPLATE implements ConstantType {
        APPLY_PAY_SUCCESS("支付成功"),
        APPLY_UNDERWRITING_SUCCESS("核保成功"),
        APPLY_ACCEPT_SUCCESS("承包成功");

        private String code;
        private String desc;

        private MESSAGE_TEMPLATE(String desc) {
            this.code = code;
            this.desc = desc;
        }

        public String code() {
            return code;
        }

        public String desc() {
            return desc;
        }
    }


    /**
     * 消息模板
     */
    public enum MARITAL_STATUS implements ConstantType {
        MARRIED("已婚"),
        UNMARRIED("未婚");

        private String code;
        private String desc;

        private MARITAL_STATUS(String desc) {
            this.code = code;
            this.desc = desc;
        }

        public String code() {
            return code;
        }

        public String desc() {
            return desc;
        }
    }

    /**
     * 积分任务编码
     */

    public enum INTEGRAL_TASK_CODE implements ConstantType {
        /**
         * 积分任务编码
         */
        TASK_OUT_POLICY("出单体验"),
        ;

        private String code;
        private String desc;

        private INTEGRAL_TASK_CODE(String desc) {
            this.code = code;
            this.desc = desc;
        }

        public String code() {
            return code;
        }

        public String desc() {
            return desc;
        }
    }

    /**
     * 缴费方式
     */
    public enum PAYMENT_METHODS implements ConstantType {
        CASH("现金交易"),
        CHEQUE("支票"),
        BANK_TRANSFER("银行转账"),
        BANK_DEDUCT("银行划扣"),
        WING_H5("WING在线支付"),
        WING_OFFLINE("wing线下支付"),
        OVER_PAYMENT_ACCOUNT_DEDUCTION("溢缴账户抵扣"),
        BANK_DIRECT_DEBIT("银行代扣"),
        ABA_PAYMENTS("ABA支付");

        private String code;
        private String desc;

        private PAYMENT_METHODS(String desc) {
            this.code = code;
            this.desc = desc;
        }

        public String code() {
            return code;
        }

        public String desc() {
            return desc;
        }
    }


    public enum EVENT_RECORD_TYPE implements ConstantType {
        /**
         * 事件记录键值对类型
         */
        PLAN_BOOK_NUMBER("计划书"),
        POLICY_BOOK_NUMBER("保单"),
        TOTAL_PREMIUM("保费");

        private String code;
        private String desc;

        private EVENT_RECORD_TYPE(String desc) {
            this.code = code;
            this.desc = desc;
        }

        public String code() {
            return code;
        }

        public String desc() {
            return desc;
        }
    }

    /**
     * 计划书状态
     */
    public enum APPLY_PLAN_STATUS implements ConstantType {

        END("已结束"),
        FINISH("已完成"),
        INITIAL("初始化"),
        ;

        private String code;
        private String desc;

        private APPLY_PLAN_STATUS(String desc) {
            this.code = code;
            this.desc = desc;
        }

        public String code() {
            return code;
        }

        public String desc() {
            return desc;
        }
    }

    /**
     * 投保单操作
     */
    public enum APPLY_OPERATION_CODE implements ConstantType {

        REAPPLYING("正在重新申请投保"),
        REAPPLY_END("重新申请结束"),
        ;

        private String desc;

        APPLY_OPERATION_CODE(String desc) {
            this.desc = desc;
        }

        public String desc() {
            return desc;
        }
    }

    /**
     * 计划书投保单状态
     */
    public enum PLAN_APPLY_STATUS implements ConstantType {

        UNDERWRITING("已承保"),
        UNCOVERED("未承保"),
        ;

        private String desc;

        PLAN_APPLY_STATUS(String desc) {
            this.desc = desc;
        }

        public String desc() {
            return desc;
        }
    }

    /**
     * 缴费业务类型
     */
    public enum CHANNEL_TYPE implements ConstantType {

        AGENT("个代"),
        CUSTOMS("海关"),
        BANK("银渠"),
        ONLINE("网销渠道"),
        ;

        private String code;
        private String desc;

        private CHANNEL_TYPE(String desc) {
            this.code = code;
            this.desc = desc;
        }

        public String code() {
            return code;
        }

        public String desc() {
            return desc;
        }
    }

    /**
     * 产品
     */
    public enum PRODUCT implements ConstantType {
        PRODUCT_3("PRO88000000000001V2018", "DZHRS_ZNCCBX_UPGRADE", "子女成才"),
        PRODUCT_1("PRO88000000000003", "DZHRS_HSFRSYWSHBX", "护身符"),
        PRODUCT_4("PRO88000000000004", "DZHRS_FJZDJBBX", "附加重大疾病"),
        PRODUCT_5("PRO88000000000005", "DZHRS_WYWL", "无忧无虑"),
        PRODUCT_7("PRO88000000000007", "DZHRS_FJYWSHZYJTBX", "附加意外伤害住院津贴"),
        PRODUCT_8("PRO88000000000008", "DZHRS_HJL", "好将来"),
        PRODUCT_9("PRO88000000000009", "DZHRS_FHSC", "福海岁长"),
        PRODUCT_11("PRO8800000000000G11", "DZHRS_FJYWSHZYJTBX_DGRBZYJT_G", "附加团体登革热病住院津贴保险"),
        PRODUCT_12("PRO8800000000000G12", "DZHRS_FJTTYWSHYLBX_G12", "附加团体意外伤害医疗保险条款"),
        PRODUCT_1_PLUS("PRO8800000000000G3", "DZHRS_HSFRSYWSHBX_G", "“护身符”团体保险"),
        PRODUCT_7_PLUS("PRO8800000000000G7", "DZHRS_FJYWSHZYJTBX_G", "附加团体意外伤害住院津贴保险"),
        PRODUCT_13("PRO880000000000013", "DZHRS_QYB", "“GC Enrich Life” Insurance"),
        PRODUCT_14("PRO880000000000014", "DZHRS_FJZDJBBX_UPGRADE", "Critical Illness Benefit Rider Insurance"),
        PRODUCT_15("PRO880000000000015", "DZHRS_FJYWSWHGCBX", "Accidental Death and Disability Insurance"),
        PRODUCT_16A("PRO880000000000016A", "DZHRS_WOP", "Waiver of Premium Rider for Insured"),
        PRODUCT_16B("PRO880000000000016B", "DZHRS_WOP", "Waiver of Premium Rider for Payor"),
        PRODUCT_17("PRO880000000000017", "DZHRS_GC_GROUP_CARE", "GC GROUP CARE"),
        PRODUCT_18("PRO880000000000018", "DZHRS_GROUP_MEDICARE_RIDER", "GROUP MEDICARE RIDER"),
        PRODUCT_19("PRO880000000000019", "DZHRS_GC_SCHOLAR", "GC Scholar"),
        PRODUCT_20("PRO880000000000020", "DZHRS_GC_MULTIPROTECT", "GC Multi Protect"),
        PRODUCT_20A("PRO880000000000020A", "DZHRS_GC_MULTIPROTECT_ONLINE", "GC Multi Protect(Online)"),
        PRODUCT_21("PRO880000000000021", "DZHRS_GC_NEARY_CARE", "GC Neary Care"),
        PRODUCT_22("PRO880000000000022", "DZHRS_JUVENILE_CRITICAL_ILLNESS_RIDER", "Juvenile Critical Illness Rider"),
        PRODUCT_23A("PRO880000000000023A", "DZHRS_TERM_PROTECTION_RIDER_FOR_INSURED", "Term Protection Rider for Insured"),
        PRODUCT_23B("PRO880000000000023B", "DZHRS_TERM_PROTECTION_RIDER_FOR_PAYOR", "Term Protection Rider for Payor"),
        PRODUCT_24("PRO880000000000024", "DZHRS_GC_PUREPROTECT", "GC PureProtect"),
        PRODUCT_26("PRO880000000000026", "DZHRS_GC_GADD", "GROUP ACCIDENTAL DEATH AND DISMEMBERMENT RIDER"),
        PRODUCT_27("PRO880000000000027", "DZHRS_GROUP_MEDICASH_RIDER", "GROUP MEDICASH RIDER"),
        PRODUCT_28("PRO880000000000028", "DZHRS_GCCP", "GC Credit Protect"),
        ;
        private String id;
        private String code;
        private String desc;

        PRODUCT(String id, String code, String desc) {
            this.id = id;
            this.code = code;
            this.desc = desc;
        }

        public String id() {
            return id;
        }

        public String code() {
            return code;
        }

        public String desc() {
            return desc;
        }
    }

    /**
     * 缴费业务类型
     */
    public enum BUSINESS_TYPE implements ConstantType {

        POLICY("保单"),
        APPLY("投保单"),
        PLAN("计划书"),
        ;

        private String code;
        private String desc;

        private BUSINESS_TYPE(String desc) {
            this.code = code;
            this.desc = desc;
        }

        public String code() {
            return code;
        }

        public String desc() {
            return desc;
        }
    }

    /**
     * 优惠活动
     */
    public enum PROMOTION_TYPE implements ConstantType {
        PROMOTION_TYPE("优惠活动"),
        CASH_DEDUCTION("现金抵扣优惠活动"),
        CCC_DISCOUNT_CAMPAIGN("CCC（柬埔寨商会）10%折扣"),
        CUSTOM_DISCOUNT_OFFER("自定义折扣优惠"),
        ;

        private String code;
        private String desc;

        private PROMOTION_TYPE(String desc) {
            this.code = code;
            this.desc = desc;
        }

        public String code() {
            return code;
        }

        public String desc() {
            return desc;
        }
    }

    /**
     * 支付回调类型
     */
    public enum NOTIFY_CONFIG_TYPE implements ConstantType {

        APPLY("个险"),
        RENEWAL("续期"),
        ENDORSE("个险保全"),
        RENEWAL_INSURANCE("续保"),
        AGENT_WITHDRAW("业务员提现"),
        GROUP_ENDORSE("团险保全"),
        POLICY_CLAIM("保单理赔"),
        GROUP_RENEWAL("团险续保"),
        APPLY_REFUND("个险退费"),
        SCRATCH_CARD_PRE_PAID("刮刮卡-预付费"),
        SCRATCH_CARD_REFUND("刮刮卡-退费"),
        ;

        private String code;
        private String desc;

        private NOTIFY_CONFIG_TYPE(String desc) {
            this.code = code;
            this.desc = desc;
        }

        public String code() {
            return code;
        }

        public String desc() {
            return desc;
        }
    }

    public enum APPLICANT_TYPE implements ConstantType {
        /**
         * 投保人类型
         */
        PERSONAL("个人"),
        GROUP("团体"),
        ;

        private String code;
        private String desc;

        private APPLICANT_TYPE(String desc) {
            this.code = code;
            this.desc = desc;
        }

        public String code() {
            return code;
        }

        public String desc() {
            return desc;
        }
    }

    /**
     * 有效标识有效标识(effective:有效，invalid:失效)
     */
    public enum VALID_FLAG implements ConstantType {

        effective("有效"),
        invalid("失效");

        private String code;
        private String desc;

        private VALID_FLAG(String desc) {
            this.code = code;
            this.desc = desc;
        }

        public String code() {
            return code;
        }

        public String desc() {
            return desc;
        }
    }

    /**
     * 是否    YES   NO
     */
    public enum YES_NO implements ConstantType {

        YES("是"),
        NO("否");

        private String code;
        private String desc;

        private YES_NO(String desc) {
            this.code = code;
            this.desc = desc;
        }

        public String code() {
            return code;
        }

        public String desc() {
            return desc;
        }
    }

    /**
     * 特别约定类型
     */
    public enum SPECIAL_CONTRACT_TYPE implements ConstantType {

        INCREASE_OR_DECREASE_FEES("增减费"),
        CONTINUED_INSURANCE_PREMIUMS("续保保费"),
        OTHER("其他");

        private String code;
        private String desc;

        private SPECIAL_CONTRACT_TYPE(String desc) {
            this.code = code;
            this.desc = desc;
        }

        public String code() {
            return code;
        }

        public String desc() {
            return desc;
        }
    }

    public enum SEND_STATUS implements ConstantType {
        SEND_STATUS("下发状态"),
        YES("是"),
        NO("否");

        private String code;
        private String desc;

        private SEND_STATUS(String desc) {
            this.code = code;
            this.desc = desc;
        }

        public String code() {
            return code;
        }

        public String desc() {
            return desc;
        }
    }

    public enum UNDERWRITE_FLAG implements ConstantType {
        NEED_UNDERWRITE("直接承保"),
        NEED_NOT_UNDERWRITE("不直接承保");

        private String code;
        private String desc;

        private UNDERWRITE_FLAG(String desc) {
            this.code = code;
            this.desc = desc;
        }

        public String code() {
            return code;
        }

        public String desc() {
            return desc;
        }
    }

    public enum APPLY_CONFIG_FLAG implements ConstantType {
        UNDERWRITE_FLAG("承保标识"),
        SIGN_TYPE("签单类型"),
        PAYMENT_MODE("付款方式"),
        WING_H5("wing支付"),
        ;

        private String code;
        private String desc;

        private APPLY_CONFIG_FLAG(String desc) {
            this.code = code;
            this.desc = desc;
        }

        public String code() {
            return code;
        }

        public String desc() {
            return desc;
        }
    }


    /**
     * 录入页面数据字典集
     */
    public enum INPUT_DICTIONARIES implements ConstantType {
        PAYMENT_METHODS("缴费方式"),
        ;


        private String code;
        private String desc;

        private INPUT_DICTIONARIES(String desc) {
            this.code = code;
            this.desc = desc;
        }

        public String code() {
            return code;
        }

        public String desc() {
            return desc;
        }
    }

    /**
     * 有效标识有效标识(QUESTION:有效，invalid:失效)
     */
    public enum QUESTION_TYPE implements ConstantType {
        /**
         * 问题类型
         */
        QUESTION("问题"),
        REMARK("备注");

        private String code;
        private String desc;

        private QUESTION_TYPE(String desc) {
            this.code = code;
            this.desc = desc;
        }

        public String code() {
            return code;
        }

        public String desc() {
            return desc;
        }
    }

    /**
     * 账户使用类型
     */
    public enum ACCOUNT_USE_TYPE implements ConstantType {

        PAY("付费"),
        ;

        private String code;
        private String desc;

        private ACCOUNT_USE_TYPE(String desc) {
            this.code = code;
            this.desc = desc;
        }

        public String code() {
            return code;
        }

        public String desc() {
            return desc;
        }
    }


    /**
     * 客户类型
     */
    public enum CUSTOMER_TYPE implements ConstantType {

        APPLICANT("投保人"),
        APPLICANT_DELEGATE("投保人代表"),
        INSURED("被保人"),
        AGENT("业务员"),
        BENEFICIARY("受益人"),
        ACCEPT("受理人"),
        INPUT("录入人");

        private String code;
        private String desc;

        private CUSTOMER_TYPE(String desc) {
            this.code = code;
            this.desc = desc;
        }

        public String code() {
            return code;
        }

        public String desc() {
            return desc;
        }
    }


    /**
     * 是否需要投保单影像件
     */
    public enum IS_IMAGE_FLAG implements ConstantType {

        NEED("需要"),
        NEEDLESS("不需要");

        private String code;
        private String desc;

        private IS_IMAGE_FLAG(String desc) {
            this.code = code;
            this.desc = desc;
        }

        public String code() {
            return code;
        }

        public String desc() {
            return desc;
        }
    }


    /**
     * 投保单来源
     */
    public enum APPLY_SOURCE implements ConstantType {

        APP("APP"),
        ACCEPT_INPUT("受理录入"),
        CIQ_INPUT("海关录入"),
        AGENT_INPUT("业务员录入");

        private String code;
        private String desc;

        private APPLY_SOURCE(String desc) {
            this.code = code;
            this.desc = desc;
        }

        public String code() {
            return code;
        }

        public String desc() {
            return desc;
        }
    }

    /**
     * 签单类型
     */
    public enum SIGN_TYPE implements ConstantType {

        PREMIUM_ISSUE("见费出单"),
        ISSUE_PREMIUM("非见费出单"),
        ;

        private String code;
        private String desc;

        private SIGN_TYPE(String desc) {
            this.code = code;
            this.desc = desc;
        }

        public String code() {
            return code;
        }

        public String desc() {
            return desc;
        }
    }

    /**
     * 强制核保标志
     */
    public enum FORCE_MANUAL_UNDERWRITE_FLAG implements ConstantType {

        FORCE("强制人工核保"),
        UNFORCE("不强制人工核保");

        private String code;
        private String desc;

        private FORCE_MANUAL_UNDERWRITE_FLAG(String desc) {
            this.code = code;
            this.desc = desc;
        }

        public String code() {
            return code;
        }

        public String desc() {
            return desc;
        }
    }


    /**
     * 产品字段是否需要录入标识
     */
    public enum PRODUCT_FIELD_INPUT implements ConstantType {

        INPUT("录入"),
        DEFAULT("默认(采用lable展示)"),
        SELECT("选择"),
        NOT("不录入");

        private String code;
        private String desc;

        private PRODUCT_FIELD_INPUT(String desc) {
            this.code = code;
            this.desc = desc;
        }

        public String code() {
            return code;
        }

        public String desc() {
            return desc;
        }
    }

    public enum PRODUCT_PRIMARY_FLAG implements ConstantType {

        MAIN("主险"),
        ADDITIONAL("附加险");

        private String code;
        private String desc;

        private PRODUCT_PRIMARY_FLAG(String desc) {
            this.code = code;
            this.desc = desc;
        }

        public String code() {
            return code;
        }

        public String desc() {
            return desc;
        }
    }

    /**
     * 产品
     */
    public enum DUTY implements ConstantType {
        PRO8800000000000G12_DUTY_1("门诊"),
        PRO8800000000000G12_DUTY_2("住院"),
        PRO8800000000000G12_DUTY_3("危重症转院"),
        ;
        private String desc;

        DUTY(String desc) {
            this.desc = desc;
        }

        public String desc() {
            return desc;
        }
    }


    /**
     * 产品组件类型
     */
    public enum PRODUCT_COMPONENT_TYPE implements ConstantType {

        SELECT("下拉框"),
        TEXT("文本框");

        private String code;
        private String desc;

        private PRODUCT_COMPONENT_TYPE(String desc) {
            this.code = code;
            this.desc = desc;
        }

        public String code() {
            return code;
        }

        public String desc() {
            return desc;
        }
    }


    /**
     * 问题件状态
     */
    public enum ERROR_QUESTION_STATUS implements ConstantType {
        INIT("初始化"),
        WAIT_SEND("待发送"),
        COMPLETE_ANSWER("已回答"),
        WAIT_ANSWER("待回答");

        private String code;
        private String desc;

        private ERROR_QUESTION_STATUS(String desc) {
            this.code = code;
            this.desc = desc;
        }

        public String code() {
            return code;
        }

        public String desc() {
            return desc;
        }
    }

    public enum OPTION_STATUS implements ConstantType {
        /**
         * 问题件操作状态
         */
        INIT("初始化"),
        COMPLETE("已经提交");

        private String code;
        private String desc;

        private OPTION_STATUS(String desc) {
            this.code = code;
            this.desc = desc;
        }

        public String code() {
            return code;
        }

        public String desc() {
            return desc;
        }
    }


    public enum DECISION_TYPE implements ConstantType {
        /**
         * 核保决定
         */
        STANDARD("标准体", "1"),
        SUBSTANDARD("次标准体", "2"),
        DELAY("延期", "3"),
        DECLINATURE("拒保", "4"),
        AMOUNT_DECISION("保额决定", null),
        EXCLUSION_DECISION("除外决定", null),
        ADD_PREMIUM_DECISION("加费决定", null),
        ;

        private String code;
        private String desc;
        private String id;

        private DECISION_TYPE(String desc, String id) {
            this.code = code;
            this.desc = desc;
            this.id = id;
        }

        public String code() {
            return code;
        }

        public String desc() {
            return desc;
        }

        public String id() {
            return id;
        }
    }


    /**
     * 缴费终止期间单位
     */
    public enum PRODUCT_PREMIUM_PERIOD_UNIT implements ConstantType {
        PREMIUM_PERIOD_UNIT("缴费期限类型"),
        YEAR("年"),
        LIFELONG("终身"),
        MONTH("月"),
        DAY("日"),
        SINGLE("一次性缴费"),
        AGE("岁");

        private String code;
        private String desc;

        private PRODUCT_PREMIUM_PERIOD_UNIT(String desc) {
            this.code = code;
            this.desc = desc;
        }

        public String code() {
            return code;
        }

        public String desc() {
            return desc;
        }
    }


    /**
     * 与被保人关系
     */
    public enum RELATIONSHIP_WITH_THE_INSURED implements ConstantType {
        ONESELF("Self", "本人"),
        SPOUSE("Spouse", "配偶"),
        PARENTS("Parents", "父母"),
        CHILD("Children", "子女"),
        BROTHERS("Siblings", "兄弟姐妹"),
        OTHER("Other", "其他"),

        SISTERS("Sisters", "姐妹"),
        BORROW("Borrow", "借贷关系"),
        ;

        private String code;
        private String desc;

        private RELATIONSHIP_WITH_THE_INSURED(String code, String desc) {
            this.code = code;
            this.desc = desc;
        }

        public String code() {
            return code;
        }

        public String desc() {
            return desc;
        }
    }

    /**
     * 与投保人关系
     */
    public enum RELATIONSHIP_WITH_THE_APPLICANT implements ConstantType {
        ONESELF("本人"),
        SPOUSE("配偶"),
        CHILD("子女"),
        PARENTS("父母"),
        GRANDPARENT("祖父母"),
        GRANDCHILD("孙子孙女"),
        BROTHERS("兄弟姐妹"),
        SISTERS("姐妹"),
        UNCLE("叔姨"),
        NEPHEW("侄儿侄女"),
        OTHER("其他"),;

        private String code;
        private String desc;

        private RELATIONSHIP_WITH_THE_APPLICANT(String desc) {
            this.code = code;
            this.desc = desc;
        }

        public String code() {
            return code;
        }

        public String desc() {
            return desc;
        }
    }


    /**
     * 给付频次
     */
    public enum PRODUCT_PAY_FREQUENCY implements ConstantType {
        YEAR("年付"),
        SEMIANNUAL("半年缴费"),
        SEASON("季度缴费"),
        MONTH("月付"),
        SINGLE("一次性付清");

        private String code;
        private String desc;

        private PRODUCT_PAY_FREQUENCY(String desc) {
            this.code = code;
            this.desc = desc;
        }

        public String code() {
            return code;
        }

        public String desc() {
            return desc;
        }
    }

    /**
     * 缴费周期系数
     */
    public enum PREMIUM_FREQUENCY_CONVERSION_FACTOR implements ConstantType {
        YEAR(1.0),
        SEMIANNUAL(0.52),
        SEASON(0.27),
        MONTH(0.09),
        SINGLE(1.0),
        ;

        private String code;
        private Double value;


        private PREMIUM_FREQUENCY_CONVERSION_FACTOR(Double value) {
            this.value = value;
        }

        public String code() {
            return code;
        }

        public Double value() {
            return value;
        }
    }

    /**
     * 缴费周期年度系数
     */
    public enum PREMIUM_FREQUENCY_YEAR_FACTOR implements ConstantType {
        YEAR(1),
        SEMIANNUAL(2),
        SEASON(4),
        MONTH(12),
        SINGLE(1),
        ;

        private String code;
        private int value;


        private PREMIUM_FREQUENCY_YEAR_FACTOR(int value) {
            this.value = value;
        }

        public String code() {
            return code;
        }

        public int value() {
            return value;
        }
    }

    /**
     * 责任给付状态
     */
    public enum COVERAGE_TITLE implements ConstantType {
        COVERAGE_NAME("险种名称"),
        COVERAGE_TYPE("险种类型"),
        CURRENCY("币种"),
        PREMIUM_FREQUENCY("缴费周期"),
        PREMIUM_PERIOD("缴费期限"),
        COVERAGE_PERIOD("保险期间"),
        MULT("份数"),
        STANDARD_PREMIUM("期缴标准保费"),
        EXTRA_PREMIUM("期缴加费保费"),
        PREMIUM("期缴保险费"),
        PRODUCT_LEVEL("档次"),
        COVERAGE_PLAN("选项"),
        COVERAGE_CODE("险种编号"),
        FINANCING_METHOD("交费方式"),
        ACC_SI_MULTIPLE("额外意外保额倍数"),
        AMOUNT("保险金额");

        private String code;
        private String desc;

        private COVERAGE_TITLE(String desc) {
            this.code = code;
            this.desc = desc;
        }

        public String code() {
            return code;
        }

        public String desc() {
            return desc;
        }
    }

    /**
     * 责任给付状态
     */
    public enum DUTY_STATUS implements ConstantType {
        UNSTART("未开始"),
        PROCESS("进行中"),
        FINISH("已结束");

        private String code;
        private String desc;

        private DUTY_STATUS(String desc) {
            this.code = code;
            this.desc = desc;
        }

        public String code() {
            return code;
        }

        public String desc() {
            return desc;
        }
    }


    /**
     * 投保单状态
     */
    public enum APPLY_STATUS_FLAG implements ConstantType {

        APPLY_STATUS("投保单状态"),
        NEW_APPLY_STATUS("投保单状态"),
        APPLY_STATUS_GROUP("投保单状态"),
        APPLY("投保单"),

        /***最新整理的投保单状态***/
        APPLY_STATUS_INITIAL("初始化"),
        APPLY_STATUS_INITIAL_COMPLETE("初始化完成"),
        APPLY_STATUS_ACCEPT("受理"),
        APPLY_STATUS_ACCEPT_COMPLETE("受理完成"),
        APPLY_STATUS_ACCEPT_PROBLEM("受理问题件"),
        APPLY_STATUS_IMAGE_UPLOAD("影像上传"),
        APPLY_STATUS_IMAGE_UPLOAD_COMPLETE("影像上传完成"),
        APPLY_STATUS_IMAGE_UPLOAD_PROBLEM("影像上传问题件"),
        APPLY_STATUS_INPUT("录入"),
        APPLY_STATUS_INPUT_COMPLETE("录入完成"),
        APPLY_STATUS_INPUT_PROBLEM("录入问题件"),
        APPLY_STATUS_INPUT_REVIEW("录入复核"),
        APPLY_STATUS_INPUT_REVIEW_COMPLETE("录入复核完成"),
        APPLY_STATUS_INPUT_REVIEW_PROBLEM("录入复核问题件"),
        APPLY_STATUS_UNDERWRITE_AUTO("自动核保"),
        APPLY_STATUS_UNDERWRITE_ARTIFICIAL("人工核保"),
        APPLY_STATUS_UNDERWRITE_NOTE("照会"),
        APPLY_STATUS_UNDERWRITE_WAIT_NOTE_WRITE_OFF("照会待回销"),
        APPLY_STATUS_UNDERWRITE_NOTE_WRITE_OFF_COMPLETE("回销完成"),
        APPLY_STATUS_UNDERWRITE_PASS("核保通过"),
        APPLY_STATUS_APPROVE_SUCCESS("承保成功"),
        APPLY_STATUS_APPROVE_FAILED("承保失败"),
        APPLY_STATUS_PAYMENT_COMPLETE("支付完成"),
        APPLY_STATUS_REFUND("拒保"),
        APPLY_STATUS_DELAY("延期"),
        APPLY_STATUS_REVOKE("撤单"),
        APPLY_STATUS_ABANDONED("废弃"),
        APPLY_STATUS_PAID_PENDING_ON_UW("已交费待核保"),
        APPLY_STATUS_TEMPORARY_COVER_EFFECTIVE("暂保生效"),
        APPROVED_HC("投保单暂予承保"),
        POLICY_EFFECTIVE_HC("保单暂予承保"),
        ;
        private String code;
        private String desc;

        private APPLY_STATUS_FLAG(String desc) {
            this.code = code;
            this.desc = desc;
        }

        public String code() {
            return code;
        }

        public String desc() {
            return desc;
        }
    }



    /**
     * 流程中有效的投保单状态
     */
    public enum APPLY_STATUS_PROCESS implements ConstantType {
        APPLY_STATUS_INITIAL("初始化"),
        APPLY_STATUS_INITIAL_COMPLETE("初始化完成"),
        APPLY_STATUS_INPUT_REVIEW("录入复核"),
        APPLY_STATUS_INPUT_REVIEW_COMPLETE("录入复核完成"),
        APPLY_STATUS_INPUT_REVIEW_PROBLEM("录入复核问题件"),
        APPLY_STATUS_UNDERWRITE_AUTO("自动核保"),
        APPLY_STATUS_UNDERWRITE_ARTIFICIAL("人工核保"),
        APPLY_STATUS_UNDERWRITE_PASS("核保通过"),
        APPLY_STATUS_APPROVE_SUCCESS("承保成功"),
        APPLY_STATUS_APPROVE_FAILED("承保失败"),
        APPLY_STATUS_PAID_PENDING_ON_UW("已交费待核保"),
        APPLY_STATUS_TEMPORARY_COVER_EFFECTIVE("暂保生效"),
        APPROVED_HC("投保单暂予承保"),
        POLICY_EFFECTIVE_HC("保单暂予承保"),
        ;
        private String code;
        private String desc;

        private APPLY_STATUS_PROCESS(String desc) {
            this.code = code;
            this.desc = desc;
        }

        public String code() {
            return code;
        }

        public String desc() {
            return desc;
        }
    }

    /**
     * 投保附件
     */
    public enum CERTIFY_ATTACHMENT_TYPE implements ConstantType {
        APPLY_ATTACHMENT_TYPE("投保单附件"),
        APPLY_PAYMENT_INSTRUMENT("投保单承保支付凭证"),
        CERTIFY_ATTACHMENT_APPLY_APPLICANT_IDTYPE("投保人证件类型"),
        GROUP_APPLY_CONTRACT("团体保险投保单影像"),
        GROUP_APPLY_CONTRACT_INSURED_INVENTORY("团体保险被保人清单"),
        RECEIPT_CERTIFY("收款凭证影像"),
        RECEIPT_INSTRUMENT("付费凭证"),
        CUSTOMER_INSURED_PHOTO("客户合照影像"),
        CUSTOMER_INSURED_VIDEO("客户合照视频"),
        APPLY_PLAN_OTHER("其他附件"),
        PLAN_BOOK("计划书附件"),
        APPLY_BOOK("投保单附件"),
        APPLY_PLAN_SIGNATURE_AGENT("销售顾问电子签名"),
        APPLY_PLAN_SIGNATURE_APPLICANT("投保人电子签名"),
        APPLY_PLAN_SIGNATURE_INSURED("被保人电子签名"),
        APPLY_PLAN_SIGNATURE_APPLICANT_VIDEO("投保人电子签名授权影像"),
        APPLY_PLAN_SIGNATURE_INSURED_VIDEO("被保人电子签名授权影像"),
        APPLY_PLAN_SIGNATURE_HANDWRITING_APPLICANT("投保人手写电子签名"),
        APPLY_PLAN_SIGNATURE_HANDWRITING_INSURED("被保人手写电子签名"),
        PRE_UNDERWRITING_ATTACHMENT_TYPE("暂予承保申请书影像"),
        APPLY_PLAN_SIGNATURE_TOGETHER_VIDEO("投被保人合照影像"),
        APPLY_PLAN_SIGNATURE_TOGETHER_PHOTO("投被保人合照照片"),
        CERTIFY_ATTACHMENT_APPLY_APPLICANT_PASSPORT("投保人护照个人资料页"),
        CERTIFY_ATTACHMENT_APPLY_APPLICANT_PASSPORT_VISA("投保人护照签证页"),
        CERTIFY_ATTACHMENT_APPLY_INSURED_PASSPORT("被保人护照个人资料页"),
        CERTIFY_ATTACHMENT_APPLY_INSURED_PASSPORT_VISA("被保人护照签证页"),
        CERTIFY_ATTACHMENT_APPLY_BENEFICIARY_PASSPORT("受益人护照个人资料页"),
        CERTIFY_ATTACHMENT_APPLY_BENEFICIARY_PASSPORT_VISA("受益人护照签证页"),
        /**
         * 折扣申请表
         */
        DISCOUNT_REQUEST_FORM("折扣申请表"),
        HEALTH_DISCLOSURE("健康告知书"),
        PAYMENT_MODE_MODIFICATION_REQUEST_FORM("缴费周期变更申请书"),

        REFERRAL_ACTIVITY_BANK("推荐活动银行卡"),
        REFERRAL_ACTIVITY_ID("推荐活动身份证件"),
        ;

        private String code;
        private String desc;

        private CERTIFY_ATTACHMENT_TYPE(String desc) {
            this.code = code;
            this.desc = desc;
        }

        public String code() {
            return code;
        }

        public String desc() {
            return desc;
        }
    }

    public enum APPLY_TYPE implements ConstantType {
        /**
         * 投保单类型
         */
        LIFE_INSURANCE_GROUP("新契约团险"),
        LIFE_INSURANCE_PERSONAL("新契约个险"),
        ;

        private String code;
        private String desc;

        private APPLY_TYPE(String desc) {
            this.code = code;
            this.desc = desc;
        }

        public String code() {
            return code;
        }

        public String desc() {
            return desc;
        }
    }

    /**
     * 核保决定类型
     */
    public enum UNDERWRITE_TYPE implements ConstantType {
        UNDERWRITE_PAY("承保支付"),
        UNDERWRITE_PASS("承保通过"),
        UNDERWRITE_REJECT("承保拒绝"),
        ;

        private String code;
        private String desc;

        private UNDERWRITE_TYPE(String desc) {
            this.code = code;
            this.desc = desc;
        }

        public String code() {
            return code;
        }

        public String desc() {
            return desc;
        }
    }

    public enum ADD_START_TYPE implements ConstantType {
        /**
         * 加费开始时间类型(date:指定时间，immediately：立刻)
         */
        date("指定时间"),
        immediately("立刻"),
        ;

        private String code;
        private String desc;

        private ADD_START_TYPE(String desc) {
            this.code = code;
            this.desc = desc;
        }

        public String code() {
            return code;
        }

        public String desc() {
            return desc;
        }
    }

    /**
     * 结算状态
     * 结算状态(RECEIVABLE:应收,CHECK:已对帐,ACTUAL:实收)
     */
    public enum SETTLEMENT_STATUS implements ConstantType {

        RECEIVABLE("应收"),
        CHECK("已对帐"),
        ACTUAL("实收"),
        DEDUCTION("抵扣");
        private String code;
        private String desc;

        private SETTLEMENT_STATUS(String desc) {
            this.code = code;
            this.desc = desc;
        }

        public String code() {
            return code;
        }

        public String desc() {
            return desc;
        }
    }

    /**
     * 打印PDF类型
     */
    public enum PDF_TYPE implements ConstantType {

        PLAN("计划书pdf"),

        APPLY("投保单pdf"),

        POLICY("保单pdf"),
        ;

        private String desc;

        PDF_TYPE(String desc) {
            this.desc = desc;
        }

        public String desc() {
            return desc;
        }
    }


    /**
     * 附件类型
     */
    public enum ATTACHMENT_TYPE_FLAG implements ConstantType {
        POLICY_BOOK("保单"),
        POLICY_TERMS_BOOK("保单条款"),
        APPLY_BOOK("投保单"),
        APPLICANT_HEALTH_BOOK("投保人健康告知书"),
        INSURED_HEALTH_BOOK("被保人健康告知书"),
        PLAN_BOOK("计划书"),
        ACKNOWLEDGMENT_LETTER_BOOK("签收回执"),
        APPLY_RECEIPT_BOOK("收据"),
        CUSTOMER_SERVICE_INSTRUCTION_BOOK("客户服务指南"),
        GROUP_INVOICE("团险发票打印"),
        TEMPORARY_COVER("暂保单"),

        ;

        private String desc;

        ATTACHMENT_TYPE_FLAG(String desc) {
            this.desc = desc;
        }

        public String desc() {
            return desc;
        }
    }

    /**
     * 职业
     */
    public enum CAREER implements ConstantType {
        CAREER("职业"),
        ;

        private String desc;

        CAREER(String desc) {
            this.desc = desc;
        }

        public String desc() {
            return desc;
        }
    }

    public enum APPLY_ATTACHMENT_TYPE implements ConstantType {
        /**
         * 投保单附件
         */
        APPLY_TIPS("人身保险产品投保提示书"),
        CERTIFY_ATTACHMENT_APPLY_APPLICANT_BANKCARD_FRONT("投保人银行卡正面"),
        POLICY_APPLICATION_CONFIRMATION("电子保单申请确认书"),
        APPLY_PAYMENT_INSTRUMENT("承保支付凭证"),
        RISK_TIPS("人身保险产品风险提示书"),
        APPLICANT_TRANSFER_AUTHORIZATION_DECLARATION("投保人转账授权声明"),
        APPLICANT_INSURED_DECLARATION("投被保人声明"),
        ;

        private String desc;

        APPLY_ATTACHMENT_TYPE(String desc) {
            this.desc = desc;
        }

        public String desc() {
            return desc;
        }
    }


    public enum APPLY_APPLICANT_TYPE implements ConstantType {
        /**
         * 投保单附件
         */
        PERSONAL("个人"),
        GROUP("团体"),
        ;

        private String desc;

        APPLY_APPLICANT_TYPE(String desc) {
            this.desc = desc;
        }

        public String desc() {
            return desc;
        }
    }

    public enum WORKFLOW_ITEM_STATUS implements ConstantType {
        /**
         * 工作流状态
         */
        PROCESSING("处理中"),
        NEW_TASK("新任务"),
        GO_BACK("问题件"),
        ;

        private String desc;

        WORKFLOW_ITEM_STATUS(String desc) {
            this.desc = desc;
        }

        public String desc() {
            return desc;
        }
    }

    /**
     * 支付状态
     */
    public enum PAYMENT_STATUS implements ConstantType {

        PAYMENT_FAILED("支付失败"),
        PAYMENT_FINISHED("支付完成"),
        PAYMENT_WAITTING("等待支付"),
        PAYMENT_INITIAL("初始化"),
        PAYMENT_TIMEOUT("支付超时"),
        PAYMENT_SUCCESS("支付成功"),
        PAYMENT_INVALID("支付作废"),
        PENDING_ON_PAYMENT("支付中"),
        PENDING_ON_CONFIRMATION("已支付待确认");

        private String desc;

        PAYMENT_STATUS(String desc) {
            this.desc = desc;
        }

        public String desc() {
            return desc;
        }
    }

    /**
     * 账户类型
     */
    public enum ACCOUNT_TYPE implements ConstantType {

        CASH_ACCOUNT("现金账户"),
        INTEGRAL_ACCOUNT("积分账户"),
        OVER_PAYMENT_ACCOUNT("溢缴账户"),
        ;

        private String desc;

        ACCOUNT_TYPE(String desc) {
            this.desc = desc;
        }

        public String desc() {
            return desc;
        }
    }

    public enum SMS_TYPE implements ConstantType {
        /**
         * 短信类型
         */
        APPLY("客户投保"),
        AUTHENTICATION("身份验证"),
        LOGIN("登录"),
        REGISTER("注册"),
        ;

        private String desc;

        SMS_TYPE(String desc) {
            this.desc = desc;
        }

        public String desc() {
            return desc;
        }
    }

    /**
     * 支付类型/收付费类型
     */
    public enum PAYMENT_TYPE implements ConstantType {

        DEDUCTION("抵扣"),
        ACTUAL("实际支付"),
        PAYMENT("收费"),
        RECEIPT("付费"),
        SUSPENSE_PREMIUM("暂收"),
        SUSPENSE_PREMIUM_REFUND("暂收退费");

        private String desc;

        PAYMENT_TYPE(String desc) {
            this.desc = desc;
        }

        public String desc() {
            return desc;
        }
    }

    /**
     * 缴费类型(PREPAID_PREMIUM:预交保费,NORMAL_PAYMENT:正常交费)
     */
    public enum CHARGE_TYPE implements ConstantType {

        PREPAID_PREMIUM("预交保费"),
        NORMAL_PAYMENT("正常交费");

        private String desc;

        CHARGE_TYPE(String desc) {
            this.desc = desc;
        }

        public String desc() {
            return desc;
        }
    }

    /**
     * 退费类型(FULL_REFUND:全额退费，PARTIAL_REFUND：部分退费)
     */
    public enum REFUND_TYPE implements ConstantType {

        FULL_REFUND("全额退费"),
        PARTIAL_REFUND("部分退费");

        private String desc;

        REFUND_TYPE(String desc) {
            this.desc = desc;
        }

        public String desc() {
            return desc;
        }
    }

    /**
     * excel 导入导出模版
     */
    public enum IMPORT_EXPORT_TEMPLATE implements ConstantType {
        GROUP_INSURED_TEMPLATE("PRO8800000000000G3", "#1+被保人模板"),
        GROUP_INSURED_TEMPLATE_V2021("PRO880000000000017","#17#18团险被保人清单模版"),
        GROUP_INSURED_TEMPLATE_PRO29("PRO880000000000029","#29团险被保人清单模版"),
        ;
        private String code;
        private String productId;
        private String desc;

        private IMPORT_EXPORT_TEMPLATE(String productId, String desc) {
            this.code = code;
            this.productId = productId;
            this.desc = desc;
        }

        public String code() {
            return code;
        }

        public String productId() {
            return productId;
        }

        public String desc() {
            return desc;
        }
    }

    /**
     * excel 导入导出模版
     */
    public enum PRODUCT_LEVEL_29 implements ConstantType {
        PRE_SCHOOL("Pre-School", "幼儿园"),
        PRIMARY_SCHOOL("Primary", "小学"),
        SECONDARY_SCHOOL("Secondary/High","中学"),
        UNIVERSITY("College/University","大学"),
        ;
        private String code;
        private String value;
        private String desc;

        private PRODUCT_LEVEL_29(String value, String desc) {
            this.code = code;
            this.value = value;
            this.desc = desc;
        }

        public String code() {
            return code;
        }

        public String value() {
            return value;
        }

        public String desc() {
            return desc;
        }
    }

    public enum SALES_PLAN implements ConstantType {
        COMPULSORY("Compulsory", "强制"),
        NON_COMPULSORY("Non-compulsory","非强制"),
        ;
        private String code;
        private String value;
        private String desc;

        private SALES_PLAN(String value, String desc) {
            this.code = code;
            this.value = value;
            this.desc = desc;
        }

        public String code() {
            return code;
        }

        public String value() {
            return value;
        }

        public String desc() {
            return desc;
        }
    }

    public enum NATIONALITY implements ConstantType {
        CAMBODIA("Cambodian", "柬埔寨"),
        CHINA("Chinese", "中国"),
        VIETNAM("Vietnamese", "越南"),
        LAO_PEOPLE_DEMOCRATIC_REPUBLIC("Laotian", "老挝"),
        THAILAND("Thailand", "泰国"),
        JAPAN("Japanese", "日本"),
        SOUTH_KOREA("South Korean", "韩国"),
        SINGAPORE("Singaporean", "新加坡"),
        PHILIPPINES("Philippines", "菲律宾"),
        MALAYSIA("Malaysian", "马来西亚"),
        USA("American", "美国"),
        FRANCE("French", "法国"),
        GERMANY("German", "德国"),
        ENGLAND("English", "英国"),
        AUSTRALIA("Australian", "澳大利亚"),
        INDIA("Indian", "印度"),
        INDONESIA("Indonesians", "印度尼西亚"),
        OTHER("Other", "其他"),
        ;
        private String code;
        private String value;
        private String desc;

        private NATIONALITY(String value, String desc) {
            this.code = code;
            this.value = value;
            this.desc = desc;
        }

        public String code() {
            return code;
        }

        public String value() {
            return value;
        }

        public String desc() {
            return desc;
        }
    }

    public enum SCHOOL_TYPE implements ConstantType {
        PRE_SCHOOL("幼儿园"),
        PRIMARY("小学"),
        SECONDARY("初中/高中"),
        UNIVERSITY("大学"),
        ;

        private String code;
        private String desc;


        SCHOOL_TYPE(String desc) {
            this.desc = desc;
        }

        public String desc() {
            return desc;
        }
    }




    /**
     * 基础因子编码
     */
    public enum BASE_FACTOR_CONFIG_CODE implements ConstantType {
        APPLY_WECHAT_FACEBOOK_VERIFICATION("新契约微信FACEBOOK校验"),
        APPLY_INPUT_REQUIRED_FIELD_VERIFICATION("新契约必填字段是否需要校验"),
        APPLY_HEALTH_NOTICE_BOOK_VERIFICATION("新契约健康告知书是否需要校验"),
        APPLY_AGENT_SELF_PRESERVATION_VERIFICATION("代理人自保件校验"),
        APPLY_ACCEPT_INPUT("新契约受理录入信息"),

        ;
        private String code;
        private String desc;

        private BASE_FACTOR_CONFIG_CODE(String desc) {
            this.code = code;
            this.desc = desc;
        }

        public String code() {
            return code;
        }

        public String desc() {
            return desc;
        }
    }


    /*
     *  * 备注操作类型
     */
    public enum OPERATIONAL_RESULT implements ConstantType {
        OPERATIONAL_RESULT("审核结论类型"),
        SUBMITTED("已提交"),
        NO_PASS("未通过"),
        ;

        private String code;
        private String desc;


        OPERATIONAL_RESULT(String desc) {
            this.desc = desc;
        }

        public String desc() {
            return desc;
        }
    }


    public enum EBAO_OPERATION implements ConstantType {

        issue("出单"),
        quote("投保单创建或数据变更"),
        advice("建议书创建或数据变更"),
        ;

        private String code;
        private String desc;

        private EBAO_OPERATION(String desc) {
            this.code = code;
            this.desc = desc;
        }

        public String code() {
            return code;
        }

        public String desc() {
            return desc;
        }
    }


    /**
     * 交费期间
     */
    public enum EBAO_INSTALLMENT implements ConstantType {

        Y1("趸交"),
        Y2("2年交"),
        Y3("3年交"),
        Y5("5年交"),
        Y10("10年交"),
        Y15("15年交"),
        Y20("20年交"),
        Y25("25年交"),
        Y30("30年交"),
        Y35("35年交"),
        Y40("40年交"),
        A18("交到18岁"),
        A20("交到20岁"),
        A30("交到30岁"),
        A40("交到40岁"),
        A45("交到45岁"),
        A50("交到50岁"),
        A55("交到55岁"),
        A60("交到60岁"),
        A65("交到65岁"),
        A70("交到70岁"),
        WL("交终身");

        private String code;
        private String desc;

        private EBAO_INSTALLMENT(String desc) {
            this.code = code;
            this.desc = desc;
        }

        public String code() {
            return code;
        }

        public String desc() {
            return desc;
        }
    }


    /**
     * 保障期间
     */
    public enum EBAO_COVERAGEPERIOD implements ConstantType {

        Y1("保1年"),
        Y2("保2年"),
        Y3("保3年"),
        Y5("保5年"),
        Y10("保10年"),
        Y15("保15年"),
        Y20("保20年"),
        Y25("保25年"),
        Y30("保30年"),
        Y35("保35年"),
        Y40("保40年"),
        A18("保到18岁"),
        A20("保到20岁"),
        A30("保到30岁"),
        A40("保到40岁"),
        A45("保到45岁"),
        A50("保到50岁"),
        A55("保到55岁"),
        A60("保到60岁"),
        A65("保到65岁"),
        A70("保到70岁"),
        M1("保1月"),
        M2("保2月"),
        D1("保1天"),
        D2("保2天"),
        WL("保终身"),
        ;

        private String code;
        private String desc;

        private EBAO_COVERAGEPERIOD(String desc) {
            this.code = code;
            this.desc = desc;
        }

        public String code() {
            return code;
        }

        public String desc() {
            return desc;
        }
    }


    /**
     * 年金/生存金/满期金领取期间
     */
    public enum EBAO_PAYPERIOD implements ConstantType {

        IMD("立即领取"),
        Y5("5年后领取"),
        Y10("10年后领取"),
        Y15("15年后领取"),
        Y20("20年后领取"),
        Y25("25年后领取"),
        Y30("30年后领取"),
        Y35("35年后领取"),
        Y40("40年后领取"),
        AA18("18岁保单年度日开始领取"),
        AA20("20岁保单年度日开始领取"),
        AA30("30岁保单年度日开始领取"),
        AA40("40岁保单年度日开始领取"),
        AA45("45岁保单年度日开始领取"),
        AA50("50岁保单年度日开始领取"),
        AA55("55岁保单年度日开始领取"),
        AA60("60岁保单年度日开始领取"),
        AA65("65岁保单年度日开始领取"),
        AA70("70岁保单年度日开始领取"),
        AB18("18岁生日开始领取"),
        AB20("20岁生日开始领取"),
        AB30("30岁生日开始领取"),
        AB40("40岁生日开始领取"),
        AB45("45岁生日开始领取"),
        AB50("50岁生日开始领取"),
        AB55("55岁生日开始领取"),
        AB60("60岁生日开始领取"),
        AB65("65岁生日开始领取"),
        AB70("70岁生日开始领取");

        private String code;
        private String desc;

        private EBAO_PAYPERIOD(String desc) {
            this.code = code;
            this.desc = desc;
        }

        public String code() {
            return code;
        }

        public String desc() {
            return desc;
        }
    }

    /**
     * 事件业务类型
     */
    public enum EVENT_BUSINESS_TYPE implements ConstantType {

        HAPPY_NEWS("HAPPY_NEWS", "喜报"),
        INCREASE("INCREASE", "增员"),
        PLAN("PLAN", "计划书"),
        INSURE("INSURE", "交单"),
        ;
        private String code;
        private String desc;

        EVENT_BUSINESS_TYPE(String code, String desc) {
            this.code = code;
            this.desc = desc;
        }

        public String code() {
            return code;
        }

        public String desc() {
            return desc;
        }
    }

    public enum BENEFICIARY_NO implements ConstantType {
        /**
         * 受益顺位
         */
        ORDER_ONE("ORDER_ONE", "第一", "First Beneficiary"),
        ORDER_TWO("ORDER_TWO", "第二", "Second Beneficiary"),
        ORDER_THREE("ORDER_THREE", "第三", "Third Beneficiary"),
        ;
        private String code;
        private String desc;
        private String value;

        BENEFICIARY_NO(String code, String desc, String value) {
            this.code = code;
            this.desc = desc;
            this.value = value;
        }

        public String code() {
            return code;
        }

        public String desc() {
            return desc;
        }

        public String value() {
            return value;
        }
    }

    /**
     * 业务类型
     */
    public enum PAYMENT_BUSINESS_TYPE implements ConstantType {

        APPLY("保单"),
        POLICY_RENEWAL_PAYMENT("保单续期支付"),
        POLICY_RENEWAL_INSURANCE("续保支付"),
        POLICY_ENDORSE("保全支付"),
        APPLY_GROUP("团险保单支付"),
        GROUP_ADD_INSURED("保全增员"),
        GROUP_SUBTRACT_INSURED("团险保全减员"),
        GROUP_ADD_ADDITIONAL("团险增加附加险"),
        SCRATCH_CARD_PRE_PAID("刮刮卡-预付费"),

        ;
        private String code;
        private String desc;

        private PAYMENT_BUSINESS_TYPE(String desc) {
            this.code = code;
            this.desc = desc;
        }

        public String code() {
            return code;
        }

        public String desc() {
            return desc;
        }
    }


    /**
     * 业务类型
     */
    public enum UNDER_WRITE_PROMPT implements ConstantType {

        UNDER_WRITE_PROMPT("核保提示"),
        SELF_INSURANCE("自保健"),
        INSURED_AMOUNT("被保人保额"),
        OCCUPATION_TYPE("职业风险"),

        ;
        private String code;
        private String desc;

        private UNDER_WRITE_PROMPT(String desc) {
            this.code = code;
            this.desc = desc;
        }

        public String code() {
            return code;
        }

        public String desc() {
            return desc;
        }
    }

    /**
     * 智能核保问题
     */
    public enum AUTO_UNDERWRITE_PROBLEM implements ConstantType {
        THIS_APPLICATION_APPLIED_BACKDATING_FUNCTION("该投保单已使用回溯功能"),
        HEALTH_NOTIFICATION_NOT_STANDARD("健康告知不达标"),
        MAXIMUM_AMOUNT_OF_A_SINGLE_POLICY("累积保单保额超过上限"),
        ACCUMULATED_SUM_AT_RISK_OF_TPD_LIMIT("累计高度残疾风险保额超过上限"),
        APPLICANT_BMI_NOT_STANDARD("投保人BMI不达标"),
        INSURED_BMI_NOT_STANDARD("被保人BMI不达标"),
        STANDARD_OCCUPATION("非标准职业"),
        ACCUMULATED_SUM_AT_RISK_OF_ADD_LIMIT("累计意外死亡风险保额超过上限"),
        ACCUMULATED_SUM_AT_RISK_OF_CI_LIMIT("累计重大疾病风险保额超过上限"),
        BENEFICIARY_RELATIONSHIP_IS_OTHER("受益人关系为“本人”或“其他”"),
        EXISTING_CLIENT("疑似客户"),
        REAL_CLIENT("存在客户"),
        EXISTING_CLAIM("有理赔历史"),
        APPLICANT_OCCUPATION_IS_OTHER("投保人职业为其他"),
        INSURED_OCCUPATION_IS_OTHER("被保人职业为“家庭主妇”、“学生”或“其他”"),
        THIS_APPLICATION_HAS_REQUESTED_FOR_DISCOUNT("该投保单有折扣申请"),
        ;

        private String code;
        private String desc;

        private AUTO_UNDERWRITE_PROBLEM(String desc) {
            this.desc = desc;
        }

        public String code() {
            return code;
        }

        public String desc() {
            return desc;
        }
    }

    /**
     * 核保任务类型
     */
    public enum UNDERWRITE_TASK_TYPE implements ConstantType {
        MANUAL_UNDERWRITING("人工核保"),
        AUTO_UNDERWRITING("智能核保");

        private String code;
        private String desc;

        private UNDERWRITE_TASK_TYPE(String desc) {
            this.desc = desc;
        }

        public String code() {
            return code;
        }

        public String desc() {
            return desc;
        }
    }

    /**
     * ANNUAL_EARNED_INCOME
     */
    public enum ANNUAL_EARNED_INCOME implements ConstantType {

        ANNUAL_EARNED_INCOME("收入国际化"),

        ;
        private String code;
        private String desc;

        private ANNUAL_EARNED_INCOME(String desc) {
            this.code = code;
            this.desc = desc;
        }

        public String code() {
            return code;
        }

        public String desc() {
            return desc;
        }
    }

    /**
     * ANNUAL_EARNED_INCOME
     */
    public enum COMMISSION_DISCOUNT_TYPE implements ConstantType {

        BEFORE_DISCOUNT("折扣前"),
        AFTER_DISCOUNT("折扣后"),

        ;
        private String code;
        private String desc;

        private COMMISSION_DISCOUNT_TYPE(String desc) {
            this.code = code;
            this.desc = desc;
        }

        public String code() {
            return code;
        }

        public String desc() {
            return desc;
        }
    }

    /**
     * 备注类型
     */
    public enum REMARK_TYPE implements ConstantType {
        /**
         * 折扣备注
         */
        DISCOUNT_REMARK("折扣备注"),

        ;
        private String code;
        private String desc;

        private REMARK_TYPE(String desc) {
            this.code = code;
            this.desc = desc;
        }

        public String code() {
            return code;
        }

        public String desc() {
            return desc;
        }
    }


    /**
     * 折扣类型
     */
    public enum DISCOUNT_TYPE implements ConstantType {
        FULLY_BORNE_COMPANY("Fully borne by Company", "公司承担"),
        FULLY_BORNE_AGENT("Fully borne by LC/IA", "业务员承担"),
        SPLIT_BETWEEN_COMPANY_AND_AGENT("Split between Company and LC/IA", "按比例承担"),
        ;

        private String code;
        private String desc;

        private DISCOUNT_TYPE(String code, String desc) {
            this.code = code;
            this.desc = desc;
        }

        public String code() {
            return code;
        }

        public String desc() {
            return desc;
        }
    }

    /**
     * 单位类型
     */
    public enum COMPANY_TYPE implements ConstantType {
        BUSINESS_ORGANIZATION("Business Organization", "事业组织"),
        ENTERPRISE("Enterprise", "企业"),
        FOREIGN_COMPANIES("Foreign Enterprise", "外资企业"),
        GOVERNMENT("Government", "政府"),
        GOVERNMENT_INSTITUTION("Government Institution", "政府机关"),
        INSTITUTION("Institution", "机构"),
        JOINT_VENTURE("Cooperate Enterprise", "合资企业"),
        NATIONALIZED_BUSINESS("National Enterprise", "国有企业"),
        OTHER("Other", "其他"),
        PRIVATE_ENTERPRISE("Private Enterprise", "私营企业"),
        SOCIAL_GROUP("Social Group", "社会团体"),
        STATE_ADMINISTRATION("National Administration", "国家行政机关"),
        STATE_OWNED_ENTERPRISES("National Holding Enterprise", "国有控股企业"),
        ;

        private String code;
        private String desc;

        private COMPANY_TYPE(String code, String desc) {
            this.code = code;
            this.desc = desc;
        }

        public String code() {
            return code;
        }

        public String desc() {
            return desc;
        }
    }

    /**
     * 保额计算依据
     */
    public enum BASIS_OF_SUM_INSURED implements ConstantType {
        MULTIPLE_OF_CURRENT_SALARY("上次领取工资的倍数"),
        RANK_AND_POSITION_IN_THE_COMPANY("在单位的级别和职位"),
        FLAT_SUM_INSURED_FOR_ALL_EMPLOYEES("所有员工固定保额"),
        YEARS_OF_SERVICE_WITH_THE_COMPANY("在单位工作年限"),
        OTHER_CATEGORY("其他类别（请说明）"),
        ;
        private String code;
        private String desc;

        private BASIS_OF_SUM_INSURED(String desc) {
            this.code = code;
            this.desc = desc;
        }

        public String code() {
            return code;
        }

        public String desc() {
            return desc;
        }
    }

    public enum INSURED_TYPE implements ConstantType {
        /**
         * 受益顺位
         */
        EMPLOYEE("EMPLOYEE", "员工", "Employee"),
        SPOUSE("SPOUSE", "配偶", "Spouse"),
        CHILD("CHILD", "子女", "Child"),
        ;
        private String code;
        private String desc;
        private String value;

        INSURED_TYPE(String code, String desc, String value) {
            this.code = code;
            this.desc = desc;
            this.value = value;
        }

        public String code() {
            return code;
        }

        public String desc() {
            return desc;
        }

        public String value() {
            return value;
        }
    }

    /**
     * 投保单变更状态
     */
    public enum CHANGE_STATUS implements ConstantType {
        INITIAL("初始化"),
        WAIT_REVIEW("待审核"),
        REVIEW("审核中"),
        REVIEW_NOT_PASS("审核不通过"),
        CHANGED("已变更");

        private String code;
        private String desc;

        private CHANGE_STATUS(String desc) {
            this.code = code;
            this.desc = desc;
        }

        public String code() {
            return code;
        }

        public String desc() {
            return desc;
        }
    }

    /**
     * 投保单变更状态
     */
    public enum CHANGE_TYPE implements ConstantType {
        MODIFY_MODE_OF_PAYMENT("缴费周期变更"),
        EXTENDED_PAYMENT_DUE_TIME("支付到期时间延长");

        private String code;
        private String desc;

        private CHANGE_TYPE(String desc) {
            this.code = code;
            this.desc = desc;
        }

        public String code() {
            return code;
        }

        public String desc() {
            return desc;
        }
    }

    /**
     * 支付到期时间延长下拉选
     */
    public enum EXTENDED_PAYMENT_DUE_TIME implements ConstantType {
        EXTENDED_PAYMENT_DUE_TIME_ONE("7天"),
        EXTENDED_PAYMENT_DUE_TIME_TWO("14天");

        private String code;
        private String desc;

        private EXTENDED_PAYMENT_DUE_TIME(String desc) {
            this.code = code;
            this.desc = desc;
        }

        public String code() {
            return code;
        }

        public String desc() {
            return desc;
        }
    }

    /**
     * 审核结论
     */
    public enum AUDIT_CONCLUSION implements ConstantType {
        PASS("审核通过"),
        REFUSE("审核不通过");

        private String code;
        private String desc;

        private AUDIT_CONCLUSION(String desc) {
            this.code = code;
            this.desc = desc;
        }

        public String code() {
            return code;
        }

        public String desc() {
            return desc;
        }
    }
}
