package com.gclife.apply.model.config;


import com.gclife.common.model.inter.IEnum;

/**
 * <AUTHOR>
 * create 17-9-18
 * description
 */
public enum ApplyErrorConfigEnum implements IEnum {

    /**
     * 编码规则
     * 参数错误: 模块名称_PARAMETER_表名称_字段名称_验证结果{IS_NOT_NULL:不能为空,FORMAT_ERROR:格式错误,IS_NOT_FOUND_VALUE:未找到值}
     * 业务错误: 模块名称_BUSINESS_规则描述_验证结果{IS_NOT_FOUND_OBJECT:未找到对象}
     * 数据转换错误: 模块名称_CONVERSION_DTO对象名称_ERROR
     * 保存错误: 模块名称_SAVE_表名称_ERROR
     * 查询错误: 模块名称_QUERY_表名称_查询结果(ERROR:查询出错);
     * 通用错误: 模块名称_FAIL
     */
    APPLY_FAIL("2030000000", "投保单模块异常", "APPLY"),
    APPLY_APPLY_NO_IS_NOT_NULL("2030000001", "投保单号不能为空", "APPLY"),
    APPLY_POLICY_NO_IS_NOT_NULL("2030000002", "保单号不能为空", "APPLY"),
    APPLY_APPLY_ID_IS_NOT_NULL("2030000003", "投保单ID不能为空", "APPLY"),
    APPLY_AGENT_ID_IS_NOT_NULL("2030000004", "业务员编号不能为空", "APPLY"),
    APPLY_CHANNEL_TYPE_IS_NOT_NULL("2030000005", "渠道类型不能为空", "APPLY"),
    APPLY_ACCEPT_FIRST_PREMIUM_IS_NOT_NULL("2030000006", "首期保费不能为空", "APPLY"),
    APPLY_QUERY_ACCEPTS_ERROR("2030000007", "查询受理列表异常", "APPLY"),
    APPLY_QUERY_ACCEPTS_STATUS_ERROR("2030000008", "查询受理状态异常", "APPLY"),
    APPLY_SAVE_ACCEPT_ERROR("2030000009", "受理信息保存异常", "APPLY"),
    APPLY_AGENT_CODE_IS_NOT_NULL("2030000010", "业务员编码不能为空", "APPLY"),
    APPLY_USER_DETAIL_IS_NOT_NULL("2030000011", "用户不能为空", "APPLY"),
    APPLY_QUERY_APPLY_ACCEPT_ERROR("2030000012", "查询保单受理异常", "APPLY"),
    APPLY_QUERY_APPLY_ACCEPT_IS_NULL_ERROR("2030000013", "查询投保单为空", "APPLY"),
    APPLY_APPLY_SAVE_ERROR("2030000014", "投保单保存异常", "APPLY"),
    APPLY_APPLY_ACCEPT_SAVE_ERROR("2030000015", "投保受理保存异常", "APPLY"),
    APPLY_APPLY_AGENT_SAVE_ERROR("**********", "投保单业务员保存异常", "APPLY"),
    APPLY_APPLY_COVERAGE_ACCEPT_SAVE_ERROR("**********", "投保单险种保存异常", "APPLY"),
    APPLY_APPLY_ATTACHMENT_SAVE_ERROR("**********", "投保单附件保存异常", "APPLY"),
    APPLY_APPLY_ATTACHMENT_CONFIG_SAVE_ERROR("**********", "投保单配置保存异常", "APPLY"),
    APPLY_APPLY_ATTACHMENT_CONFIG_DETAIL_SAVE_ERROR("**********", "投保单配置详情保存异常", "APPLY"),
    APPLY_APPLY_ACCOUNT_SAVE_ERROR("**********", "投保人账户保存异常", "APPLY"),
    APPLY_APPLY_APPLICANT_SAVE_ERROR("**********", "投保人保存异常", "APPLY"),
    APPLY_APPLY_BENEFICIARY_INFO_SAVE_ERROR("**********", "受益人保存异常", "APPLY"),
    APPLY_APPLY_CONTACT_INFO_SAVE_ERROR("**********", "投保单联系方式保存异常", "APPLY"),
    APPLY_ELECTRONIC_SIGNATURE_INFO_SAVE_ERROR("***********","保存电子签名异常","APPLY"),
    APPLY_APPLY_BENEFICIARY_SAVE_ERROR("**********", "受益人保存异常", "APPLY"),
    APPLY_APPLY_COVERAGE_SAVE_ERROR("**********", "投保单险种保存异常", "APPLY"),
    APPLY_APPLY_COVERAGE_DUTY_SAVE_ERROR("**********", "投保单险种责任保存异常", "APPLY"),
    APPLY_APPLY_HEALTH_QUESTIONNAIRE_SAVE_ERROR("**********", "投保单健康问卷保存异常", "APPLY"),
    APPLY_APPLY_HEALTH_QUESTIONNAIRE_ANSWER_SAVE_ERROR("**********", "投保单健康问卷回答保存异常", "APPLY"),
    APPLY_APPLY_INSURED_SAVE_ERROR("**********", "被保人保存异常", "APPLY"),
    APPLY_QUERY_APPLY_IMAGE_ERROR("**********", "查询影像件信息异常", "APPLY"),
    APPLY_QUERY_APPLY_WAIT_INPUT_ERROR("**********", "查询待录入列表异常", "APPLY"),
    APPLY_CERTIFY_IS_NULL_ERROR("**********", "附件类型不存在", "APPLY"),
    APPLY_QUERY_ACCEPT_USER_ERROR("**********", "查询受理用户异常", "APPLY"),
    APPLY_ACCEPT_SIGN_ERROR("**********", "受理任务签收异常", "APPLY"),
    APPLY_WAIT_INPUT_SIGN_ERROR("**********", "待录入任务签收异常", "APPLY"),
    APPLY_REVIEW_SIGN_ERROR("**********", "待复核任务签收异常", "APPLY"),
    APPLY_ACCEPT_PRODUCT_IS_NOT_NULL("**********", "险种信息不能为空", "APPLY"),
    APPLY_ACCEPT_PRODUCT_ERROR("**********", "险种信息不存在", "APPLY"),
    APPLY_ACCEPT_ATTACHMENT_IS_NOT_NULL("2030000040", "影像件信息不能为空", "APPLY"),
    APPLY_QUERY_INPUT_DICTIONARIES_ERROR("2030000041", "获取录入所有数据字典异常", "APPLY"),
    APPLY_QUERY_INPUT_SALE_BRANCH_ERROR("2030000042", "获取销售机构异常", "APPLY"),
    APPLY_QUERY_APPLY_REVIEW_ERROR("2030000043", "查询复核列表异常", "APPLY"),
    APPLY_QUERY_APPLY_REVIEW_INPUT_ERROR("2030000044", "查询复核录入页面数据异常", "APPLY"),
    APPLY_QUERY_APPLY_UNDER_WRITE_ERROR("2030000045", "查询复核列表异常", "APPLY"),
    APPLY_QUERY_APPLY_INPUT_USER_ERROR("2030000046", "查询投保单异常", "APPLY"),
    APPLY_QUERY_APPLY_AGENT_ERROR("2030000047", "查询业务员信息异常", "APPLY"),
    APPLY_QUERY_APPLY_AGENT_LIST_ERROR("2030000048", "查询保单机构列表信息异常", "APPLY"),
    APPLY_QUERY_APPLY_CHANNEL_ERROR("2030000049", "查询保单渠道信息异常", "APPLY"),
    APPLY_QUERY_ATTACHMENT_ERROR("2030000050", "查询投保单影像件信息异常", "APPLY"),
    APPLY_SAVE_ATTACHMENT_ERROR("2030000051", "保存附件信息异常", "APPLY"),
    APPLY_COMMIT_ATTACHMENT_ERROR("2030000052", "提交附件信息异常", "APPLY"),
    APPLY_ORDER_ATTACHMENT_APPLY_ID_IS_NOT_NULL("2030000053", "投保单ID不能为空", "APPLY"),
    APPLY_ORDER_ATTACHMENT_INFO_IS_NOT_NULL("2030000054", "投保单附件列表信息不能为空", "APPLY"),
    APPLY_ORDER_SIGN_USER_ID_IS_NOT_NULL("2030000055", "签收用户不能为空", "APPLY"),
    APPLY_ATTACHMENT_DELETE_ERROR("2030000056", "附件删除异常", "APPLY"),
    APPLY_ATTACHMENT_SIGN_ERROR("2030000057", "附件任务签收异常", "APPLY"),
    APPLY_ATTACHMENT_CONFIG_DETAIL_DELETE_ERROR("2030000058", "附件配置详细删除异常", "APPLY"),
    APPLY_ATTACHMENT_IS_NO_LESS_THAN_REQUIRED("2030000059", "附件上传不能少于必传张数", "APPLY"),
    APPLY_ATTACHMENT_ID_IS_NO_NULL("***********", "附件ID不为空", "APPLY"),
    APPLY_QUERY_APPLY_ERROR("2030000060", "查询投保单信息异常", "APPLY"),
    APPLY_BUSINESS_APPLY_QUESTION_CUSTOMER_IS_NOT_FOUND("2030000061", "未找到投保单联系人", "APPLY"),
    APPLY_QUERY_APPLY_CERTIFY_CODE_IS_NOT_NULL("2030000062", "单证编码不能为空", "APPLY"),
    APPLY_QUERY_APPLY_CERTIFY_IS_USING("2030000063", "单证已被占用", "APPLY"),
    APPLY_QUERY_APPLY_CERTIFY_ERROR("2030000064", "单证信息查询异常", "APPLY"),
    APPLY_PARAMETER_APPLY_APPLY_ID_IS_NOT_NULL("2030000065", "投保单ID不能为空", "APPLY"),
    APPLY_PARAMETER_APPLY_APPLY_NO_IS_NOT_NULL("***********", "投保单号不能为空", "APPLY"),
    APPLY_PARAMETER_APPLY_QUESTION_NODE_IS_NOT_NULL("2030000066", "问题节点不能为空", "APPLY"),
    APPLY_PARAMETER_APPLY_CONTACT_TYPE_IS_NOT_NULL("2030000067", "投保单联系人类型不能为空", "APPLY"),
    APPLY_PARAMETER_APPLY_QUESTION_ANSWER_ID_IS_NOT_NULL("2030000068", "问题回答ID不能为空", "APPLY"),
    APPLY_PARAMETER_APPLY_REMARK_IS_NOT_NULL("2030000069", "备注信息不能为空", "APPLY"),
    APPLY_PARAMETER_APPLY_REMARK_TYPE_IS_NOT_NULL("2030000069", "备注类型不能为空", "APPLY"),
    APPLY_PARAMETER_APPLY_QUESTION_DESC_IS_NOT_NULL("2030000070", "问题详细信息不能为空", "APPLY"),
    APPLY_PARAMETER_APPLY_ISIMGFLAG_IS_NOT_NULL("2030000071", "是否需要影像件标识不能为空", "APPLY"),
    APPLY_PARAMETER_APPLY_ISIMGFLAG_FORMAT_ERROR("2030000072", "未找到匹配是否需要影像件标识数据", "APPLY"),
    APPLY_PARAMETER_APPLY_CONTACT_TYPE_FORMAT_ERROR("2030000073", "未找到匹配的联系人类型", "APPLY"),
    APPLY_PARAMETER_APPLY_VALID_FLAG_FORMAT_ERROR("2030000074", "未找到匹配的有效失效数据", "APPLY"),
    APPLY_BUSINESS_APPLY_QUESTION_NOT_FOUND_OBJECT("2030000075", "未找到相应问题信息", "APPLY"),
    APPLY_BUSINESS_APPLY_IS_NOT_FOUND_OBJECT("2030000076", "未找到投保单信息", "APPLY"),
    APPLY_QUERY_APPLY_ERROR_QUESTION_REMARK_ERROR("2030000077", "查询备注异常", "APPLY"),
    APPLY_BUSINESS_LOAD_APPLY_ERROR_QUESTION_ERROR("2030000078", "加载问题件问题信息信息异常", "APPLY"),
    APPLY_QUERY_APPLY_ERROR_QUESTION_ADD_QUESTION_ERROR("2030000079", "保存问题异常", "APPLY"),
    APPLY_QUERY_APPLY_ERROR_QUESTION_ANSWER_ERROR("2030000080", "查询问题件信息异常", "APPLY"),
    APPLY_QUERY_APPLY_ERROR_QUESTION_ERROR("2030000081", "查询问题信息异常", "APPLY"),
    APPLY_QUERY_APPLY_ERROR_QUESTION_CONTACT_ERROR("**********", "查询投保单联系人信息异常", "APPLY"),
    APPLY_QUERY_APPLY_COMMON_ERROR_QUESTION_ERROR("**********", "查询常见问题异常", "APPLY"),
    APPLY_QUERY_APPLY_COMMON_ERROR_QUESTION_IS_NOT_FOUND_OBJECT("**********", "未找到常见问题", "APPLY"),
    APPLY_SEND_APPLY_ERROR_QUESTION_ERROR("**********", "发送问题件信息异常", "APPLY"),
    APPLY_DELETE_APPLY_ERROR_QUESTION_ERROR("**********", "删除问题信息异常", "APPLY"),
    APPLY_BUSINESS_BANK_IS_NOT_FOUND_OBJECT("**********", "未找到银行信息", "APPLY"),
    APPLY_FEIGN_CLIENT_PLATFORM_IS_NOT_NULL("**********", "调用平台微服务接口异常", "APPLY"),
    APPLY_FEIGN_CLIENT_AGENT_IS_NOT_NULL("**********", "调用业务员微服务接口异常", "APPLY"),
    APPLY_PARAMETER_APPLY_CONTACT_ID_IS_NOT_NULL("**********", "客户ID不能为空", "APPLY"),
    APPLY_PARAMETER_APPLY_CONTACT_NAME_IS_NOT_NULL("**********", "客户名称不能为空", "APPLY"),
    APPLY_APPLY_INFO_SAVE_ERROR("**********", "投保单录入信息保存异常", "APPLY"),
    APPLY_APPLY_INFO_SUBMIT_ERROR("**********", "投保单录入信息提交异常", "APPLY"),
    APPLY_INPUT_APPLICANT_INFO_IS_NOT_NULL("**********", "投保人信息不能为空", "APPLY"),
    APPLY_INPUT_INSURED_INFO_IS_NOT_NULL("**********", "被保人信息不能为空", "APPLY"),
    APPLY_INPUT_COVERAGE_INFO_IS_NOT_NULL("**********", "险种信息不能为空", "APPLY"),
    APPLY_INPUT_APPLY_ACCOUNT_INFO_IS_NOT_NULL("**********", "投保单账户信息不能为空", "APPLY"),
    APPLY_INPUT_HEALTH_QUESTION_ANSWER_INFO_IS_NOT_NULL("**********", "投保问卷答案信息不能为空", "APPLY"),
    APPLY_REVIEW_INFO_SAVE_ERROR("**********", "投保单复核保存异常", "APPLY"),
    APPLY_REVIEW_INFO_SUBMIT_ERROR("**********", "投保单复核提交异常", "APPLY"),
    APPLY_INPUT_APPLICANT_NAME_IS_NOT_NULL("**********", "投保人姓名不能为空", "APPLY"),
    APPLY_INPUT_APPLICANT_SEX_IS_NOT_NULL("**********", "投保人性别不能为空", "APPLY"),
    APPLY_INPUT_APPLICANT_IS_NOT_ADULT("**********", "投保人未满18岁", "APPLY"),
    APPLY_INPUT_APPLICANT_BIRTHDAY_IS_NOT_NULL("**********", "投保人出生日期不能为空", "APPLY"),
    APPLY_INPUT_APPLICANT_ID_TYPE_IS_NOT_NULL("**********", "投保人证件类型不能为空", "APPLY"),
    APPLY_INPUT_APPLICANT_ID_NO_IS_NOT_NULL("**********", "投保人证件号码不能为空", "APPLY"),
    APPLY_INPUT_APPLICANT_MARRIAGE_IS_NOT_NULL("**********", "投保人婚姻状况不能为空", "APPLY"),
    APPLY_INPUT_APPLICANT_NATIVE_PLACE_IS_NOT_NULL("2030000108", "投保人国籍不能为空", "APPLY"),
    APPLY_INPUT_APPLICANT_STATURE_IS_NOT_NULL("2030000109", "投保人身高不能为空", "APPLY"),
    APPLY_INPUT_APPLICANT_AVOIRDUPOIS_IS_NOT_NULL("2030000110", "投保人体重不能为空", "APPLY"),

    APPLY_INPUT_APPLICANT_STATURE_IS_NOT_NORMAL("2030000111", "投保人身高必须大于0", "APPLY"),
    APPLY_INPUT_APPLICANT_AVOIRDUPOIS_IS_NOT_NORMAL("2030000112", "投保人体重必须大于0", "APPLY"),
    APPLY_INPUT_APPLICANT_BMI_IS_NOT_NORMAL("2021122801", "您当前的BMI值为{num}，该值须介于 18-35 之间", "APPLY"),
    APPLY_INPUT_INSURED_BMI_IS_NOT_NORMAL("2021122801", "您的BMI不达标，该值必须在17-30之间", "APPLY"),
    APPLY_INSURED_CAN_ONLY_PURCHASE_ONE_POLICY("2021122801", "每个人只能购买一份#34产品保单", "APPLY"),
    APPLY_INPUT_APPLICANT_HOME_ADDRESS_IS_NOT_NULL("2030000111", "投保人详细地址不能为空", "APPLY"),
    APPLY_INPUT_APPLICANT_COMPANY_ADDRESS_IS_NOT_NULL("2030000112", "投保人单位地址不能为空", "APPLY"),
    APPLY_INPUT_INSURED_NAME_IS_NOT_NULL("2030000113", "被保人姓名不能为空", "APPLY"),
    APPLY_INPUT_INSURED_SEX_IS_NOT_NULL("2030000114", "被保人性别不能为空", "APPLY"),
    APPLY_INPUT_INSURED_RELATIONSHIP_INSTRUCTIONS_IS_NOT_NULL("2030000114", "与投保人关系的说明不能为空", "APPLY"),
    APPLY_INPUT_INSURED_BIRTHDAY_IS_NOT_NULL("2030000115", "被保人出生日期不能为空", "APPLY"),
    APPLY_INPUT_INSURED_ID_TYPE_IS_NOT_NULL("2030000116", "被保人证件类型不能为空", "APPLY"),
    APPLY_INPUT_INSURED_ID_NO_IS_NOT_NULL("2030000117", "被保人证件号码不能为空", "APPLY"),
    APPLY_INPUT_INSURED_MARRIAGE_IS_NOT_NULL("2030000118", "被保人婚姻状况不能为空", "APPLY"),
    APPLY_INPUT_INSURED_NATIVE_PLACE_IS_NOT_NULL("2030000119", "被保人国籍不能为空", "APPLY"),
    APPLY_INPUT_INSURED_STATURE_IS_NOT_NULL("2030000120", "被保人身高不能为空", "APPLY"),
    APPLY_INPUT_INSURED_AVOIRDUPOIS_IS_NOT_NULL("2030000121", "被保人体重不能为空", "APPLY"),
    APPLY_INPUT_INSURED_HOME_ADDRESS_IS_NOT_NULL("2030000122", "被保人详细地址不能为空", "APPLY"),
    APPLY_INPUT_INSURED_COMPANY_ADDRESS_IS_NOT_NULL("2030000123", "被保人单位地址不能为空", "APPLY"),
    APPLY_COMPANY_NAME_IS_NOT_NULL("2030000123", "公司名称不能为空", "APPLY"),
    APPLY_INPUT_PRODUCT_ID_IS_NOT_NULL("2030000124", "产品ID不能为空", "APPLY"),
    APPLY_INPUT_PRODUCT_NAME_IS_NOT_NULL("2030000125", "险种编码不能为空", "APPLY"),
    APPLY_INPUT_PRODUCT_PRIMARY_FLAG_IS_NOT_NULL("2030000126", "主附险标志不能为空", "APPLY"),
    APPLY_INPUT_APPLY_COVERAGE_DELETE_ERROR("2030000127", "删除投保单险种异常", "APPLY"),
    APPLY_INPUT_APPLY_COVERAGE_DUTY_DELETE_ERROR("2030000128", "删除投保单险种责任异常", "APPLY"),
    APPLY_INPUT_APPLY_COVERAGE_DUTY_AMOUNT_IS_NOT_FOUND_OBJECT("**********", "未找到险种责任信息", "APPLY"),
    APPLY_APPLY_QUESTION_ERROR_ANSWER_SAVE_ERROR("**********", "问题信息保存异常", "APPLY"),
    APPLY_SAVE_APPLY_QUESTION_ERROR_ANSWER_HEAD_ERROR("**********", "问题件头信息保存异常", "APPLY"),
    APPLY_APPLY_QUESTION_ERROR_ANSWER_DELETE_ERROR("**********", "问题信息删除异常", "APPLY"),
    APPLY_APPLY_QUESTION_ERROR_ANSWER_COUNT_ERROR("**********", "问题信息不能为空", "APPLY"),
    APPLY_INPUT_ACCOUNT_INITIAL_PAYMENT_MODE_IS_NOT_NULL("**********", "首期缴费方式不能为空", "APPLY"),
    APPLY_INPUT_ACCOUNT_NO_IS_NOT_NULL("**********", "账号号码不能为空", "APPLY"),
    APPLY_INPUT_ACCOUNT_BANK_CODE_IS_NOT_NULL("**********", "开户行代码不能为空", "APPLY"),
    APPLY_PARAMETER_BANK_IS_NOT_NULL("**********", "请选择银行", "APPLY"),
    APPLY_INPUT_ACCOUNT_SUB_BRANCH_IS_NOT_NULL("**********", "支行名称不能为空", "APPLY"),
    APPLY_INPUT_ACCOUNT_OWNER_IS_NOT_NULL("**********", "账号持有人姓名不能为空", "APPLY"),
    APPLY_INPUT_ACCOUNT_RELATIONSHIP_WITH_APPLICANT_IS_NOT_NULL("**********", "投保人与账号持有人不能为空", "APPLY"),
    APPLY_INPUT_ACCOUNT_ID_TYPE_IS_NOT_NULL("**********", "账号持有人证件类型不能为空", "APPLY"),
    APPLY_INPUT_ACCOUNT_ID_NO_IS_NOT_NULL("**********", "证件号码不能为空", "APPLY"),
    APPLY_INPUT_ACCOUNT_TYPE_IS_NOT_NULL("**********", "账户类型不能为空", "APPLY"),
    APPLY_INPUT_ACCOUNT_DELETE_ERROR("**********", "删除投保单账户异常", "APPLY"),
    APPLY_INPUT_ACCOUNT_IS_NOT_FOUND("**********", "未找到投保单账户", "APPLY"),
    APPLY_INPUT_ACCOUNT_AREA_CODE_NO_IS_NOT_NULL("**********", "开户行地址编码不能为空", "APPLY"),
    APPLY_INPUT_ACCOUNT_NO_FORMAT_ERROR("**********", "账户号码格式有误", "APPLY"),
    APPLY_INPUT_ACCOUNT_RELATIONSHIP_WITH_APPLICANT_FORMAT_ERROR("**********", "投保人与账号持有人格式有误", "APPLY"),
    APPLY_INPUT_ACCOUNT_ID_TYPE_FORMAT_ERROR("**********", "账户持有人证件类型格式有误", "APPLY"),
    APPLY_INPUT_ACCOUNT_TYPE_FORMAT_ERROR("**********", "账号类型格式有误", "APPLY"),
    APPLY_INPUT_ACCOUNT_AREA_CODE_FORMAT_ERROR("**********", "开户行所在地区格式有误", "APPLY"),
    APPLY_INPUT_ACCOUNT_BANK_CODE_FORMAT_ERROR("**********", "开户行格式有误", "APPLY"),
    APPLY_INPUT_APPLY_BENEFICIARY_DELETE_ERROR("**********", "删除受益人异常", "APPLY"),
    APPLY_INPUT_APPLY_BENEFICIARY_INFO_DELETE_ERROR("**********", "删除受益人详情异常", "APPLY"),
    APPLY_INPUT_APPLY_QUESTION_ID_IS_NOT_NULL("**********", "健康告知问题编码不能为空", "APPLY"),
    APPLY_INPUT_APPLY_QUESTION_ANSWER_LIST_IS_NOT_NULL("**********", "健康告知回答列表不能为空", "APPLY"),
    APPLY_INPUT_APPLY_QUESTION_ANSWER_IS_NOT_NULL("**********", "健康告知回答不能为空", "APPLY"),
    APPLY_INPUT_APPLY_QUESTION_ANSWER_DES_IS_NOT_NULL("**********", "健康告知回答描述不能为空", "APPLY"),
    APPLY_INPUT_APPLY_QUESTION_ANSWER_CUSTOMER_IS_NOT_NULL("**********", "健康告知回答客户类型不能为空", "APPLY"),
    APPLY_INPUT_APPLY_QUESTION_ANSWER_DELETE_ERROR("**********", "投保单问卷回答删除异常", "APPLY"),
    APPLY_PARAMETER_APPLY_UNDERWRITECONFIG_FORCE_MANUAL_UNDER_WRITE_FLAG_IS_NOT_NULL("**********", "强制人工核保标示不能为空", "APPLY"),
    APPLY_QUERY_APPLY_UNDER_WRITE_CONFIG_ERROR("**********", "查询核保配置异常", "APPLY"),
    APPLY_SAVE_APPLY_UNDER_WRITE_CONFIG_ERROR("**********", "保存核保配置异常", "APPLY"),
    APPLY_QUERY_APPLY_UNDER_WRITE_FORCE_FLAG_ERROR("**********", "查询强制核保标志列表异常", "APPLY"),
    APPLY_QUERY_APPLY_UNDER_WRITE_HEALTH_ERROR("**********", "查询核保健康告知异常", "APPLY"),
    APPLY_QUERY_APPLY_UNDER_WRITE_INFO_ERROR("**********", "查询人工核保异常", "APPLY"),
    APPLY_QUERY_APPLY_WORK_ITEM_TRACK_ERROR("**********", "查询投保单操作履历异常", "APPLY"),
    APPLY_SAVE_APPLY_UNDER_WRITE_TASK_ERROR("**********", "新建核保任务异常", "APPLY"),
    APPLY_QUERY_APPLY_PAYMENT_INFO_ERROR("**********", "查询投保单缴费信息异常", "APPLY"),
    APPLY_UNDERWRITE_APPLYID_IS_NOT_NULL("**********", "投保单ID不能为空", "APPLY"),
    APPLY_UNDERWRITE_INSUREDID_IS_NOT_NULL("**********", "被保人ID不能为空", "APPLY"),
    APPLY_UNDER_WRITE_TASK_IS_NULL("**********", "核保任务不存在", "APPLY"),
    APPLY_UNDERWRITE_DECISION_APPLYID_ERROR("**********", "投保单ID不能为空", "APPLY"),
    APPLY_UNDERWRITE_DECISION_COVERAGEID_ERROR("**********", "险种ID不能为空", "APPLY"),
    APPLY_UNDERWRITE_DECISION_CUSTORMERID_ERROR("**********", "客户ID不能为空", "APPLY"),
    APPLY_UNDERWRITE_DECISION_UNDERWRITEDECISIONCODE_IS_NOT_NULL("2030000174", "核保决定编码不能为空", "APPLY"),
    APPLY_UNDERWRITE_EXTRA_IS_NULL_ERROR("2030000175", "至少一项 加费", "APPLY"),
    APPLY_UNDERWRITE_DECISION_REMARKS_ERROR("2030000176", "请填写加费说明", "APPLY"),
    APPLY_UNDERWRITE_DECISION_SEE_ERROR("2030000177", "查询保单决定异常", "APPLY"),
    APPLY_UNDERWRITE_DECISION_SAVE_UPDATE_ERROR("2030000178", "核保决定异常", "APPLY"),
    APPLY_UNDERWRITE_DECISION_SAVE_ERROR("2030000179", "发送核保结论异常", "APPLY"),
    APPLY_UNDERWRITE_DECISION_CODE_FORMAT_ERROR("2030000180", "核保决定编码异常", "APPLY"),
    APPLY_UNDERWRITE_DECISION_SEND_ERROR("2030000181", "下发核保任务异常", "APPLY"),
    APPLY_UNDERWRITE_DECISION_SEND_IS_NULL_ERROR("2030000182", "未有核保通知书要下发", "APPLY"),
    APPLY_TRANSFORM_POLICY_DATA_ERROR("2030000183", "投保单转保单数据异常", "APPLY"),
    APPLY_BUSINESS_SAVE_POLICY_DATA_ERROR("2030000184", "承保异常", "APPLY"),
    APPLY_QUERY_APPLY_PREMIUM_PAY_ERROR("2030000185", "查询投保单保费支付异常", "APPLY"),
    APPLY_BUSINESS_APPLY_PREMIUM_IS_NOT_FOUND_OBJECT("2030000186", "未找到投保单保费支付记录", "APPLY"),
    APPLY_QUERY_WORKFLOW_FAIL("2030000187", "请求工作流数据异常", "APPLY"),
    POLICY_QUERY_CUSTOMER_POLICY_MEDAL_ERROR("2030000188", "查询保单勋章信息异常", "APPLY"),
    APPLY_INPUT_APPLY_INSURED_DELETE_ERROR("2030000189", "删除被保人异常", "APPLY"),
    APPLY_INPUT_APPLICANT_COUNTRY_CODE_IS_NOT_NULL("2030000190", "投保人手机国家区号不能为空", "APPLY"),
    APPLY_INPUT_APPLICANT_MOBILE_IS_NOT_NULL("2030000191", "投保人手机号码不能为空", "APPLY"),
    APPLY_INPUT_APPLICANT_MOBILE_VERIFY_CODE_IS_NOT_NULL("2030000192", "投保人手机验证码不能为空", "APPLY"),
    APPLY_INPUT_APPLICANT_EMAIL_IS_NOT_NULL("2030000193", "投保人电子邮箱不能为空", "APPLY"),
    APPLY_INPUT_APPLICANT_CUSTOMER_SOURCE_IS_NOT_NULL("2030000194", "投保人客户来源不能为空", "APPLY"),
    APPLY_INPUT_APPLICANT_SOCIAL_SECURITY_IS_NOT_NULL("2030000195", "投保人是否有社保不能为空", "APPLY"),
    APPLY_INPUT_APPLICANT_OCCUPATION_CODE_IS_NOT_NULL("2030000196", "投保人职业不能为空", "APPLY"),
    APPLY_INPUT_INSURED_RELATIONSHIP_IS_NOT_NULL("2030000197", "被保人与投保人的关系不能为空", "APPLY"),
    APPLY_INPUT_APPLY_CHECK_ERROR("2030000198", "投保单录入信息核对异常", "APPLY"),
    APPLY_INPUT_INSURED_SOCIAL_SECURITY_IS_NOT_NULL("2030000199", "被保人是否有社保不能为空", "APPLY"),
    APPLY_INPUT_INSURED_OCCUPATION_CODE_IS_NOT_NULL("2030000200", "被保人职业编码不能为空", "APPLY"),
    APPLY_QUERY_APPLY_PLAN_ERROR("2030000201", "查询计划书异常", "APPLY"),
    APPLY_BUSINESS_APPLY_APPLICANT_IS_NOT_FOUND_OBJECT("2030000202", "未找到投保人信息", "APPLY"),
    APPLY_BUSINESS_APPLY_INSURED_IS_NOT_FOUND_OBJECT("2030000203", "未找到被保人信息", "APPLY"),
    APPLY_BUSINESS_APPLY_INSURED_COLLECT_IS_NOT_FOUND_OBJECT("2030000204", "未找到被保人统计信息", "APPLY"),
    APPLY_PLAN_ID_IS_NOT_NULL("2030000205", "计划书id不能为空", "APPLY"),
    APPLY_PLAN_SAVE_ERROR("2030000206", "计划书保存异常", "APPLY"),
    APPLY_APPLICANT_PLAN_SAVE_ERROR("2030000207", "计划书投保人保存异常", "APPLY"),
    APPLY_INSURED_PLAN_SAVE_ERROR("2030000208", "计划书被保人保存异常", "APPLY"),
    APPLY_PLAN_COVERAGE_SAVE_ERROR("2030000209", "计划书险种保存异常", "APPLY"),
    APPLY_BUSINESS_AGENT_IS_NOT_FOUND_OBJECT("2030000210", "未找到业务员信息", "APPLY"),
    APPLY_BUSINESS_AGENT_DETAIL_IS_NOT_FOUND_OBJECT("2030000211", "未找到业务员详细信息", "APPLY"),
    APPLY_BUSINESS_AGENT_IDENTITY_IS_NOT_FOUND_OBJECT("2030000212", "未找到业务员实名信息", "APPLY"),
    APPLY_PLAN_COVERAGE_DELETE_ERROR("2030000213", "计划书险种删除异常", "APPLY"),
    APPLY_QUERY_PLAN_APPLY_NO_ERROR("**********", "查询投保单信息异常", "APPLY"),
    APPLY_ATTACHMENT_ID_IS_NOT_NULL("**********", "附件ID不能为空", "APPLY"),
    APPLY_PLAN_IS_FLAG_IS_NOT_NULL("********","客户是否在一起标识不能为空","APPLY"),
    APPLY_ATTACHMENT_SEQ_IS_NOT_NULL("**********", "附件编号不能为空", "APPLY"),
    APPLY_SAVE_ID_ATTACHMENT_ERROR("**********", "身份证附件保存异常", "APPLY"),
    APPLY_SAVE_BANKCARD_ATTACHMENT_ERROR("**********", "银行卡附件保存异常", "APPLY"),
    APPLY_SAVE_IMAGE_ATTACHMENT_ERROR("**********", "影像附件保存异常", "APPLY"),
    APPLY_ATTACHMENT_RISK_TIPS_IS_NOT_NULL("**********", "风险提示书不能为空", "APPLY"),
    APPLY_ATTACHMENT_APPLY_TIPS_IS_NOT_NULL("**********", "投保提示书不能为空", "APPLY"),
    APPLY_APPLICATION_CONFIRMATION_IS_NOT_NULL("**********", "电子保单申请确认书不能为空", "APPLY"),
    APPLY_PLAN_COVERAGE_DUTY_SAVE_ERROR("**********", "计划书险种责任保存异常", "APPLY"),
    APPLY_PLAN_COVERAGE_DUTY_DELETE_ERROR("**********", "计划书险种责任删除异常", "APPLY"),
    APPLY_QUERY_PLAN_INFO_CHECK_ERROR("**********", "查询信息核对异常", "APPLY"),
    APPLY_QUERY_APPLICANT_ERROR("**********", "查询投保人异常", "APPLY"),
    APPLY_QUERY_INSURED_ERROR("**********", "查询被保人异常", "APPLY"),
    APPLY_QUERY_BENEFICIAL_ERROR("**********", "查询受益人异常", "APPLY"),
    APPLY_SAVE_APPLICANT_ERROR("2030000229", "保存投保人异常", "APPLY"),
    APPLY_SAVE_INSURED_ERROR("2030000230", "保存被保人异常", "APPLY"),
    APPLY_SAVE_BENEFICIAL_ERROR("2030000231", "保存受益人异常", "APPLY"),
    APPLY_SAVE_CALCULATION_ERROR("2030000232", "投保单试算信息保存异常", "APPLY"),
    APPLY_CALCULATION_PARAMETERS_IS_NOT_NULL("2030000233", "投保单试算属性字段不能为空", "APPLY"),
    APPLY_COVERAGE_LEVEI_PRODUCT_LEVEI_IS_NOT_NULL("2030000234", "产品档次字段不能为空", "APPLY"),
    APPLY_COVERAGE_LEVEI_MULT_IS_NOT_NULL("2030000235", "产品份数字段不能为空", "APPLY"),
    APPLY_COVERAGE_LEVEI_PRODUCT_LEVEI__OR_MULT_IS_NOT_NULL("2030000236", "产品档次和产品份数不能为空", "APPLY"),
    APPLY_QUERY_CALCULATION_ERROR("2030000237", "查询投保单试算信息异常", "APPLY"),
    APPLY_APP_APPLICANT_COMPANY_TYPE_IS_NOT_NULL("2030000238", "投保人单位类型不能为空", "APPLY"),
    APPLY_APP_APPLICANT_COMPANY_AREA_CODE_IS_NOT_NULL("2030000239", "投保人单位地区不能为空", "APPLY"),
    APPLY_APP_APPLICANT_HOME_AREA_CODE_IS_NOT_NULL("2030000240", "投保人所在地区不能为空", "APPLY"),
    APPLY_APP_APPLICANT_HOME_ZIP_CODE_IS_NOT_NULL("2030000241", "投保人邮编不能为空", "APPLY"),
    APPLY_APP_APPLICANT_INCOME_IS_NOT_NULL("2030000242", "投保人收入不能为空", "APPLY"),
    APPLY_APP_APPLICANT_SOCIAL_SECURITY_IS_NOT_NULL("2030000243", "投保人社保不能为空", "APPLY"),
    APPLY_APP_APPLICANT_INCOME_SOURCE_IS_NOT_NULL("2030000244", "投保人收入来源不能为空", "APPLY"),
    APPLY_APP_APPLICANT_STATURE_FORMAT_ERROR("2030000245", "投保人身高格式有误", "APPLY"),
    APPLY_APP_APPLICANT_AVOIRDUPOIS_FORMAT_ERROR("2030000246", "投保人体重格式有误", "APPLY"),
    APPLY_APP_APPLICANT_BIRTHDAY_FORMAT_ERROR("2030000247", "投保人生日格式有误", "APPLY"),
    APPLY_APP_APPLICANT_MOBILE_FORMAT_ERROR("2030000248", "投保人手机号码格式有误", "APPLY"),
    APPLY_APP_APPLICANT_HOME_MOBILE_FORMAT_ERROR("2030000249", "投保人地址电话格式有误", "APPLY"),
    APPLY_APP_APPLICANT_HOME_ZIP_CODE_FORMAT_ERROR("2030000250", "投保人邮政编码格式有误", "APPLY"),
    APPLY_APP_APPLICANT_COMPANY_ZIP_CODE_FORMAT_ERROR("2030000251", "投保人单位邮编格式有误", "APPLY"),
    APPLY_APP_APPLICANT_SEX_FORMAT_ERROR("2030000252", "投保人性别格式有误", "APPLY"),
    APPLY_APP_APPLICANT_WECHAT_OR_FACEBOOK_IS_NOT_NULL("2030000253", "Input at least one item on WeChat or Facebook", "APPLY"),
    APPLY_APP_APPLICANT_MARRIAGE_FORMAT_ERROR("2030000254", "投保人婚姻状况格式有误", "APPLY"),
    APPLY_APP_APPLICANT_ID_TYPE_FORMAT_ERROR("2030000255", "投保人证件类型格式有误", "APPLY"),
    APPLY_APP_APPLICANT_NATIVE_PLACE_FORMAT_ERROR("2030000256", "投保人国籍格式有误", "APPLY"),
    APPLY_APP_APPLICANT_COMPANY_TYPE_FORMAT_ERROR("2030000257", "投保人单位类型格式有误", "APPLY"),
    APPLY_APP_APPLICANT_SOCIAL_SECURITY_FORMAT_ERROR("**********", "投保人是否有社保格式有误", "APPLY"),
    APPLY_APP_APPLICANT_INCOME_SOURCE_FORMAT_ERROR("**********", "投保人收入来源格式有误", "APPLY"),
    APPLY_APP_APPLICANT_VERIFY_CODE_FORMAT_ERROR("**********", "投保人短信验证码格式有误", "APPLY"),
    APPLY_APP_APPLICANT_SMS_TYPE_FORMAT_ERROR("**********", "投保人短信类型格式有误", "APPLY"),
    APPLY_APP_APPLICANT_EMAIL_FORMAT_ERROR("**********", "投保人邮箱格式有误", "APPLY"),
    APPLY_APP_ACCOUNT_AREA_CODE_FORMAT_ERROR("**********", "投保人开户城市地址编码格式有误", "APPLY"),
    APPLY_APP_BANK_CARD_TYPE_FORMAT_ERROR("**********", "投保人账户类型格式有误", "APPLY"),
    APPLY_APPLY_ACCOUNT_QUERY_ERROR("**********", "投保人账户查询异常", "APPLY"),
    APPLY_APP_APPLICANT_BELONG_COMPANY_PHONE_FORMAT_ERROR("**********", "投保人单位电话格式有误", "APPLY"),
    APPLY_APP_APPLICANT_INCOME_FORMAT_ERROR("**********", "投保人年均收入格式有误", "APPLY"),
    APPLY_APP_INSURED_EMAIL_IS_NOT_NULL("**********", "被保人邮箱不能为空", "APPLY"),
    APPLY_APP_INSURED_MOBILE_IS_NOT_NULL("**********", "被保人邮箱不能为空", "APPLY"),
    APPLY_APP_INSURED_COMPANY_TYPE_IS_NOT_NULL("**********", "被保人单位类型不能为空", "APPLY"),
    APPLY_APP_INSURED_COMPANY_AREA_CODE_IS_NOT_NULL("**********", "被保人单位地区不能为空", "APPLY"),
    APPLY_APP_INSURED_HOME_AREA_CODE_IS_NOT_NULL("**********", "被保人所在地区不能为空", "APPLY"),
    APPLY_APP_INSURED_HOME_ZIP_CODE_IS_NOT_NULL("**********", "被保人家庭邮编不能为空", "APPLY"),
    APPLY_APP_INSURED_INCOME_IS_NOT_NULL("**********", "被保人收入不能为空", "APPLY"),
    APPLY_APP_INSURED_SOCIAL_SECURITY_IS_NOT_NULL("2030000275", "被保人社保不能为空", "APPLY"),
    APPLY_APP_INSURED_STATURE_FORMAT_ERROR("2030000276", "被保人身高格式有误", "APPLY"),
    APPLY_APP_INSURED_AVOIRDUPOIS_FORMAT_ERROR("2030000277", "被保人体重格式有误", "APPLY"),
    APPLY_APP_INSURED_BIRTHDAY_FORMAT_ERROR("2030000278", "被保人生日格式有误", "APPLY"),
    APPLY_APP_INSURED_MOBILE_FORMAT_ERROR("2030000279", "被保人手机号码格式有误", "APPLY"),
    APPLY_APP_INSURED_HOME_ZIP_CODE_FORMAT_ERROR("2030000280", "被保人邮政编码格式有误", "APPLY"),
    APPLY_APP_INSURED_SEX_FORMAT_ERROR("2030000281", "被保人性别格式有误", "APPLY"),
    APPLY_APP_INSURED_MARRIAGE_FORMAT_ERROR("2030000282", "被保人婚姻状况格式有误", "APPLY"),
    APPLY_APP_INSURED_COMPANY_ZIP_CODE_FORMAT_ERROR("2030000283", "被保人单位邮编格式有误", "APPLY"),
    APPLY_APP_INSURED_ID_TYPE_FORMAT_ERROR("2*********", "被保人证件类型格式有误", "APPLY"),
    APPLY_APP_INSURED_NATIVE_PLACE_FORMAT_ERROR("2030000285", "被保人国籍格式有误", "APPLY"),
    APPLY_APP_INSURED_COMPANY_TYPE_FORMAT_ERROR("2030000286", "被保人单位类型格式有误", "APPLY"),
    APPLY_APP_INSURED_SOCIAL_SECURITY_FORMAT_ERROR("2030000287", "被保人是否有社保格式有误", "APPLY"),
    APPLY_APP_INSURED_RELATIONSHIP_FORMAT_ERROR("2030000288", "被保人与投保人的关系格式有误", "APPLY"),
    APPLY_APP_INSURED_RELATIONSHIP_WITH_THE_APPLICANT_FORMAT_ERROR("2030000289", "被保人与投保人关系格式有误", "APPLY"),
    APPLY_APP_INSURED_HOME_MOBILE_FORMAT_ERROR("2030000290", "被保人地址电话格式有误", "APPLY"),
    APPLY_APP_INSURED_BELONG_COMPANY_PHONE_FORMAT_ERROR("2030000291", "被保人单位电话格式有误", "APPLY"),
    APPLY_APP_INSURED_INCOME_FORMAT_ERROR("2030000292", "被保人年均收入格式有误", "APPLY"),
    APPLY_APP_INSURED__EMAIL_FORMAT_ERROR("2030000293", "被保人邮箱格式有误", "APPLY"),
    APPLY_APP_BENEFICIAL_NAME_IS_NOT_NULL("2030000294", "受益人姓名不能为空", "APPLY"),
    APPLY_APP_BENEFICIAL_SEX_IS_NOT_NULL("2030000295", "受益人性别不能为空", "APPLY"),
    APPLY_APP_BENEFICIAL_BIRTHDAY_IS_NOT_NULL("2030000296", "受益人出生日期不能为空", "APPLY"),
    APPLY_APP_BENEFICIAL_ID_TYPE_IS_NOT_NULL("2030000297", "受益人证件类型不能为空", "APPLY"),
    APPLY_APP_BENEFICIAL_ID_NO_IS_NOT_NULL("2030000298", "受益人证件号码不能为空", "APPLY"),
    APPLY_APP_BENEFICIAL_NO_IS_NOT_NULL("2030000299", "受益人受益顺序不能为空", "APPLY"),
    APPLY_APP_BENEFICIAL_PROPORTION_IS_NOT_NULL("2030000300", "受益人受益份额不能为空", "APPLY"),
    APPLY_APP_BENEFICIAL_SOCIAL_SECURITY_IS_NOT_NULL("2030000301", "受益人社保不能为空", "APPLY"),
    APPLY_APP_BENEFICIAL_OCCUPATION_CODE_IS_NOT_NULL("2030000302", "受益人职业不能为空", "APPLY"),
    APPLY_APP_BENEFICIAL_ID_EXP_DATE_IS_NOT_NULL("2030000303", "受益人有效期不能为空", "APPLY"),
    APPLY_APP_BENEFICIAL_IS_NOT_NULL("2030000304", "受益人不能为空", "APPLY"),
    APPLY_APP_BENEFICIAL_SEX_FORMAT_ERROR("2030000305", "被保人性别格式有误", "APPLY"),
    APPLY_APP_BENEFICIAL_ID_TYPE_FORMAT_ERROR("2030000306", "被保人证件类型格式有误", "APPLY"),
    APPLY_APP_BENEFICIAL_SOCIAL_SECURITY_FORMAT_ERROR("2030000307", "被保人是否有社保格式有误", "APPLY"),
    APPLY_APP_BENEFICIAL_RELATIONSHIP_IS_NOT_NULL("2030000308", "受益人与被保人关系不能为空", "APPLY"),
    APPLY_APP_BENEFICIAL_PROPORTION_FORMAT_ERROR("2030000309", "受益人受益份额格式有误", "APPLY"),
    APPLY_APP_BENEFICIAL_NO_FORMAT_ERROR("2030000310", "受益人受益顺序格式有误", "APPLY"),
    APPLY_APP_BENEFICIAL_RELATIONSHIP_FORMAT_ERROR("2030000311", "受益人与被保人关系格式有误", "APPLY"),
    APPLY_APP_BENEFICIAL_BIRTHDAY_FORMAT_ERROR("2030000312", "受益人生日格式有误", "APPLY"),
    APPLY_APP_BENEFICIAL_ID_EXP_DATE_FORMAT_ERROR("2030000313", "受益人身份证过期时间格式有误", "APPLY"),
    APPLY_APP_APPLICANT_OCCUPATION_FORMAT_ERROR("2030000314", "投保人职业格式有误", "APPLY"),
    APPLY_APP_INSURED_OCCUPATION_FORMAT_ERROR("2030000315", "被保人职业格式有误", "APPLY"),
    APPLY_APP_APPLICANT_COMPANY_AREA_FORMAT_ERROR("2030000316", "投保人单位地区格式有误", "APPLY"),
    APPLY_APP_APPLICANT_HOME_AREA_FORMAT_ERROR("2030000317", "投保人家庭地区格式有误", "APPLY"),
    APPLY_APP_INSURED_COMPANY_AREA_FORMAT_ERROR("2030000318", "被保人单位地区格式有误", "APPLY"),
    APPLY_APP_INSURED_HOME_AREA_FORMAT_ERROR("2030000319", "被保人家庭地区格式有误", "APPLY"),
    APPLY_APP_BENEFICIAL_OCCUPATION_FORMAT_ERROR("2030000320", "受益人职业格式有误", "APPLY"),
    APPLY_AGENT_QUERY_TEAM_PRE_PAID_PREMIUMS_ERROR("2030000321", "查询团队预收保费异常", "APPLY"),
    APPLY_BUSINESS_AGENT_BRANCH_IS_NOT_FOUND_OBJECT("2030000322", "未找到业务员机构信息", "APPLY"),
    APPLY_SAVE_CUSTOMER_ERROR("2030000323", "添加客户异常", "APPLY"),
    APPLY_START_PAYMENT_ERROR("2030000324", "调用收付费服务异常", "APPLY"),
    APPLY_APP_TRANS_BIRTHDAY_TO_AGE_ERROR("2030000325", "出生日期转年龄异常", "APPLY"),
    APPLY_APP_CONTACT_INFO_IS_NOT_NULL("2030000326", "投保单寄送地址不能为空", "APPLY"),
    APPLY_APP_CONTACT_INFO_ERROR("2030000327", "投保单寄送地址获取异常", "APPLY"),
    APPLY_APP_CONTACT_INFO_NAME_IS_NOT_NULL("2030000328", "投保单收件人姓名不能为空", "APPLY"),
    APPLY_APP_CONTACT_INFO_MOBILE_IS_NOT_NULL("2030000329", "投保单收件人手机号不能为空", "APPLY"),
    APPLY_APP_CONTACT_INFO_AREA_CODE_IS_NOT_NULL("2030000330", "投保单寄送区域不能为空", "APPLY"),
    APPLY_APP_CONTACT_INFO_ADDRESS_INFO_IS_NOT_NULL("2030000331", "投保单详细地址不能为空", "APPLY"),
    APPLY_APP_CONTACT_INFO_POST_CODE_IS_NOT_NULL("2030000332", "投保单邮政编码不能为空", "APPLY"),
    APPLY_APP_CONTACT_INFO_MOBILE_FORMAT_ERROR("2030000333", "寄送地址手机号码格式有误", "APPLY"),
    APPLY_APP_CONTACT_INFO_POST_CODE_FORMAT_ERROR("2030000334", "寄送地址邮政编码格式有误", "APPLY"),
    APPLY_APP_CONTACT_INFO_ADDRESS_AREA_IS_NOT_FOUND_OBJECT("2030000335", "寄送地区不存在", "APPLY"),
    APPLY_DELETE_BENEFICIAL_ERROR("2030000336", "删除受益人异常", "APPLY"),
    APPLY_APPLY_BENEFICIARY_PROPORTION_ERROR("2030000337", "受益人份额格式有误", "APPLY"),
    APPLY_CIQ_WORK_LOG_SAVE_ERROR("2030000338", "当班日志保存异常", "APPLY"),
    APPLY_CIQ_APPLY_REQUEST_SAVE_ERROR("2030000339", "投保数据保存异常", "APPLY"),
    APPLY_QUERY_APPLY_DASHBOARD_ERROR("2030000340", "查询仪表板数据异常", "APPLY"),
    APPLY_QUERY_APPLY_CERTIFY_TYPE_ERROR("2030000341", "附件类型查询异常", "APPLY"),
    APPLY_APP_PAYMENT_ERROR("2030000342", "支付失败", "APPLY"),
    APPLY_APPLY_IS_NOT_NULL("2030000343", "投保单已经存在", "APPLY"),
    APPLY_QUERY_APPLY_CERTIFY_ATTACHMENT_ERROR("2030000344", "附件信息查询异常", "APPLY"),
    APPLY_PAYMENT_ID_IS_NOT_NULL("2030000345", "支付ID不能为空", "APPLY"),
    APPLY_PAYMENT_STATUS_NOT_NULL("2030000346", "支付状态不能为空", "APPLY"),
    APPLY_PAYMENT_DUE_PAY_AMOUNT_NOT_NULL("2030000347", "应付金额不能为空", "APPLY"),
    APPLY_PAYMENT_ACTUAL_PAY_AMOUNT_NOT_NULL("2030000348", "实际金额不能为空", "APPLY"),
    APPLY_PREMIUM_PAY_IS_EXIST("2030000349", "支付已提交，请勿重复提交", "APPLY"),
    APPLY_COVERAGE_ADD_PREMIUM_ERROR("2030000350", "险种加费查询异常", "APPLY"),
    APPLY_BENEFICIARY_PROPORTION_ERROR("2030000351", "同一顺序受益人的分配比例总和必须100% ", "APPLY"),
    APPLY_BUSINESS_APPLY_ACCEPT_IS_NOT_FOUND_OBJECT("2030000352", "未找到受理信息", "APPLY"),
    APPLY_CONTRACT_PHONE_FORMAT_ERROR("2030000353", "投保单联系电话格式有误", "APPLY"),
    APPLY_CONTRACT_MOBILE_FORMAT_ERROR("2030000354", "本单联系地址联系电话格式有误", "APPLY"),
    APPLY_CONTRACT_MOBILE_IS_NOT_NULL("2030000355", "本单联系地址联系电话不能为空", "APPLY"),
    APPLY_APPLY_DATE_IS_NOT_NULL("2030000356", "投保日期不能为空", "APPLY"),
    APPLY_EFFECTIVE_DATE_IS_NOT_NULL("2030000357", "生效日期不能为空", "APPLY"),
    APPLY_APPROVE_DATE_IS_NOT_NULL("**********", "承保日期不能为空", "APPLY"),
    APPLY_QUERY_UNDERWRITE_ERROR("**********", "获取承保信息异常", "APPLY"),
    APPLY_SAVE_UNDERWRITE_ERROR("**********", "保存承保信息异常", "APPLY"),
    APPLY_UNDERWRITE_ATTACHMENT_IS_NOT_NULL("**********", "保单照片不能为空", "APPLY"),
    APPLY_QUERY_CONFIG_BRANCH_DETAIL_ERROR("**********", "投保机构配置查询异常", "APPLY"),
    APPLY_QUERY_APPLY_HEALTH_NOTICE_ERROR("**********", "查询健康告知异常", "APPLY"),
    APPLY_SAVE_APPLY_HEALTH_NOTICE_ERROR("**********", "保存健康告知异常", "APPLY"),
    APPLY_INPUT_QUESTION_CODE_IS_NOT_NULL("**********", "健康告知问题编码不能为空", "APPLY"),
    APPLY_QUERY_APPLY_HEALTH_NOTICE_IS_NOT_FOUND_OBJECT("**********", "未找到健康告知", "APPLY"),
    APPLY_HEALTH_NOTICE_QUESTION_CODE_FORMAT_ERROR("**********", "健康告知问题编码格式有误", "APPLY"),
    APPLY_IMAGE_TYPE_TOTAL_PAGE_FORMAT_ERROR("**********", "影像件必传总张数格式有误", "APPLY"),
    APPLY_IMAGE_TYPE_TOTAL_PAGE_IS_NOT_NULL("**********", "影像件必传总张数不能为空", "APPLY"),
    APPLY_ATTACHMENT_IS_NO_MORE_THAN_MAX("**********", "影像件数量不能大于最大张数", "APPLY"),
    APPLY_ATTACHMENT_IS_NO_LESS_THAN_MIN("**********", "影像件数量不能小于必传张数", "APPLY"),
    APPLY_ORDER_TASK_FINISH_ERROR("**********", "出单体验任务完成失败", "APP"),
    APPLY_SIGNATURE_USER_LOGIN_ERROR("20211117009", "电子签名用户登录异常", "APPLY"),
    APPLY_PDF_DOWNLOAD_ERROR("20211123001", "pdf下载异常", "APPLY"),
    APPLY_QUERY_IMAGE_ATTACHMENT_ERROR("**********", "影像附件查询异常", "APPLY"),
    APPLY_QUERY_IMAGE_SIGNATURE_INFO_ERROR("20211117001","查询电子签名信息异常","APPLY"),
    APPLY_SAVE_IMAGE_SIGNATURE_INFO_ERROR("20211117002","保存电子签名信息异常","APPLY"),
    APPLY_QUERY_IS_ONESELF_INFO_ERROR("20211117003","查询被保人是否为投保人信息异常","APPLY"),
    APPLY_INPUT_APPLY_COVERAGE_IS_NOT_FOUND_OBJECT("2030000374", "未找到险种信息", "APPLY"),
    APPLY_QUERY_IMAGE_ATTACHMENT_IS_NOT_FOUND_OBJECT("2030000375", "未找到影像附件", "APPLY"),
    APPLY_IMAGE_ATTACHMENT_CODE_FORMAT_ERROR("2030000376", "影像附件编码格式有误", "APPLY"),
    APPLY_IMAGE_ATTACHMENT_CODE_IS_NOT_NULL("2030000377", "影像附件编码不能为空", "APPLY"),
    APPLY_IMAGE_ATTACHMENT_IS_NOT_NULL("**********", "影像附件不能为空", "APPLY"),
    APPLY_IMAGE_ATTACHMENT_REQUIRED_WEIGHT_IS_NOT_NULL("**********", "影像附件必传张数不能为空", "APPLY"),
    APPLY_IMAGE_ATTACHMENT_MAX_WEIGHT_IS_NOT_NULL("**********", "影像附件最大张数不能为空", "APPLY"),
    APPLY_ATTACHMENT_IS_NO_LESS_THAN_REQUIRED_OR_MORE_THAN_MAX("**********", "请上传影像附件!", "APPLY"),
    APPLY_APP_APPLICANT_ACCOUNT_NO_FORMAT_ERROR("**********", "投保人银行账号格式有误", "APPLY"),
    APPLY_QUERY_APP_PAGE_JUMP_ERROR("**********", "查询页面跳转信息异常", "APPLY"),
    APPLY_BUSINESS_APPLY_PLAN_IS_NOT_FOUND_OBJECT("**********", "未找到投保单计划书", "APPLY"),
    APPLY_BUSINESS_INSURED_BIRTHDAY_IS_NOT_EQUALS_PLAN("**********", "被保人年龄与计划书中不一致", "APPLY"),
    APPLY_BUSINESS_INSURED_SEX_IS_NOT_EQUALS_PLAN("**********", "被保人性别与计划书中不一致", "APPLY"),
    APPLY_BUSINESS_APPLY_PREMIUM_IS_NOT_EQUALS_PLAN("**********", "投保单保费与计划书保费不一致，请确认投被保人信息是否正确", "APPLY"),
    APPLY_APPLY_UNDERWRITE_ERROR("**********", "申请承保出错", "APPLY"),
    APPLY_APPLY_CONFIRM_ERROR("**********", "保障计划确认出错", "APPLY"),
    APPLY_PLAN_RECEIVABLE_PREMIUM_ERROR("**********", "计划书费用为空", "APPLY"),
    APPLY_APPLY_RECEIVABLE_PREMIUM_ERROR("**********", "保费为空", "APPLY"),
    APPLY_APPLY_ID_EXP_DATE_ERROR("2030000392", "证件有效期不能小于当天", "APPLY"),
    APPLY_PARAMETER_LANGUAGE_IS_NOT_NULL("2030000393", "语言不能为空", "APPLY"),
    APPLY_PARAMETER_LANGUAGE_FORMAT_INVALID("2030000393", "语言格式化错误", "APPLY"),
    APPLY_GENERATE_APPLY_PDF_ERROR("2030000394", "生成投保单PDF附件出错", "APPLY"),
    APPLY_QUERY_APPLY_SOURCE_ERROR("2030000395", "查询投保单来源异常", "APPLY"),
    APPLY_PARAMETER_APPLY_QUESTION_FLOW_ID_IS_NOT_NULL("2030000396", "投保单异常问题ID不能为空", "APPLY"),
    APPLY_APPLY_HAVE_WAIT_UNDERWRITING("2030000397", "投保单已经申请承保", "APPLY"),
    APPLY_PREMIUM_PAY_HAVE_FINISH("2030000398", "支付已完成，请勿重复支付", "APPLY"),
    APPLY_PREMIUM_PAY_HAVE_SUCCESS("2030000399", "支付已成功，请勿重复支付", "APPLY"),
    APPLY_UNDERWRITE_DECISION_REVIEW_COMMENT_IS_NOT_NULL("2030000400", "审核意见不能为空", "APPLY"),
    APPLY_PLAN_AGENT_NO_SIGN("2030000401", "业务员未签约,不能产生计划书", "APPLY"),
    APPLY_APPLY_AGENT_NO_SIGN("2030000402", "业务员未签约,不能产生投保单", "APPLY"),
    APPLY_QUERY_PREMIUM_PAY_ERROR("2030000403", "查询支付信息出错", "APPLY"),
    APPLY_INPUT_APPLICANT_COMPANY_INDUSTRY_IS_NOT_NULL("2030000404", "所属行业不能为空", "APPLY"),
    APPLY_INPUT_COMPANY_CONTRACT_ID_TYPE_IS_NOT_NULL("2030000405", "投保人代表(单位联系人)证件类型不能为空", "APPLY"),
    APPLY_INPUT_COMPANY_CONTRACT_NAME_IS_NOT_NULL("2030000406", "单位联系人姓名不能为空", "APPLY"),
    APPLY_INPUT_COMPANY_CONTRACT_MOBILE_IS_NOT_NULL("2030000407", "单位联系人手机号码不能为空", "APPLY"),
    APPLY_INPUT_COMPANY_CONTRACT_MOBILE_FORMAT_ERROR("2030000408", "单位联系人手机号码格式有误", "APPLY"),
    APPLY_INPUT_COMPANY_CONTRACT_OFFICE_NUMBER_FORMAT_ERROR("2030000408", "单位联系人办公号码格式有误", "APPLY"),
    APPLY_INPUT_COMPANY_MOBILE_FORMAT_ERROR("2030000408", "单位手机号码格式有误", "APPLY"),
    APPLY_INPUT_COMPANY_CONTRACT_ID_TYPE_FORMAT_ERROR("2030000409", "单位联系人证件类型格式有误", "APPLY"),
    APPLY_COMPANY_AREA_CODE_FORMAT_ERROR("2030000410", "单位地址编码格式有误", "APPLY"),
    APPLY_COMPANY_LEGAL_PERSON_ID_TYPE_FORMAT_ERROR("2030000411", "单位法人证件类型格式有误", "APPLY"),
    APPLY_INPUT_APPLICANT_COMPANY_NAME_IS_NOT_NULL("2030000412", "单位名称不能为空", "APPLY"),
    APPLY_INPUT_APPLICANT_COMPANY_ID_TYPE_IS_NOT_NULL("2030000413", "单位证件类型不能为空", "APPLY"),
    APPLY_INPUT_APPLICANT_COMPANY_ID_TYPE_FORMAT_ERROR("2030000414", "单位证件类型格式有误", "APPLY"),
    APPLY_INPUT_APPLICANT_COMPANY_ID_NO_IS_NOT_NULL("2030000415", "单位证件号码不能为空", "APPLY"),
    APPLY_QUERY_PRODUCT_ATTACHMENT_IS_NOT_FOUND_OBJECT("2030000416", "未找到产品附件", "APPLY"),
    APPLY_QUERY_APPLY_SPECIAL_CONTRACT_ERROR("2030000417", "查询特约信息出错", "APPLY"),
    APPLY_SAVE_APPLY_SPECIAL_CONTRACT_ERROR("2030000418", "保存特约信息出错", "APPLY"),
    APPLY_SPECIAL_CONTRACT_TYPE_CODE_IS_NOT_NULL("2030000419", "特别约定类型编码不能为空", "APPLY"),
    APPLY_SPECIAL_CONTRACT_CONTENT_IS_NOT_NULL("2030000420", "特别约定内容不能为空", "APPLY"),
    APPLY_QUERY_CONTRACT_ATTACHMENT_IS_NOT_FOUND_OBJECT("2030000421", "未找到合同影像", "APPLY"),
    APPLY_QUERY_APPLY_PAYMENT_INFO_IS_NOT_NULL("2030000422", "未找到缴费信息", "APPLY"),
    APPLY_DELETE_APPLY_ERROR("2030000423", "删除投保单异常", "APPLY"),
    APPLY_PAYMENT_CONFIRM_PAYMENT_ERROR("2030000424", "确认支付结果异常", "APPLY"),
    APPLY_PAYMENT_HAND_PAYMENT_NOTIFY_ERROR("2030000425", "处理支付回调异常", "APPLY"),
    APPLY_APPLICANT_VERIFY_CODE_ERROR("2030000426", "验证码出错", "APPLY"),
    APPLY_PAYMENT_ITEM_IS_NOT_NULL("2030000427", "支付项不可为空", "APPLY"),
    APPLY_PAYMENT_TYPE_CODE_IS_NOT_NULL("2030000428", "支付类型不可为空", "APPLY"),
    APPLY_PAYMENT_METHOD_CODE_NOT_NULL("2030000429", "支付方式不可为空", "APPLY"),
    APPLY_BUSINESS_APPLY_PREMIUM_IS_NOT_EQUALS_PAYMENT_AMOUNT("2030000430", "实际支付金额与应收保费不一致", "APPLY"),
    APPLY_APPLY_RECEIVABLE_PREMIUM_IS_NOT_NULL("2030000431", "应收保费不能为空", "APPLY"),
    APPLY_PLAN_ROLLBACK_ERROR("2030000432", "计划书收件人回滚异常", "APPLY"),
    APPLY_PAYMENT_AMOUNT_IS_NO_LESS_THAN_ZERO("2030000433", "支付金额不能为负数", "APPLY"),
    APPLY_POLICY_UNDERWRITE_PASSWORD_IS_NOT_NULL("2030000434", "承保的密码不能为空", "APPLY"),
    APPLY_POLICY_UNDERWRITE_PASSWORD_IS_ERROR("2030000435", "承保的密码不正确", "APPLY"),
    APPLY_IS_NO_NEED_REUNDERWRITE("2030000436", "投保单状态不是承保失败", "APPLY"),
    APPLY_QUERY_APPLY_LIST_ERROR("2030000437", "查询投保单类型异常", "APPLY"),
    APPLY_QUERY_APPLY_GET_CODE_ERROR("**********", "查询数据字典异常", "APPLY"),
    APPLY_SAVE_APPLY_ABANDONED_ERROR("**********", "保存投保单作废出错", "APPLY"),
    APPLY_SAVE_APPLY_ABANDONED_IS_EXIST("**********", "该投保单已作废，请勿重复操作", "APPLY"),
    APPLY_BUSINESS_APPLY_CAN_NOT_ABANDONED("**********", "该投保单不能作废", "APPLY"),
    APPLY_QUERY_APPLY_ABANDONED_ERROR("**********", "查询投保单作废列表异常", "APPLY"),
    APPLY_QUERY_APPLY_HEALTH_QUESTIONNAIRE_REMARK_ERROR("**********", "查询投保单健康告知备注出错", "APPLY"),
    APPLY_QUERY_APPLY_AGENT_MOBILE_SAME_ERROR("**********", "客户疑似业务员，信息不一致", "APPLY"),
    APPLY_GENERATE_APPLY_RECEIPT_BOOK_ERROR("**********", "生成投保单收据PDF附件出错", "APPLY"),
    APPLY_EBAO_NOTIFY_SAVE_ERROR("**********", "保存易保通知数据错误", "APPLY"),
    APPLY_EBAO_ADVICE_SAVE_ERROR("**********", "保存易保计划书数据错误", "APPLY"),
    APPLY_EBAO_BUSINESS_ADVICE_IS_NOT_FOUND("**********", "未找到计划书", "APPLY"),
    APPLY_APPLY_SIGN_BRANCH_ID_IS_NOT_NULL("**********", "签约机构不能为空", "APPLY"),
    APPLY_APP_BENEFICIAL_IS_NOT_NULL_FORMAT_ERROR("**********", "受益人不能为空或者格式有误", "APPLY"),
    APPLY_APP_BENEFICIAL_IS_NOT_FOUND_OBJECT("**********", "未找到受益人", "APPLY"),
    APPLY_IMAGE_ATTACHMENT_FORMAT_ERROR("**********", "附件格式有误", "APPLY"),
    APPLY_CUSTOMER_IMAGE_ATTACHMENT_IS_NOT_NULL("2030000453", "客户合照和客户视频至少上传一个", "APPLY"),
    APPLY_INPUT_DELEGATE_NAME_IS_NOT_NULL("2030000454", "投保人代表姓名不能为空", "APPLY"),
    APPLY_INPUT_DELEGATE_ID_NO_IS_NOT_NULL("2030000455", "投保人代表证件号码不能为空", "APPLY"),
    APPLY_INPUT_DELEGATE_MOBILE_IS_NOT_NULL("**********", "投保人代表手机号码不能为空", "APPLY"),
    APPLY_INPUT_DELEGATE_MOBILE_IS_FORMAT_ERROR("2030000457", "投保人代表手机号码格式有误", "APPLY"),
    APPLY_INPUT_DELEGATE_ID_TYPE_IS_NOT_NULL("2030000458", "投保人代表证件类型不能为空", "APPLY"),
    APPLY_INPUT_DELEGATE_ID_TYPE_FORMAT_ERROR("2030000459", "投保人代表证件类型格式有误", "APPLY"),
    APPLY_INPUT_APPLICANT_COMPANY_CONTRACT_EMAIL_FORMAT_ERROR("2030000460", "单位联系人邮箱格式有误", "APPLY"),
    APPLY_INPUT_APPLICANT_COMPANY_EMAIL_FORMAT_ERROR("2030000460", "单位邮箱格式有误", "APPLY"),
    APPLY_INPUT_MISSING_IMAGES_ATTACHMENT("2030000461", "缺少影像件,请补充完善!", "APPLY"),
    APPLY_SAVE_PRINT_INVOICE_TYPE_IS_NOT_NULL("2030000462", "发票类型不能为空", "APPLY"),
    APPLY_SAVE_PRINT_COMPANY_NAME_IS_NOT_NULL("2030000463", "发票单位名称不能为空", "APPLY"),
    APPLY_SAVE_PRINT_TAX_REGISTRATION_NO_IS_NOT_NULL("2030000464", "发票税务登记号不能为空", "APPLY"),
    APPLY_SAVE_TAX_REGISTRATION_NO_IS_NOT_NULL("2030000464", "税务登记号不能为空", "APPLY"),
    APPLY_SAVE_BASIC_FREE_COVER_LIMIT_IS_NOT_NULL("2030000464", "免体检限额不能为空", "APPLY"),
    APPLY_SAVE_BASIC_FREE_COVER_LIMIT_FORMAT_ERROR("2030000464", "免体检限额格式有误", "APPLY"),


    //APPLY_BASE
    APPLY_BASE_PARAMETER_APPLY_ID_IS_NOT_NULL("2030000465", "投保单ID不能为空", "APPLY_BASE"),
    APPLY_BASE_PARAMETER_INSURED_BATCH_ID_IS_NOT_NULL("2030000466", "被保人上传批次ID不能为空", "APPLY_BASE"),
    APPLY_BASE_PARAMETER_APPLY_COVERAGE_INSURED_BOTH_NULL("2030000467", "险种保存参数险种与被保人不能同时为空", "APPLY_BASE"),
    APPLY_BASE_PARAMETER_APPLY_IS_NOT_NULL("2030000468", "投保单信息不能为空", "APPLY_BASE"),
    APPLY_BASE_PARAMETER_APPLY_NOT_EXISTENT("2030000468", "投保单信息不存在", "APPLY_BASE"),
    APPLY_BASE_PARAMETER_APPLY_COVERAGE_IS_NOT_NULL("2030000469", "投保单险种信息不能为空", "APPLY_BASE"),
    APPLY_BASE_PARAMETER_APPLY_INSURED_IS_NOT_NULL("2030000470", "投保单被保人信息不能为空", "APPLY_BASE"),
    APPLY_BASE_PARAMETER_APPLY_APPLICANT_IS_NOT_NULL("2030000471", "投保单投保人信息不能为空", "APPLY_BASE"),
    APPLY_BASE_PARAMETER_APPLY_PAYMENT_TRANSACTION_IS_NOT_NULL("2030000472", "投保单保费支付信息不能为空", "APPLY_BASE"),
    APPLY_BASE_PARAMETER_UNDERWRITE_TASK_ID_IS_NOT_NULL("2030000473", "核保任务ID不能为空", "APPLY_BASE"),
    APPLY_BASE_PARAMETER_UNDERWRITE_DECISION_CODE_IS_NOT_NULL("2030000474", "核保决定编码不能为空", "APPLY_BASE"),
    APPLY_BASE_PARAMETER_UNDERWRITE_DECISION_REMARK_IS_NOT_NULL("2030000475", "审核意见不能为空", "APPLY_BASE"),
    APPLY_BASE_PARAMETER_ADD_PREMIUM_REMARK_IS_NOT_NULL("**********", "加费说明不能为空", "APPLY_BASE"),
    APPLY_BASE_PARAMETER_AGENT_ID_IS_NOT_NULL("2030000477", "业务员ID不能为空", "APPLY_BASE"),
    APPLY_BASE_PARAMETER_INSURED_DUPLICATION("2030000478", "被保人重复", "APPLY_BASE"),
    APPLY_BASE_PARAMETER_INSURED_ID_IS_NOT_NULL("2030000479", "被保人ID不能为空", "APPLY_BASE"),
    APPLY_BASE_PARAMETER_PAYMENT_TRANSACTION_ID_IS_NOT_NULL("2030000480", "支付事务ID不能为空", "APPLY_BASE"),
    APPLY_BASE_QUERY_APPLY_ERROR("2030000481", "查询投保单信息出错", "APPLY_BASE"),
    APPLY_BASE_QUERY_APPLY_APPLICANT_ERROR("**********", "查询投保人信息出错", "APPLY_BASE"),
    APPLY_BASE_QUERY_APPLY_ATTACHMENT_ERROR("**********", "查询投保单附件列表出错", "APPLY_BASE"),
    APPLY_BASE_QUERY_APPLY_COVERAGE_ERROR("**********", "查询投保单险种列表出错", "APPLY_BASE"),
    APPLY_BASE_QUERY_APPLY_INSURED_ERROR("**********", "查询投保单被保人列表出错", "APPLY_BASE"),
    APPLY_BASE_QUERY_APPLY_ACCOUNT_ERROR("**********", "查询投保单账户信息出错", "APPLY_BASE"),
    APPLY_BASE_QUERY_APPLY_AGENT_ERROR("**********", "查询投保单业务员信息出错", "APPLY_BASE"),
    APPLY_BASE_QUERY_APPLY_INSURED_COLLECT_ERROR("**********", "查询被保人统计信息出错", "APPLY_BASE"),
    APPLY_BASE_QUERY_APPLY_CONTACT_INFO_ERROR("**********", "查询投保单联系信息出错", "APPLY_BASE"),
    APPLY_BASE_QUERY_APPLY_PREMIUM_ERROR("**********", "查询投保单缴费信息出错", "APPLY_BASE"),
    APPLY_BASE_QUERY_APPLY_QUESTION_FLOW_ERROR("**********", "查询问题件信息异常", "APPLY_BASE"),
    APPLY_BASE_QUERY_APPLY_SPECIAL_CONTRACT_ERROR("**********", "查询特别约定信息异常", "APPLY_BASE"),
    APPLY_BASE_QUERY_APPLY_REMARK_ERROR("**********", "查询备注信息异常", "APPLY_BASE"),
    APPLY_BASE_QUERY_APPLY_INSURED_UPLOAD_ERROR("**********", "查询被保人清单上传表异常", "APPLY_BASE"),
    APPLY_BASE_QUERY_APPLY_ADD_PREMIUM_ERROR("2030000495", "查询投保单加费信息出错", "APPLY_BASE"),
    APPLY_BASE_QUERY_APPLY_UNDERWRITE_TASK_ERROR("2030000496", "查询核保任务出错", "APPLY_BASE"),
    APPLY_BASE_QUERY_BASE_UNDERWRITE_DECISION_ERROR("2030000497", "查询基础核保决定出错", "APPLY_BASE"),
    APPLY_BASE_QUERY_APPLY_PAYMENT_ATTACHMENT_ERROR("2030000498", "查询投保单支付附件出错", "APPLY_BASE"),
    APPLY_BASE_QUERY_APPLY_PAYMENT_TRANSACTION_ERROR("2030000499", "查询投保单支付事务出错", "APPLY_BASE"),
    APPLY_BASE_QUERY_APPLY_INSURED_EXTEND_ERROR("2030000500", "查询被保人拓展信息出错", "APPLY_BASE"),
    APPLY_BASE_QUERY_APPLY_PAYMENT_TRANSACTION_ITEM_ERROR("2030000501", "查询投保单支付事务项目出错", "APPLY_BASE"),
    APPLY_BASE_QUERY_APPLY_COVERAGE_ACCEPT_ERROR("2030000502", "查询受理险种信息出错", "APPLY_BASE"),
    APPLY_BASE_BUSINESS_APPLY_IS_NOT_FOUND_OBJECT("2030000503", "未找到投保单信息", "APPLY_BASE"),
    APPLY_BASE_BUSINESS_APPLY_COVERAGE_IS_NOT_FOUND_OBJECT("2030000504", "未找到投保单险种信息", "APPLY_BASE"),
    APPLY_BASE_BUSINESS_APPLY_CALCULATE_PREMIUM_ERROR("2030000505", "投保单算费异常", "APPLY_BASE"),
    APPLY_BASE_BUSINESS_APPLY_PREMIUM_IS_NOT_FOUNT_OBJECT("2030000506", "未找到投保单保费信息", "APPLY_BASE"),
    APPLY_BASE_BUSINESS_PRODUCT_INFO_IS_NOT_FOUNT_OBJECT("2030000507", "未找到产品信息", "APPLY_BASE"),
    APPLY_BASE_BUSINESS_APPLY_APPLICANT_IS_NOT_FOUND_OBJECT("2030000508", "未找到投保人信息", "APPLY_BASE"),
    APPLY_BASE_BUSINESS_APPLY_UNDER_WRITE_TASK_IS_NOT_FOUNT_OBJECT("2030000509", "未找到核保任务", "APPLY_BASE"),
    APPLY_BASE_BUSINESS_SAVE_POLICY_DATA_ERROR("2030000510", "承保异常", "APPLY_BASE"),
    APPLY_BASE_BUSINESS_BALANCE_IS_NOT_ENOUGH_TO_PAY("2030000511", "账户余额不足以支付", "APPLY_BASE"),
    APPLY_BASE_BUSINESS_REPEAT_PAYMENT_ERROR("2030000512", "已经发起支付，请勿重复支付", "APPLY_BASE"),
    APPLY_BASE_BUSINESS_PAYMENT_SUCCESS_ERROR("2030000513", "已经支付成功，请勿重复支付", "APPLY_BASE"),
    APPLY_BASE_SAVE_APPLY_ERROR("2030000514", "保存投保单基本信息出错", "APPLY_BASE"),
    APPLY_BASE_SAVE_APPLY_COVERAGE_ERROR("2030000515", "保存投保单险种信息出错", "APPLY_BASE"),
    APPLY_BASE_SAVE_APPLY_APPLICANT_ERROR("2030000516", "保存投保单投保人信息出错", "APPLY_BASE"),
    APPLY_BASE_SAVE_APPLY_ATTACHMENT_ERROR("2030000517", "保存投保单附件信息出错", "APPLY_BASE"),
    APPLY_BASE_SAVE_APPLY_INSURED_COLLECT_ERROR("2030000518", "保存投保单被保人统计信息出错", "APPLY_BASE"),
    APPLY_BASE_SAVE_APPLY_REMARK_ERROR("2030000519", "保存投保单备注信息出错", "APPLY_BASE"),
    APPLY_BASE_SAVE_APPLY_INSURED_BATCH_ERROR("2030000520", "保存投保单被保人批次信息出错", "APPLY_BASE"),
    APPLY_BASE_SAVE_APPLY_INSURED_UPLOAD_ERROR("2030000521", "保存投保单被保人上传信息出错", "APPLY_BASE"),
    APPLY_BASE_SAVE_APPLY_INSURED_ERROR("2030000522", "保存投保单被保人信息出错", "APPLY_BASE"),
    APPLY_BASE_SAVE_APPLY_INSURED_EXTEND_ERROR("2030000523", "保存投保单被保人拓展信息出错", "APPLY_BASE"),
    APPLY_BASE_SAVE_APPLY_INSURED_DONE_ERROR("2030000524", "保存投保单被保人完成信息出错", "APPLY_BASE"),
    APPLY_BASE_SAVE_APPLY_PREMIUM_ERROR("2030000525", "保存投保单保费信息出错", "APPLY_BASE"),
    APPLY_BASE_SAVE_APPLY_CONTACT_INFO_ERROR("2030000526", "保存投保单联系信息出错", "APPLY_BASE"),
    APPLY_BASE_SAVE_APPLY_SPECIAL_CONTRACT_ERROR("2030000527", "保存特别约定信息异常", "APPLY_BASE"),
    APPLY_BASE_SAVE_APPLY_APPLY_PAYMENT_TRANSACTION_ERROR("2030000528", "保存保费支付事务异常", "APPLY_BASE"),
    APPLY_BASE_SAVE_APPLY_POSTPONED_DECISION_ERROR("2030000529", "保存核保延期决定出错", "APPLY_BASE"),
    APPLY_BASE_SAVE_APPLY_AMOUNT_DECISION_ERROR("2030000530", "保存核保保额决定出错", "APPLY_BASE"),
    APPLY_BASE_SAVE_APPLY_EXCLUSION_DECISION_ERROR("2030000531", "保存核保除外决定出错", "APPLY_BASE"),
    APPLY_BASE_SAVE_APPLY_ADD_PREMIUM_DECISION_ERROR("2030000532", "保存核保加费决定出错", "APPLY_BASE"),
    APPLY_BASE_SAVE_APPLY_UNDERWRITE_DECISION_ERROR("2030000533", "保存核保决定出错", "APPLY_BASE"),
    APPLY_BASE_SAVE_APPLY_UNDERWRITE_TASK_ERROR("2030000534", "保存核保任务出错", "APPLY_BASE"),
    APPLY_BASE_SAVE_APPLY_AGENT_ERROR("2030000535", "保存投保单业务员信息出错", "APPLY_BASE"),
    APPLY_BASE_SAVE_APPLY_APPLY_PAYMENT_TRANSACTION_ITEM_ERROR("2030000536", "保存保费支付事务项目异常", "APPLY_BASE"),
    APPLY_BASE_SAVE_APPLY_ADD_PREMIUM_ERROR("2030000537", "保存投保单加费信息异常", "APPLY_BASE"),
    APPLY_BASE_QUERY_APPLY_ADD_PREMIUM_FORMAT_ERROR("2030000538", "加费信息不完整", "APPLY_BASE"),
    APPLY_BASE_QUERY_APPLY_UNDERWRITE_DECISION_SUBSTANDARD_FORMAT_ERROR("2030000539", "次标准体的录入信息不完整", "APPLY_BASE"),
    APPLY_BASE_QUERY_WORKFLOW_SERVICE_FAIL("2030000540", "请求工作流微服务异常", "APPLY_BASE"),
    APPLY_BASE_QUERY_PAYMENT_FAIL("2030000541", "调用收付费服务异常", "APPLY_BASE"),
    APPLY_BASE_QUERY_PAYMENT_STATUS_FAIL("**********", "调用收付费服务支付状态为空", "APPLY_BASE"),
    APPLY_BASE_SAVE_APPLY_PAYMENT_ATTACHMENT_ERROR("**********", "保存投保单支付附件出错", "APPLY_BASE"),
    APPLY_BASE_APPLY_PAYMENT_ATTACHMENT_IS_NOT_NULL("**********", "投保单支付附件不能为空", "APPLY_BASE"),
    APPLY_BASE_SAVE_APPLY_ABANDONED_ERROR("**********", "保存投保单作废出错", "APPLY_BASE"),
    APPLY_BASE_SAVE_APPLY_HEALTH_QUESTIONNAIRE_REMARK_ERROR("**********", "保存投保单健康告知备注出错", "APPLY_BASE"),
    APPLY_DATE_OF_BIRTH_DOES_NOT_MATCH_REQUIREMENT("**********", "出生日期不符合要求", "APPLY_BASE"),
    APPLY_APPLY_COVERAGE_DUTY_IS_NOT_NULL("**********", "险种责任不能为空", "APPLY"),
    AGENT_ID_EXPIRED_ERROR("**********", "您的证件已到期，请先登录APP更新证件方可出单", "AGENT"),

    APPLY_INPUT_DELEGATE_BIRTHDAY_IS_NOT_NULL("**********", "投保人代表出生日期不能为空", "APPLY"),
    APPLY_BASE_BUSINESS_BENEFICIAL_NO_ERROR("**********", "请先维护上级顺序的受益人", "APPLY_BASE"),
    APPLY_BASE_BUSINESS_ONLY_FIRST_BENEFICIARY_ERROR("**********", "该产品的受益人只能选择第一顺位", "APPLY_BASE"),
    APPLY_APP_BENEFICIAL_RELATIONSHIP_INSTRUCTIONS_IS_NOT_NULL("**********", "请补充与被保人关系", "APPLY"),

    APPLY_BASE_PARAMETER_RATINGS_NAME_IS_NOT_NULL("**********", "加费要素不能为空", "APPLY_BASE"),
    APPLY_BASE_PARAMETER_ADD_PREMIUM_OBJECT_CODE_IS_NOT_NULL("**********", "加费类型不能为空", "APPLY_BASE"),
    APPLY_BASE_PARAMETER_TOTAL_ADD_PREMIUM_IS_NOT_NULL("**********", "加费金额不能为空", "APPLY_BASE"),
    APPLY_BASE_PARAMETER_ADD_PREMIUM_PERIOD_IS_NOT_NULL("**********", "加费期限不能为空", "APPLY_BASE"),
    APPLY_BASE_PARAMETER_ADD_PREMIUM_PERIOD_FORMAT_ERROR("**********", "加费期限格式有误", "APPLY_BASE"),
    APPLY_BASE_PARAMETER_ADD_PREMIUM_PERIOD_CANNOT_EXCEED_THE_PREMIUM_PERIOD("**********", "加费期限不能超过险种缴费周期", "APPLY_BASE"),
    APPLY_APP_ADDRESS_TYPE_IS_NOT_NULL("2030000242", "地址类型不能为空", "APPLY"),
    APPLY_OCCUPATION_NATURE_IS_NOT_NULL("2030000242", "职业性质不能为空", "APPLY"),
    APPLY_OCCUPATION_NATURE_OCCUPATION_IS_NOT_NULL("2030000242", "职业不能为空", "APPLY"),
    APPLY_OCCUPATION_NATURE_EXACTDUTIES_IS_NOT_NULL("2030000242", "准确职责不能为空", "APPLY"),
    APPLY_BASE_PARAMETER_EM_FORMAT_ERROR("2030000242", "EM必须是25的倍数", "APPLY"),
    APPLY_CANNOT_BE_OPERATED_AUTO_UW_ERROR("2030000242", "当前投保单不能操作自核", "APPLY"),
    APPLY_CANNOT_BE_OPERATED_CHANGE_PREMIUM_FREQUENCY_ERROR("2030000242", "当前投保单不能操作缴费周期变更", "APPLY"),
    APPLY_CANNOT_BE_OPERATED_CHANGE_PREMIUM_FREQUENCY_BY_PAYMENT_ERROR("2030000242", "该投保单已完成预交保费支付，不能操作缴费周期变更", "APPLY"),
    APPLY_CANNOT_BE_OPERATED_APPLY_CHANGE_BY_PAYMENT_ERROR("2030000242", "该投保单已完成保费支付，不能操作投保单变更", "APPLY"),
    APPLY_CANNOT_BE_OPERATED_APPLY_CHANGE_BY_GROUP_ERROR("2030000242", "团险投保单不能操作缴费周期变更", "APPLY"),
    APPLY_BUSINESS_PREMIUM_FREQUENCY_NO_MODIFY("2240002021", "投保单缴费周期变更前后一致", "APPLY"),
    APPLY_CUSTOMER_HAS_UPLOADED_THE_PAYMENT_VOUCHER("2030000242", "当前客户已上传支付凭证，请先完成预缴保费确认!", "APPLY"),
    APPLY_APPLY_TOTAL_PREMIUM_IS_NOT_NULL("2030000431", "保费不能为空", "APPLY"),
    APPLY_APPLY_AMOUNT_IS_NOT_NULL("2030000431", "保额不能为空", "APPLY"),
    APPLY_APPLY_CHANGE_ID_IS_NOT_NULL("2030000431", "投保单变更ID不能为空", "APPLY"),
    APPLY_CHANGE_TYPE_IS_NOT_NULL("2030000431", "变更类型不能为空", "APPLY"),
    APPLY_CHANGE_CONCLUSION_IS_NOT_NULL("2030000431", "审核结论不能为空", "APPLY"),
    APPLY_PREMIUM_FREQUENCY_IS_NOT_NULL("2030000431", "缴费周期不能为空", "APPLY"),
    APPLY_EXTENDED_PAYMENT_DUE_TIME_IS_NOT_NULL("2030000431", "支付到期延长时间不能为空", "APPLY"),
    APPLY_HAS_APPLIED_EXTENDED_PAYMENT_DUE_TIME_ERROR("2030000431", "该投保单已申请支付到期日期延长", "APPLY"),
    APPLY_APPLY_CHANGE_ID_IS_NOT_FOUND_ERROR("2030000431", "未找到投保单变更记录", "APPLY"),
    APPLY_PRINT_INFO_SAVE_ERROR("20220413","保存投保单打印信息出错","APPLY"),
    APPLY_PRINT_INFO_QUERY_ERROR("20220413","查询投保单打印信息出错","APPLY"),
    APPLY_PRINT_ID_IS_NOT_NULL("2030000003", "投保单打印ID不能为空", "APPLY"),
    APPLY_BUSINESS_APPLY_PRINT_INFO_IS_NOT_FOUND_OBJECT("20220413","未找到投保单打印信息", "APPLY"),
    APPLY_HOLDER_RELATIONSHIP_IS_NOT_NULL("2030000308", "与被保人关系不能为空", "APPLY"),
    APPLY_HOLDER_IS_NOT_ADULT("2030000308", "保单持有人不能小于18岁", "APPLY"),
    APPLY_HOLDER_IS_REPEAT("2030000308", "投保人已默认为保单持有人，请重新录入或跳过", "APPLY"),
    AGENT_AGENT_NAME_IS_NOT_NULL("2010000248", "姓名不能为空", "AGENT"),
    AGENT_AGENT_BIRTHDAY_IS_NOT_NULL("2010000250", "出生日期不能为空", "AGENT"),
    AGENT_ID_SEX_IS_NOT_NULL("2010000425", "性别不能为空", "AGENT"),
    AGENT_AGENT_ID_NO_IS_NOT_NULL("2010000249", "证件号码不能为空", "AGENT"),
    AGENT_ID_TYPE_CODE_IS_NOT_NULL("2010000456", "证件类型不能为空", "AGENT"),
    AGENT_VALIDITY_OF_CERTIFICATE_IS_NOT_NULL("2010000441", "证件有效期日期不能为空", "AGENT"),
    APPLY_PRINT_INSURED_LIST_IS_NOT_FOUND_ERROR("2010000442", "找不到被保人清单", "APPLY"),
    APPLY_SEND_BACK_ERROR("**********", "投保单回退异常", "APPLY"),

    // #29产品错误枚举
    APPLY_PRO29_SCHOOL_NAME_IS_NOT_NULL("2010000442", "学校名称不能为空", "APPLY"),
    APPLY_PRO29_SCHOOL_NUMBER_IS_NOT_NULL("2010000442", "学校登记号码不能为空", "APPLY"),
    APPLY_PRO29_SALES_PLAN_IS_NOT_NULL("2010000442", "销售方式不能为空", "APPLY"),
    APPLY_PRO29_SCHOOL_PROPERTIES_IS_NOT_NULL("2010000442", "学校性质不能为空", "APPLY"),
    APPLY_PRO29_SCHOOL_TYPE_IS_NOT_NULL("2010000442", "学校类型不能为空", "APPLY"),
    APPLY_PRO29_PRE_SCHOOL_NUM_IS_NOT_NULL("2010000442", "幼儿园人数不能为空", "APPLY"),
    APPLY_PRO29_PRIMARY_NUM_IS_NOT_NULL("2010000442", "小学人数不能为空", "APPLY"),
    APPLY_PRO29_SECONDARY_NUM_IS_NOT_NULL("2010000442", "初中/高中人数不能为空", "APPLY"),
    APPLY_PRO29_UNIVERSITY_NUM_IS_NOT_NULL("2010000442", "大学人数不能为空", "APPLY"),
    APPLY_PRO29_PRE_SCHOOL_AMOUNT_IS_NOT_NULL("2010000442", "幼儿园保险金额不能为空", "APPLY"),
    APPLY_PRO29_PRIMARY_AMOUNT_IS_NOT_NULL("2010000442", "小学保险金额不能为空", "APPLY"),
    APPLY_PRO29_SECONDARY_AMOUNT_IS_NOT_NULL("2010000442", "初中/高中保险金额不能为空", "APPLY"),
    APPLY_PRO29_UNIVERSITY_AMOUNT_IS_NOT_NULL("2010000442", "大学保险金额不能为空", "APPLY"),
    APPLY_PRO29_PARTICIPATION_NUM_IS_NOT_NULL("2010000442", "学生参保的人数不能为空", "APPLY"),
    APPLY_PRO29_STUDENTS_NUM_IS_NOT_NULL("2010000442", "学校学生的总数不能为空", "APPLY"),
    APPLY_PRO29_PARTICIPATION_PERCENTAGE_IS_NOT_NULL("2010000442", "参保百分比不能为空", "APPLY"),
    APPLY_PRO29_SCHOOL_ADDRESS_IS_NOT_NULL("2010000442", "学校地址不能为空", "APPLY"),
    APPLY_PRO29_DELEGATE_NAME_IS_NOT_NULL("2010000442", "学校代表姓名不能为空", "APPLY"),
    APPLY_PRO29_DELEGATE_BIRTHDAY_IS_NOT_NULL("2010000442", "学校代表出生日期不能为空", "APPLY"),
    APPLY_PRO29_DELEGATE_POSITION_IS_NOT_NULL("2010000442", "学校代表职位不能为空", "APPLY"),
    APPLY_PRO29_DELEGATE_MOBILE_IS_NOT_NULL("2010000442", "学校代表联系电话不能为空", "APPLY"),
    APPLY_PRO29_DELEGATE_NATIONALITY_IS_NOT_NULL("2010000442", "学校代表国籍不能为空", "APPLY"),
    APPLY_PRO29_DELEGATE_ID_TYPE_IS_NOT_NULL("2010000442", "学校代表证件类型不能为空", "APPLY"),
    APPLY_PRO29_DELEGATE_ID_NO_IS_NOT_NULL("2010000442", "学校代表证件号码不能为空", "APPLY"),
    APPLY_PRO29_DELEGATE_SEX_IS_NOT_NULL("2010000442", "学校代表性别不能为空", "APPLY"),
    APPLY_PRO29_DELEGATE_EMAIL_IS_NOT_NULL("2010000442", "学校代表邮箱不能为空", "APPLY"),
    APPLY_PRO29_DELEGATE_EMAIL_FORMAT_ERROR("2030000460", "学校代表邮箱邮箱格式有误", "APPLY"),
    APPLY_PRO29_DELEGATE_MOBILE_FORMAT_ERROR("2010000442", "学校代表联系电话格式有误", "APPLY"),
    APPLY_PRO29_DELEGATE_BIRTHDAY_FORMAT_ERROR("2010000442", "学校代表出生日期格式有误", "APPLY"),
    ONLINE_NAME_IS_NOT_NULL("2010000248", "姓名不能为空", "APPLY"),
    ONLINE_MOBILE_IS_NOT_NULL("2010000248", "号码不能为空", "APPLY"),
    ONLINE_EMAIL_IS_NOT_RIGHT("2010000248", "邮箱格式有误", "APPLY"),
    ONLINE_SAVE_LATER_ERROR("2010000248", "网销暂存失败", "APPLY"),
    ONLINE_MOBILE_FORMAT_ERROR("2030000248", "手机号码格式有误", "APPLY"),

    // 刮刮卡错误枚举
    APPLY_SCRATCH_CARD_PARTNER_NAME_IS_NOT_NULL("2010000441", "合作伙伴姓名不能为空", "AGENT"),
    APPLY_SCRATCH_CARD_PARTNER_ID_IS_NOT_FOUND_ERROR("2010000441", "未找到合作伙伴信息", "AGENT"),
    APPLY_SCRATCH_CARD_CONTACT_MOBILE_IS_NOT_NULL("2010000441", "联系人手机号码不能为空", "AGENT"),
    APPLY_SCRATCH_CARD_NO_HAS_REACHED_THE_MAXIMUM("2020000024", "业务号已到最大值，请联系系统管理员", "PAYMENT"),
    APPLY_SCRATCH_CARD_GENERATE_NO_ERROR("2020000024", "生成业务号出错", "PAYMENT"),
    APPLY_SCRATCH_CARD_NUM_ERROR("2020000024", "刮刮卡数量请输入1到1000的正整数", "PAYMENT"),
    APPLY_SCRATCH_CARD_ID_IS_NOT_NULL("2020000024", "刮刮卡ID不能为空", "PAYMENT"),
    APPLY_SCRATCH_CARD_LOST_OBJECT_IS_NOT_NULL("2020000024", "丢失对象不能为空", "PAYMENT"),
    APPLY_SCRATCH_CARD_IS_NOT_FOUND_ERROR("2030000431", "未找到刮刮卡信息", "APPLY"),
    APPLY_SCRATCH_CARD_LOST_STATUS_ERROR("2030000431", "只有“待分配”和“退回”的状态才可以修改为“丢失”状态", "APPLY"),
    APPLY_SCRATCH_CARD_NUM_IS_NOT_NULL("2020000024", "刮刮卡数量不能为空", "PAYMENT"),
    APPLY_SCRATCH_CARD_START_DATE_IS_NOT_NULL("2020000024", "开始日期不能为空", "PAYMENT"),
    APPLY_SCRATCH_CARD_END_DATE_IS_NOT_NULL("2020000024", "结束日期不能为空", "PAYMENT"),
    APPLY_SCRATCH_CARD_ASSIGN_NUM_MAX_ERROR("2020000024", "分配数量大于待分配刮刮卡的数量，请先生成刮刮卡", "PAYMENT"),
    APPLY_SCRATCH_CARD_ASSIGN_IS_NOT_FOUND_ERROR("2030000431", "未找到刮刮卡分配信息", "APPLY"),
    APPLY_SCRATCH_CARD_RETURN_ERROR("2030000431", "只能退回“待激活”的卡", "APPLY"),
    APPLY_SCRATCH_CARD_AMOUNT_IS_NOT_NULL("2030000431", "非意外保险保额不能为空", "APPLY"),
    APPLY_SCRATCH_CARD_AMOUNT_ERROR("2030000431", "非意外保险保额不能低于500美元，不高于10000美元", "APPLY"),
    APPLY_SCRATCH_CARD_ACC_AMOUNT_IS_NOT_NULL("2030000431", "意外保险金额不能为空", "APPLY"),
    APPLY_SCRATCH_CARD_ACC_AMOUNT_ERROR("2030000431", "意外保险金额必须是非意外保险保额的1-10倍，并且不能超过100000美元。", "APPLY"),
    APPLY_SCRATCH_CARD_RECYCLE_ERROR("2030000431", "只有“丢失”和“退回”的状态才可以回收", "APPLY"),
    APPLY_SCRATCH_CARD_AGE_ERROR("2030000431", "被保人年龄必须在18-64岁之间", "APPLY"),
    APPLY_SCRATCH_CARD_BMI_ERROR("2030000431", "您的(BMI)超出本保险计划的可接受范围", "APPLY"),
    APPLY_SCRATCH_CARD_OCCUPATION_ERROR("2030000431", "根据您所选的职业，您无法投保此项保险计划", "APPLY"),
    APPLY_SCRATCH_CARD_ACTIVATION_CODE_ERROR("2030000431", "请输入有效的激活码", "APPLY"),
    APPLY_SCRATCH_CARD_ACTIVATION_CODE_NOT_FOUND("2030000431", "请扫描正确的激活码", "APPLY"),
    ;

    private String code;

    private String value;

    private String group;

    @Override
    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    @Override
    public String getValue() {
        return value;
    }

    public void setValue(String value) {
        this.value = value;
    }

    @Override
    public String getGroup() {
        return group;
    }

    @Override
    public String getCode(String value, String group) {
        return null;
    }

    @Override
    public String getValue(String code, String group) {
        return null;
    }

    public void setGroup(String group) {
        this.group = group;
    }

    ApplyErrorConfigEnum(String code, String value, String group) {
        this.code = code;
        this.value = value;
        this.group = group;
    }

}
