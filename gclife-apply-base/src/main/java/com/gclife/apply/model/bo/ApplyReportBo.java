package com.gclife.apply.model.bo;

import com.gclife.apply.core.jooq.tables.pojos.ApplyPo;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 *         create 18-11-12
 *         description:
 */
public class ApplyReportBo extends ApplyPo{
    //产品名称
    private String productName;
    //产品代码
    private String productCode;
    //档次
    private String productLevel;
    //投保人姓名
    private String applicantName;
    //被保人姓名
    private String insuredName;
    //被保人出生日期
    private Long insuredBirthday;
    private BigDecimal specialDiscount;
    private String     discountType;
    private String     promotionType;
    private String     discountModel;
    private BigDecimal originalPremium;
    private String premiumPeriod;

    public BigDecimal getSpecialDiscount() {
        return specialDiscount;
    }

    public void setSpecialDiscount(BigDecimal specialDiscount) {
        this.specialDiscount = specialDiscount;
    }

    public BigDecimal getOriginalPremium() {
        return originalPremium;
    }

    public void setOriginalPremium(BigDecimal originalPremium) {
        this.originalPremium = originalPremium;
    }

    public String getPremiumPeriod() {
        return premiumPeriod;
    }

    public void setPremiumPeriod(String premiumPeriod) {
        this.premiumPeriod = premiumPeriod;
    }

    public String getProductName() {
        return productName;
    }

    public void setProductName(String productName) {
        this.productName = productName;
    }

    public String getProductCode() {
        return productCode;
    }

    public void setProductCode(String productCode) {
        this.productCode = productCode;
    }

    public String getProductLevel() {
        return productLevel;
    }

    public void setProductLevel(String productLevel) {
        this.productLevel = productLevel;
    }

    public String getApplicantName() {
        return applicantName;
    }

    public void setApplicantName(String applicantName) {
        this.applicantName = applicantName;
    }

    public String getInsuredName() {
        return insuredName;
    }

    public void setInsuredName(String insuredName) {
        this.insuredName = insuredName;
    }

    public Long getInsuredBirthday() {
        return insuredBirthday;
    }

    public void setInsuredBirthday(Long insuredBirthday) {
        this.insuredBirthday = insuredBirthday;
    }

    public String getDiscountType() {
        return discountType;
    }

    public void setDiscountType(String discountType) {
        this.discountType = discountType;
    }

    public String getPromotionType() {
        return promotionType;
    }

    public void setPromotionType(String promotionType) {
        this.promotionType = promotionType;
    }

    public String getDiscountModel() {
        return discountModel;
    }

    public void setDiscountModel(String discountModel) {
        this.discountModel = discountModel;
    }
}