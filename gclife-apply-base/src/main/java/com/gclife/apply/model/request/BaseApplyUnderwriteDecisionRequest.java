package com.gclife.apply.model.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * create 18-5-17
 * description:
 */
@Data
public class BaseApplyUnderwriteDecisionRequest {
    @ApiModelProperty(value = "投保单ID", example = "004", required = true)
    private String applyId;
    @ApiModelProperty(value = "核保决定编码(STANDARD:标准体,SUBSTANDARD:次标准体,DELAY:延期,DECLINATURE:拒保", example = "STANDARD", required = true)
    private String underWriteDecisionCode;
    @ApiModelProperty(value = "核保意见", example = "这是核保意见")
    private String reviewComment;
    @ApiModelProperty(value = "是否上传暂予申请书标识符", example = "是否上传暂予申请书标识符")
    private String preUnderwritingFlag;

    /**
     * 17号产品附件
     */
    private List<PreImageAttachmentRequest> appAttachmentRequests;

    /**
     * 加费决定
     */
    private ApplyAddPremiumDecisionRequest addPremium;

    /**
     * 延期决定 TODO 暂未完善
     */
    private ApplyPostponedDecisionRequest postponed;

    /**
     * 保额决定 TODO 暂未完善
     */
    private ApplyAmountDecisionRequest amount;

    /**
     * 除外决定 TODO 暂未完善
     */
    private ApplyExclusionDecisionRequest exclusion;

    /*public String getApplyId() {
        return applyId;
    }

    public void setApplyId(String applyId) {
        this.applyId = applyId;
    }

    public String getUnderWriteDecisionCode() {
        return underWriteDecisionCode;
    }

    public void setUnderWriteDecisionCode(String underWriteDecisionCode) {
        this.underWriteDecisionCode = underWriteDecisionCode;
    }

    public String getReviewComment() {
        return reviewComment;
    }

    public void setReviewComment(String reviewComment) {
        this.reviewComment = reviewComment;
    }

    public ApplyAddPremiumDecisionRequest getAddPremium() {
        return addPremium;
    }

    public void setAddPremium(ApplyAddPremiumDecisionRequest addPremium) {
        this.addPremium = addPremium;
    }

    public ApplyPostponedDecisionRequest getPostponed() {
        return postponed;
    }

    public void setPostponed(ApplyPostponedDecisionRequest postponed) {
        this.postponed = postponed;
    }

    public ApplyAmountDecisionRequest getAmount() {
        return amount;
    }

    public void setAmount(ApplyAmountDecisionRequest amount) {
        this.amount = amount;
    }

    public ApplyExclusionDecisionRequest getExclusion() {
        return exclusion;
    }

    public void setExclusion(ApplyExclusionDecisionRequest exclusion) {
        this.exclusion = exclusion;
    }*/
}
