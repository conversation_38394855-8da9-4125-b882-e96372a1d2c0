package com.gclife.apply.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.gclife.agent.api.AgentApi;
import com.gclife.agent.model.response.AgentResponse;
import com.gclife.apply.core.jooq.tables.pojos.ApplyCoverageLevelPo;
import com.gclife.apply.core.jooq.tables.pojos.ApplyInsuredExtendPo;
import com.gclife.apply.core.jooq.tables.pojos.ApplyPaymentTransactionPo;
import com.gclife.apply.core.jooq.tables.pojos.ApplyUnderwriteDecisionPo;
import com.gclife.apply.dao.ApplyPaymentTransactionBaseDao;
import com.gclife.apply.model.bo.*;
import com.gclife.apply.model.config.ApplyErrorConfigEnum;
import com.gclife.apply.model.config.ApplyTermEnum;
import com.gclife.apply.model.feign.payment.PaymentBusinessDataReqFc;
import com.gclife.apply.service.*;
import com.gclife.apply.transform.ApplyDataTransform;
import com.gclife.apply.transform.ApplyPaymentDataTransform;
import com.gclife.common.exception.RequestException;
import com.gclife.common.model.ResultObject;
import com.gclife.common.model.base.Users;
import com.gclife.common.model.config.TerminologyConfigEnum;
import com.gclife.common.model.pojo.AppRequestHeads;
import com.gclife.common.service.impl.BaseBusinessServiceImpl;
import com.gclife.common.util.AssertUtils;
import com.gclife.common.util.ClazzUtils;
import com.gclife.common.util.DateUtils;
import com.gclife.common.util.JackSonUtils;
import com.gclife.finance.api.FinanceUserAccountApi;
import com.gclife.finance.model.response.UserAccountResponse;
import com.gclife.payment.api.PaymentBaseApi;
import com.gclife.payment.api.ReceiptBaseApi;
import com.gclife.payment.model.request.PaymentItemRequest;
import com.gclife.payment.model.request.PaymentRequest;
import com.gclife.payment.model.request.ReceiptItemRequest;
import com.gclife.payment.model.request.ReceiptRequest;
import com.gclife.payment.model.response.PaymentResponse;
import com.gclife.payment.model.response.ReceiptResponse;
import com.gclife.platform.api.PlatformConfigApi;
import com.gclife.platform.model.response.NotifyConfigResponse;
import com.gclife.policy.api.PolicyApi;
import com.gclife.policy.model.vo.PolicyVo;
import com.gclife.product.model.config.ProductTermEnum;
import lombok.extern.slf4j.Slf4j;
import org.modelmapper.TypeToken;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;


/**
 * <AUTHOR>
 * @version v1.0
 * Description: 投保单支付相关基础接口
 * @date 18-5-14
 */
@Service
@Slf4j
public class ApplyPaymentBaseServiceImpl extends BaseBusinessServiceImpl implements ApplyPaymentBaseService {
    private static final Logger LOGGER = LoggerFactory.getLogger(ApplyPaymentBaseServiceImpl.class);

    @Autowired
    private ApplyBaseService applyBaseService;
    @Autowired
    private PaymentBaseApi paymentBaseApi;
    @Autowired
    private ReceiptBaseApi receiptBaseApi;
    @Autowired
    private FinanceUserAccountApi financeUserAccountApi;
    @Autowired
    private ApplyDataTransform applyDataTransform;
    @Autowired
    private PolicyApi policyApi;
    @Autowired
    private PlatformConfigApi platformConfigApi;
    @Autowired
    private ApplyPaymentDataTransform applyPaymentDataTransform;
    @Autowired
    private ApplyPaymentTransactionBaseDao applyPaymentTransactionBaseDao;
    @Autowired
    private ApplyApplicantBaseService applyApplicantBaseService;
    @Autowired
    private ApplyPaymentTransactionBaseService applyPaymentTransactionBaseService;
    @Autowired
    private ApplyPremiumBaseService applyPremiumBaseService;
    @Autowired
    private ApplyCoverageBaseService applyCoverageBaseService;
    @Autowired
    private MessageBaseBusinessService messageBaseBusinessService;
    @Autowired
    private AgentApi agentApi;
    @Autowired
    private ApplyInsuredBaseService applyInsuredBaseService;
    @Autowired
    private ApplyUnderwriteBaseService applyUnderwriteBaseService;

    /**
     * 保存支付方式及支付金额(并提交到收付费中心处理)
     *
     * @param userId                     当前用户ID
     * @param deviceChannel              设备渠道
     * @param applyPaymentTransactionBo  投保单保费支付数据
     * @param groupApplyAccountBo
     * @param extendedPaymentDueTime
     */
    @Override
    @Transactional
    public ApplyPremiumBo saveApplyPayment(String userId, String deviceChannel, ApplyPaymentTransactionBo applyPaymentTransactionBo, ApplyBo applyBo, GroupApplyAccountBo groupApplyAccountBo, Long extendedPaymentDueTime) {
        ApplyPremiumBo applyPremiumBo = null;
        try {
            AssertUtils.isNotNull(LOGGER, applyPaymentTransactionBo, ApplyErrorConfigEnum.APPLY_BASE_PARAMETER_APPLY_PAYMENT_TRANSACTION_IS_NOT_NULL);
            AssertUtils.isNotEmpty(LOGGER, applyPaymentTransactionBo.getApplyPaymentTransactionItemBos(), ApplyErrorConfigEnum.APPLY_BASE_PARAMETER_APPLY_PAYMENT_TRANSACTION_IS_NOT_NULL);
            LOGGER.info("userId:" + JSON.toJSONString(userId));
            LOGGER.info("deviceChannel:" + JSON.toJSONString(deviceChannel));
            LOGGER.info("applyPaymentTransactionBo:" + JSON.toJSONString(applyPaymentTransactionBo));

            String applyId = applyPaymentTransactionBo.getApplyId();
//            AssertUtils.isNotEmpty(LOGGER, applyId, ApplyErrorConfigEnum.APPLY_BASE_PARAMETER_APPLY_ID_IS_NOT_NULL);
//            ApplyPo applyPo = applyBaseService.queryApplyPo(applyId);
//            AssertUtils.isNotNull(this.getLogger(), applyPo, ApplyErrorConfigEnum.APPLY_BASE_BUSINESS_APPLY_IS_NOT_FOUND_OBJECT);

            /*
             1.将原有事务支付状态改为“作废”
             */
            ApplyPaymentTransactionBo paymentTransactionBo = applyPaymentTransactionBaseService.queryApplyPaymentTransactionByType(applyId, applyPaymentTransactionBo.getPaymentType(), applyPaymentTransactionBo.getFeeType());
            if (AssertUtils.isNotNull(paymentTransactionBo)) {
//                if (ApplyTermEnum.PAYMENT_STATUS.PAYMENT_FINISHED.name().equals(paymentTransactionBo.getPaymentStatus())) {
//                    // 支付状态为“支付完成”，不允许重复支付
//                    throw new RequestException(ApplyErrorConfigEnum.APPLY_BASE_BUSINESS_REPEAT_PAYMENT_ERROR);
//                }
                if (ApplyTermEnum.PAYMENT_STATUS.PAYMENT_SUCCESS.name().equals(paymentTransactionBo.getPaymentStatus())) {
                    // 支付状态为“支付成功”，不允许重复支付
                    throw new RequestException(ApplyErrorConfigEnum.APPLY_BASE_BUSINESS_PAYMENT_SUCCESS_ERROR);
                }
                if (ApplyTermEnum.PAYMENT_STATUS.PAYMENT_WAITTING.name().equals(paymentTransactionBo.getPaymentStatus())
                        || ApplyTermEnum.PAYMENT_STATUS.PENDING_ON_CONFIRMATION.name().equals(paymentTransactionBo.getPaymentStatus())
                        || ApplyTermEnum.PAYMENT_STATUS.PAYMENT_FINISHED.name().equals(paymentTransactionBo.getPaymentStatus())
                ) {
                    paymentTransactionBo.setPaymentStatus(ApplyTermEnum.PAYMENT_STATUS.PAYMENT_INVALID.name());
                    paymentTransactionBo.setValidFlag(TerminologyConfigEnum.VALID_FLAG.invalid.name());
                    paymentTransactionBo.getApplyPaymentTransactionItemBos().forEach(applyPaymentTransactionItemBo -> {
                        applyPaymentTransactionItemBo.setPaymentStatus(ApplyTermEnum.PAYMENT_STATUS.PAYMENT_INVALID.name());
                        applyPaymentTransactionItemBo.setValidFlag(TerminologyConfigEnum.VALID_FLAG.invalid.name());
                    });
                    // 更新支付事务
                    applyPaymentTransactionBaseService.saveApplyPaymentTransaction(userId, paymentTransactionBo);
                }
            }

            /*
             2.查询投保人账户信息，校验账户余额是否足够支付
             */
            final String[] accountId = {""};
            applyPaymentTransactionBo.getApplyPaymentTransactionItemBos().stream()
                    .filter(applyPaymentTransactionItemBo -> ApplyTermEnum.PAYMENT_TYPE.DEDUCTION.name().equals(applyPaymentTransactionItemBo.getPaymentTypeCode()))
                    .findFirst().ifPresent(applyPaymentTransactionItemBo -> {
                ApplyApplicantBo applyApplicantBo = applyApplicantBaseService.queryApplyApplicant(applyId);
                AssertUtils.isNotNull(LOGGER, applyApplicantBo, ApplyErrorConfigEnum.APPLY_BASE_BUSINESS_APPLY_APPLICANT_IS_NOT_FOUND_OBJECT);
                ResultObject<UserAccountResponse> userAccount =
                        financeUserAccountApi.queryOneAcount(applyApplicantBo.getCustomerId(), ApplyTermEnum.ACCOUNT_TYPE.OVER_PAYMENT_ACCOUNT.name());
                AssertUtils.isResultObjectError(LOGGER, userAccount);
                UserAccountResponse userAccountResponse = userAccount.getData();

                accountId[0] = userAccountResponse.getAccountId();
                if (applyPaymentTransactionItemBo.getPaymentAmount().compareTo(userAccountResponse.getResidueAmount()) > 0) {
                    throw new RequestException(ApplyErrorConfigEnum.APPLY_BASE_BUSINESS_BALANCE_IS_NOT_ENOUGH_TO_PAY);
                }
            });

            /*
             3.组装支付参数，发起支付
             */
            // 查询投保单保费表
            applyPremiumBo = applyPremiumBaseService.queryApplyPremium(applyId);
            AssertUtils.isNotNull(LOGGER, applyPremiumBo, ApplyErrorConfigEnum.APPLY_BASE_BUSINESS_APPLY_PREMIUM_IS_NOT_FOUNT_OBJECT);

            //发起支付所需参数
            PaymentRequest paymentRequest = new PaymentRequest();
            paymentRequest.setUserId(userId);
            paymentRequest.setBranchId(applyBo.getSalesBranchId());
            paymentRequest.setDeviceChannelId(deviceChannel);
            paymentRequest.setBusinessId(applyBo.getApplyId());
            paymentRequest.setBusinessNo(applyBo.getApplyNo());
            if (applyBo.getApplyType().equals(ApplyTermEnum.APPLY_TYPE.LIFE_INSURANCE_PERSONAL.name())) {
                paymentRequest.setBusinessType("APPLY");
            } else if (applyBo.getApplyType().equals(ApplyTermEnum.APPLY_TYPE.LIFE_INSURANCE_GROUP.name())) {
                paymentRequest.setBusinessType("APPLY_GROUP");
            }
            paymentRequest.setDuePayAmount(applyPaymentTransactionBo.getPaymentAmount());
            paymentRequest.setCurrency(applyBo.getCurrencyCode());
            //支付通知接口配置化,回调接口路径
            ResultObject<NotifyConfigResponse> notifyConfig = platformConfigApi.getNotifyConfig(ApplyTermEnum.NOTIFY_CONFIG_TYPE.APPLY.name());
            AssertUtils.isResultObjectDataNull(this.getLogger(), notifyConfig);
            paymentRequest.setPaymentHandler(notifyConfig.getData().getNotifyUrl());

            final Long[] receiptPrintDate = {null};
            final boolean[] printFlag = {true};
            List<PaymentItemRequest> paymentItemRequests = new ArrayList<>();
            final String[] paymentMethodCode = new String[1];
            applyPaymentTransactionBo.getApplyPaymentTransactionItemBos().forEach(applyPaymentTransactionItemBo -> {
                PaymentItemRequest paymentItemRequest = new PaymentItemRequest();
                paymentMethodCode[0] = applyPaymentTransactionItemBo.getPaymentMethodCode();
                if (AssertUtils.isNotEmpty(paymentMethodCode[0])) {
                    paymentRequest.setPaymentStatusUpdateFlag(TerminologyConfigEnum.WHETHER.YES.name());
                }
                paymentItemRequest.setPaymentMethodCode(paymentMethodCode[0]);
                paymentItemRequest.setPaymentTypeCode(applyPaymentTransactionItemBo.getPaymentTypeCode());
                paymentItemRequest.setDuePayAmount(applyPaymentTransactionItemBo.getPaymentAmount());

                /*
                    ******** 收据打印日期调整
                    业务类型为新契约、续期/续保缴费
                    1、如果缴费方式为银行转账 或 Wing Offline，收据日期需调整为业务员上传客户付款凭证的日期。
                 */
                boolean b = ApplyTermEnum.PAYMENT_METHODS.BANK_TRANSFER.name().equals(paymentMethodCode[0])
                        || ApplyTermEnum.PAYMENT_METHODS.WING_OFFLINE.name().equals(paymentMethodCode[0])
                        || ApplyTermEnum.PAYMENT_METHODS.WING_H5.name().equals(paymentMethodCode[0])
                        || ApplyTermEnum.PAYMENT_METHODS.ABA_PAYMENTS.name().equals(paymentMethodCode[0]);
                if (printFlag[0] && b) {
                    receiptPrintDate[0] = DateUtils.getCurrentTime();
                    printFlag[0] = false;
                }
                if (ApplyTermEnum.PAYMENT_METHODS.OVER_PAYMENT_ACCOUNT_DEDUCTION.name().equals(paymentMethodCode[0])) {
                    Map<String, String> map = new HashMap<>();
                    map.put("accountId", accountId[0]);
                    paymentItemRequest.setPaymentParam(JSON.toJSONString(map));
                } else if (ApplyTermEnum.PAYMENT_METHODS.BANK_DIRECT_DEBIT.name().equals(paymentMethodCode[0])
                        || ApplyTermEnum.PAYMENT_METHODS.BANK_TRANSFER.name().equals(paymentMethodCode[0])) {
                    if (AssertUtils.isNotNull(groupApplyAccountBo)) {
                        Map<String, String> map = new HashMap<>();
                        map.put("bankCode", groupApplyAccountBo.getBankCode());
                        map.put("bankAccountNo", groupApplyAccountBo.getAccountNo());
                        paymentItemRequest.setPaymentParam(JSON.toJSONString(map));
                    }
                }
                paymentItemRequests.add(paymentItemRequest);
            });
            paymentRequest.setItems(paymentItemRequests);

            Long expireEndTime = null;
            //缴费截止日期=下发核保日期+7天
            ApplyUnderwriteDecisionPo applyUnderwriteDecisionPo = applyUnderwriteBaseService.queryApplyUnderwriteDecisionPo(applyId);
            if (AssertUtils.isNotNull(applyUnderwriteDecisionPo)) {
                Optional<ApplyCoverageBo> first = applyBo.getListInsuredCoverage().stream().filter(applyCoverageBo -> applyCoverageBo.getPrimaryFlag().equals(ApplyTermEnum.PRODUCT_PRIMARY_FLAG.MAIN.name())).distinct().findFirst();
                //v3.6.7.******** 对于5号产品的投保单人工核保通过之后的有效时间延长至30天
                Long createdDate = applyUnderwriteDecisionPo.getCreatedDate();
                if (first.isPresent() && Arrays.asList(ApplyTermEnum.PRODUCT.PRODUCT_5.id(), ApplyTermEnum.PRODUCT.PRODUCT_28.id()).contains(first.get().getProductId())) {
                    expireEndTime = DateUtils.addStringDayRT(createdDate, 30);
                } else {
                    //5.8.3 作废时间默认14天
                    expireEndTime = DateUtils.addStringDayRT(createdDate, 14);
                    //若是存在延长支付到期时间
                    if (AssertUtils.isNotNull(extendedPaymentDueTime)) {
                        expireEndTime = extendedPaymentDueTime;
                    }
                }
//                // 6.9.5 禁用网销投保单14天自动废弃逻辑
//                if (ONLINE.name().equals(applyBo.getChannelTypeCode())) {
//                    expireEndTime = null;
//                }
                paymentRequest.setPaymentExpDate(expireEndTime);
            }
            log.info("payment start:" + JSONObject.toJSON(paymentRequest));

            //冗余业务数据到支付中心
            PaymentBusinessDataReqFc paymentBusinessDataReqFc = applyPaymentDataTransform.transferPaymentBusinessData(applyBo);
            paymentBusinessDataReqFc.setReceiptPrintDate(receiptPrintDate[0]);
            paymentRequest.setBusinessJson(JSON.toJSONString(paymentBusinessDataReqFc));

            //收费类型
            if (ApplyTermEnum.CHARGE_TYPE.PREPAID_PREMIUM.name().equals(applyPaymentTransactionBo.getPaymentType())) {
                paymentRequest.setPaymentType(ApplyTermEnum.PAYMENT_TYPE.SUSPENSE_PREMIUM.name());
                paymentRequest.setIsPolicyReview(TerminologyConfigEnum.WHETHER.NO.name());
            }

            //流程是否为智能核保通过，通过需要加上标识
            if (applyDataTransform.doWhetherThroughAutoUW(applyBo.getApplyId(), applyBo.getApplyType())) {
                paymentRequest.setIsPolicyReview(TerminologyConfigEnum.WHETHER.NO.name());
            }
            // 发起支付
            getLogger().info("发起支付:{}", JackSonUtils.toJson(paymentRequest));
            ResultObject<PaymentResponse> paymentRespFcResultObject = paymentBaseApi.startPayment(paymentRequest);
            AssertUtils.isResultObjectError(LOGGER, paymentRespFcResultObject);

            /*
             4.保存支付事务
             */
            PaymentResponse paymentResponse = paymentRespFcResultObject.getData();
            AssertUtils.isNotNull(getLogger(), paymentResponse, ApplyErrorConfigEnum.APPLY_BASE_QUERY_PAYMENT_FAIL);
            AssertUtils.isNotNull(getLogger(), paymentResponse.getPaymentId(), ApplyErrorConfigEnum.APPLY_BASE_QUERY_PAYMENT_FAIL);
            AssertUtils.isNotEmpty(getLogger(), paymentResponse.getStatus(), ApplyErrorConfigEnum.APPLY_BASE_QUERY_PAYMENT_STATUS_FAIL);
            log.info("payment result:" + JSONObject.toJSON(paymentResponse));

            // 设置支付事务数据
            applyPaymentTransactionBo.setPremiumId(applyPremiumBo.getPremiumId());
            applyPaymentTransactionBo.setPaymentDate(DateUtils.getCurrentTime());
            applyPaymentTransactionBo.setPaymentId(paymentResponse.getPaymentId());
            if (paymentResponse.getStatus().equals(ApplyTermEnum.PAYMENT_STATUS.PAYMENT_WAITTING.name()) || paymentResponse.getStatus().equals(ApplyTermEnum.PAYMENT_STATUS.PENDING_ON_CONFIRMATION.name())) {
                //待支付才需要判断审核标识
                if (AssertUtils.isNotEmpty(paymentResponse.getIsAudit()) && ApplyTermEnum.YES_NO.YES.name().equals(paymentResponse.getIsAudit())
                        && !ApplyTermEnum.PAYMENT_METHODS.WING_OFFLINE.name().equals(paymentMethodCode[0])
                        && !ApplyTermEnum.PAYMENT_METHODS.ABA_PAYMENTS.name().equals(paymentMethodCode[0])) {
                    applyPaymentTransactionBo.setPaymentStatus(ApplyTermEnum.PAYMENT_STATUS.PAYMENT_FINISHED.name());
                } else {
                    applyPaymentTransactionBo.setPaymentStatus(ApplyTermEnum.PAYMENT_STATUS.PAYMENT_WAITTING.name());
                }
                applyBo.setApplyStatus(ApplyTermEnum.APPLY_STATUS_FLAG.APPLY_STATUS_UNDERWRITE_PASS.name());
            } else {
                applyPaymentTransactionBo.setPaymentStatus(paymentResponse.getStatus());
                applyBo.setApplyStatus(ApplyTermEnum.APPLY_STATUS_FLAG.APPLY_STATUS_UNDERWRITE_PASS.name());
            }
            final String[] paymentUrl = new String[1];
            // 设置支付事务项数据
            applyPaymentTransactionBo.getApplyPaymentTransactionItemBos().forEach(paymentItemBo -> {
                paymentResponse.getItems().stream()
                        .filter(paymentItemResponse ->
                                (!AssertUtils.isNotEmpty(paymentItemBo.getPaymentMethodCode()) && !AssertUtils.isNotEmpty(paymentItemResponse.getPaymentMethodCode()))
                                        || paymentItemBo.getPaymentMethodCode().equals(paymentItemResponse.getPaymentMethodCode()))
                        .findFirst().ifPresent(paymentItemRespFc -> {
                    // 将返回数据复制到支付事务项
                    ClazzUtils.copyPropertiesIgnoreNull(paymentItemRespFc, paymentItemBo);
                    paymentItemBo.setPaymentStatus(applyPaymentTransactionBo.getPaymentStatus());
                    //设置支付跳转的h5页面
                    if (ApplyTermEnum.PAYMENT_METHODS.WING_H5.name().equals(paymentItemRespFc.getPaymentMethodCode())) {
                        if (AssertUtils.isNotEmpty(paymentItemRespFc.getReturnParam()) && paymentItemRespFc.getReturnParam().contains("paymentUrl")) {
                            JSONObject jsonObject = JSONObject.parseObject(paymentItemRespFc.getReturnParam());
                            paymentUrl[0] = jsonObject.getString("paymentUrl");
                        }
                    }
                });
            });

            // 保存数据
            applyPaymentTransactionBaseService.saveApplyPaymentTransaction(userId, applyPaymentTransactionBo);
            applyBo.setInitialPaymentMode(applyPaymentTransactionBo.getApplyPaymentTransactionItemBos().get(0).getPaymentMethodCode());
            applyBo.setPaymentMode(applyPaymentTransactionBo.getApplyPaymentTransactionItemBos().get(0).getPaymentMethodCode());
            /*
            5.更新投保单保费表状态
             */
            applyPremiumBo.setPaymentType(applyPaymentTransactionBo.getPaymentType());
            applyPremiumBo.setPremiumStatus(applyPaymentTransactionBo.getPaymentStatus());
            applyPremiumBo.setPaymentId(paymentResponse.getPaymentId());
            applyPremiumBo.setPaymentUrl(paymentUrl[0]);
            applyPremiumBo.setPaymentCodeNo(paymentResponse.getPaymentCodeNo());
            applyPremiumBo.setExpireEndTime(expireEndTime);
            applyPremiumBo.setReceivableDate(DateUtils.getCurrentTime());
            applyPremiumBaseService.saveApplyPremium(userId, applyPremiumBo);

            /*
            6.更新投保单
             */
            applyBaseService.saveApply(userId, applyBo);
        } catch (Exception e) {
            e.printStackTrace();
            //事务回滚
            throwsTransactionalException(LOGGER, e, ApplyErrorConfigEnum.APPLY_BASE_SAVE_APPLY_APPLY_PAYMENT_TRANSACTION_ERROR);
        }
        return applyPremiumBo;
    }

    /**
     * 投保单转保单(团险)
     *
     * @param applyId       投保单ID
     * @param userId
     * @param actualPayDate 财务实收时间
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void transferApplyToPolicy(String applyId, String userId, String actualPayDate) {
        try {
            log.info("transferApplyToPolicy start time :" + DateUtils.timeStrToString(System.currentTimeMillis(), "yyyy-MM-dd HH:mm:ss"));

            AssertUtils.isNotEmpty(LOGGER, applyId, ApplyErrorConfigEnum.APPLY_BASE_PARAMETER_APPLY_ID_IS_NOT_NULL);
            // 查询投保单数据
            ApplyBo applyBo = applyBaseService.queryApply(applyId);
            AssertUtils.isNotNull(LOGGER, applyBo, ApplyErrorConfigEnum.APPLY_BASE_BUSINESS_APPLY_IS_NOT_FOUND_OBJECT);

            // 查询险种档次
            List<ApplyCoverageLevelPo> applyCoverageLevelPos = applyCoverageBaseService.listApplyCoverageLevel(applyId);
            if (AssertUtils.isNotEmpty(applyCoverageLevelPos)) {
                // 按险种ID分组
                Map<String, List<ApplyCoverageLevelPo>> applyCoverageLevelPoMap =
                        applyCoverageLevelPos.parallelStream().collect(Collectors.groupingBy(ApplyCoverageLevelPo::getCoverageId));
                // 设置险种档次数据
                applyBo.getListInsured().forEach(applyInsuredBo -> {
                    applyInsuredBo.getListCoverage().forEach(applyCoverageBo -> {
                        applyCoverageBo.setListCoverageLevel(applyCoverageLevelPoMap.get(applyCoverageBo.getCoverageId()));
                    });
                });
            }

            //数据转换
            PolicyVo policyVo = applyDataTransform.transApplyDataToPolicyData(applyBo, actualPayDate);
            this.getLogger().info("transferApplyToPolicy policyReqFc :{}", JSONObject.toJSONString(policyVo));

            //投保单转保单
            ResultObject<PolicyVo> resPolicyResult = policyApi.applyTransToPolicyBase(policyVo);
            AssertUtils.isResultObjectError(resPolicyResult);
            //判断是否转换成功
            AssertUtils.isResultObjectDataNull(this.getLogger(), resPolicyResult, ApplyErrorConfigEnum.APPLY_BASE_BUSINESS_SAVE_POLICY_DATA_ERROR);
            //保存保单号
            PolicyVo returnPolicyVo = resPolicyResult.getData();
            applyBo.setPolicyNo(returnPolicyVo.getPolicyNo());
            applyBo.setApplyStatus(ApplyTermEnum.APPLY_STATUS_FLAG.APPLY_STATUS_APPROVE_SUCCESS.name());
            applyBo.setApproveDate(returnPolicyVo.getApproveDate());
            applyBo.setEffectiveDate(returnPolicyVo.getEffectiveDate());

            //保存被保人扩展
            if (AssertUtils.isNotNull(applyBo.getListInsured())) {
                List<ApplyInsuredExtendPo> applyInsuredExtendPos = (List<ApplyInsuredExtendPo>) this.converterList(applyBo.getListInsured(), new TypeToken<List<ApplyInsuredExtendPo>>() {
                }.getType());
                List<ApplyInsuredExtendPo> applyInsuredExtendPoTemps = null;
                if (AssertUtils.isNotNull(applyBo.getEffectiveDate()) && AssertUtils.isNotNull(applyInsuredExtendPos)) {
                    applyInsuredExtendPoTemps = applyInsuredExtendPos.stream().filter(applyInsuredExtendPo -> AssertUtils.isNotNull(applyInsuredExtendPo.getInsuredExtendId())).collect(Collectors.toList());
                    if (AssertUtils.isNotNull(applyInsuredExtendPoTemps)) {
                        applyInsuredExtendPoTemps.forEach(applyInsuredExtendPo -> {
                            applyInsuredExtendPo.setEffectiveDate(applyBo.getEffectiveDate());
                        });
                        //保存被保人扩展
                        applyInsuredBaseService.saveApplyInsuredExtend(userId, applyInsuredExtendPoTemps);
                    }
                }
            }
            applyBaseService.saveApply(userId, applyBo);
            try {
                //设置代理人信息
                AgentResponse agentResponse = agentApi.agentByIdGet(applyBo.getApplyAgentBo().getAgentId()).getData();
                applyBo.getApplyAgentBo().setAgentCode(agentResponse.getAgentCode());
                applyBo.getApplyAgentBo().setAgentName(agentResponse.getAgentName());

                this.getLogger().info("团单承保提醒业务员短信");
                messageBaseBusinessService.pushApplyMessageAgent(ApplyTermEnum.MSG_BUSINESS_TYPE.GROUP_APPLY_APPROVE_REMINDER_AGENT.name(), applyBo, agentResponse.getUserId());

                this.getLogger().info("团单承保提醒客户");
                messageBaseBusinessService.pushApplyMessageCustomerSMS(ApplyTermEnum.MSG_BUSINESS_TYPE.GROUP_APPLY_APPROVE_REMINDER_CUSTOMER.name(), applyBo, applyBo.getApplicant().getDelegateMobile());

                this.getLogger().info("团单承保出单成功发消息给后台操作人员及业务员");
                messageBaseBusinessService.pushApplyMessageBatch(ApplyTermEnum.MSG_BUSINESS_TYPE.APPLY_TRANFER_POLICY_CP.name(), applyBo);
            } catch (Exception e) {
                e.printStackTrace();
            }

            log.info("transferApplyToPolicy end time :" + DateUtils.timeStrToString(System.currentTimeMillis(), "yyyy-MM-dd HH:mm:ss"));
        } catch (Exception e) {
            e.printStackTrace();
            throwsTransactionalException(LOGGER, e, ApplyErrorConfigEnum.APPLY_BASE_BUSINESS_SAVE_POLICY_DATA_ERROR);
        }
    }

    @Override
    public ApplyPaymentTransactionPo queryPaymentTransactionPo(String applyIp) {
        return applyPaymentTransactionBaseDao.queryListPaymentTransactionPo(applyIp);
    }

    @Override
    public ApplyPremiumBo queryApplyPremiumBo(String applyIp) {
        return applyPaymentTransactionBaseDao.queryApplyPremiumBo(applyIp);
    }

    @Override
    @Transactional
    public void initiateReceipt(Users users, AppRequestHeads appRequestHandler, ApplyBo applyBo, BigDecimal duePayAmount) {
        String applyId = applyBo.getApplyId();
        ApplyPaymentTransactionBo applyPaymentTransactionBo = applyPaymentTransactionBaseService.queryApplyPaymentTransactionByType(applyId, null, ApplyTermEnum.PAYMENT_TYPE.SUSPENSE_PREMIUM_REFUND.name());
        if (AssertUtils.isNotNull(applyPaymentTransactionBo)) {
            if (ApplyTermEnum.PAYMENT_STATUS.PAYMENT_FINISHED.name().equals(applyPaymentTransactionBo.getPaymentStatus())) {
                // 支付状态为“支付完成”，不允许重复支付
                throw new RequestException(ApplyErrorConfigEnum.APPLY_BASE_BUSINESS_REPEAT_PAYMENT_ERROR);
            }
            if (ApplyTermEnum.PAYMENT_STATUS.PAYMENT_SUCCESS.name().equals(applyPaymentTransactionBo.getPaymentStatus())) {
                // 支付状态为“支付成功”，不允许重复支付
                throw new RequestException(ApplyErrorConfigEnum.APPLY_BASE_BUSINESS_PAYMENT_SUCCESS_ERROR);
            }
        } else {
            applyPaymentTransactionBo = new ApplyPaymentTransactionBo();
        }
        /*
         2.调用收付费中心接口给客户付费
         */
        ReceiptRequest receiptRequest = this.transReceiptRequest(users, appRequestHandler, applyBo, duePayAmount);
        // 发起支付
        this.getLogger().info("processGetFee发起支付：{}", JSON.toJSONString(receiptRequest));
        ResultObject<ReceiptResponse> responseResultObject = receiptBaseApi.applyReceipt(receiptRequest);
        this.getLogger().info("processGetFee支付返回：{}", JSON.toJSONString(responseResultObject));
        AssertUtils.isResultObjectError(this.getLogger(), responseResultObject);

         /*
         3.保存支付事务
         */
        ReceiptResponse receiptResponse = responseResultObject.getData();
        applyPaymentTransactionBo.setApplyId(applyId);
        applyPaymentTransactionBo.setPremiumId(applyBo.getApplyPremiumBo().getPremiumId());
        applyPaymentTransactionBo.setPaymentAmount(duePayAmount);
        applyPaymentTransactionBo.setPaymentStatus(ApplyTermEnum.PAYMENT_STATUS.PAYMENT_FINISHED.name());
        applyPaymentTransactionBo.setPaymentDate(System.currentTimeMillis());
        applyPaymentTransactionBo.setPaymentId(receiptResponse.getReceiptId());
        applyPaymentTransactionBo.setFeeType(ApplyTermEnum.PAYMENT_TYPE.SUSPENSE_PREMIUM_REFUND.name());

        // 支付事务项目
        List<ApplyPaymentTransactionItemBo> transactionItemPos = new ArrayList<>();
        if (AssertUtils.isNotEmpty(receiptResponse.getItems())) {
            ApplyPaymentTransactionBo finalApplyPaymentTransactionBo = applyPaymentTransactionBo;
            receiptResponse.getItems().forEach(item -> {
                ApplyPaymentTransactionItemBo transactionItemPo = new ApplyPaymentTransactionItemBo();
                ClazzUtils.copyPropertiesIgnoreNull(item, transactionItemPo);
                transactionItemPo.setPaymentTransactionId(finalApplyPaymentTransactionBo.getPaymentTransactionId());
                transactionItemPo.setPaymentAmount(duePayAmount);
                transactionItemPo.setPaymentStatus(ApplyTermEnum.PAYMENT_STATUS.PAYMENT_FINISHED.name());
                transactionItemPos.add(transactionItemPo);
            });
            applyPaymentTransactionBo.setApplyPaymentTransactionItemBos(transactionItemPos);
        }
        // 保存数据
        applyPaymentTransactionBaseService.saveApplyPaymentTransaction(users.getUserId(), applyPaymentTransactionBo);
    }

    /**
     * 组装发起支付请求参数
     *
     * @param users             当前用户
     * @param appRequestHandler 请求头
     * @param applyBo           保全数据
     * @return
     */
    public ReceiptRequest transReceiptRequest(Users users, AppRequestHeads appRequestHandler, ApplyBo applyBo, BigDecimal duePayAmount) {
        //发起支付所需参数
        ReceiptRequest receiptRequest = new ReceiptRequest();
        receiptRequest.setUserId(users.getUserId());
        receiptRequest.setBranchId(applyBo.getSalesBranchId());
        receiptRequest.setDeviceChannelId(appRequestHandler.getDeviceChannel());
        receiptRequest.setBusinessId(applyBo.getApplyId());
        receiptRequest.setBusinessNo(applyBo.getApplyNo());
        receiptRequest.setBusinessType("APPLY");
        receiptRequest.setDuePayAmount(duePayAmount);
        receiptRequest.setCurrency("USD");
        receiptRequest.setReceiptType(ApplyTermEnum.PAYMENT_TYPE.SUSPENSE_PREMIUM_REFUND.name());
        String refundType = ApplyTermEnum.REFUND_TYPE.FULL_REFUND.name();
        if (applyBo.getReceivablePremium().compareTo(duePayAmount) > 0) {
            refundType = ApplyTermEnum.REFUND_TYPE.PARTIAL_REFUND.name();
        }
        receiptRequest.setRefundType(refundType);
        ResultObject<NotifyConfigResponse> notifyConfig = platformConfigApi.getNotifyConfig(ApplyTermEnum.NOTIFY_CONFIG_TYPE.APPLY_REFUND.name());
        AssertUtils.isResultObjectDataNull(this.getLogger(), notifyConfig);
        receiptRequest.setPaymentHandler(notifyConfig.getData().getNotifyUrl());

        // 支付项参数
        List<ReceiptItemRequest> receiptItemRequests = new ArrayList<>();
        ReceiptItemRequest receiptItemRequest = new ReceiptItemRequest();
        //TODO 默认发起某种付费方式
        String paymentMethodCode = "CASH";
        receiptItemRequest.setPaymentMethodCode(paymentMethodCode);
//        if (ApplyTermEnum.PAYMENT_METHODS.BANK_TRANSFER.name().equals(paymentMethodCode)) {
//            Map<String, String> map = new HashMap<>();
//            map.put("bankCode", endorsePayInfoPo.getBankCode());
//            map.put("bankAccountNo", endorsePayInfoPo.getAccountNo());
//            map.put("bankAccountName", endorsePayInfoPo.getAccountOwner());
//            map.put("bankAccountType", endorsePayInfoPo.getAccountType());
//            receiptItemRequest.setPaymentParam(JSON.toJSONString(map));
//        }
        receiptItemRequest.setPaymentTypeCode(ApplyTermEnum.PAYMENT_TYPE.ACTUAL.name());
        receiptItemRequest.setDuePayAmount(duePayAmount);
        receiptItemRequests.add(receiptItemRequest);
        receiptRequest.setItems(receiptItemRequests);
        //转换业务json数据
        PaymentBusinessDataReqFc paymentBusinessDataReqFc = this.transferPaymentBusinessData(applyBo, duePayAmount);
        receiptRequest.setBusinessJson(AssertUtils.isNotNull(paymentBusinessDataReqFc) ? JSON.toJSONString(paymentBusinessDataReqFc) : null);
        System.out.println("receipt start:" + JSONObject.toJSON(receiptRequest));

        return receiptRequest;
    }

    private PaymentBusinessDataReqFc transferPaymentBusinessData(ApplyBo applyBo, BigDecimal duePayAmount) {
        PaymentBusinessDataReqFc paymentBusinessDataReqFc = new PaymentBusinessDataReqFc();
        //项目国际化
        //保单信息
        if (AssertUtils.isNotNull(applyBo.getApplyId())) {
            paymentBusinessDataReqFc.setApplyId(applyBo.getApplyId());
            paymentBusinessDataReqFc.setApplyNo(applyBo.getApplyNo());

            paymentBusinessDataReqFc.setAgentId(applyBo.getApplyAgentBo().getAgentId());
            paymentBusinessDataReqFc.setAgentCode(applyBo.getApplyAgentBo().getAgentCode());
            paymentBusinessDataReqFc.setApplicantName(applyBo.getApplicant().getName());
            applyBo.getListInsuredCoverage().stream().filter(applyCoverageBo -> applyCoverageBo.getPrimaryFlag().equals(ApplyTermEnum.PRODUCT_PRIMARY_FLAG.MAIN.name())).findFirst().ifPresent(applyCoverageBo -> {
                paymentBusinessDataReqFc.setMainProductCode(applyCoverageBo.getProductCode());
                paymentBusinessDataReqFc.setMainProductId(applyCoverageBo.getProductId());
                paymentBusinessDataReqFc.setMainProductName(applyCoverageBo.getProductName());
                paymentBusinessDataReqFc.setPremiumPeriod(applyCoverageBo.getPremiumPeriod());
                paymentBusinessDataReqFc.setPremiumPeriodUnit(applyCoverageBo.getPremiumPeriodUnit());
                paymentBusinessDataReqFc.setPremiumFrequency(applyCoverageBo.getPremiumFrequency());
            });
        }
        //转换险种信息
        paymentBusinessDataReqFc.setTotalPremium(duePayAmount);
        paymentBusinessDataReqFc.getInstallmentNo().add("--");
        return paymentBusinessDataReqFc;
    }
}
