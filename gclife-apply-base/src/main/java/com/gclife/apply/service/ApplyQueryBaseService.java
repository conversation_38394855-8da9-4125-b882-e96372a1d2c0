package com.gclife.apply.service;

import com.gclife.apply.model.bo.ApplyListBo;
import com.gclife.apply.model.bo.ApplyUnderwriteNoticeListBo;
import com.gclife.apply.model.vo.ApplyListVo;
import com.gclife.common.service.BaseBusinessService;
import com.gclife.workflow.model.request.WaitingTaskRequest;

import java.util.List;

/**
 * <AUTHOR>
 * create 18-5-2
 * description:
 */
public interface ApplyQueryBaseService extends BaseBusinessService {

    /**
     * 查询用户所属的投保单列表
     *
     * @param userId      当前用户
     * @param applyListVo 列表请求数据
     * @param applyType   投保单类型
     * @param applyStatus 投保单状态
     * @return List
     */
    List<ApplyListBo> loadApplyListByUserId(String userId, ApplyListVo applyListVo, String applyType, String applyStatus);

    /**
     * 查询用户所属的团险投保单列表
     *
     * @param userId      当前用户
     * @param applyListVo 列表请求数据
     * @param applyType   投保单类型
     * @return List
     */
    List<ApplyListBo> loadGroupApplyListByUserId(String userId, ApplyListVo applyListVo, String applyType);

    /**
     * 查询当前用户所属的投保单列表
     *
     * @param userId      当前用户
     * @param applyListVo 列表请求数据
     * @param applyType   投保单类型
     * @return List
     */
    List<ApplyListBo> loadApplyListByBranch(String userId, ApplyListVo applyListVo, String applyType);

    /**
     * 查询用户能处理的投保单列表(待办任务)
     *
     * @param waitingTaskRequest 工作流请求数据
     * @param applyListVo  列表请求数据
     * @param applyType    投保单类型
     * @return List
     */
    List<ApplyListBo> loadApplyListByWorkFlow(WaitingTaskRequest waitingTaskRequest, ApplyListVo applyListVo, String applyType);

    /**
     * 分页查询核保通知书打印列表
     *
     * @param userId      当前用户
     * @param applyListVo 列表请求参数
     * @return ApplyUnderwriteNoticeListBo
     */
    List<ApplyUnderwriteNoticeListBo> getApplyUnderwriteNoticeList(String userId,ApplyListVo applyListVo);
}
