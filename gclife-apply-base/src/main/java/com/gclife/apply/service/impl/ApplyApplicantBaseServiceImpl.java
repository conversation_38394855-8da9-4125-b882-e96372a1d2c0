package com.gclife.apply.service.impl;

import com.alibaba.fastjson.JSON;
import com.gclife.apply.core.jooq.tables.daos.ApplyApplicantDao;
import com.gclife.apply.core.jooq.tables.pojos.ApplyAgentPo;
import com.gclife.apply.core.jooq.tables.pojos.ApplyApplicantPo;
import com.gclife.apply.dao.ApplyBaseDao;
import com.gclife.apply.model.bo.ApplyApplicantBo;
import com.gclife.apply.model.config.ApplyErrorConfigEnum;
import com.gclife.apply.service.ApplyAgentBaseService;
import com.gclife.apply.service.ApplyApplicantBaseService;
import com.gclife.common.exception.RequestException;
import com.gclife.common.model.ResultObject;
import com.gclife.common.model.config.TerminologyConfigEnum;
import com.gclife.common.service.impl.BaseBusinessServiceImpl;
import com.gclife.common.util.AssertUtils;
import com.gclife.common.util.DateUtils;
import com.gclife.common.util.UUIDUtils;
import com.gclife.party.api.CustomerManageApi;
import com.gclife.party.model.request.CustomerBusinessRequest;
import com.gclife.party.model.response.UserCustomerResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.interceptor.TransactionAspectSupport;

import java.util.List;

/**
 * <AUTHOR>
 * @version v1.0
 * Description: 投保人基础服务
 * @date 19-8-16
 */
@Service
public class ApplyApplicantBaseServiceImpl extends BaseBusinessServiceImpl implements ApplyApplicantBaseService {
    @Autowired
    private ApplyBaseDao applyBaseDao;
    @Autowired
    private CustomerManageApi customerManageApi;
    @Autowired
    private ApplyAgentBaseService applyAgentBaseService;
    @Autowired
    private ApplyApplicantDao applyApplicantDao;

    /**
     * 根据投保单ID查询投保人信息
     *
     * @param applyId 投保单ID
     * @return ApplyApplicantPo
     */
    @Override
    public ApplyApplicantBo queryApplyApplicant(String applyId) {
        ApplyApplicantPo applyApplicantPo = applyBaseDao.getApplyApplicant(applyId);

        return (ApplyApplicantBo) this.converterObject(applyApplicantPo, ApplyApplicantBo.class);
    }

    /**
     * 保存投保单投保人信息
     *
     * @param userId           用户ID
     * @param applyApplicantBo 投保人信息
     */
    @Override
    @Transactional
    public void saveApplyApplicant(String userId, ApplyApplicantBo applyApplicantBo) {
        try {
            getLogger().info("applyApplicantBo:" + JSON.toJSONString(applyApplicantBo));
            AssertUtils.isNotNull(getLogger(), applyApplicantBo, ApplyErrorConfigEnum.APPLY_BASE_PARAMETER_APPLY_APPLICANT_IS_NOT_NULL);

            ApplyApplicantPo applyApplicantPo = (ApplyApplicantPo) this.converterObject(applyApplicantBo, ApplyApplicantPo.class);
            this.saveApplyApplicant(userId, applyApplicantPo);

        } catch (Exception e) {
            e.printStackTrace();
            //事务回滚
            TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
            if (e instanceof RequestException) {
                throw e;
            } else {
                throw new RequestException(ApplyErrorConfigEnum.APPLY_BASE_SAVE_APPLY_APPLICANT_ERROR);
            }
        }
    }

    /**
     * 保存投保单投保人信息
     *
     * @param userId           用户ID
     * @param applyApplicantPo 投保人信息
     */
    @Override
    @Transactional
    public void saveApplyApplicant(String userId, ApplyApplicantPo applyApplicantPo) {
        try {
            getLogger().info("applyApplicantPo:" + JSON.toJSONString(applyApplicantPo));
            AssertUtils.isNotNull(getLogger(), applyApplicantPo, ApplyErrorConfigEnum.APPLY_BASE_PARAMETER_APPLY_APPLICANT_IS_NOT_NULL);

            ApplyAgentPo applyAgentPo = applyAgentBaseService.queryApplyAgent(applyApplicantPo.getApplyId());
            String agentId = AssertUtils.isNotNull(applyAgentPo) ? applyAgentPo.getAgentId() : userId;

            if (!AssertUtils.isNotEmpty(applyApplicantPo.getApplicantId())) {
                //执行新增
                applyApplicantPo.setApplicantId(UUIDUtils.getUUIDShort());
                applyApplicantPo.setCreatedDate(DateUtils.getCurrentTime());
                applyApplicantPo.setCreatedUserId(userId);
                applyApplicantPo.setValidFlag(TerminologyConfigEnum.VALID_FLAG.effective.name());

                // 保存客户信息
                // 调用客户保存接口拿取客户ID
                if (AssertUtils.isNotEmpty(applyApplicantPo.getIdNo()) && AssertUtils.isNotEmpty(applyApplicantPo.getIdType())) {
                    CustomerBusinessRequest customerBusinessRequest = (CustomerBusinessRequest) this.converterObject(applyApplicantPo, CustomerBusinessRequest.class);
                    customerBusinessRequest.setUserId(agentId);
                    getLogger().info("customerBusinessRequest:::::" + JSON.toJSONString(customerBusinessRequest));
                    ResultObject<UserCustomerResponse> respFcResultObject = customerManageApi.saveCustomerMessagePo(customerBusinessRequest);
                    AssertUtils.isResultObjectError(getLogger(), respFcResultObject);
                    if (!AssertUtils.isResultObjectDataNull(respFcResultObject)) {
                        applyApplicantPo.setCustomerId(respFcResultObject.getData().getCustomerId());
                    }
                }

                applyApplicantDao.insert(applyApplicantPo);
            } else {
                //执行修改
                applyApplicantPo.setUpdatedDate(DateUtils.getCurrentTime());
                applyApplicantPo.setUpdatedUserId(userId);

                // 保存客户信息
                if (AssertUtils.isNotEmpty(applyApplicantPo.getIdNo()) && AssertUtils.isNotEmpty(applyApplicantPo.getIdType())) {
                    // 调用客户保存接口拿取客户ID
                    CustomerBusinessRequest customerBusinessRequest = (CustomerBusinessRequest) this.converterObject(applyApplicantPo, CustomerBusinessRequest.class);
                    customerBusinessRequest.setUserId(agentId);
                    ResultObject<UserCustomerResponse> respFcResultObject = customerManageApi.saveCustomerMessagePo(customerBusinessRequest);
                    AssertUtils.isResultObjectError(getLogger(), respFcResultObject);
                    if (!AssertUtils.isResultObjectDataNull(respFcResultObject)) {
                        applyApplicantPo.setCustomerId(respFcResultObject.getData().getCustomerId());
                    }
                }

                applyApplicantDao.update(applyApplicantPo);
            }
        } catch (Exception e) {
            e.printStackTrace();
            //事务回滚
            TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
            if (e instanceof RequestException) {
                throw e;
            } else {
                throw new RequestException(ApplyErrorConfigEnum.APPLY_BASE_SAVE_APPLY_APPLICANT_ERROR);
            }
        }
    }

    @Override
    public List<ApplyApplicantBo> queryApplyApplicantList(String applyType) {
        return applyBaseDao.queryApplyApplicantList(applyType);
    }
}
