package com.gclife.policy.controller.group;

import com.gclife.common.controller.base.BaseController;
import com.gclife.common.model.ResultObject;
import com.gclife.common.util.JackSonUtils;
import com.gclife.policy.model.bo.PolicyBo;
import com.gclife.policy.model.request.PolicyRequest;
import com.gclife.policy.service.base.PolicyGenerateBaseService;
import com.gclife.policy.service.business.group.GroupPolicyService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiResponse;
import io.swagger.annotations.ApiResponses;
import lombok.extern.slf4j.Slf4j;
import org.modelmapper.ModelMapper;
import org.modelmapper.convention.MatchingStrategies;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

/**
 * <AUTHOR>
 * @version v1.0
 * Description: 保单生成
 * @date 18-5-30
 */
@Slf4j
@Api(tags = "policy_transform", description = "保单生成")
@RestController
@RequestMapping(value = "/v1/policy")
public class PolicyGenerateBaseController extends BaseController {
    @Autowired
    private PolicyGenerateBaseService policyGenerateBaseService;
    @Autowired
    private  GroupPolicyService groupPolicyService;

    private final ModelMapper modelMapper = new ModelMapper();

    @ApiOperation(value = "团险投保单转保单", notes = "团险投保单转保单")
    @ApiResponses({
            @ApiResponse(code = 200, message = "请求成功")
    })
    @PostMapping(value = "/base/transform")
    public ResultObject applyTransToPolicy(@RequestBody PolicyRequest policyRequest) {
        System.out.println("未转换之前的policyRequest:" + JackSonUtils.toJson(policyRequest));
        PolicyBo policyBo = null;
        try {
            // 修改匹配策略为严格
            this.modelMapper.getConfiguration().setMatchingStrategy(MatchingStrategies.STRICT);
            this.modelMapper.getConfiguration().setAmbiguityIgnored(true);
            policyBo = this.modelMapper.map(policyRequest, PolicyBo.class);
        } catch (Exception e) {
            e.printStackTrace();
        }
        ResultObject resultObject = policyGenerateBaseService.applyDataTransToPolicy(this.getCurrentLoginUsers(), policyBo);
        //异步调用产生保单附件
        log.info("产生附件1-1");
        groupPolicyService.produceAttachment(this.getCurrentLoginUsers(), policyBo);
        return resultObject;
    }


    @ApiOperation(value = "middle/base/transfer", notes = "投保单转保单")
    @ApiResponses({
            @ApiResponse(code = 200, message = "请求成功")
    })
    @PostMapping(value = "/middle/transform")
    public ResultObject applyTransToPolicy1(@RequestBody PolicyRequest policyRequest) {
        System.out.println("未转换之前的policyRequest:" + JackSonUtils.toJson(policyRequest));
        PolicyBo policyBo = null;
        try {
            // 修改匹配策略为严格
            this.modelMapper.getConfiguration().setMatchingStrategy(MatchingStrategies.STRICT);
            this.modelMapper.getConfiguration().setAmbiguityIgnored(true);
            policyBo = this.modelMapper.map(policyRequest, PolicyBo.class);
        } catch (Exception e) {
            e.printStackTrace();
        }
        ResultObject resultObject = policyGenerateBaseService.applyDataTransToPolicy(this.getCurrentLoginUsers(), policyBo);
        //异步调用产生保单附件
        log.info("产生附件1-2");
        groupPolicyService.produceAttachment(this.getCurrentLoginUsers(), policyBo);
        return resultObject;
    }

    @ApiOperation(value = "/base/transfer/rollback", notes = "投保单转保单回滚")
    @ApiResponses({
            @ApiResponse(code = 200, message = "请求成功")
    })
    @DeleteMapping(value = "/base/transform/rollback")
    public ResultObject applyToPolicyRollback(@RequestParam("applyId") String applyId) {
        return policyGenerateBaseService.applyToPolicyRollback(applyId);
    }


    @ApiOperation(value = "/middle/transfer/rollback", notes = "投保单转保单回滚")
    @ApiResponses({
            @ApiResponse(code = 200, message = "请求成功")
    })
    @DeleteMapping(value = "/middle/transform/rollback")
    public ResultObject applyToPolicyRollback1(@RequestParam("applyId") String applyId) {
        return policyGenerateBaseService.applyToPolicyRollback(applyId);
    }

    @ApiOperation(value = "29号产品生成coi单独接口", notes = "29号产品生成coi单独接口")
    @ApiResponses({
            @ApiResponse(code = 200, message = "请求成功")
    })
    @GetMapping(value = "group/coi/generate")
    public ResultObject groupCoiGenerate(@RequestParam("policyId") String policyId) {
        return groupPolicyService.groupCoiGenerate(policyId);
    }

}
