package com.gclife.policy.controller.manage;

import com.gclife.common.controller.base.BaseController;
import com.gclife.common.model.BasePageResponse;
import com.gclife.common.model.ResultObject;
import com.gclife.policy.model.config.PolicyTermEnum;
import com.gclife.policy.model.request.manage.PolicyUpdateListRequest;
import com.gclife.policy.model.response.manage.PolicyUpdateListResponse;
import com.gclife.policy.service.business.manage.PolicyUpdateAuditService;
import com.gclife.policy.service.business.manage.PolicyUpdateProjectService;
import com.gclife.policy.service.business.manage.PolicyUpdateRemarkService;
import com.gclife.policy.service.business.manage.PolicyUpdateService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiResponse;
import io.swagger.annotations.ApiResponses;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 *         create 18-12-3
 *         description:
 */
@Api(tags = "Policy_Update", description = "保单修改列表")
@RestController
@RequestMapping(value = "/v1/update/")
public class PolicyUpdateController extends BaseController {
    @Autowired
    private PolicyUpdateService policyUpdateService;
    @Autowired
    private PolicyUpdateAuditService policyUpdateAuditService;
    @Autowired
    private PolicyUpdateRemarkService policyUpdateRemarkService;
    @Autowired
    private PolicyUpdateProjectService policyUpdateProjectService;

    @ApiOperation(value = "查询保单修改列表", notes = "查询保单修改列表")
    @ApiResponses({
            @ApiResponse(code = 200, message = "请求成功")
    })
    @GetMapping(value = "policyUpdate")
    public ResultObject<BasePageResponse<PolicyUpdateListResponse>> listPolicyUpdate(PolicyUpdateListRequest policyUpdateListRequest) {
        return policyUpdateService.queryPolicyUpdateList(policyUpdateListRequest,this.getCurrentLoginUsers());
    }

    @ApiOperation(value = "查询保单修改审核列表", notes = "查询保单修改审核列表")
    @ApiResponses({
            @ApiResponse(code = 200, message = "请求成功")
    })
    @GetMapping(value = "auditList")
    public ResultObject<BasePageResponse<PolicyUpdateListResponse>>  auditlistPolicyUpdate(PolicyUpdateListRequest policyUpdateListRequest) {
        List<String> auditStatus = new ArrayList<>();
        auditStatus.add(PolicyTermEnum.POLICY_UPDATE_AUDIT_STATUS.PENDING_AUDIT.name());
        auditStatus.add(PolicyTermEnum.POLICY_UPDATE_AUDIT_STATUS.AUDITING.name());
        policyUpdateListRequest.setAuditStatus(auditStatus);
        policyUpdateListRequest.setFlag("auditList");
        return policyUpdateService.queryPolicyUpdateList(policyUpdateListRequest,this.getCurrentLoginUsers());
    }

    @ApiOperation(value = "查询保单修改申请列表", notes = "查询保单修改申请列表")
    @ApiResponses({
            @ApiResponse(code = 200, message = "请求成功")
    })
    @GetMapping(value = "applyList")
    public ResultObject<BasePageResponse<PolicyUpdateListResponse>>  applyListPolicyUpdate(PolicyUpdateListRequest policyUpdateListRequest) {
        List<String> auditStatus = new ArrayList<>();
        auditStatus.add(PolicyTermEnum.POLICY_UPDATE_AUDIT_STATUS.INITIAL.name());
        auditStatus.add(PolicyTermEnum.POLICY_UPDATE_AUDIT_STATUS.AUDIT_NO_PASS.name());
        policyUpdateListRequest.setAuditStatus(auditStatus);
        return policyUpdateService.queryPolicyUpdateList(policyUpdateListRequest,this.getCurrentLoginUsers());
    }

    @ApiOperation(value = "保单审核结论", notes = "保单审核结论")
    @ApiResponses({
            @ApiResponse(code = 200, message = "请求成功")
    })
    @GetMapping(value = "policyAudit")
    public ResultObject updatePolicyAudit(String policyUpdateId, String policyUpdateAuditStatus, String remark) {
        return policyUpdateAuditService.updatePolicyAuditBo(this.getCurrentLoginUsers(),policyUpdateId,policyUpdateAuditStatus,remark);
    }

    @ApiOperation(value = "查询流程记录", notes = "查询流程记录")
    @ApiResponses({
            @ApiResponse(code = 200, message = "请求成功")
    })
    @GetMapping(value = "policyRemark")
    public ResultObject policyUpdateRemark(String policyUpdateId) {
        return policyUpdateRemarkService.queryPolicyUpdateRemarkList(policyUpdateId,this.getCurrentLoginUsers());
    }

    @ApiOperation(value = "删除保单修改", notes = "删除保单修改")
    @ApiResponses({
            @ApiResponse(code = 200, message = "请求成功")
    })
    @PostMapping(value = "delectPolicy")
    public ResultObject delectPolicyUpdateRemark(@RequestBody List<String> policyUpdateId) {
        return policyUpdateService.delectPolicyUpdate(policyUpdateId);
    }

    @ApiOperation(value = "数据字典", notes = "数据字典")
    @ApiResponses({
            @ApiResponse(code = 200, message = "请求成功")
    })
    @GetMapping(value = "initPolicy")
    public ResultObject initPolicyProject() {
        return policyUpdateProjectService.policyUpdateProjectInit();
    }

    @ApiOperation(value = "查询签约机构", notes = "查询签约机构")
    @ApiResponses({
            @ApiResponse(code = 200, message = "请求成功")
    })
    @GetMapping(value = "salesBranch")
    public ResultObject querySalesBranch(String policyNo) {
        return policyUpdateService.queryBranchRespFc(policyNo);
    }
}