package com.gclife.policy.validate.transfer;

import com.gclife.common.service.impl.BaseBusinessServiceImpl;
import com.gclife.common.util.AssertUtils;
import com.gclife.common.util.ClazzUtils;
import com.gclife.common.util.DateUtils;
import com.gclife.policy.core.jooq.tables.pojos.PolicyCoverageExtendPo;
import com.gclife.policy.core.jooq.tables.pojos.PolicyCoveragePo;
import com.gclife.policy.model.bo.PolicyBo;
import com.gclife.policy.model.bo.PolicyCoverageBo;
import com.gclife.policy.model.bo.PolicyPremiumBo;
import com.gclife.policy.model.config.PolicyTermEnum;
import com.gclife.policy.service.base.PolicyCoverageBaseService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version v2.0
 * Description:
 * @date 18-7-10
 */
@Component
public class RenewalDataTransfer extends BaseBusinessServiceImpl {
    @Autowired
    private PolicyCoverageBaseService policyCoverageBaseService;
    @Autowired
    private PolicyTransData policyTransData;

    /**
     * 获取保单险种
     * @param policyId 保单ID
     * @return
     */
    public List<PolicyCoveragePo> getPolicyCoverage(String policyId) {
        // 查询保单险种信息
        List<PolicyCoveragePo> policyCoveragePos = policyCoverageBaseService.listPolicyCoveragePo(policyId);
        // 查询待生效险种(缴费周期变更、增加附加险同一时间只会有一个)
        List<PolicyCoverageExtendPo> policyCoverageExtendPos = policyCoverageBaseService.listPendingCoverageExtend(policyId);
        if (AssertUtils.isNotEmpty(policyCoverageExtendPos)) {
            if (PolicyTermEnum.BUSINESS_TYPE.ENDORSE_MODIFY_PREMIUM_FREQUENCY.name().equals(policyCoverageExtendPos.get(0).getDataType())) {
                policyCoveragePos.forEach(policyCoveragePo -> {
                    policyCoverageExtendPos.stream()
                            .filter(extendPo -> extendPo.getProductId().equals(policyCoveragePo.getProductId()) &&
                                    !extendPo.getPremiumFrequency().equals(policyCoveragePo.getPremiumFrequency()))
                            .findFirst().ifPresent(extendPo -> {
                        ClazzUtils.copyPropertiesIgnoreNull(extendPo, policyCoveragePo);
                    });
                });
            } else if (PolicyTermEnum.BUSINESS_TYPE.ENDORSE_ADD_ADDITIONAL.name().equals(policyCoverageExtendPos.get(0).getDataType())) {
                List<String> productIds = policyCoveragePos.stream().map(PolicyCoveragePo::getProductId).collect(Collectors.toList());
                policyCoverageExtendPos.forEach(coverageExtendPo -> {
                    if (!productIds.contains(coverageExtendPo.getProductId())) {
                        PolicyCoveragePo policyCoveragePo = (PolicyCoveragePo) this.converterObject(coverageExtendPo, PolicyCoveragePo.class);
                        policyCoveragePos.add(policyCoveragePo);
                    }
                });
            }
        }
        return policyCoveragePos;
    }

    /**
     * 设置待生效信息到险种及保费
     *
     * @param policyBo
     */
    public void setPendingInfo(PolicyBo policyBo) {
        // 查询待生效险种(缴费周期变更、增加附加险同一时间只会有一个)
        List<PolicyCoverageExtendPo> policyCoverageExtendPos = policyCoverageBaseService.listPendingCoverageExtend(policyBo.getPolicyId());
        if (AssertUtils.isNotEmpty(policyCoverageExtendPos)) {
            List<PolicyCoverageBo> listPolicyCoverage = policyBo.getListInsured().get(0).getListPolicyCoverage();
            if (PolicyTermEnum.BUSINESS_TYPE.ENDORSE_MODIFY_PREMIUM_FREQUENCY.name().equals(policyCoverageExtendPos.get(0).getDataType())) {
                listPolicyCoverage.forEach(policyCoverageBo -> {
                    policyCoverageExtendPos.stream()
                            .filter(extendPo -> extendPo.getProductId().equals(policyCoverageBo.getProductId()) &&
                                    !extendPo.getPremiumFrequency().equals(policyCoverageBo.getPremiumFrequency()))
                            .findFirst().ifPresent(extendPo -> {
                        ClazzUtils.copyPropertiesIgnoreNull(extendPo, policyCoverageBo);
                    });
                });
            } else if (PolicyTermEnum.BUSINESS_TYPE.ENDORSE_ADD_ADDITIONAL.name().equals(policyCoverageExtendPos.get(0).getDataType())) {
                List<String> productIds = listPolicyCoverage.stream().map(PolicyCoverageBo::getProductId).collect(Collectors.toList());
                policyCoverageExtendPos.forEach(coverageExtendPo -> {
                    if (!productIds.contains(coverageExtendPo.getProductId())) {
                        PolicyCoverageBo policyCoverageBo = (PolicyCoverageBo) this.converterObject(coverageExtendPo, PolicyCoverageBo.class);
                        listPolicyCoverage.add(policyCoverageBo);
                    }
                });
            }
            // 设置险种数据
            policyBo.setListInsuredCoverage(listPolicyCoverage);
            policyBo.getListInsured().forEach(policyInsuredBo -> policyInsuredBo.setListPolicyCoverage(listPolicyCoverage));

            // 设置保单保费
            BigDecimal totalPremium = BigDecimal.ZERO;
            BigDecimal originalPremium = BigDecimal.ZERO;
            for (PolicyCoverageExtendPo coverageExtendPo : policyCoverageExtendPos) {
                totalPremium = totalPremium.add(coverageExtendPo.getTotalPremium());
                originalPremium = originalPremium.add(coverageExtendPo.getOriginalPremium());
                if (PolicyTermEnum.PRODUCT_PRIMARY_FLAG.MAIN.name().equals(coverageExtendPo.getPrimaryFlag())) {
                    policyBo.getPolicyPremium().setPremiumFrequency(coverageExtendPo.getPremiumFrequency());
                }
            }
            policyBo.getPolicyPremium().setPeriodTotalPremium(totalPremium);
            policyBo.getPolicyPremium().setPeriodOriginalPremium(originalPremium);
        }
    }

    /**
     * 获取应收时间
     *
     * @param policyCoverageBo 保单险种数据
     * @param receivableDates  已生成应收数据的应收时间
     * @return lsit
     */
    public List<Long> getReceivableDates(PolicyCoverageBo policyCoverageBo, List<Long> receivableDates) {
        List<Long> receivableDateList = new ArrayList<>();
        Long newReceivableDate = AssertUtils.isNotNull(policyCoverageBo.getPolicyEffectiveDate()) ? policyCoverageBo.getPolicyEffectiveDate() : policyCoverageBo.getApproveDate();
        // 续期期数
        int frequency = 1;
        // 循环计算新的应收时间
        while (AssertUtils.isNotNull(newReceivableDate) && DateUtils.timeToTimeTop(newReceivableDate) < DateUtils.getCurrentTime()) {
            newReceivableDate = calculateReceivableDate(policyCoverageBo.getPremiumFrequency(), policyCoverageBo.getPolicyEffectiveDate(), frequency);
            // 判断是否需要生成应收数据
            if (!receivableDates.contains(newReceivableDate) && checkReceivable(policyCoverageBo, newReceivableDate)) {
                receivableDateList.add(newReceivableDate);
            }
            frequency++;
        }
        return receivableDateList;
    }

    public Long getLastReceivableDate(PolicyPremiumBo policyPremiumBo) {
        return getLastReceivableDate(policyPremiumBo, policyPremiumBo.getApproveDate());
    }

    public Long getLastReceivableDate(PolicyPremiumBo policyPremiumBo, Long effectiveDate) {
        List<Long> receivableDateList = new ArrayList<>();
        Long newReceivableDate = effectiveDate;
        // 续期期数
        int frequency = 1;
        // 循环计算新的应收时间
        while (AssertUtils.isNotNull(newReceivableDate) && newReceivableDate <= policyPremiumBo.getPaymentCompleteDate()) {
            receivableDateList.clear();
            receivableDateList.add(newReceivableDate);
            newReceivableDate = calculateReceivableDate(policyPremiumBo.getPremiumFrequency(), effectiveDate, frequency);
            frequency++;
        }
        if (receivableDateList.size() > 0) {
            newReceivableDate = receivableDateList.get(0);
        }
        return newReceivableDate;
    }

    /**
     * 计算续期应缴日期
     *
     * @param premiumFrequency 缴费周期
     * @param effectiveDate    承保日期
     * @param frequency        续期期数
     * @return 应缴日期
     */
    public Long calculateReceivableDate(String premiumFrequency, Long effectiveDate, Integer frequency) {
        if (!AssertUtils.isNotEmpty(premiumFrequency) || !AssertUtils.isNotNull(effectiveDate) ||
                !AssertUtils.isNotNull(frequency) || PolicyTermEnum.PRODUCT_PREMIUM_FREQUENCY.SINGLE.name().equals(premiumFrequency)) {
            return null;
        }
        if (PolicyTermEnum.PRODUCT_PREMIUM_FREQUENCY.YEAR.name().equals(premiumFrequency)) {
            return DateUtils.addStringYearsRT(effectiveDate, frequency);
        } else {
            return DateUtils.addStringMonthRT(effectiveDate, PolicyTermEnum.FREQUENCY_VALUE.valueOf(premiumFrequency).value() * frequency);
        }
    }

    /**
     * 判断是否要生成应收
     *
     * @param policyCoverageBo 保单险种数据
     * @param receivableDate   应收日期
     * @return 应收生成日期
     */
    private boolean checkReceivable(PolicyCoverageBo policyCoverageBo, Long receivableDate) {
        if (!AssertUtils.isNotEmpty(policyCoverageBo.getPremiumFrequency()) || !AssertUtils.isNotNull(receivableDate)) {
            return false;
        }
        // 只有保单状态为“有效”“复效”“失效”的保单才产生续期
        if (!PolicyTermEnum.POLICY_STATUS_FLAG.POLICY_STATUS_EFFECTIVE.name().equals(policyCoverageBo.getPolicyStatus())
                && !PolicyTermEnum.POLICY_STATUS_FLAG.POLICY_STATUS_REINSTATEMENT.name().equals(policyCoverageBo.getPolicyStatus())
                && !PolicyTermEnum.POLICY_STATUS_FLAG.POLICY_STATUS_INVALID.name().equals(policyCoverageBo.getPolicyStatus())) {
            return false;
        }
        // 不产生当前时间之前的续期
        if (receivableDate < DateUtils.timeToTimeLow(System.currentTimeMillis())) {
            return false;
        }
        // 针对附加险续期调整
        List<PolicyCoveragePo> policyCoveragePos = this.getPolicyCoverage(policyCoverageBo.getPolicyId());
        boolean needToPay = false;
        boolean singleOnlyFlag = true;
        for (PolicyCoveragePo policyCoveragePo : policyCoveragePos) {
            if (policyTransData.isNeedToPay(policyCoveragePo, policyCoverageBo, receivableDate)) {
                needToPay = true;
                boolean isSingle = PolicyTermEnum.PRODUCT_PREMIUM_FREQUENCY.SINGLE.name().equals(policyCoveragePo.getPremiumFrequency()) ||
                        ("1".equals(policyCoveragePo.getPremiumPeriod()) && PolicyTermEnum.PRODUCT_PREMIUM_FREQUENCY.YEAR.name().equals(policyCoveragePo.getPremiumPeriodUnit()));
                if (!isSingle) {
                    singleOnlyFlag = false;
                }
            }
        }
        if (!needToPay) {
            return false;
        }
        // 只有短期险需要缴费，缴费周期自动变为年缴
        if (singleOnlyFlag) {
            long month = DateUtils.intervalMonth(policyCoverageBo.getApproveDate(), receivableDate);
            if (month % 12 != 0) {
                return false;
            }
        }

        long currentTime = DateUtils.timeToTimeTop(DateUtils.getCurrentTime());
        if (PolicyTermEnum.PRODUCT_PREMIUM_FREQUENCY.MONTH.name().equals(policyCoverageBo.getPremiumFrequency())) {
            return DateUtils.addStringDayRT(receivableDate, -15) <= currentTime;
        } else if (PolicyTermEnum.PRODUCT_PREMIUM_FREQUENCY.SEASON.name().equals(policyCoverageBo.getPremiumFrequency())) {
            return DateUtils.addStringDayRT(receivableDate, -31) <= currentTime;
        } else if (PolicyTermEnum.PRODUCT_PREMIUM_FREQUENCY.SEMIANNUAL.name().equals(policyCoverageBo.getPremiumFrequency())) {
            return DateUtils.addStringDayRT(receivableDate, -31) <= currentTime;
        } else if (PolicyTermEnum.PRODUCT_PREMIUM_FREQUENCY.YEAR.name().equals(policyCoverageBo.getPremiumFrequency())) {
            return DateUtils.addStringDayRT(receivableDate, -60) <= currentTime;
        } else {
            return false;
        }
    }


    /**
     * 计算上一个应缴日期
     *
     * @param premiumFrequency 缴费周期
     * @param receivableDate   应缴日期
     * @return 上一个应缴日期
     */
    public Long calculateSubReceivableDate(String premiumFrequency, Long receivableDate) {
        if (!(AssertUtils.isNotEmpty(premiumFrequency) && AssertUtils.isNotNull(receivableDate) && receivableDate != -1L)) {
            return -1L;
        }
        if (PolicyTermEnum.PRODUCT_PREMIUM_FREQUENCY.MONTH.name().equals(premiumFrequency)) {
            return DateUtils.addStringMonthRT(receivableDate, -1);
        } else if (PolicyTermEnum.PRODUCT_PREMIUM_FREQUENCY.SEASON.name().equals(premiumFrequency)) {
            return DateUtils.addStringMonthRT(receivableDate, -3);
        } else if (PolicyTermEnum.PRODUCT_PREMIUM_FREQUENCY.SEMIANNUAL.name().equals(premiumFrequency)) {
            return DateUtils.addStringMonthRT(receivableDate, -6);
        } else if (PolicyTermEnum.PRODUCT_PREMIUM_FREQUENCY.YEAR.name().equals(premiumFrequency)) {
            return DateUtils.addStringYearsRT(receivableDate, -1);
        } else {
            return -1L;
        }
    }

    public boolean checkRenewalYearMonth(String renewalYearMonth, Long receivableDate, Long paymentCompleteDate) {
        if (!AssertUtils.isNotEmpty(renewalYearMonth) || !AssertUtils.isNotNull(receivableDate) || !AssertUtils.isNotNull(paymentCompleteDate)) {
            return false;
        }
        if (receivableDate > paymentCompleteDate) {
            return false;
        }
        String timeYearMonth = DateUtils.getTimeYearMonth(receivableDate);
        return renewalYearMonth.equals(timeYearMonth);
    }

    /**
     * 计算保单年度
     *
     * @param receivableDate 应收日期
     * @param effectiveDate  承保日期
     * @return Long
     */
    public long calculatePolicyYear(Long receivableDate, Long effectiveDate) {
        Long months = DateUtils.intervalMonth(effectiveDate, receivableDate);
        return months / 12 + 1;
    }

    /**
     * 计算频次
     *
     * @param premiumFrequency 缴费周期
     * @param renewalYearMonth 应收年月
     * @param approveDate      承保日期
     * @return long
     */
    public long calculateFrequency(String premiumFrequency, String renewalYearMonth, Long approveDate) {
        int months = DateUtils.getMonthSpace(DateUtils.timeStrToString(approveDate, DateUtils.FORMATE16), renewalYearMonth);
        if (PolicyTermEnum.PRODUCT_PREMIUM_FREQUENCY.MONTH.name().equals(premiumFrequency)) {
            return months + 1;
        } else if (PolicyTermEnum.PRODUCT_PREMIUM_FREQUENCY.SEASON.name().equals(premiumFrequency)) {
            return months / 3 + 1;
        } else if (PolicyTermEnum.PRODUCT_PREMIUM_FREQUENCY.SEMIANNUAL.name().equals(premiumFrequency)) {
            return months / 6 + 1;
        } else if (PolicyTermEnum.PRODUCT_PREMIUM_FREQUENCY.YEAR.name().equals(premiumFrequency)) {
            return months / 12 + 1;
        } else if (PolicyTermEnum.PRODUCT_PREMIUM_FREQUENCY.SINGLE.name().equals(premiumFrequency)) {
            return months / 12 + 1;
        }
        return 1;
    }

    /**
     * 计算当期应收时间
     *
     * @param renewalYearMonth 应收年月
     * @param effectiveDate    承保时间
     * @return 应收时间
     */
    public Long calculateNowReceivableDate(String renewalYearMonth, Long effectiveDate) {
        if (!(AssertUtils.isNotEmpty(renewalYearMonth) && AssertUtils.isNotNull(effectiveDate))) {
            return null;
        }
        int months = DateUtils.getMonthSpace(DateUtils.timeStrToString(effectiveDate, DateUtils.FORMATE16), renewalYearMonth);
        return DateUtils.addStringMonthRT(effectiveDate, months);
    }
}
