package com.gclife.policy.model.bo;

import com.gclife.common.annotation.BigDecimalFormat;
import com.gclife.common.annotation.DateFormat;
import com.gclife.common.annotation.Internation;
import com.gclife.common.annotation.Internations;
import com.gclife.common.model.config.DateFormatPatternEnum;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * create 17-11-8
 * description:
 */
@Data
public class PolicyTodoListBo {
    @ApiModelProperty(example = "保单ID")
    private String policyId;
    @ApiModelProperty(example = "保单No")
    private String policyNo;
    @ApiModelProperty(example = "产品名称")
    private String productName;

    private String insuredName;

    private String relationship;

    private String occupationCode;

    @ApiModelProperty(example = "投保人")
    private String applicantName;

    @ApiModelProperty(example = "保障期限")
    private String securityDate;

    @ApiModelProperty(example = "保单状态")
    private String policyStatus;

    @ApiModelProperty(example = "保单类型")
    private String policyType;

    @ApiModelProperty(example = "创建时间")
    private Long createDate;
    @ApiModelProperty(example = "创建时间")
    private String createDateFormat;

    @ApiModelProperty(example = "投保日期")
    private Long applyDate;
    @ApiModelProperty(example = "投保Id")
    private String applyId;
    @ApiModelProperty(example = "投保编号")
    private String applyNo;
    @ApiModelProperty(example = "投保日期格式化")
    private String applyDateFormat;
    @ApiModelProperty(example = "保单生效日期")
    private Long effectiveDate;
    @ApiModelProperty(example = "保单生效期格式化")
    private String effectiveDateFormat;

    @ApiModelProperty(example = "支付截止时间")
    private Long paymentExpDate;

    @ApiModelProperty(example = "保单满期日期")
    private Long maturityDate;
    @ApiModelProperty(example = "保单满期日期格式化")
    private String maturityDateFormat;

    @ApiModelProperty(example = "销售机构ID")
    private String salesBranchId;

    @ApiModelProperty(example = "成保日期")
    private Long approveDate;
    @ApiModelProperty(example = "成保日期")
    private String approveDateFormat;
    @ApiModelProperty(example = "期缴总保费")
    private BigDecimal totalPremium;

    @ApiModelProperty(example = "按钮编码")
    private String buttonCode;
    @ApiModelProperty(example = "按钮名称")
    private String buttonName;
    @ApiModelProperty(example = "按钮色值")
    private String colorValue;

    @ApiModelProperty(example = "业务类型")
    private String paymentBusinessType;


    @ApiModelProperty(example = "缴费周期")
    private String premiumFrequency;

    @ApiModelProperty(example = "缴费周期")
    private BigDecimal amount;

    @ApiModelProperty(example = "保障期限")
    private String coveragePeriod;

    @ApiModelProperty(example = "保障期限单位")
    private String coveragePeriodUnit;

    /***排序字段***/
    private int orderIndex;

    private String acceptBranchId;
    private String managerBranchId;
    private String delegateBranchId;
    private String applicantId;
    private String certifyId;
    private Long   bizDate;
    private Long   hesitation;
    private Long   hesitationEndDate;
    private String applySource;
    private String channelTypeCode;
    private String currencyCode;
    private String verifyNo;
    private String providerId;
    private String signBranchId;
    private String versionNo;
    private Long   invalidDate;
    private Long   thoroughInvalidDate;
    private String autoRenewalInsurance;
    private Long   dataEffectiveDate;
    private String selfInsuranceFlag;
    private String firstPolicyNo;
    private Long   policyPeriod;
    private Long   backTrackDate;
    private Long   riskCommencementDate;
    private String firstSalesBranchId;
}
