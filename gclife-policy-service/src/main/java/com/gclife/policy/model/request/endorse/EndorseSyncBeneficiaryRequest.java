package com.gclife.policy.model.request.endorse;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * create 2022/12/9 15:23
 * description:
 */
@Data
public class EndorseSyncBeneficiaryRequest {
    @ApiModelProperty(example = "保单id")
    private String policyId;
    @ApiModelProperty(example = "数据生效日期")
    private Long dataEffectiveDate;

    private List<EndorseSyncBeneficiaryInfoRequest> syncBeneficiaryInfo;
}
