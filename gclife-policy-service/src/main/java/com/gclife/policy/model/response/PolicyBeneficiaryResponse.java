package com.gclife.policy.model.response;

import com.gclife.common.annotation.DateFormat;
import com.gclife.common.annotation.Internation;
import com.gclife.common.model.config.DateFormatPatternEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * create 17-12-25
 * description:
 */
@ApiModel(description = "受益人")
@Data
public class PolicyBeneficiaryResponse {
    @ApiModelProperty(example = "受益人ID")
    private String beneficiaryId;
    @ApiModelProperty(example = "客户id")
    private String customerId;
    @ApiModelProperty(example = "与被保人关系")
    @Internation(filed = "relationship", codeType = "RELATIONSHIP_WITH_THE_INSURED")
    private String relationship;
    @ApiModelProperty(example = "与被保人的关系说明")
    private String relationshipInstructions;
    @ApiModelProperty(example = "与被保人关系")
    private String relationshipCode;
    @ApiModelProperty(example = "姓名")
    private String name;
    @ApiModelProperty(example = "出生日期")
    private Long birthday;
    @ApiModelProperty(example = "出生日期")
    @DateFormat(filed = "birthday", pattern = DateFormatPatternEnum.FORMATE3)
    private String birthdayFormat;
    @ApiModelProperty(example = "证件类型")
    @Internation(filed = "idType",codeType = "ID_TYPE")
    private String idType;
    @ApiModelProperty(example = "性别")
    @Internation(filed = "sex",codeType = "GENDER")
    private String sex;
    @ApiModelProperty(example = "证件号码")
    private String idNo;
    @ApiModelProperty(example = "受益人顺位")
    private Long beneficiaryNo;
    @ApiModelProperty(value = "受益人顺序",example = "受益人顺序")
    private String beneficiaryNoOrder;
    @ApiModelProperty(value = "受益人顺序",example = "受益人顺序")
    @Internation(codeType = "BENEFICIARY_NO", filed = "beneficiaryNoOrder")
    private String beneficiaryNoOrderName;
    @ApiModelProperty(example = "受益份额")
    private BigDecimal beneficiaryProportion;
    @ApiModelProperty(value = "证件有效期至", example = "证件有效期至")
    private String idExpDate;

}