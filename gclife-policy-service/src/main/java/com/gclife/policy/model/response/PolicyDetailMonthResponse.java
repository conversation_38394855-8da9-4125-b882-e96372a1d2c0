package com.gclife.policy.model.response;

import com.gclife.common.annotation.BigDecimalFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

@Data
public class PolicyDetailMonthResponse {
    @ApiModelProperty(value = "指定月份的保单数", example = "5")
    private String policyNum;
    @ApiModelProperty(value = "指定月份的总保费数", example = "15000")
    private BigDecimal policyTotalPremium;
}
