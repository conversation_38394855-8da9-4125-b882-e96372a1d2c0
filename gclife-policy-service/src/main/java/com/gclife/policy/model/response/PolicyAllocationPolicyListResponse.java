package com.gclife.policy.model.response;

import com.gclife.common.annotation.Internation;
import com.gclife.common.model.BaseResponse;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

/**
 * <AUTHOR>
 * @date 2021/11/3
 */
@Getter
@Setter
public class PolicyAllocationPolicyListResponse extends BaseResponse {
    @ApiModelProperty(value = "保单ID")
    private String policyId;
    @ApiModelProperty(value = "保单号码")
    private String policyNo;
    @ApiModelProperty(value = "所属渠道")
    private String channelTypeCode;
    @ApiModelProperty(example = "所属渠道国际化")
    @Internation(codeType = "CHANNEL_TYPE",filed = "channelTypeCode")
    private String channelTypeCodeI18N;
    @ApiModelProperty(value = "保单类型")
    private String policyType;
    @Internation(codeType = "POLICY_TYPE",filed = "policyType")
    @ApiModelProperty(value = "保单类型国际化")
    private String policyTypeI18N;
    @ApiModelProperty(value = "险种名称")
    private String coverageName;
    @ApiModelProperty(value = "当前业务员Id")
    private String currentAgentId;
    @ApiModelProperty(value = "当前业务员姓名")
    private String currentAgentName;
    @ApiModelProperty(value = "当前业务员代码")
    private String currentAgentCode;
    @ApiModelProperty(value = "当前业务员状态")
    private String currentAgentStatus;
    @Internation(filed = "currentAgentStatus", codeType = "AGENT_STATUS")
    @ApiModelProperty(value = "当前业务员状态国际化")
    private String currentAgentStatusI18N;

    @ApiModelProperty(value = "当前服务业务员Id")
    private String agentId;
    @ApiModelProperty(value = "当前服务业务员姓名")
    private String agentName;
    @ApiModelProperty(value = "当前服务业务员代码")
    private String agentCode;
    @ApiModelProperty(value = "当前服务业务员状态")
    private String agentStatus;
    @Internation(filed = "agentStatus", codeType = "AGENT_STATUS")
    @ApiModelProperty(value = "当前服务业务员状态国际化")
    private String agentStatusI18N;

    @ApiModelProperty(example = "推荐业务员ID")
    private String recommendAgentId;
    @ApiModelProperty(example = "推荐业务员姓名")
    private String recommendAgentName;
    @ApiModelProperty(example = "推荐业务员代码")
    private String recommendAgentCode;
    @ApiModelProperty(example = "推荐业务员状态")
    private String recommendAgentStatus;
    @Internation(filed = "recommendAgentStatus", codeType = "AGENT_STATUS")
    @ApiModelProperty(example = "推荐业务员状态名称")
    private String recommendAgentStatusName;

    @ApiModelProperty(value = "分配状态")
    private String assignStatus;
    @Internation(filed = "assignStatus", codeType = "ASSIGN_STATUS")
    @ApiModelProperty(value = "分配状态国际化")
    private String assignStatusI18N;

    @ApiModelProperty(example = "总行数")
    private Integer totalLine;
}
