package com.gclife.policy.model.response;

import io.swagger.annotations.ApiModelProperty;

/**
 * <AUTHOR>
 * @version v1.0
 * Description: 保费对账说明
 * @date 18-1-15
 */
public class PremiumRemarkResponse {
    @ApiModelProperty(example = "对账批次ID")
    private String checkId;
    @ApiModelProperty(example = "备注")
    private String checkRemark;

    public String getCheckId() {
        return checkId;
    }

    public void setCheckId(String checkId) {
        this.checkId = checkId;
    }

    public String getCheckRemark() {
        return checkRemark;
    }

    public void setCheckRemark(String checkRemark) {
        this.checkRemark = checkRemark;
    }
}
