package com.gclife.policy.model.request.renewal;

import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @version v2.0
 * Description:
 * @date 18-12-20
 */
@Data
public class RenewalInsuranceEffectRequest {
    private String     policyId;
    private Long       receivableDate;
    private Long       gainedDate;
    private BigDecimal paymentAmount;
    private String     paymentStatus;
    private String     paymentModeCode;
    private String     renewalId;
    private String     paymentId;

    /**
     * 附加险是否续保参数
     */
    private List<RenewalInsuranceAdditionalRequest> renewalInsuranceAdditional;
}
