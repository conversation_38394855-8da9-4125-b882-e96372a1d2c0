package com.gclife.policy.model.request;

import io.swagger.annotations.ApiModelProperty;

import java.util.List;

/**
 * <AUTHOR>
 * @description
 * @date 2019-07-04 14:25
 */
public class PolicyReturnVisitChangeRequest {

    @ApiModelProperty(example = "保单回访ID")
    private String policyReturnVisitId;
    @ApiModelProperty(example = "保单修改申请备注")
    private String operationRemark;
    /**
     * 回访附件
     */
    private List<PolicyReturnVisitAttachmentRequest> attachmentList;


    public String getPolicyReturnVisitId() {
        return policyReturnVisitId;
    }

    public void setPolicyReturnVisitId(String policyReturnVisitId) {
        this.policyReturnVisitId = policyReturnVisitId;
    }

    public String getOperationRemark() {
        return operationRemark;
    }

    public void setOperationRemark(String operationRemark) {
        this.operationRemark = operationRemark;
    }

    public List<PolicyReturnVisitAttachmentRequest> getAttachmentList() {
        return attachmentList;
    }

    public void setAttachmentList(List<PolicyReturnVisitAttachmentRequest> attachmentList) {
        this.attachmentList = attachmentList;
    }
}
