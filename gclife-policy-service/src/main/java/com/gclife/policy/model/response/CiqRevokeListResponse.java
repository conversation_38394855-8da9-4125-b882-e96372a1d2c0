package com.gclife.policy.model.response;

import io.swagger.annotations.ApiModelProperty;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @version v1.0
 * Description: 海关撤单列表
 * @date 18-3-23
 */
public class CiqRevokeListResponse {
    @ApiModelProperty(example = "保单Id")
    private String policyId;
    @ApiModelProperty(example = "承保(投保)时间")
    private Long   approveDate;
    @ApiModelProperty(example = "机构ID")
    private String branchId;
    @ApiModelProperty(example = "投保网点")
    private String branchName;
    @ApiModelProperty(example = "证件类型")
    private String idType;
    @ApiModelProperty(example = "护照号码")
    private String idNo;
    @ApiModelProperty(example = "投保人")
    private String name;
    @ApiModelProperty(example = "录单员")
    private String agentName;
    @ApiModelProperty(example = "录单员工号")
    private String agentCode;
    @ApiModelProperty(example = "录单员ID")
    private String agentId;
    @ApiModelProperty(example = "撤单金额")
    private BigDecimal revokeAmt;
    @ApiModelProperty(example = "撤单员")
    private String revokeAgentName;
    @ApiModelProperty(example = "撤单员工号")
    private String revokeAgentCode;
    @ApiModelProperty(example = "撤单员用户ID")
    private String revokeUserId;
    @ApiModelProperty(example = "撤单时间")
    private BigDecimal revokeDate;

    public String getPolicyId() {
        return policyId;
    }

    public void setPolicyId(String policyId) {
        this.policyId = policyId;
    }

    public Long getApproveDate() {
        return approveDate;
    }

    public void setApproveDate(Long approveDate) {
        this.approveDate = approveDate;
    }

    public String getBranchId() {
        return branchId;
    }

    public void setBranchId(String branchId) {
        this.branchId = branchId;
    }

    public String getBranchName() {
        return branchName;
    }

    public void setBranchName(String branchName) {
        this.branchName = branchName;
    }

    public String getIdType() {
        return idType;
    }

    public void setIdType(String idType) {
        this.idType = idType;
    }

    public String getIdNo() {
        return idNo;
    }

    public void setIdNo(String idNo) {
        this.idNo = idNo;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public BigDecimal getRevokeAmt() {
        return revokeAmt;
    }

    public void setRevokeAmt(BigDecimal revokeAmt) {
        this.revokeAmt = revokeAmt;
    }

    public String getAgentName() {
        return agentName;
    }

    public void setAgentName(String agentName) {
        this.agentName = agentName;
    }

    public String getAgentCode() {
        return agentCode;
    }

    public void setAgentCode(String agentCode) {
        this.agentCode = agentCode;
    }

    public String getAgentId() {
        return agentId;
    }

    public void setAgentId(String agentId) {
        this.agentId = agentId;
    }

    public String getRevokeAgentName() {
        return revokeAgentName;
    }

    public void setRevokeAgentName(String revokeAgentName) {
        this.revokeAgentName = revokeAgentName;
    }

    public String getRevokeAgentCode() {
        return revokeAgentCode;
    }

    public void setRevokeAgentCode(String revokeAgentCode) {
        this.revokeAgentCode = revokeAgentCode;
    }

    public String getRevokeUserId() {
        return revokeUserId;
    }

    public void setRevokeUserId(String revokeUserId) {
        this.revokeUserId = revokeUserId;
    }

    public BigDecimal getRevokeDate() {
        return revokeDate;
    }

    public void setRevokeDate(BigDecimal revokeDate) {
        this.revokeDate = revokeDate;
    }
}
