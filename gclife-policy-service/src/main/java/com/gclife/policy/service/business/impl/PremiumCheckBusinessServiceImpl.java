package com.gclife.policy.service.business.impl;

import com.gclife.common.exception.RequestException;
import com.gclife.common.model.BaseExcelResponse;
import com.gclife.common.model.BasePageResponse;
import com.gclife.common.model.BaseResponse;
import com.gclife.common.model.ResultObject;
import com.gclife.common.model.base.Users;
import com.gclife.common.model.config.TerminologyTypeEnum;
import com.gclife.common.model.feign.SyscodeRespFc;
import com.gclife.common.service.impl.BaseExcelBusinessServiceImpl;
import com.gclife.common.util.AssertUtils;
import com.gclife.common.util.DateUtils;
import com.gclife.common.util.UUIDUtils;
import com.gclife.platform.api.PlatformBranchApi;
import com.gclife.platform.model.response.BranchResponse;
import com.gclife.policy.core.jooq.tables.pojos.PremiumCheckAttachPo;
import com.gclife.policy.core.jooq.tables.pojos.PremiumCheckPo;
import com.gclife.policy.core.jooq.tables.pojos.PremiumCheckRelationPo;
import com.gclife.policy.core.jooq.tables.pojos.PremiumPersistPo;
import com.gclife.policy.dao.PremiumCheckExtDao;
import com.gclife.platform.api.PlatformBaseInternationServiceApi;
import com.gclife.policy.model.bo.*;
import com.gclife.policy.model.config.PolicyErrorConfigEnum;
import com.gclife.policy.model.config.PolicyTermEnum;
import com.gclife.policy.model.excel.PremiumCheckExcelBo;
import com.gclife.policy.model.request.*;
import com.gclife.policy.model.response.*;
import com.gclife.policy.service.business.PremiumCheckBusinessService;
import com.gclife.policy.service.data.PremiumCheckService;
import com.gclife.policy.validate.parameter.PremiumCheckParameterValidate;
import com.gclife.policy.validate.transfer.PremiumCheckTransfer;
import org.apache.poi.ss.usermodel.Workbook;
import org.jeecgframework.poi.excel.ExcelExportUtil;
import org.jeecgframework.poi.excel.entity.ExportParams;
import org.modelmapper.TypeToken;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.interceptor.TransactionAspectSupport;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version v1.0
 * Description: 保费对账
 * @date 18-1-9
 */
@Service
public class PremiumCheckBusinessServiceImpl extends BaseExcelBusinessServiceImpl implements PremiumCheckBusinessService {
    @Autowired
    private PremiumCheckExtDao premiumCheckExtDao;
    @Autowired
    private PremiumCheckParameterValidate premiumCheckParameterValidate;
    @Autowired
    private PremiumCheckTransfer premiumCheckTransfer;
    @Autowired
    private PremiumCheckService premiumCheckService;
    @Autowired
    private PlatformBaseInternationServiceApi platformBaseInternationServiceApi;
    @Autowired
    private PlatformBranchApi platformBranchApi;
    /**
     * 应收应付数据写入
     * @param request 请求对象
     * @param userId 用户ID
     * @return 结果
     */
    @Override
    @Transactional
    public ResultObject<BaseResponse> savePremiumPersist(PremiumPersistRequest request, String userId) {
        ResultObject<BaseResponse> resultObject = new ResultObject<>();
        try {
            //参数校验
            premiumCheckParameterValidate.validParameterPremiumPersist(request);
            //数据转换
            PremiumPersistPo premiumPersistPo = premiumCheckTransfer.transferPremiumPersist(request, userId);
            //数据保存
            premiumCheckService.savePremiumPersistPo(premiumPersistPo);
        } catch (Exception e) {
            //事务回滚
            TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
            if (e instanceof RequestException) {
                RequestException error = (RequestException) e;
                //错误设置
                resultObject.setIenum(error.getiEnum());
            } else {
                resultObject.setIenum(PolicyErrorConfigEnum.POLICY_FAIL);
            }
        }
        return resultObject;
    }

    /**
     * 获取保费对账清单
     * @param request 请求对象
     * @return 对账清单
     */
    @Override
    public ResultObject<BasePageResponse<PremiumCheckListResponse>> getPremiumCheckList(PremiumCheckListRequest request) {
        ResultObject<BasePageResponse<PremiumCheckListResponse>> resultObject = new ResultObject<>();
        try {
            // 调用平台微服务查询当前用户管理机构的叶子机构
            List<BranchResponse> branchRespFcs = platformBranchApi.userManagerLeafBranchs().getData();
            List<String> branchIds = branchRespFcs.stream().map(BranchResponse::getBranchId).distinct().collect(Collectors.toList());

            // 获取保费对账清单
            List<PremiumCheckListBo> premiumCheckListBos = this.premiumCheckExtDao.getPremiumCheckPo(request, branchIds);

            // 数据转换
            List<PremiumCheckListResponse> premiumCheckListRespons = (List<PremiumCheckListResponse>) this.converterList(
                    premiumCheckListBos, new TypeToken<List<PremiumCheckListResponse>>() {}.getType()
            );

            //获取总页数
            Integer totalLine = AssertUtils.isNotNull(premiumCheckListBos) ? premiumCheckListBos.get(0).getTotalLine() : null;
            BasePageResponse basePageResponse = BasePageResponse.getData(
                    request.getCurrentPage(), request.getPageSize(), totalLine, premiumCheckListRespons);

            resultObject.setData(basePageResponse);
        } catch (Exception e) {
            if (e instanceof RequestException) {
                RequestException error = (RequestException) e;
                //错误设置
                resultObject.setIenum(error.getiEnum());
            } else {
                resultObject.setIenum(PolicyErrorConfigEnum.POLICY_FAIL);
            }
        }
        return resultObject;
    }

    /**
     * 保费对账清单导出
     * @param currentLoginUsers 当前登陆用户
     * @param request 请求对象
     * @return ResultObject
     */
    @Override
    @Transactional
    public ResultObject<BaseExcelResponse> premiumCheckExport(Users currentLoginUsers, PremiumCheckExportRequest request) {
        ResultObject<BaseExcelResponse> resultObject = new ResultObject<>();
        BaseExcelResponse baseExcelResponse = new BaseExcelResponse();
        try {
            // 校验
            AssertUtils.isNotEmpty(this.getLogger(),request.getApplyDates(), PolicyErrorConfigEnum.POLICY_PARAMETER_APPLY_DATE_IS_NOT_NULL);
            // 调用平台微服务查询当前用户管理机构的叶子机构
            List<BranchResponse> respFcs = platformBranchApi.userManagerLeafBranchs().getData();
            List<String> branchIds = respFcs.stream().map(BranchResponse::getBranchId).distinct().collect(Collectors.toList());

            // 查询数据
            List<PremiumPersistPo> premiumPersistPos = premiumCheckExtDao.getPremiumPersistPoList(request.getApplyDates(), branchIds);
            // 数据转换
            List<PremiumCheckExcelBo> premiumCheckExcelBos = (List<PremiumCheckExcelBo>) this.converterList(
                    premiumPersistPos, new TypeToken<List<PremiumCheckExcelBo>>() {}.getType()
            );

            if(AssertUtils.isNotEmpty(premiumCheckExcelBos)) {
                // 币种
                List<SyscodeRespFc> currencySyscodes = platformBaseInternationServiceApi.getTerminologyList(TerminologyTypeEnum.CURRENCY.name()).getData();

                premiumCheckExcelBos.forEach((excelBo -> {
                    if (AssertUtils.isNotEmpty(currencySyscodes) && AssertUtils.isNotEmpty(excelBo.getCurrencyCode())) {
                        currencySyscodes.stream()
                                .filter(syscode -> syscode.getCodeKey().equals(excelBo.getCurrencyCode()))
                                .findFirst().ifPresent(syscode -> {
                            excelBo.setCurrencyCode(syscode.getSymbol());
                        });
                    }
                }));

                // 调用平台微服务查询当前用户管理机构的叶子机构
                List<BranchResponse> branchRespFcs = platformBranchApi.userManagerLeafBranchs().getData();

                premiumCheckExcelBos.forEach(excelBo -> {
                    if(AssertUtils.isNotEmpty(branchRespFcs) && AssertUtils.isNotEmpty(excelBo.getBizBranchId())) {
                        branchRespFcs.stream()
                                .filter(branchRespFc -> excelBo.getBizBranchId().equals(branchRespFc.getBranchId()))
                                .findFirst().ifPresent(branchRespFc -> {
                            excelBo.setBizBranchId(branchRespFc.getBranchName());
                        });
                    }
                });
            } else {
                premiumCheckExcelBos = new ArrayList<>();
            }

            //创建excel
            Workbook workbook = ExcelExportUtil.exportExcel(new ExportParams(PolicyTermEnum.EXCEL_FILE_SHEET_TITLE.PREMIUM_CHECK_EXPORT.name(),
                            PolicyTermEnum.EXCEL_FILE_SHEET_NAME.PREMIUM_CHECK_EXPORT.name()),
                    PremiumCheckExcelBo.class, premiumCheckExcelBos);
            baseExcelResponse.setFileName(PolicyTermEnum.EXCEL_FILE_NAME.PREMIUM_CHECK_EXPORT.name());
            baseExcelResponse.setWorkbook(workbook);
            // 国际化
            this.excelInternationalization(baseExcelResponse, true);

            if(AssertUtils.isNotEmpty(premiumPersistPos)) {
                /**
                 * 1.更新应付应收清单表
                 */
                premiumPersistPos.forEach(premiumPersistPo -> {
                    premiumPersistPo.setUpdatedUserId(currentLoginUsers.getUserId());
                    premiumPersistPo.setUpdatedDate(DateUtils.getCurrentTime());
                    premiumPersistPo.setStatusCode(PolicyTermEnum.PERSIST_STATUS.CHECK.name());
                });
                premiumCheckService.updatePremiumPersistPos(premiumPersistPos);

                /**
                 * 2.存对账清单表
                 */
                String checkId = UUIDUtils.getUUIDStr();

                // new一个po对象
                PremiumCheckPo premiumCheckPo =
                        premiumCheckTransfer.transferPremiumCheckPo(currentLoginUsers.getUserId(), checkId, premiumPersistPos, request);

                premiumCheckService.savePremiumCheckPo(premiumCheckPo);

                /**
                 * 3.存对账清单关联表
                 */
                List<PremiumCheckRelationPo> premiumCheckRelationPos = new ArrayList<>();
                premiumPersistPos.stream().forEach(premiumPersistPo -> {
                    // new一个po对象
                    PremiumCheckRelationPo premiumCheckRelationPo =
                            premiumCheckTransfer.transferPremiumCheckRelationPo(currentLoginUsers.getUserId(), checkId, premiumPersistPo);

                    premiumCheckRelationPos.add(premiumCheckRelationPo);
                });
                premiumCheckService.savePremiumCheckRelationPos(premiumCheckRelationPos);
            }

            resultObject.setData(baseExcelResponse);
        } catch (Exception e) {
            //事务回滚
            TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
            if (e instanceof RequestException) {
                RequestException error = (RequestException) e;
                //错误设置
                resultObject.setIenum(error.getiEnum());
            } else {
                resultObject.setIenum(PolicyErrorConfigEnum.POLICY_FAIL);
            }
        }
        return resultObject;
    }

    /**
     * 查询保费应收列表
     * @param request 请求对象
     * @return list
     */
    @Override
    public ResultObject<BasePageResponse<PremiumReceivableListResponse>> getPremiumReceivableList(PremiumReceivableListRequest request) {
        ResultObject<BasePageResponse<PremiumReceivableListResponse>> resultObject = new ResultObject<>();
        try {
            // 调用平台微服务查询当前用户管理的销售机构集合
            List<BranchResponse> branchRespFcs = platformBranchApi.userManagerBranchs2().getData();
            List<String> branchIds = branchRespFcs.stream().map(BranchResponse::getBranchId).distinct().collect(Collectors.toList());

            // 获取保费对账清单
            List<PremiumReceivableListBo> premiumReceivableListBos = this.premiumCheckExtDao.getPremiumReceivableList(request, branchIds);

            // 数据转换
            List<PremiumReceivableListResponse> premiumCheckListResponses = (List<PremiumReceivableListResponse>) this.converterList(
                    premiumReceivableListBos, new TypeToken<List<PremiumReceivableListResponse>>() {}.getType()
            );

            if(AssertUtils.isNotEmpty(premiumCheckListResponses)) {
                // 应收状态
                List<SyscodeRespFc> receivableStatusSyscodes =
                        platformBaseInternationServiceApi.getTerminologyList(PolicyTermEnum.INTERNATIONAL_TEXT.RECEIVABLE_STATUS.name()).getData();
                premiumCheckListResponses.forEach(response -> {
                    if (AssertUtils.isNotEmpty(receivableStatusSyscodes) && AssertUtils.isNotEmpty(response.getReceivableStatus())) {
                        receivableStatusSyscodes.stream()
                                .filter(syscode -> response.getReceivableStatus().equals(syscode.getCodeKey()))
                                .findFirst().ifPresent(syscode -> {
                                    response.setReceivableStatus(syscode.getCodeName());
                        });
                    }
                });
            }

            //获取总页数
            Integer totalLine = AssertUtils.isNotNull(premiumReceivableListBos) ? premiumReceivableListBos.get(0).getTotalLine() : null;
            BasePageResponse basePageResponse = BasePageResponse.getData(
                    request.getCurrentPage(), request.getPageSize(), totalLine, premiumCheckListResponses);

            resultObject.setData(basePageResponse);
        } catch (Exception e) {
            if (e instanceof RequestException) {
                RequestException error = (RequestException) e;
                //错误设置
                resultObject.setIenum(error.getiEnum());
            } else {
                resultObject.setIenum(PolicyErrorConfigEnum.POLICY_FAIL);
            }
        }
        return resultObject;
    }

    /**
     * 查询保费应收详情列表
     * @param request 请求对象
     * @return list
     */
    @Override
    public ResultObject<BasePageResponse<PremiumReceivableDetailResponse>> getPremiumReceivableDetail(PremiumReceivableDetailRequest request) {
        ResultObject<BasePageResponse<PremiumReceivableDetailResponse>> resultObject = new ResultObject<>();
        try {
            // 参数校验
            AssertUtils.isNotEmpty(this.getLogger(), request.getCheckId(), PolicyErrorConfigEnum.POLICY_PARAMETER_CHECK_ID_IS_NOT_NULL);
            // 查询数据
            List<PremiumReceivableDetailBo> premiumReceivableDetailBos = this.premiumCheckExtDao.getPremiumReceivableDetail(request);
            // 数据转换
            List<PremiumReceivableDetailResponse> responses = (List<PremiumReceivableDetailResponse>) this.converterList(
                    premiumReceivableDetailBos, new TypeToken<List<PremiumReceivableDetailResponse>>() {}.getType()
            );

            if(AssertUtils.isNotEmpty(responses)) {
                // 调平台微服务查机构信息
                List<String> branchIds = responses.stream().map(PremiumReceivableDetailResponse :: getBizBranchId).collect(Collectors.toList());
                List<BranchResponse> branchRespFcs = platformBranchApi.userParentBranchs(branchIds).getData();
                responses.stream().forEach(response -> {
                    if(AssertUtils.isNotNull(branchRespFcs) && AssertUtils.isNotEmpty(response.getBizBranchId())) {
                        branchRespFcs.stream()
                                .filter(branchRespFc -> response.getBizBranchId().equals(branchRespFc.getBranchId()))
                                .findFirst().ifPresent(branchRespFc -> response.setBizBranchName(branchRespFc.getBranchName()));
                    }
                });
            }

            //获取总页数
            Integer totalLine = AssertUtils.isNotNull(premiumReceivableDetailBos) ? premiumReceivableDetailBos.get(0).getTotalLine() : null;
            BasePageResponse basePageResponse = BasePageResponse.getData(
                    request.getCurrentPage(), request.getPageSize(), totalLine, responses);

            resultObject.setData(basePageResponse);
        } catch (Exception e) {
            if (e instanceof RequestException) {
                RequestException error = (RequestException) e;
                //错误设置
                resultObject.setIenum(error.getiEnum());
            } else {
                resultObject.setIenum(PolicyErrorConfigEnum.POLICY_FAIL);
            }
        }
        return resultObject;
    }

    /**
     * 保费应收确认
     * @param checkIds 对账批次ID
     * @param userId 当前用户ID
     * @return 结果
     */
    @Override
    @Transactional
    public ResultObject<BaseResponse> confirmReceivable(List<String> checkIds, String userId) {
        ResultObject<BaseResponse> resultObject = new ResultObject<>();
        try {
            // 参数校验
            AssertUtils.isNotEmpty(this.getLogger(), checkIds, PolicyErrorConfigEnum.POLICY_PARAMETER_CHECK_ID_IS_NOT_NULL);
            // 查询对账
            List<PremiumCheckPo> premiumCheckPos = this.premiumCheckExtDao.getPremiumCheckPos(checkIds);
            // 数据验证
            AssertUtils.isNotEmpty(this.getLogger(), premiumCheckPos, PolicyErrorConfigEnum.POLICY_BUSINESS_PREMIUM_CHECK_IS_NOT_FOUND_OBJECT);

            // 检查是否已处理
            premiumCheckPos.forEach(premiumCheckPo -> {
                if (PolicyTermEnum.RECEIVABLE_STATUS.ENTER.name().equals(premiumCheckPo.getReceivableStatus())) {
                    throw new RequestException(PolicyErrorConfigEnum.POLICY_BUSINESS_PREMIUM_CHECK_ERROR);
                }
            });

            /**
             * 1.更新对账清单表
             */
            this.premiumCheckTransfer.receivableSetCheckPo(premiumCheckPos, userId);
            // 更新数据
            this.premiumCheckService.updatePremiumCheckPo(premiumCheckPos);

            /**
             * 2.更新保费应收应付清单表
             */
            // 查询实收实付清单表数据
            List<PremiumPersistPo> premiumPersistPos = this.premiumCheckExtDao.getPremiumPersistPoList(checkIds);
            // 数据验证
            AssertUtils.isNotEmpty(this.getLogger(), premiumPersistPos, PolicyErrorConfigEnum.POLICY_BUSINESS_PREMIUM_PERSIST_IS_NOT_FOUND_OBJECT);
            // 设置状态
            premiumPersistPos.forEach(premiumPersistPo -> {
                premiumPersistPo.setStatusCode(PolicyTermEnum.PERSIST_STATUS.CHECK.name());
                premiumPersistPo.setUpdatedDate(DateUtils.getCurrentTime());
                premiumPersistPo.setUpdatedUserId(userId);
            });
            this.premiumCheckService.updatePremiumPersistPos(premiumPersistPos);

        } catch (Exception e) {
            //事务回滚
            TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
            if (e instanceof RequestException) {
                RequestException error = (RequestException) e;
                //错误设置
                resultObject.setIenum(error.getiEnum());
            } else {
                resultObject.setIenum(PolicyErrorConfigEnum.POLICY_FAIL);
            }
        }
        return resultObject;
    }

    /**
     * 查看对账备注说明
     * @param checkId 对账批次ID
     * @return 备注
     */
    @Override
    public ResultObject<PremiumRemarkResponse> getPremiumRemark(String checkId) {
        ResultObject<PremiumRemarkResponse> resultObject = new ResultObject<>();
        try {
            // 参数校验
            AssertUtils.isNotEmpty(this.getLogger(), checkId, PolicyErrorConfigEnum.POLICY_PARAMETER_CHECK_ID_IS_NOT_NULL);
            // 查询数据
            PremiumCheckPo premiumCheckPo = this.premiumCheckExtDao.getPremiumCheckPo(checkId);
            // 数据验证
            AssertUtils.isNotNull(this.getLogger(), premiumCheckPo, PolicyErrorConfigEnum.POLICY_BUSINESS_PREMIUM_CHECK_IS_NOT_FOUND_OBJECT);
            PremiumRemarkResponse premiumRemarkResponse = (PremiumRemarkResponse) this.converterObject(premiumCheckPo, PremiumRemarkResponse.class);

            resultObject.setData(premiumRemarkResponse);
        } catch (Exception e) {
            if (e instanceof RequestException) {
                RequestException error = (RequestException) e;
                //错误设置
                resultObject.setIenum(error.getiEnum());
            } else {
                resultObject.setIenum(PolicyErrorConfigEnum.POLICY_FAIL);
            }
        }
        return resultObject;
    }

    /**
     * 查询保费实收列表
     * @param request 请求对象
     * @return 分页list
     */
    @Override
    public ResultObject<BasePageResponse<PremiumActualListResponse>> getPremiumActualList(PremiumActualListRequest request) {
        ResultObject<BasePageResponse<PremiumActualListResponse>> resultObject = new ResultObject<>();
        try {
            // 调用平台微服务查询当前用户管理的销售机构集合
            List<BranchResponse> branchRespFcs = platformBranchApi.userManagerBranchs2().getData();
            List<String> branchIds = branchRespFcs.stream().map(BranchResponse::getBranchId).distinct().collect(Collectors.toList());

            // 获取保费对账清单
            List<PremiumActualListBo> premiumActualListBos = this.premiumCheckExtDao.getPremiumActualList(request, branchIds);

            // 数据转换
            List<PremiumActualListResponse> premiumActualListResponses = (List<PremiumActualListResponse>) this.converterList(
                    premiumActualListBos, new TypeToken<List<PremiumActualListResponse>>() {}.getType()
            );

            if(AssertUtils.isNotEmpty(premiumActualListResponses)) {
                // 应收状态
                List<SyscodeRespFc> actualStatusSyscodes =
                        platformBaseInternationServiceApi.getTerminologyList(PolicyTermEnum.INTERNATIONAL_TEXT.ACTUAL_STATUS.name()).getData();

                premiumActualListResponses.forEach(response -> {
                    if (AssertUtils.isNotEmpty(actualStatusSyscodes) && AssertUtils.isNotEmpty(response.getActualStatus())) {
                        actualStatusSyscodes.stream()
                                .filter(syscode -> response.getActualStatus().equals(syscode.getCodeKey()))
                                .findFirst().ifPresent(syscode -> response.setActualStatus(syscode.getCodeName()));
                    }
                });
            }

            //获取总页数
            Integer totalLine = AssertUtils.isNotNull(premiumActualListBos) ? premiumActualListBos.get(0).getTotalLine() : null;
            BasePageResponse basePageResponse = BasePageResponse.getData(
                    request.getCurrentPage(), request.getPageSize(), totalLine, premiumActualListResponses);

            resultObject.setData(basePageResponse);
        } catch (Exception e) {
            if (e instanceof RequestException) {
                RequestException error = (RequestException) e;
                //错误设置
                resultObject.setIenum(error.getiEnum());
            } else {
                resultObject.setIenum(PolicyErrorConfigEnum.POLICY_FAIL);
            }
        }
        return resultObject;
    }

    /**
     * 查询保费实收详情列表
     * @param request 请求对象
     * @return 分页list
     */
    @Override
    public ResultObject<BasePageResponse<PremiumActualDetailResponse>> getPremiumActualDetail(PremiumActualDetailRequest request) {
        ResultObject<BasePageResponse<PremiumActualDetailResponse>> resultObject = new ResultObject<>();
        try {
            // 参数校验
            AssertUtils.isNotEmpty(this.getLogger(), request.getCheckId(), PolicyErrorConfigEnum.POLICY_PARAMETER_CHECK_ID_IS_NOT_NULL);
            // 查询数据
            List<PremiumActualDetailBo> premiumActualDetailBos = this.premiumCheckExtDao.getPremiumActualDetail(request);
            // 数据转换
            List<PremiumActualDetailResponse> responses = (List<PremiumActualDetailResponse>) this.converterList(
                    premiumActualDetailBos, new TypeToken<List<PremiumActualDetailResponse>>() {}.getType()
            );

            if(AssertUtils.isNotEmpty(responses)) {
                // 调平台微服务查机构信息 TODO 未展示全部层级机构名称
                List<String> branchIds = responses.stream().map(PremiumActualDetailResponse :: getBizBranchId).collect(Collectors.toList());
                List<BranchResponse> branchRespFcs = platformBranchApi.userParentBranchs(branchIds).getData();
                responses.stream().forEach(response -> {
                    if(AssertUtils.isNotNull(branchRespFcs) && AssertUtils.isNotEmpty(response.getBizBranchId())) {
                        branchRespFcs.stream()
                                .filter(branchRespFc -> response.getBizBranchId().equals(branchRespFc.getBranchId()))
                                .findFirst().ifPresent(branchRespFc -> response.setBizBranchName(branchRespFc.getBranchName()));
                    }
                });
            }

            //获取总页数
            Integer totalLine = AssertUtils.isNotNull(premiumActualDetailBos) ? premiumActualDetailBos.get(0).getTotalLine() : null;
            BasePageResponse basePageResponse = BasePageResponse.getData(
                    request.getCurrentPage(), request.getPageSize(), totalLine, responses);

            resultObject.setData(basePageResponse);
        } catch (Exception e) {
            if (e instanceof RequestException) {
                RequestException error = (RequestException) e;
                //错误设置
                resultObject.setIenum(error.getiEnum());
            } else {
                resultObject.setIenum(PolicyErrorConfigEnum.POLICY_FAIL);
            }
        }
        return resultObject;
    }

    /**
     * 保费实收确认
     * @param checkIds 对账批次IDs
     * @param userId 当前用户ID
     * @return 结果
     */
    @Override
    @Transactional
    public ResultObject<BaseResponse> confirmActual(List<String> checkIds, String userId) {
        ResultObject<BaseResponse> resultObject = new ResultObject<>();
        try {
            // 参数校验
            AssertUtils.isNotEmpty(this.getLogger(), checkIds, PolicyErrorConfigEnum.POLICY_PARAMETER_CHECK_ID_IS_NOT_NULL);
            // 查询对账
            List<PremiumCheckPo> premiumCheckPos = this.premiumCheckExtDao.getPremiumCheckPos(checkIds);
            // 数据验证
            AssertUtils.isNotEmpty(this.getLogger(), premiumCheckPos, PolicyErrorConfigEnum.POLICY_BUSINESS_PREMIUM_CHECK_IS_NOT_FOUND_OBJECT);

            // 检查是否已处理
            premiumCheckPos.forEach(premiumCheckPo -> {
                if (PolicyTermEnum.ACTUAL_STATUS.ENTER.name().equals(premiumCheckPo.getActualStatus())) {
                    // 已确认收款
                    throw new RequestException(PolicyErrorConfigEnum.POLICY_BUSINESS_PREMIUM_ACTUAL_ERROR);
                } else if(PolicyTermEnum.ACTUAL_STATUS.INITIATE.name().equals(premiumCheckPo.getActualStatus())) {
                    // 未收款
                    throw new RequestException(PolicyErrorConfigEnum.POLICY_BUSINESS_PREMIUM_ACTUAL_INITIATE_ERROR);
                }
            });

            /**
             * 1.更新对账清单表
             */
            this.premiumCheckTransfer.actualSetCheckPo(premiumCheckPos, userId);
            // 保存数据
            this.premiumCheckService.updatePremiumCheckPo(premiumCheckPos);

            /**
             * 2.更新保费应收应付清单表
             */
            // 查询实收实付清单表数据
            List<PremiumPersistPo> premiumPersistPos = this.premiumCheckExtDao.getPremiumPersistPoList(checkIds);
            // 数据验证
            AssertUtils.isNotEmpty(this.getLogger(), premiumPersistPos, PolicyErrorConfigEnum.POLICY_BUSINESS_PREMIUM_PERSIST_IS_NOT_FOUND_OBJECT);
            // 设置状态
            premiumPersistPos.forEach(premiumPersistPo -> {
                premiumPersistPo.setStatusCode(PolicyTermEnum.PERSIST_STATUS.ACTUAL.name());
                premiumPersistPo.setUpdatedDate(DateUtils.getCurrentTime());
                premiumPersistPo.setUpdatedUserId(userId);
            });
            this.premiumCheckService.updatePremiumPersistPos(premiumPersistPos);

        } catch (Exception e) {
            //事务回滚
            TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
            if (e instanceof RequestException) {
                RequestException error = (RequestException) e;
                //错误设置
                resultObject.setIenum(error.getiEnum());
            } else {
                resultObject.setIenum(PolicyErrorConfigEnum.POLICY_FAIL);
            }
        }
        return resultObject;
    }

    /**
     * 保存保费收款凭证
     * @param request 请求对象
     * @param userId 当前用户ID
     * @return 结果
     */
    @Override
    @Transactional
    public ResultObject<BaseResponse> savePremiumActualAttach(PremiumActualAttachRequest request, String userId) {
        ResultObject<BaseResponse> resultObject = new ResultObject<>();
        try {
            // 参数校验
            AssertUtils.isNotEmpty(this.getLogger(), request.getCheckId(), PolicyErrorConfigEnum.POLICY_PARAMETER_CHECK_ID_IS_NOT_NULL);
            // 查询数据
            PremiumCheckPo premiumCheckPo = this.premiumCheckExtDao.getPremiumCheckPo(request.getCheckId());
            // 数据验证
            AssertUtils.isNotNull(this.getLogger(), premiumCheckPo, PolicyErrorConfigEnum.POLICY_BUSINESS_PREMIUM_CHECK_IS_NOT_FOUND_OBJECT);
            /**
             * 1.更新对账清单表
             */
            premiumCheckPo.setActualRemark(request.getActualRemark());
            premiumCheckPo.setActualStatus(PolicyTermEnum.ACTUAL_STATUS.PAY_WAIT.name());
            premiumCheckPo.setUpdatedDate(DateUtils.getCurrentTime());
            premiumCheckPo.setUpdatedUserId(userId);
            // 更新数据
            this.premiumCheckService.updatePremiumCheckPo(premiumCheckPo);
            /**
             * 2.保存附件信息
             */
            List<PremiumCheckAttachPo> premiumCheckAttachPos = this.premiumCheckTransfer.transferPremiumAttach(request, userId);
            if(AssertUtils.isNotEmpty(premiumCheckAttachPos)) {
                this.premiumCheckService.savePremiumCheckAttachPo(premiumCheckAttachPos);
            }
        } catch (Exception e) {
            //事务回滚
            TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
            if (e instanceof RequestException) {
                RequestException error = (RequestException) e;
                //错误设置
                resultObject.setIenum(error.getiEnum());
            } else {
                resultObject.setIenum(PolicyErrorConfigEnum.POLICY_FAIL);
            }
        }
        return resultObject;
    }

    /**
     * 查询收款凭证
     * @param checkId 对账批次ID
     * @return 凭证与说明
     */
    @Override
    public ResultObject<PremiumActualAttachResponse> getPremiumActualAttach(String checkId) {
        ResultObject<PremiumActualAttachResponse> resultObject = new ResultObject<>();
        try {
            // 参数校验
            AssertUtils.isNotEmpty(this.getLogger(), checkId, PolicyErrorConfigEnum.POLICY_PARAMETER_CHECK_ID_IS_NOT_NULL);
            // 查询数据
            List<PremiumActualAttachBo> premiumActualAttachBos = this.premiumCheckExtDao.getPremiumActualAttach(checkId);
            // 数据转换
            PremiumActualAttachResponse premiumActualAttachResponse = this.premiumCheckTransfer.transferPremiumAttach(premiumActualAttachBos);

            resultObject.setData(premiumActualAttachResponse);
        } catch (Exception e) {
            if (e instanceof RequestException) {
                RequestException error = (RequestException) e;
                //错误设置
                resultObject.setIenum(error.getiEnum());
            } else {
                resultObject.setIenum(PolicyErrorConfigEnum.POLICY_FAIL);
            }
        }
        return resultObject;
    }
}
