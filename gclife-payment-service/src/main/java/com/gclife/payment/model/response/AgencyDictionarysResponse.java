package com.gclife.payment.model.response;

import com.gclife.common.model.feign.SyscodeRespFc;
import com.gclife.platform.model.response.SyscodeResponse;
import io.swagger.annotations.ApiModelProperty;

import java.util.List;

/**
 * <AUTHOR>
 * @version v2.0
 * Description:
 * @date 18-7-12
 */
public class AgencyDictionarysResponse {
    @ApiModelProperty(example = "业务类型")
    private List<SyscodeResponse> businessType;

    @ApiModelProperty(example = "开户行")
    private List<SyscodeResponse> bankCode;

    public List<SyscodeResponse> getBusinessType() {
        return businessType;
    }

    public void setBusinessType(List<SyscodeResponse> businessType) {
        this.businessType = businessType;
    }

    public List<SyscodeResponse> getBankCode() {
        return bankCode;
    }

    public void setBankCode(List<SyscodeResponse> bankCode) {
        this.bankCode = bankCode;
    }
}
