package com.gclife.payment.model.response;

import com.gclife.common.model.BaseResponse;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * create 17-10-16
 * description: payment列表查询
 */

@ApiModel(value = "支付详细信息",description = "支付详细信息")
public class PaymentApplyCashDetailResponse extends BaseResponse {

    //支付基础信息
    @ApiModelProperty(value = "支付信息")
    private PaymentDetailResponse payment;
    //保单信息
    @ApiModelProperty(value = "保单信息")
    private PaymentApplyDetailResponse apply;

    public PaymentDetailResponse getPayment() {
        return payment;
    }

    public void setPayment(PaymentDetailResponse payment) {
        this.payment = payment;
    }

    public PaymentApplyDetailResponse getApply() {
        return apply;
    }

    public void setApply(PaymentApplyDetailResponse apply) {
        this.apply = apply;
    }

}
