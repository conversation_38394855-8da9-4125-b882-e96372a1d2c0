package com.gclife.payment.model.response.wing;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 支付成功回调返回给wing
 */
@Data
public class PaymentWingOfflineResponse {
    @ApiModelProperty(value = "Session ID",example = "Session ID")
    private String session_id;
    @ApiModelProperty(value = "Wing system recognize only response 200 as transaction success",example = "Wing system recognize only response 200 as transaction success")
    private String response_code;
    @ApiModelProperty(value = "Response message",example = "Response message")
    private String response_msg;
    @ApiModelProperty(value = "Customer ID or loan ID or Account Number支付码",example = "Customer ID or loan ID or Account Number支付码",required = true)
    private String reference_number;
    @ApiModelProperty(value = "Customer Name",example = "Customer Name")
    private String customer_name;
    @ApiModelProperty(value = "Unique identifier of access medium",example = "Unique identifier of access medium")
    private String request_medium_id;
    @ApiModelProperty(value = "Access medium. POS or USSD",example = "Access medium. POS or USSD")
    private String request_medium;
    @ApiModelProperty(value = "Payment currency币种",example = "Payment currency币种",required = true)
    private String currency;
    @ApiModelProperty(value = "Payment amount金额",example = "Payment amount金额",required = true)
    private String amount;
    @ApiModelProperty(value = "Tranaction ID that generated by partner side",example = "Tranaction ID that generated by partner side",required = true)
    private String partner_txn_id;
    @ApiModelProperty(value = "Random string 随机字符串",example = "Random string 随机字符串",required = true)
    private String nonce_str;
    @ApiModelProperty(value = "MD5 signature of the message md5签名消息",example = "MD5 signature of the message md5签名消息",required = true)
    private String message_md5;
    @ApiModelProperty(value = "Reserve for additional information",example = "Reserve for additional information")
    private String attr_1_name;
    @ApiModelProperty(value = "Reserve for additional information",example = "Reserve for additional information")
    private String attr_1_value;
    @ApiModelProperty(value = "Reserve for additional information",example = "Reserve for additional information")
    private String attr_2_name;
    @ApiModelProperty(value = "Reserve for additional information",example = "Reserve for additional information")
    private String attr_2_value;
}
