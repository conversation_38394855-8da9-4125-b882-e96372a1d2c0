package com.gclife.payment.service.base.payment.impl;

import com.gclife.common.model.config.TerminologyConfigEnum;
import com.gclife.common.service.impl.BaseBusinessServiceImpl;
import com.gclife.payment.base.model.bo.PaymentDo;
import com.gclife.payment.core.jooq.tables.daos.PaymentItemDao;
import com.gclife.payment.core.jooq.tables.pojos.PaymentItemPo;
import com.gclife.payment.core.jooq.tables.pojos.PaymentTypeConfigPo;
import com.gclife.payment.model.config.PaymentTermEnum;
import com.gclife.payment.model.response.PaymentItemResponse;
import com.gclife.payment.service.base.payment.PaymentProcessBusinessService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * create 18-05-17
 * description:Wing线下支付　处理实现类
 */
@Service("paymentWingOfflineBusinessServiceImpl")
public class PaymentWingOfflineBusinessServiceImpl extends BaseBusinessServiceImpl implements PaymentProcessBusinessService {

    @Autowired
    private PaymentItemDao paymentItemDao;

    /**
     * 银行卡转账方法前置校验
     *
     * @param paymentTypeConfigPo 　类型配置
     * @param userId              用户ID
     * @param deviceChannel       渠道ID
     * @return boolean
     */
    @Override
    public boolean validPreUsePaymentMethod(PaymentTypeConfigPo paymentTypeConfigPo, String userId, String deviceChannel) {
        return true;
    }

    /**
     * 下单处理
     *
     * @param paymentTypeConfigPo 支付方式配置
     * @param paymentDo           支付事物主对象
     * @param paymentItemPo       申请支付开始接口请求参数
     * @return PaymentItemResponse
     */
    @Override
    public PaymentItemResponse applyPaymentHandle(PaymentTypeConfigPo paymentTypeConfigPo, PaymentDo paymentDo, PaymentItemPo paymentItemPo) {
        PaymentItemResponse paymentItemResponse = new PaymentItemResponse();
        try {
            paymentItemResponse = (PaymentItemResponse) this.converterObject(paymentItemPo, PaymentItemResponse.class);
            paymentItemResponse.setStatus(paymentDo.getStatus());
        } catch (Exception e) {
            paymentItemResponse.setErrorMsg(e.getMessage());
            paymentItemResponse.setStatus(PaymentTermEnum.PAYMENT_STATUS.PAYMENT_FAILED.name());
            paymentItemResponse.setHandleStatus(PaymentTermEnum.PAYMENT_STATUS.PAYMENT_FAILED.name());
        }
        return paymentItemResponse;
    }

    /**
     * 　支付申请处理接口
     *
     * @param paymentItemPo 申请支付开始接口请求参数  @return PaymentResponse
     */
    @Override
    public void deletePaymentHandle(PaymentItemPo paymentItemPo) {
        paymentItemPo.setValidFlag(TerminologyConfigEnum.VALID_FLAG.invalid.name());
        paymentItemDao.update(paymentItemPo);
    }

    /**
     * 下单撤回
     *
     * @param paymentTypeConfigPo 支付方式配置
     * @param paymentItemPo       申请支付开始接口请求参数
     */
    @Override
    public void revokeApplyPaymentHandle(PaymentTypeConfigPo paymentTypeConfigPo, PaymentItemPo paymentItemPo) {

    }

    /**
     * 支付成功处理
     *
     * @param paymentTypeConfigPo 支付方式配置
     * @param paymentDo           　支付事物主对象
     * @param paymentItemPo       申请支付开始接口请求参数
     * @return PaymentItemResponse
     */
    @Override
    public PaymentItemResponse successPaymentHandle(PaymentTypeConfigPo paymentTypeConfigPo, PaymentDo paymentDo, PaymentItemPo paymentItemPo) {
        PaymentItemResponse paymentItemResponse = new PaymentItemResponse();
        try {
            paymentItemResponse = (PaymentItemResponse) this.converterObject(paymentItemPo, PaymentItemResponse.class);
            paymentItemResponse.setStatus(paymentDo.getStatus());
        } catch (Exception e) {
            paymentItemResponse.setErrorMsg(e.getMessage());
            paymentItemResponse.setStatus(PaymentTermEnum.PAYMENT_STATUS.PAYMENT_FAILED.name());
            paymentItemResponse.setHandleStatus(PaymentTermEnum.PAYMENT_STATUS.PAYMENT_FAILED.name());
        }
        return paymentItemResponse;
    }

    /**
     * 支付失败处理
     *
     * @param paymentTypeConfigPo 支付方式配置
     * @param paymentDo           　支付事物主对象
     * @param paymentItemPo       申请支付开始接口请求参数
     * @return PaymentItemResponse
     */
    @Override
    public PaymentItemResponse faildPaymentHandle(PaymentTypeConfigPo paymentTypeConfigPo, PaymentDo paymentDo, PaymentItemPo paymentItemPo) {
        PaymentItemResponse paymentItemResponse = new PaymentItemResponse();
        try {
            paymentItemResponse = (PaymentItemResponse) this.converterObject(paymentItemPo, PaymentItemResponse.class);
            paymentItemResponse.setStatus(paymentDo.getStatus());
        } catch (Exception e) {
            paymentItemResponse.setErrorMsg(e.getMessage());
            paymentItemResponse.setStatus(PaymentTermEnum.PAYMENT_STATUS.PAYMENT_FAILED.name());
            paymentItemResponse.setHandleStatus(PaymentTermEnum.PAYMENT_STATUS.PAYMENT_FAILED.name());
        }
        return paymentItemResponse;
    }

    /**
     * 支付成功撤回
     *
     * @param paymentTypeConfigPo 支付方式配置
     * @param paymentItemPo       申请支付开始接口请求参数
     */
    @Override
    public void revokeSuccessPaymentHandle(PaymentTypeConfigPo paymentTypeConfigPo, PaymentItemPo paymentItemPo) {

    }

    /**
     * 支付失败撤回
     *
     * @param paymentTypeConfigPo 支付方式配置
     * @param paymentItemPo       申请支付开始接口请求参数
     */
    @Override
    public void revokeFaildPaymentHandle(PaymentTypeConfigPo paymentTypeConfigPo, PaymentItemPo paymentItemPo) {

    }

    /**
     * 下单处理校验
     *
     * @param paymentTypeConfigPo 支付方式配置
     * @param paymentItemPo       申请支付开始接口请求参数
     */
    @Override
    public void validBusinessApplyPayment(PaymentTypeConfigPo paymentTypeConfigPo, PaymentItemPo paymentItemPo) {

    }

    /**
     * 支付成功处理校验
     *
     * @param paymentDo           　支付事物主对象
     * @param paymentTypeConfigPo 支付方式配置
     * @param paymentItemPo       申请支付开始接口请求参数
     */
    @Override
    public void validBusinessSuccessPayment(PaymentDo paymentDo, PaymentTypeConfigPo paymentTypeConfigPo, PaymentItemPo paymentItemPo) {

    }

    /**
     * 　(后置)　支付处理
     */
    @Override
    public void backPaymentMethodHandler() {

    }


}
