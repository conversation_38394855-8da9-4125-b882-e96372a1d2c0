package com.gclife.payment.service.middle;

import com.gclife.common.model.BasePageResponse;
import com.gclife.common.model.ResultObject;
import com.gclife.common.model.base.Users;
import com.gclife.common.model.pojo.AppRequestHeads;
import com.gclife.common.service.BaseBusinessService;
import com.gclife.payment.base.model.vo.PaymentListVo;
import com.gclife.payment.model.response.receipt.ReceiptAuditDetailResponse;
import com.gclife.payment.model.response.receipt.ReceiptPagingResponse;

/**
 * <AUTHOR>
 * create 18-05-17
 * description:支付处理service类
 */
public interface ReceiptMiddleAuditBusinessService extends BaseBusinessService {

    /**
     *　查询付费审核列表
     * @param paymentListVo 请求参数
     * @return ResultObject
     */
    ResultObject<BasePageResponse<ReceiptPagingResponse>> queryReceiptOrder(Users users, PaymentListVo paymentListVo);

    /**
     *　查询付费审核订单详情
     * @param users 用户对象
     * @param receiptId 付费订单ID
     * @return ResultObject
     */
    ResultObject<ReceiptAuditDetailResponse> queryOneReceiptOrder(Users users,String receiptId,AppRequestHeads appRequestHeads);



}
