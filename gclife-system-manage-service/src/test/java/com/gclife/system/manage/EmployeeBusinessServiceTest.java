package com.gclife.system.manage;



import com.alibaba.fastjson.JSON;
import com.gclife.common.model.ResultObject;
import com.gclife.system.manage.model.response.EmployeeInfoResponse;
import com.gclife.system.manage.service.business.EmployeeBusinessService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

/**
 * \* Created with IntelliJ IDEA.
 * \* User: deep
 * \* Date: 17-9-14
 * \* Time: 下午7:37
 * \* To change this template use File | Settings | File Templates.
 * \* Description: 用户service 测试类
 * \
 */
/*
@RunWith(SpringRunner.class)
@SpringBootTest
public class EmployeeBusinessServiceTest extends UsersTest {


    @Autowired
    EmployeeBusinessService employeeBusinessService;

    @Test
    public void loadEmployeeBranchTreeTest(){

        System.out.println("=============================================提现接口测试 start================================================");
        try {
            ResultObject resultObject = employeeBusinessService.getEmployeeBranchTreesStatistic(this.getCurrentLoginUsers());
            System.out.printf(JSON.toJSONString(resultObject));
        }catch (Exception e){
            System.out.println("=============================================提现接口测试 error================================================");
        }
        System.out.println("=============================================提现接口测试 end================================================");
    }



    @Test
    public void queryEmployeeInfo() {
        System.out.println("============ 员工信息测试start  ===========");
        ResultObject<EmployeeInfoResponse> resultObject = employeeBusinessService.queryEmployeeInfo(this.getCurrentLoginUsers(), "GM_USER_101");
        System.out.println(JSON.toJSONString(resultObject));
        System.out.println("============ 员工信息测试  end  ===========");
    }



}*/
