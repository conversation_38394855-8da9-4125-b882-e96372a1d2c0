package com.gclife.system.manage.model.response;

import com.gclife.common.annotation.DateFormat;
import com.gclife.common.annotation.Internation;
import com.gclife.common.model.config.DateFormatPatternEnum;
import io.swagger.annotations.ApiModelProperty;

import java.util.List;

/**
 * <AUTHOR>
 * @version v1.0
 * @date 17-12-15
 * Description: 行政机构树
 */
public class BranchManagerTreeResponse {
    @Internation(codeType = "BRANCH_NAME", filed = "branchCode")
    @ApiModelProperty(example = "机构名称")
    private String branchName;

    @ApiModelProperty(example = "机构ID")
    private String branchId;

    @ApiModelProperty(example = "机构编码")
    private String branchCode;

    @ApiModelProperty(example = "机构所在地")
    private String branchAddress;

    @ApiModelProperty(example = "设立日期")
    private Long branchEstablishDate;

    @ApiModelProperty(example = "设立日期")
    @DateFormat(filed = "branchEstablishDate", pattern = DateFormatPatternEnum.FORMATE3)
    private Long branchEstablishDateFormat;

    @ApiModelProperty(example = "机构负责人")
    private String branchContactAdminName;

    @Internation(codeType = "BRANCH_STATUS", filed = "branchStatus")
    @ApiModelProperty(example = "设立状态")
    private String branchStatus;

    @ApiModelProperty(example = "父类机构Id")
    private String parentBranchId;

    /**
     * 子树
     */
    private List<BranchManagerTreeResponse> childs;

    public String getParentBranchId() {
        return parentBranchId;
    }

    public void setParentBranchId(String parentBranchId) {
        this.parentBranchId = parentBranchId;
    }

    public String getBranchName() {
        return branchName;
    }

    public void setBranchName(String branchName) {
        this.branchName = branchName;
    }

    public String getBranchId() {
        return branchId;
    }

    public void setBranchId(String branchId) {
        this.branchId = branchId;
    }

    public String getBranchCode() {
        return branchCode;
    }

    public void setBranchCode(String branchCode) {
        this.branchCode = branchCode;
    }

    public String getBranchAddress() {
        return branchAddress;
    }

    public void setBranchAddress(String branchAddress) {
        this.branchAddress = branchAddress;
    }

    public Long getBranchEstablishDate() {
        return branchEstablishDate;
    }

    public void setBranchEstablishDate(Long branchEstablishDate) {
        this.branchEstablishDate = branchEstablishDate;
    }

    public String getBranchContactAdminName() {
        return branchContactAdminName;
    }

    public void setBranchContactAdminName(String branchContactAdminName) {
        this.branchContactAdminName = branchContactAdminName;
    }

    public String getBranchStatus() {
        return branchStatus;
    }

    public void setBranchStatus(String branchStatus) {
        this.branchStatus = branchStatus;
    }

    public List<BranchManagerTreeResponse> getChilds() {
        return childs;
    }

    public void setChilds(List<BranchManagerTreeResponse> childs) {
        this.childs = childs;
    }

    public Long getBranchEstablishDateFormat() {
        return branchEstablishDateFormat;
    }

    public void setBranchEstablishDateFormat(Long branchEstablishDateFormat) {
        this.branchEstablishDateFormat = branchEstablishDateFormat;
    }
}
