package com.gclife.system.manage.model.response;

import com.gclife.common.annotation.Internation;
import io.swagger.annotations.ApiModelProperty;

import java.util.List;

/**
 * <AUTHOR>
 * @version v1.0
 * @date 17-12-18
 * Description: 附件信息
 */
public class BranchAttachmentResponse {
    @ApiModelProperty(example = "附件编号")
    private String attachmentId;
    @Internation(codeType = "BRANCH_ATTACHMENT_TYPE", filed="attachmentTypeCode")
    @ApiModelProperty(example = "附件类型")
    private String attachmentTypeCode;

    public String getAttachmentId() {
        return attachmentId;
    }

    public void setAttachmentId(String attachmentId) {
        this.attachmentId = attachmentId;
    }

    public String getAttachmentTypeCode() {
        return attachmentTypeCode;
    }

    public void setAttachmentTypeCode(String attachmentTypeCode) {
        this.attachmentTypeCode = attachmentTypeCode;
    }
}
