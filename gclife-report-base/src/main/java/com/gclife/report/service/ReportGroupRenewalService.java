package com.gclife.report.service;

import com.gclife.report.model.bo.ReportGroupRenewalBo;
import com.gclife.report.model.vo.ReportGroupRenewalVo;

import java.util.List;

/**
 * @program: gclife-report-service
 * @description:
 * @author: b<PERSON><PERSON><PERSON><PERSON>
 * @create: 2021-06-16 15:24
 **/
public interface ReportGroupRenewalService {
    /**
     *
     * @param groupRenewalVo
     * @return
     */
    List<ReportGroupRenewalBo> queryRenewalPageList(ReportGroupRenewalVo groupRenewalVo);

}
