package com.gclife.report.service.impl;

import com.gclife.report.dao.ReportGroupInstallmentBaseDao;
import com.gclife.report.model.bo.ReportGroupInstallmentBo;
import com.gclife.report.model.bo.ReportRenewalBo;
import com.gclife.report.model.vo.ReportMonthlyStatisticsVo;
import com.gclife.report.service.ReportGroupInstallmentBaseService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/1/19
 */
@Service
public class ReportGroupInstallmentBaseServiceImpl implements ReportGroupInstallmentBaseService {

    @Autowired
    private ReportGroupInstallmentBaseDao reportGroupInstallmentBaseDao;

    @Override
    public List<ReportGroupInstallmentBo> queryMarketStatisticalData(ReportMonthlyStatisticsVo reportMarketVo) {
        return reportGroupInstallmentBaseDao.queryMarketStatisticalData(reportMarketVo);
    }

    @Override
    public List<ReportRenewalBo> queryMarketStatisticalRateData(ReportMonthlyStatisticsVo reportMarketVo) {
        return reportGroupInstallmentBaseDao.queryMarketStatisticalRateData(reportMarketVo);
    }
}
