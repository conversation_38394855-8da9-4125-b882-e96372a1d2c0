package com.gclife.report.service;

import com.gclife.report.api.model.response.ActualPerformanceReportBo;
import com.gclife.report.model.bo.ReportPolicyBo;
import com.gclife.report.model.vo.ReportMonthlyStatisticsVo;
import com.gclife.report.model.vo.ReportPaymentVo;

import java.util.List;

/**
 * @ Author     ：bzy
 * @ Date       ：Created in 上午10:55 2020/3/26
 * @ Description：
 * @ Modified By：
 * @Version:
 */
public interface ReportPolicyBaseService {

    /**
     * 查询市场统计数据
     *
     * @param reportMarketVo
     * @return
     */
    List<ReportPolicyBo> queryMarketStatisticalData(ReportMonthlyStatisticsVo reportMarketVo);

    /**
     * 工作量
     *
     * @param reportMonthlyStatisticsVo
     * @return
     */
    List<ReportPolicyBo> queryApplyPolicyWorkload(ReportMonthlyStatisticsVo reportMonthlyStatisticsVo);

    /**
     * 有效保单积存
     *
     * @param reportMonthlyStatisticsVo
     * @return
     */
    List<ReportPolicyBo> queryEffectivePolicyAccumulate(ReportMonthlyStatisticsVo reportMonthlyStatisticsVo);

    /**
     * 统计业绩报表
     *
     * @param reportPaymentVo
     * @return
     */
    List<ActualPerformanceReportBo> queryActualPerformanceApply(ReportPaymentVo reportPaymentVo);
}
