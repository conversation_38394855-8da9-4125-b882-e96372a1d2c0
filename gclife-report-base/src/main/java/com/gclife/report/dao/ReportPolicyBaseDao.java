package com.gclife.report.dao;

import com.gclife.report.model.bo.ReportPolicyBo;
import com.gclife.report.model.vo.ReportMonthlyStatisticsVo;

import java.util.List;

/**
 * @ Author     ：bzy
 * @ Date       ：Created in 下午5:31 2020/3/25
 * @ Description：
 * @ Modified By：
 * @Version:
 */
public interface ReportPolicyBaseDao {
    /**
     * 查询市场统计数据
     *
     * @param reportMarketVo
     * @return
     */
    List<ReportPolicyBo> queryMarketStatisticalData(ReportMonthlyStatisticsVo reportMarketVo);

    /**
     * 查询工作量
     *
     * @param reportMonthlyStatisticsVo
     * @return
     */
    List<ReportPolicyBo> queryApplyPolicyWorkload(ReportMonthlyStatisticsVo reportMonthlyStatisticsVo);

    /**
     * 有效保单积存
     *
     * @param reportMonthlyStatisticsVo
     * @return
     */
    List<ReportPolicyBo> queryEffectivePolicyAccumulate(ReportMonthlyStatisticsVo reportMonthlyStatisticsVo);

}
