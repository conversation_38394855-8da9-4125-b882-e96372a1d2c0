package com.gclife.policy.model.bo;

import com.gclife.common.util.AssertUtils;
import lombok.Data;

import java.math.BigDecimal;

/**
 * @program: gclife-business-core
 * @description:
 * @author: baizhongying
 * @create: 2021-02-02 16:43
 **/
@Data
public class PolicyAmountBo {

    //设置保额
    private Long policyYear;
    private Long receivableDate;
    private Long effectiveDate;
    private Long renewalInsuranceFrequency;
    private String productId;
    private String coverageId;
    private String insuredStatus;
    private String policyStatus;
    private String coverageStatus;
    private String policyId;
    private String productLevel;
    private String mult;
    private Long insuredBirthday;
    private BigDecimal amount=new BigDecimal(0);
    private BigDecimal initialAmount=new BigDecimal(0);
    private BigDecimal assessAmount=new BigDecimal(0);
    private Long approveDate;
    private String policyType;
    private String dutyId;
}
