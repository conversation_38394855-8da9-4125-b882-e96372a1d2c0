package com.gclife.payment.model.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

@Data
public class CreditNoteRequest {
    @ApiModelProperty(value = "业务ID")
    private String businessId;
    @ApiModelProperty(value = "业务号")
    private String businessNo;
    @ApiModelProperty(value = "业务类型")
    private String businessType;
    @ApiModelProperty(value = "机构ID")
    private String branchId;
    @ApiModelProperty(value = "投保单号")
    private String applyNo;
    @ApiModelProperty(value = "保单号")
    private String policyNo;
    @ApiModelProperty(value = "客户名称")
    private String customerName;
    @ApiModelProperty(value = "客户手机号码")
    private String customerMobile;
    @ApiModelProperty(value = "主险产品ID")
    private String mainProductId;
    @ApiModelProperty(value = "主险产品编码")
    private String mainProductCode;
    @ApiModelProperty(value = "主险产品名称")
    private String mainProductName;
    @ApiModelProperty("税务登记号")
    private String taxRegistrationNo;
    @ApiModelProperty(value = "信用票据金额")
    private BigDecimal creditNoteAmount;
    @ApiModelProperty(value = "业务json")
    private String businessJson;
}
