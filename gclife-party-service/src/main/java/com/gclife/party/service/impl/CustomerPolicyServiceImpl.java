package com.gclife.party.service.impl;

import com.gclife.attachment.model.response.AttachmentResponse;
import com.gclife.client.api.ClientCustomerBaseApi;
import com.gclife.client.model.request.CustomerAndRelationshipRequest;
import com.gclife.client.model.request.CustomerRequest;
import com.gclife.common.model.ResultObject;
import com.gclife.common.model.base.Users;
import com.gclife.common.model.pojo.AppRequestHeads;
import com.gclife.common.service.impl.BaseBusinessServiceImpl;
import com.gclife.common.util.AssertUtils;
import com.gclife.common.util.ClazzUtils;
import com.gclife.common.util.DateUtils;
import com.gclife.common.util.UUIDUtils;
import com.gclife.party.core.jooq.tables.daos.*;
import com.gclife.party.core.jooq.tables.pojos.*;
import com.gclife.party.dao.CustomerManageBaseDao;
import com.gclife.party.dao.CustomerPolicyExtDao;
import com.gclife.party.model.bo.ClientPolicyListBo;
import com.gclife.party.model.bo.CustomerPolicyListBo;
import com.gclife.party.model.bo.CustomerRelationshipBo;
import com.gclife.party.model.bo.PictureBatchBo;
import com.gclife.party.model.config.PartyErrorConfigEnum;
import com.gclife.party.model.config.PartyTermEnum;
import com.gclife.party.model.request.policy.ClientPolicyQueryRequest;
import com.gclife.party.model.request.policy.DutyClassResponse;
import com.gclife.party.model.request.policy.PictureBatchRequest;
import com.gclife.party.model.request.policy.PolicyRequest;
import com.gclife.party.model.response.policy.*;
import com.gclife.party.service.CustomerPolicyService;
import com.gclife.party.service.base.ClientManagerBaseService;
import com.gclife.party.service.base.CustomerBaseService;
import com.gclife.party.validate.parameter.CustomerParameterValidate;
import com.gclife.party.validate.transfer.CustomerBaseTransfer;
import com.gclife.platform.api.PlatformInternationalBaseApi;
import com.gclife.platform.model.response.SyscodeResponse;
import com.gclife.product.model.response.ProviderResponse;
import org.modelmapper.TypeToken;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;


/**
 * 客户保单
 * <AUTHOR>
 * @version v1.0
 * Description:
 * @date 22-09-01
 */
@Service
public class CustomerPolicyServiceImpl extends BaseBusinessServiceImpl implements CustomerPolicyService {
    @Autowired
    private CustomerManageBaseDao customerManageBaseDao;
    @Autowired
    private CustomerParameterValidate customerParameterValidate;
    @Autowired
    private CustomerBaseService customerBaseService;
    @Autowired
    private ClientManagerBaseService clientManagerBaseService;
    @Autowired
    private PolicyDao policyDao;
    @Autowired
    private PolicyApplicantDao policyApplicantDao;
    @Autowired
    private PolicyInsuredDao policyInsuredDao;
    @Autowired
    private PolicyCoverageDao policyCoverageDao;
    @Autowired
    private PolicyAttachmentDao policyAttachmentDao;
    @Autowired
    private CustomerBaseTransfer customerBaseTransfer;
    @Autowired
    private PictureBatchDao pictureBatchDao;
    @Autowired
    private PictureBatchAttachmentDao pictureBatchAttachmentDao;
    @Autowired
    private ProviderDao providerDao;
    @Autowired
    private DutyClassDao dutyClassDao;
    @Autowired
    private PlatformInternationalBaseApi platformInternationalBaseApi;
    @Autowired
    private ClientCustomerBaseApi clientCustomerBaseApi;
    @Autowired
    private CustomerPolicyExtDao customerPolicyExtDao;

    /**
     * 保单保存
     * @param policyRequest
     * @param users 用户
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public ResultObject savePolicy(PolicyRequest policyRequest, Users users) {
        // 参数校验
        customerParameterValidate.verifyPolicyData(policyRequest);

        String policyId = policyRequest.getPolicyId();
        PolicyPo policyPo;
        PolicyInsuredPo policyInsuredPo;
        PolicyApplicantPo policyApplicantPo;
        if (AssertUtils.isNotEmpty(policyId)) {
            // 查询保单
            policyPo = policyDao.findById(policyId);
            AssertUtils.isNotNull(this.getLogger(), policyPo, PartyErrorConfigEnum.PARTY_POLICY_BUSINESS_POLICY_IS_NOT_FOUND);
            //查询投保人
            policyApplicantPo  = customerBaseService.queryApplicant(policyId);
            AssertUtils.isNotNull(this.getLogger(), policyApplicantPo, PartyErrorConfigEnum.PARTY_POLICY_BUSINESS_APPLICANT_IS_NOT_FOUND);
            // 查询被保人
            List<PolicyInsuredPo> policyInsuredPos  = policyInsuredDao.fetchByPolicyId(policyId);
            AssertUtils.isNotEmpty(this.getLogger(), policyInsuredPos, PartyErrorConfigEnum.PARTY_POLICY_BUSINESS_INSURED_IS_NOT_FOUND);
            policyInsuredPo = policyInsuredPos.get(0);
            // 查询险种
            List<PolicyCoveragePo> policyCoveragePos = customerBaseService.listPolicyCoverage(policyId);
            AssertUtils.isNotEmpty(this.getLogger(), policyCoveragePos, PartyErrorConfigEnum.PARTY_POLICY_BUSINESS_COVERAGE_IS_NOT_FOUND);
            policyCoverageDao.delete(policyCoveragePos);
            // 查询电子保单
            PolicyAttachmentPo attachmentPo = customerBaseService.queryOneAttachment(policyId);
            if (AssertUtils.isNotNull(attachmentPo)) {
                policyAttachmentDao.delete(attachmentPo);
            }
        } else {
            policyPo = new PolicyPo();
            policyPo.setPolicyStatus(PartyTermEnum.CLIENT_POLICY_STATUS.EFFECTIVE.name());
            policyPo.setPolicyId(UUIDUtils.getUUIDShort());
            policyPo.setForceSave(true);
            policyApplicantPo = new PolicyApplicantPo();
            policyInsuredPo = new PolicyInsuredPo();
        }
        ClazzUtils.copyPropertiesIgnoreNull(policyRequest, policyPo);
        policyPo.setAgentUserId(users.getUserId());
        policyPo.setInputUserId(users.getUserId());
        policyPo.setAddMethod(PartyTermEnum.ADD_METHOD.MANUAL_ENTRY.name());
        policyPo.setInputStatus(PartyTermEnum.POLICY_INPUT_STATUS.ENTERED.name());
        policyPo.setPremium(new BigDecimal(policyRequest.getPremium()));
        long effectiveDate = DateUtils.stringToTime(policyRequest.getEffectiveDateFormat(), DateUtils.FORMATE18);
        policyPo.setEffectiveDate(effectiveDate);

        CustomerAgentPo applicantCustomerAgentPo = customerBaseService.queryOneCustomerAgent(policyRequest.getApplicantCustomerId());
        AssertUtils.isNotNull(this.getLogger(), applicantCustomerAgentPo, PartyErrorConfigEnum.PARTY_CUSTOMER_AGENT_IS_NOT_FOUND_OBJECT);
        ClazzUtils.copyPropertiesIgnoreNull(applicantCustomerAgentPo, policyApplicantPo);
        policyApplicantPo.setPolicyId(policyPo.getPolicyId());
        policyApplicantPo.setCustomerId(applicantCustomerAgentPo.getCustomerAgentId());
        customerBaseService.savePolicyApplicant(policyApplicantPo, users.getUserId());

        // 查询客户信息
        CustomerAgentPo customerAgentPo = customerBaseService.queryOneCustomerAgent(policyRequest.getCustomerId());
        AssertUtils.isNotNull(this.getLogger(), customerAgentPo, PartyErrorConfigEnum.PARTY_CUSTOMER_AGENT_IS_NOT_FOUND_OBJECT);
        // 保存电子保单
        if (AssertUtils.isNotEmpty(policyRequest.getAttachmentId())) {
            PolicyAttachmentPo policyAttachmentPo = new PolicyAttachmentPo();
            policyAttachmentPo.setAttachmentId(policyRequest.getAttachmentId());
            policyAttachmentPo.setPolicyId(policyPo.getPolicyId());
            customerBaseService.savePolicyAttachment(policyAttachmentPo, users.getUserId());
        }
        // 保存被保人
        ClazzUtils.copyPropertiesIgnoreNull(customerAgentPo, policyInsuredPo);
        policyInsuredPo.setPolicyId(policyPo.getPolicyId());
        policyInsuredPo.setCustomerId(customerAgentPo.getCustomerAgentId());
        customerBaseService.savePolicyInsured(policyInsuredPo, users.getUserId());
        // 保存险种
        List<PolicyCoveragePo> policyCoveragePos = new ArrayList<>();
        policyRequest.getPolicyCoverages().forEach(policyCoverageRequest -> {
            PolicyCoveragePo policyCoveragePo = (PolicyCoveragePo) this.converterObject(policyCoverageRequest, PolicyCoveragePo.class);
            Long endDate = customerBaseTransfer.calculateEndDate(effectiveDate, policyCoveragePo.getCoveragePeriod(), customerAgentPo.getBirthday());
            if (PartyTermEnum.PRIMARY_FLAG.MAIN.name().equals(policyCoveragePo.getPrimaryFlag())) {
                policyCoveragePo.setProductName(policyRequest.getProductName());
                policyPo.setMaturityDate(endDate);
                if (AssertUtils.isNotNull(endDate) && endDate <= DateUtils.getCurrentTime()) {
                    policyPo.setPolicyStatus(PartyTermEnum.CLIENT_POLICY_STATUS.INVALID.name());
                }
            }
            if (AssertUtils.isNotEmpty(policyRequest.getPremiumPeriod())) {
                policyCoveragePo.setPremiumPeriod(policyRequest.getPremiumPeriod());
            }
            policyCoveragePo.setMult("1");
            policyCoveragePo.setPremiumFrequency(policyRequest.getPremiumFrequency());
            policyCoveragePo.setEffectiveDate(effectiveDate);
            policyCoveragePo.setCoveragePeriodStartDate(effectiveDate);
            policyCoveragePo.setCoveragePeriodEndDate(endDate);
            policyCoveragePo.setMaturityDate(endDate);
            policyCoveragePo.setPolicyId(policyPo.getPolicyId());
            policyCoveragePo.setInsuredId(policyInsuredPo.getInsuredId());
            policyCoveragePo.setCustomerDutyClass(policyCoverageRequest.getDutyClassId());
            policyCoveragePo.setAmount(new BigDecimal(policyCoverageRequest.getAmount()));
            policyCoveragePos.add(policyCoveragePo);
        });
        customerBaseService.addPolicyCoverage(policyCoveragePos, users.getUserId());
        customerBaseService.savePolicy(policyPo, users.getUserId());
        // 保存client客户及关系数据
        CustomerAndRelationshipRequest customerAndRelationshipRequest = new CustomerAndRelationshipRequest();
        CustomerRequest oneselfCustomerRequest = new CustomerRequest();
        ClazzUtils.copyPropertiesIgnoreNull(applicantCustomerAgentPo, oneselfCustomerRequest);
        oneselfCustomerRequest.setCustomerId(applicantCustomerAgentPo.getCustomerAgentId());
        oneselfCustomerRequest.setHeadUrl(applicantCustomerAgentPo.getAvatar());
        customerAndRelationshipRequest.setOneselfCustomer(oneselfCustomerRequest);
        CustomerRequest customerRequest = new CustomerRequest();
        ClazzUtils.copyPropertiesIgnoreNull(customerAgentPo, customerRequest);
        customerRequest.setCustomerId(customerAgentPo.getCustomerAgentId());
        customerRequest.setHeadUrl(customerAgentPo.getAvatar());
        customerAndRelationshipRequest.setCustomer(customerRequest);
        if (!applicantCustomerAgentPo.getCustomerAgentId().equals(customerAgentPo.getCustomerAgentId())) {
            // 查询被保人与投保人关系
            List<CustomerRelationshipBo> customerRelationshipBos = customerManageBaseDao.listMember(applicantCustomerAgentPo.getCustomerAgentId(), 1);
            customerRelationshipBos.stream()
                    .filter(relationshipBo -> relationshipBo.getRelationCustomerId().equals(customerAgentPo.getCustomerAgentId()))
                    .findFirst().ifPresent(customerRelationshipBo -> customerAndRelationshipRequest.setRelationship(customerRelationshipBo.getRelationship()));
        }
        clientCustomerBaseApi.saveCustomerAndRelationship(customerAndRelationshipRequest);

        return ResultObject.success();
    }

    /**
     * 保单列表
     * @param customerId 客户ID
     * @return
     */
    @Override
    public ResultObject<List<CustomerPolicyListResponse>> listPolicy(String customerId) {
        // 查询保单
        List<CustomerPolicyListBo> customerPolicyListBos = customerBaseService.listCustomerPolicy(customerId);
        List<CustomerPolicyListResponse> customerPolicyResponses = (List<CustomerPolicyListResponse>) this.converterList(
                customerPolicyListBos, new TypeToken<List<CustomerPolicyListResponse>>() {}.getType()
        );
        return ResultObject.<List<CustomerPolicyListResponse>>success().setData(customerPolicyResponses);
    }

    /**
     * 录入保单详情
     * @param policyId 保单ID
     * @return
     */
    @Override
    public ResultObject<CustomerPolicyResponse> queryPolicy(String policyId) {
        // 查询保单
        PolicyPo policyPo = policyDao.findById(policyId);
        AssertUtils.isNotNull(this.getLogger(), policyPo, PartyErrorConfigEnum.PARTY_POLICY_BUSINESS_POLICY_IS_NOT_FOUND);
        CustomerPolicyResponse policyResponse = new CustomerPolicyResponse();
        ClazzUtils.copyPropertiesIgnoreNull(policyPo, policyResponse);
        // 查询保险公司
        ProviderPo providerPo = providerDao.findById(policyPo.getProviderId());
        if (AssertUtils.isNotNull(providerPo)) {
            policyResponse.setProviderId(providerPo.getProviderId());
            policyResponse.setProviderName(providerPo.getProviderName());
            policyResponse.setLogoUrl(providerPo.getLogoUrl());
        }
        // 查询投保人
        PolicyApplicantPo policyApplicantPo = customerBaseService.queryApplicant(policyId);
        AssertUtils.isNotNull(this.getLogger(), policyApplicantPo, PartyErrorConfigEnum.PARTY_POLICY_BUSINESS_APPLICANT_IS_NOT_FOUND);
        CustomerPolicyApplicantResponse policyApplicant = (CustomerPolicyApplicantResponse) this.converterObject(policyApplicantPo, CustomerPolicyApplicantResponse.class);
        policyResponse.setPolicyApplicant(policyApplicant);
        // 查询被保人
        List<PolicyInsuredPo> policyInsuredPos  = policyInsuredDao.fetchByPolicyId(policyId);
        AssertUtils.isNotEmpty(this.getLogger(), policyInsuredPos, PartyErrorConfigEnum.PARTY_POLICY_BUSINESS_INSURED_IS_NOT_FOUND);
        CustomerPolicyInsuredResponse policyInsured =
                (CustomerPolicyInsuredResponse) this.converterObject(policyInsuredPos.get(0), CustomerPolicyInsuredResponse.class);
        if (policyInsured.getCustomerId().equals(policyApplicantPo.getCustomerId())) {
            policyInsured.setRelationship(PartyTermEnum.RELATIONSHIP.ONESELF.name());
        } else {
            // 查询家庭成员关系
            List<CustomerRelationshipBo> customerRelationshipBos = customerManageBaseDao.listMember(policyApplicantPo.getCustomerId(), null);
            customerRelationshipBos.stream()
                    .filter(relationshipBo -> relationshipBo.getRelationCustomerId().equals(policyInsured.getCustomerId()))
                    .findFirst().ifPresent(relationshipBo -> {
                if (relationshipBo.getDepth() == 1) {
                    policyInsured.setRelationship(relationshipBo.getRelationship());
                } else {
                    policyInsured.setRelationship(PartyTermEnum.RELATIONSHIP.OTHER.name());
                }
            });
        }
        policyResponse.setPolicyInsured(policyInsured);

        // 查询保障(缴费)期限国际化数据
        List<SyscodeResponse> periodSysCodes = platformInternationalBaseApi.queryInternational(PartyTermEnum.BASE_INTERNATIONAL_TEXT_TYPE.PRODUCT_PERIOD_AGE.name(), "").getData();
        List<SyscodeResponse> periodYearSysCodes = platformInternationalBaseApi.queryInternational(PartyTermEnum.BASE_INTERNATIONAL_TEXT_TYPE.PRODUCT_PERIOD_YEAR.name(), "").getData();
        periodSysCodes.addAll(periodYearSysCodes);
        // 查询险种
        List<PolicyCoveragePo> policyCoveragePos = customerBaseService.listPolicyCoverage(policyId);
        AssertUtils.isNotEmpty(this.getLogger(), policyCoveragePos, PartyErrorConfigEnum.PARTY_POLICY_BUSINESS_COVERAGE_IS_NOT_FOUND);
        Optional<PolicyCoveragePo> coverageOptional = policyCoveragePos.stream()
                .filter(policyCoveragePo -> "MAIN".equals(policyCoveragePo.getPrimaryFlag()))
                .findFirst();
        if (coverageOptional.isPresent()) {
            policyResponse.setProductName(coverageOptional.get().getProductName());
            policyResponse.setPremiumFrequency(coverageOptional.get().getPremiumFrequency());
            policyResponse.setPremiumPeriod(coverageOptional.get().getPremiumPeriod());
            periodSysCodes.stream()
                    .filter(syscode -> syscode.getCodeKey().equals(policyResponse.getPremiumPeriod()))
                    .findFirst().ifPresent(syscode -> policyResponse.setPremiumPeriodName(syscode.getCodeName()));
        }
        // 查询电子保单
        PolicyAttachmentPo attachmentPo = customerBaseService.queryOneAttachment(policyId);
        if (AssertUtils.isNotNull(attachmentPo)) {
            policyResponse.setAttachmentId(attachmentPo.getAttachmentId());
        }
        // 查询保障类别
        List<DutyClassPo> dutyClassPos = dutyClassDao.findAll();
        List<CustomerPolicyCoverageResponse> policyCoverages =  new ArrayList<>();
        // 设置险种保额
        policyCoveragePos.forEach(policyCoveragePo -> {
            if (AssertUtils.isNotNull(policyCoveragePo.getAmount()) && AssertUtils.isNotEmpty(policyCoveragePo.getMult())) {
                policyCoveragePo.setAmount(policyCoveragePo.getAmount().multiply(new BigDecimal(policyCoveragePo.getMult())));
            }
            CustomerPolicyCoverageResponse policyCoverageResponse = (CustomerPolicyCoverageResponse) this.converterObject(policyCoveragePo, CustomerPolicyCoverageResponse.class);
            periodSysCodes.stream()
                    .filter(syscode -> syscode.getCodeKey().equals(policyCoverageResponse.getCoveragePeriod()))
                    .findFirst().ifPresent(syscode -> policyCoverageResponse.setCoveragePeriodName(syscode.getCodeName()));
            policyCoverageResponse.setDutyClassId(policyCoveragePo.getCustomerDutyClass());
            dutyClassPos.stream()
                    .filter(dutyClassPo -> dutyClassPo.getDutyClassId().equals(policyCoverageResponse.getDutyClassId()))
                    .findFirst().ifPresent(dutyClassPo -> policyCoverageResponse.setDutyClassType(dutyClassPo.getDutyClassType()));
            policyCoverages.add(policyCoverageResponse);
        });
        policyResponse.setPolicyCoverages(policyCoverages);
        // 查询保单图片
        List<PictureBatchAttachmentPo> batchAttachmentPos = pictureBatchAttachmentDao.fetchByPictureBatchId(policyPo.getPictureBatchId());
        if (AssertUtils.isNotEmpty(batchAttachmentPos)) {
            List<AttachmentResponse> attachmentResponses = (List<AttachmentResponse>) this.converterList(
                    batchAttachmentPos, new TypeToken<List<AttachmentResponse>>() {}.getType()
            );
            policyResponse.setAttachments(attachmentResponses);
        }

        return ResultObject.<CustomerPolicyResponse>success().setData(policyResponse);
    }

    /**
     * 录入保单删除
     * @param policyId 保单ID
     * @return
     */
    @Override
    public ResultObject deletePolicy(String policyId) {
        // 查询保单
        PolicyPo policyPo = policyDao.findById(policyId);
        AssertUtils.isNotNull(this.getLogger(), policyPo, PartyErrorConfigEnum.PARTY_POLICY_BUSINESS_POLICY_IS_NOT_FOUND);
        policyDao.delete(policyPo);
        // 查询投保人
        PolicyApplicantPo policyApplicantPo  = customerBaseService.queryApplicant(policyId);
        if (AssertUtils.isNotNull(policyApplicantPo)) {
            policyApplicantDao.delete(policyApplicantPo);
        }
        // 查询被保人
        List<PolicyInsuredPo> policyInsuredPos  = policyInsuredDao.fetchByPolicyId(policyId);
        AssertUtils.isNotEmpty(this.getLogger(), policyInsuredPos, PartyErrorConfigEnum.PARTY_POLICY_BUSINESS_INSURED_IS_NOT_FOUND);
        policyInsuredDao.delete(policyInsuredPos);
        // 查询险种
        List<PolicyCoveragePo> policyCoveragePos = customerBaseService.listPolicyCoverage(policyId);
        AssertUtils.isNotEmpty(this.getLogger(), policyCoveragePos, PartyErrorConfigEnum.PARTY_POLICY_BUSINESS_COVERAGE_IS_NOT_FOUND);
        policyCoverageDao.delete(policyCoveragePos);
        // 查询保单附件
        List<PolicyAttachmentPo> policyAttachmentPos = policyAttachmentDao.fetchByPolicyId(policyId);
        if (AssertUtils.isNotEmpty(policyAttachmentPos)) {
            policyAttachmentDao.delete(policyAttachmentPos);
        }

        return ResultObject.success();
    }

    /**
     * 图片保存
     * @param pictureBatchRequest
     * @param users 用户
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public ResultObject savePicturePolicy(PictureBatchRequest pictureBatchRequest, Users users) {
        String pictureBatchId = pictureBatchRequest.getPictureBatchId();
        PictureBatchPo pictureBatchPo;
        PolicyPo policyPo;
        PolicyApplicantPo policyApplicantPo;
        if (AssertUtils.isNotEmpty(pictureBatchId)) {
            // 查询批次
            pictureBatchPo = pictureBatchDao.findById(pictureBatchId);
            AssertUtils.isNotNull(this.getLogger(), pictureBatchPo, PartyErrorConfigEnum.PARTY_BUSINESS_PICTURE_BATCH_IS_NOT_FOUND);
            // 查询图片附件
            List<PictureBatchAttachmentPo> pictureBatchAttachmentPos  = pictureBatchAttachmentDao.fetchByPictureBatchId(pictureBatchId);
            if (AssertUtils.isNotEmpty(pictureBatchAttachmentPos)) {
                pictureBatchAttachmentDao.delete(pictureBatchAttachmentPos);
            }
            // 查询保单
            policyPo = customerBaseService.queryPolicyByBatchId(pictureBatchId);
            if (PartyTermEnum.POLICY_INPUT_STATUS.ENTERING.name().equals(policyPo.getInputStatus())) {
                throwsException(PartyErrorConfigEnum.PARTY_BUSINESS_POLICY_ENTERING_UPDATE_ERROR);
            }
            // 查询投保人
            policyApplicantPo = customerBaseService.queryApplicant(policyPo.getPolicyId());
        } else {
            pictureBatchPo = new PictureBatchPo();
            pictureBatchPo.setPictureBatchStatus(PartyTermEnum.PICTURE_BATCH_STATUS.UPLOAD_COMPLETE.name());
            policyPo = new PolicyPo();
            policyApplicantPo = new PolicyApplicantPo();
        }
        pictureBatchPo.setCustomerId(pictureBatchRequest.getCustomerId());
        pictureBatchPo.setSubmitDate(System.currentTimeMillis());
        customerBaseService.savePictureBatch(pictureBatchPo, users.getUserId());
        // 保单保存
        policyPo.setAgentUserId(users.getUserId());
        policyPo.setPictureBatchId(pictureBatchPo.getPictureBatchId());
        policyPo.setAddMethod(PartyTermEnum.ADD_METHOD.UPLOAD.name());
        policyPo.setInputStatus(PartyTermEnum.POLICY_INPUT_STATUS.PENDING_ENTRY.name());
        customerBaseService.savePolicy(policyPo, users.getUserId());

        // 保存图片附件
        List<PictureBatchAttachmentPo> batchAttachmentPos = new ArrayList<>();
        pictureBatchRequest.getAttachments().forEach(attachmentRequest -> {
            PictureBatchAttachmentPo batchAttachmentPo = (PictureBatchAttachmentPo) this.converterObject(attachmentRequest, PictureBatchAttachmentPo.class);
            batchAttachmentPo.setPictureBatchId(pictureBatchPo.getPictureBatchId());
            batchAttachmentPo.setAttachmentId(attachmentRequest.getAttachmentId());
            batchAttachmentPo.setAttachmentUrl(attachmentRequest.getAttachmentUrl());
            batchAttachmentPos.add(batchAttachmentPo);
        });
        customerBaseService.addPictureBatchAttachment(batchAttachmentPos, users.getUserId());

        CustomerAgentPo applicantCustomerAgentPo = customerBaseService.queryOneCustomerAgent(pictureBatchRequest.getCustomerId());
        AssertUtils.isNotNull(this.getLogger(), applicantCustomerAgentPo, PartyErrorConfigEnum.PARTY_CUSTOMER_AGENT_IS_NOT_FOUND_OBJECT);
        ClazzUtils.copyPropertiesIgnoreNull(applicantCustomerAgentPo, policyApplicantPo);
        policyApplicantPo.setPolicyId(policyPo.getPolicyId());
        policyApplicantPo.setCustomerId(applicantCustomerAgentPo.getCustomerAgentId());
        customerBaseService.savePolicyApplicant(policyApplicantPo, users.getUserId());

        PolicyOptionRecordPo policyOptionRecordPo = new PolicyOptionRecordPo();
        policyOptionRecordPo.setPolicyId(policyPo.getPolicyId());
        policyOptionRecordPo.setOptionUserId(users.getUserId());
        policyOptionRecordPo.setOptionTime(DateUtils.getCurrentTime());
        policyOptionRecordPo.setOptionCode(PartyTermEnum.POLICY_OPTION_CODE.APPLY.name());
        policyOptionRecordPo.setOptionResult(PartyTermEnum.POLICY_OPTION_RESULT.INPUT_SUCCESS.name());
        clientManagerBaseService.savePolicyOptionRecord(policyOptionRecordPo, users.getUserId());

        Map<String, String> map = new HashMap<>();
        map.put("pictureBatchId", pictureBatchPo.getPictureBatchId());

        return ResultObject.success().setData(map);
    }

    /**
     * 图片保单列表
     * @param customerId 客户Id
     * @return
     */
    @Override
    public ResultObject<List<PictureListResponse>> listPicturePolicy(String customerId) {
        ResultObject<List<PictureListResponse>> resultObject = ResultObject.success();
        // 查询图片保单
        List<PictureBatchBo> pictureBatchBos = customerBaseService.listPictureBatch(customerId);
        if (AssertUtils.isNotEmpty(pictureBatchBos)) {
            List<PictureListResponse> pictureListResponses = (List<PictureListResponse>) this.converterList(
                    pictureBatchBos, new TypeToken<List<PictureListResponse>>() {
                    }.getType()
            );
            List<String> pictureBatchIds = pictureBatchBos.stream().map(PictureBatchPo::getPictureBatchId).collect(Collectors.toList());
            // 查询图片保单附件
            List<PictureBatchAttachmentPo> pictureBatchAttachmentPos = customerBaseService.listPictureBatchAttachment(pictureBatchIds);
            if (AssertUtils.isNotEmpty(pictureBatchAttachmentPos)) {
                pictureListResponses.forEach(pictureListResponse -> {
                    int count = (int) pictureBatchAttachmentPos.stream()
                            .filter(pictureBatchAttachmentPo -> pictureBatchAttachmentPo.getPictureBatchId().equals(pictureListResponse.getPictureBatchId()))
                            .count();
                    pictureListResponse.setPictureNum(count);
                });
            }
            resultObject.setData(pictureListResponses);
        }
        return resultObject;
    }

    /**
     * 图片保单信息
     * @param pictureBatchId 图片批次ID
     * @param oneselfCustomerId 本人客户ID
     * @return
     */
    @Override
    public ResultObject<PictureDetailResponse> picturePolicyDetail(String pictureBatchId, String oneselfCustomerId) {
        PictureDetailResponse pictureDetailResponse = new PictureDetailResponse();
        // 查询图片批次
        PictureBatchPo pictureBatchPo = pictureBatchDao.findById(pictureBatchId);
        AssertUtils.isNotNull(this.getLogger(), pictureBatchPo, PartyErrorConfigEnum.PARTY_BUSINESS_PICTURE_BATCH_IS_NOT_FOUND);
        pictureDetailResponse.setPictureBatchId(pictureBatchPo.getPictureBatchId());
        pictureDetailResponse.setSubmitDate(pictureBatchPo.getSubmitDate());
        pictureDetailResponse.setCustomerId(pictureBatchPo.getCustomerId());
        if (oneselfCustomerId.equals(pictureBatchPo.getCustomerId())) {
            pictureDetailResponse.setRelationship(PartyTermEnum.RELATIONSHIP.ONESELF.name());
        } else {
            // 查询家庭成员关系
            List<CustomerRelationshipBo> customerRelationshipBos = customerManageBaseDao.listMember(oneselfCustomerId, null);
            customerRelationshipBos.stream()
                    .filter(relationshipBo -> relationshipBo.getRelationCustomerId().equals(pictureBatchPo.getCustomerId()))
                    .findFirst().ifPresent(relationshipBo -> {
                if (relationshipBo.getDepth() == 1) {
                    pictureDetailResponse.setRelationship(relationshipBo.getRelationship());
                } else {
                    pictureDetailResponse.setRelationship(PartyTermEnum.RELATIONSHIP.OTHER.name());
                }
            });
        }
        // 查询客户信息
        CustomerAgentPo customerAgentPo = customerBaseService.queryOneCustomerAgent(pictureBatchPo.getCustomerId());
        AssertUtils.isNotNull(this.getLogger(), customerAgentPo, PartyErrorConfigEnum.PARTY_CUSTOMER_AGENT_IS_NOT_FOUND_OBJECT);
        pictureDetailResponse.setApplicantName(customerAgentPo.getName());
        // 查询保单
        PolicyPo policyPo = customerBaseService.queryPolicyByBatchId(pictureBatchId);
        if (AssertUtils.isNotNull(policyPo)) {
            pictureDetailResponse.setPolicyStatus(policyPo.getInputStatus());
            if (PartyTermEnum.POLICY_INPUT_STATUS.ENTRY_EXCEPTION.name().equals(policyPo.getInputStatus())) {
                // 查询操作记录
                PolicyOptionRecordPo policyOptionRecordPo = customerBaseService.queryLastPolicyOptionRecord(policyPo.getPolicyId());
                if (AssertUtils.isNotNull(policyOptionRecordPo)) {
                    pictureDetailResponse.setRemark(policyOptionRecordPo.getRemark());
                }
            }
        }

        // 查询图片附件
        List<PictureBatchAttachmentPo> batchAttachmentPos = pictureBatchAttachmentDao.fetchByPictureBatchId(pictureBatchId);
        if (AssertUtils.isNotEmpty(batchAttachmentPos)) {
            List<String> attachmentIds = batchAttachmentPos.stream().map(PictureBatchAttachmentPo::getAttachmentId).collect(Collectors.toList());
            pictureDetailResponse.setAttachmentIds(attachmentIds);
        }

        return ResultObject.<PictureDetailResponse>success().setData(pictureDetailResponse);
    }

    /**
     * 图片保单取消申请
     * @param pictureBatchId 图片批次ID
     * @param users 用户
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public ResultObject picturePolicyCancel(String pictureBatchId, Users users) {
        // 查询批次
        PictureBatchPo pictureBatchPo = pictureBatchDao.findById(pictureBatchId);
        AssertUtils.isNotNull(this.getLogger(), pictureBatchPo, PartyErrorConfigEnum.PARTY_BUSINESS_PICTURE_BATCH_IS_NOT_FOUND);
        pictureBatchPo.setPictureBatchStatus(PartyTermEnum.PICTURE_BATCH_STATUS.CANCEL.name());
        customerBaseService.savePictureBatch(pictureBatchPo, users.getUserId());

        PolicyPo policyPo = customerBaseService.queryPolicyByBatchId(pictureBatchId);
        if (AssertUtils.isNotNull(policyPo)) {
            if (PartyTermEnum.POLICY_INPUT_STATUS.ENTERING.name().equals(policyPo.getInputStatus())) {
                throwsException(PartyErrorConfigEnum.PARTY_BUSINESS_POLICY_ENTERING_CANCEL_ERROR);
            }
            policyPo.setInputStatus(PartyTermEnum.POLICY_INPUT_STATUS.CANCEL_APPLICATION.name());
            customerBaseService.savePolicy(policyPo, users.getUserId());

            PolicyOptionRecordPo policyOptionRecordPo = new PolicyOptionRecordPo();
            policyOptionRecordPo.setPolicyId(policyPo.getPolicyId());
            policyOptionRecordPo.setOptionUserId(users.getUserId());
            policyOptionRecordPo.setOptionTime(DateUtils.getCurrentTime());
            policyOptionRecordPo.setOptionCode(PartyTermEnum.POLICY_OPTION_CODE.APPLY.name());
            policyOptionRecordPo.setOptionResult(PartyTermEnum.POLICY_OPTION_RESULT.CANCEL.name());
            clientManagerBaseService.savePolicyOptionRecord(policyOptionRecordPo, users.getUserId());
        }
        return ResultObject.success();
    }

    /**
     * 图片保单删除
     * @param pictureBatchId 图片批次ID
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public ResultObject deletePicturePolicy(String pictureBatchId) {
        // 查询批次
        PictureBatchPo pictureBatchPo = pictureBatchDao.findById(pictureBatchId);
        AssertUtils.isNotNull(this.getLogger(), pictureBatchPo, PartyErrorConfigEnum.PARTY_BUSINESS_PICTURE_BATCH_IS_NOT_FOUND);
        if (!PartyTermEnum.PICTURE_BATCH_STATUS.CANCEL.name().equals(pictureBatchPo.getPictureBatchStatus())) {
            throwsException(PartyErrorConfigEnum.PARTY_BUSINESS_PICTURE_POLICY_DELETE_ERROR);
        } else {
            pictureBatchDao.delete(pictureBatchPo);
        }
        // 查询图片附件
        List<PictureBatchAttachmentPo> pictureBatchAttachmentPos  = pictureBatchAttachmentDao.fetchByPictureBatchId(pictureBatchId);
        if (AssertUtils.isNotEmpty(pictureBatchAttachmentPos)) {
            pictureBatchAttachmentDao.delete(pictureBatchAttachmentPos);
        }
        // 查询保单
        PolicyPo policyPo = customerBaseService.queryPolicyByBatchId(pictureBatchId);
        if (PartyTermEnum.POLICY_INPUT_STATUS.CANCEL_APPLICATION.name().equals(policyPo.getInputStatus())) {
            throwsException(PartyErrorConfigEnum.PARTY_BUSINESS_PICTURE_POLICY_DELETE_ERROR);
        } else {
            policyDao.delete(policyPo);
        }
        // 查询投保人
        PolicyApplicantPo policyApplicantPo = customerBaseService.queryApplicant(policyPo.getPolicyId());
        if (AssertUtils.isNotNull(policyApplicantPo)) {
            policyApplicantDao.delete(policyApplicantPo);
        }
        return ResultObject.success();
    }

    /**
     * 查询保险公司
     * @return
     */
    @Override
    public ResultObject<List<ProviderResponse>> listProvider() {
        List<ProviderPo> providerPos = providerDao.findAll();
        List<ProviderResponse> providerResponses = (List<ProviderResponse>) this.converterList(
                providerPos, new TypeToken<List<ProviderResponse>>() {}.getType()
        );
        return ResultObject.<List<ProviderResponse>>success().setData(providerResponses);
    }

    /**
     * 字典数据
     * @param appRequestHeads 请求头
     * @return
     */
    @Override
    public ResultObject<PolicyDicResponse> getPolicyDic(AppRequestHeads appRequestHeads) {
        ResultObject<PolicyDicResponse> resultObject = new ResultObject<>();
        PolicyDicResponse policyDicResponse = new PolicyDicResponse();

        // 批量查询国际化
        List<String> types = Arrays.asList(
                PartyTermEnum.BASE_INTERNATIONAL_TEXT_TYPE.PRODUCT_PREMIUM_FREQUENCY.name(),
                PartyTermEnum.BASE_INTERNATIONAL_TEXT_TYPE.PRODUCT_PERIOD_AGE.name(),
                PartyTermEnum.BASE_INTERNATIONAL_TEXT_TYPE.PRODUCT_PERIOD_YEAR.name(),
                PartyTermEnum.BASE_INTERNATIONAL_TEXT_TYPE.RELATIONSHIP.name(),
                PartyTermEnum.BASE_INTERNATIONAL_TEXT_TYPE.ID_TYPE.name(),
                PartyTermEnum.BASE_INTERNATIONAL_TEXT_TYPE.GENDER.name(),
                PartyTermEnum.BASE_INTERNATIONAL_TEXT_TYPE.CONTACT_EVENT.name()
        );
        Map<String, List<SyscodeResponse>> internationalList = platformInternationalBaseApi.queryBatchInternationalByCodeKeys(appRequestHeads.getLanguage(), types).getData();

        List<SyscodeResponse> premiumFrequencySyscodes = internationalList.get(PartyTermEnum.BASE_INTERNATIONAL_TEXT_TYPE.PRODUCT_PREMIUM_FREQUENCY.name());
        premiumFrequencySyscodes.removeIf(syscodeResponse -> "MAIN".equals(syscodeResponse.getCodeKey()));
        policyDicResponse.setPremiumFrequency(premiumFrequencySyscodes);
        policyDicResponse.setProductPeriodAge(internationalList.get(PartyTermEnum.BASE_INTERNATIONAL_TEXT_TYPE.PRODUCT_PERIOD_AGE.name()));
        policyDicResponse.setProductPeriodYear(internationalList.get(PartyTermEnum.BASE_INTERNATIONAL_TEXT_TYPE.PRODUCT_PERIOD_YEAR.name()));
        internationalList.get(PartyTermEnum.BASE_INTERNATIONAL_TEXT_TYPE.RELATIONSHIP.name()).removeIf(syscode -> PartyTermEnum.RELATIONSHIP.ONESELF.name().equals(syscode.getCodeKey()));
        policyDicResponse.setRelationship(internationalList.get(PartyTermEnum.BASE_INTERNATIONAL_TEXT_TYPE.RELATIONSHIP.name()));
        List<SyscodeResponse> idType = internationalList.get(PartyTermEnum.BASE_INTERNATIONAL_TEXT_TYPE.ID_TYPE.name()).stream()
                .filter(syscode -> PartyTermEnum.ID_TYPE.ID.name().equals(syscode.getCodeKey()) ||
                        PartyTermEnum.ID_TYPE.PASSPORT.name().equals(syscode.getCodeKey()))
                .collect(Collectors.toList());
        policyDicResponse.setIdType(idType);
        policyDicResponse.setSex(internationalList.get(PartyTermEnum.BASE_INTERNATIONAL_TEXT_TYPE.GENDER.name()));
        policyDicResponse.setContactEvent(internationalList.get(PartyTermEnum.BASE_INTERNATIONAL_TEXT_TYPE.CONTACT_EVENT.name()));

        resultObject.setData(policyDicResponse);
        return resultObject;
    }

    /**
     * 查询保障类别库
     *
     * @return
     */
    @Override
    public ResultObject<List<DutyClassResponse>> listDutyClass() {
        List<DutyClassPo> dutyClassPos = dutyClassDao.findAll();
        List<DutyClassResponse> dutyClassResponses = (List<DutyClassResponse>) this.converterList(
                dutyClassPos, new TypeToken<List<DutyClassResponse>>() {}.getType()
        );
        return ResultObject.<List<DutyClassResponse>>success().setData(dutyClassResponses);
    }

    /**
     * client保单列表
     * @param queryRequest
     * @return
     */
    @Override
    public ResultObject<List<ClientPolicyListResponse>> listClientPolicy(ClientPolicyQueryRequest queryRequest) {
        ResultObject<List<ClientPolicyListResponse>> resultObject = ResultObject.success();
        List<String> insuredCustomerIds = new ArrayList<>();
        if (AssertUtils.isNotEmpty(queryRequest.getInsuredCustomerId())) {
            insuredCustomerIds.add(queryRequest.getInsuredCustomerId());
        } else {
            if (PartyTermEnum.RELATIONSHIP.ONESELF.name().equals(queryRequest.getRelationship())) {
                insuredCustomerIds.add(queryRequest.getApplicantCustomerId());
            } else if (AssertUtils.isNotEmpty(queryRequest.getRelationship())) {
                // 查询家庭成员关系
                List<CustomerRelationshipBo> customerRelationshipBos = customerManageBaseDao.listMember(queryRequest.getApplicantCustomerId(), null);
                if (AssertUtils.isNotEmpty(customerRelationshipBos)) {
                    insuredCustomerIds = customerRelationshipBos.stream()
                            .filter(customerRelationshipBo -> customerRelationshipBo.getRelationship().equals(queryRequest.getRelationship()))
                            .map(CustomerRelationshipBo::getRelationCustomerId).distinct().collect(Collectors.toList());
                }
                if (!AssertUtils.isNotEmpty(customerRelationshipBos) || !AssertUtils.isNotEmpty(insuredCustomerIds)) {
                    // 选定关系，未找到关系成员
                    return resultObject;
                }
            }
        }
        List<ClientPolicyListBo> policyListBos = customerPolicyExtDao.listClientPolicyBo(queryRequest.getApplicantCustomerId(), insuredCustomerIds, queryRequest.getPolicyStatus(), queryRequest.getKeyword());
        List<ClientPolicyListResponse> policyListResponses = (List<ClientPolicyListResponse>) this.converterList(
                policyListBos, new TypeToken<List<ClientPolicyListResponse>>() {}.getType()
        );
        resultObject.setData(policyListResponses);

        return resultObject;
    }

}