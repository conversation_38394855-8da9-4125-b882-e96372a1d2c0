package com.gclife.party.service.account;

import com.gclife.common.model.ResultObject;
import com.gclife.common.model.base.Users;
import com.gclife.party.model.response.account.FundAccountOverviewResponse;
import com.gclife.party.model.response.account.TermResponse;

/**
 * <AUTHOR>
 * @description
 * @date 2019-07-12 08:44
 */
public interface AccountCenterService {

    /**
     * 金额账户统计
     *
     * @param currentLoginUsers
     * @return
     */
    ResultObject<FundAccountOverviewResponse> fundAccountOverview(Users currentLoginUsers);

    /**
     * 账户中心国际化术语
     * @param currentLoginUsers
     * @return
     */
    ResultObject<TermResponse> term(Users currentLoginUsers);
}
