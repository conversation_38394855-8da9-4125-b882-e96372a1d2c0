package com.gclife.party.model.response;

import com.gclife.common.TerminologyConfigEnum;
import com.gclife.common.annotation.Internation;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class VerifyRelationResponse {
    @ApiModelProperty(value = "关联的手机号是否一致", example = "YES")
    private String isSameFlag = TerminologyConfigEnum.WHETHER.NO.name();
    @ApiModelProperty(value = "提示框是否展示", example = "YES")
    private String isShowFlag = TerminologyConfigEnum.WHETHER.NO.name();

    @ApiModelProperty(value = "二次验证标识", example = "YES")
    private String reVerifyFlag = TerminologyConfigEnum.WHETHER.NO.name();
    @ApiModelProperty(value = "验证姓名", example = "YES")
    private String nameFlag = TerminologyConfigEnum.WHETHER.NO.name();
    @ApiModelProperty(value = "验证性别", example = "YES")
    private String sexFlag = TerminologyConfigEnum.WHETHER.NO.name();
    @ApiModelProperty(value = "验证生日", example = "YES")
    private String birthdayFlag = TerminologyConfigEnum.WHETHER.NO.name();
    @ApiModelProperty(value = "验证证件", example = "YES")
    private String idFlag = TerminologyConfigEnum.WHETHER.NO.name();

    @ApiModelProperty(value = "是否购买20A标识", example = "YES")
    private String isBuyFlag = TerminologyConfigEnum.WHETHER.NO.name();

    @ApiModelProperty(value = "是否疑似客户标识", example = "YES")
    private String isSusCusFlag = TerminologyConfigEnum.WHETHER.NO.name();

    @ApiModelProperty(value = "是否是本人标识", example = "YES")
    private String isOneselfFlag = TerminologyConfigEnum.WHETHER.NO.name();

    @ApiModelProperty(value = "被验证的客户ID", example = "YES")
    private String verifyCustomerId;
    @ApiModelProperty(value = "被验证的证件类型", example = "YES")
    private String verifyIdType;
    @ApiModelProperty(value = "被验证的证件类型名称", example = "YES")
    @Internation(filed = "verifyIdType",codeType = "ID_TYPE")
    private String verifyIdTypeName;
    @ApiModelProperty(value = "被验证的证件号码", example = "YES")
    private String verifyIdNo;

}
