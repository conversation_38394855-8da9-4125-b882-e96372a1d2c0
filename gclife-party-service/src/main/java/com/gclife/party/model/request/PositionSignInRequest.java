package com.gclife.party.model.request;

import com.gclife.common.model.BasePageRequest;
import io.swagger.annotations.ApiModelProperty;

/**
 * @ Author     : BaiZhongYing
 * @ Date       : Created in 14:30 2019/1/16
 * @ Description:
 * @ Modified By:
 * @ Version: $version
 */
public class PositionSignInRequest  extends BasePageRequest {
    @ApiModelProperty(example = "关键字搜索 客户名称 .....")
    private String keyword;
    @ApiModelProperty(example = "所属机构Id")
    private String branchId;
    @ApiModelProperty(example = "用户ID")
    private String userId;
    @ApiModelProperty(example = "topic类型")
    private String topicTypeCode;
    @ApiModelProperty(example = "发布开始日期")
    private String publishStartDate;
    @ApiModelProperty(example = "发布结束日期")
    private String publishEndDate;

    public String getKeyword() {
        return keyword;
    }

    public void setKeyword(String keyword) {
        this.keyword = keyword;
    }

    public String getBranchId() {
        return branchId;
    }

    public void setBranchId(String branchId) {
        this.branchId = branchId;
    }

    public String getUserId() {
        return userId;
    }

    public void setUserId(String userId) {
        this.userId = userId;
    }

    public String getTopicTypeCode() {
        return topicTypeCode;
    }

    public void setTopicTypeCode(String topicTypeCode) {
        this.topicTypeCode = topicTypeCode;
    }

    public String getPublishStartDate() {
        return publishStartDate;
    }

    public void setPublishStartDate(String publishStartDate) {
        this.publishStartDate = publishStartDate;
    }

    public String getPublishEndDate() {
        return publishEndDate;
    }

    public void setPublishEndDate(String publishEndDate) {
        this.publishEndDate = publishEndDate;
    }
}
