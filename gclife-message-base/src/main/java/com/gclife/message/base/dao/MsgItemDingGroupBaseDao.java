package com.gclife.message.base.dao;

import com.gclife.message.base.model.bo.MessageDingGroupDetailBo;
import com.gclife.message.core.jooq.tables.pojos.DingGroupConfigPo;

public interface MsgItemDingGroupBaseDao {
    /**
     * 获取钉钉群组发送详情
     * @param msgItemId
     * @return
     */
    MessageDingGroupDetailBo getSendMessageDingGroupDetail(String msgItemId);

    DingGroupConfigPo getDingGroupConfig(String dingGroupId);
}
