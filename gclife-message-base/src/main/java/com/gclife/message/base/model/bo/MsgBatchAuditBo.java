package com.gclife.message.base.model.bo;

import io.swagger.annotations.ApiModelProperty;

public class MsgBatchAuditBo {
    @ApiModelProperty(example = "消息总数")
    private String messageSum;
    @ApiModelProperty(example = "发送途径")
    private String sendWays;
    @ApiModelProperty(example = "发送对象id")
    private String batchSendObject;
    @ApiModelProperty(example = "模板id")
    private String msgTemplateId;
    @ApiModelProperty(example = "消息模版名称")
    private String msgTemplateName;
    @ApiModelProperty(example = "审批状态code")
    private String auditStatus;
}
