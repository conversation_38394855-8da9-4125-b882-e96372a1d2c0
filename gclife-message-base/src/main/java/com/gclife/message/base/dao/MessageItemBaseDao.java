package com.gclife.message.base.dao;

import com.gclife.message.base.model.bo.MessageItemBo;
import com.gclife.message.base.model.bo.MessageItemListBo;
import com.gclife.message.base.model.vo.MessageItemListVo;

import java.util.List;

/**
 * @ Author     : BaiZhongYing
 * @ Date       : Created in 15:28 2019/3/12
 * @ Description:
 * @ Modified By:
 * @ Version: $version
 */
public interface MessageItemBaseDao {

    /**
     * 获取消息发送列表
     *
     * @param messageItemListVo
     * @return
     */
    List<MessageItemListBo> getMessageItemList(MessageItemListVo messageItemListVo);

    /**
     * 获取具体消息详情
     *
     * @param msgItemId
     * @return
     */
    MessageItemBo getMessageItem(String msgItemId);
}
