package com.gclife.message.base.dao.impl;

import com.gclife.common.dao.base.impl.BaseDaoImpl;
import com.gclife.common.model.config.TerminologyConfigEnum;
import com.gclife.message.base.dao.MsgSmsTemplateParamBaseDao;
import com.gclife.message.base.model.bo.MsgSmsTemplateParamBo;
import org.jooq.Record;
import org.jooq.SelectJoinStep;
import org.springframework.stereotype.Repository;

import java.util.List;

import static com.gclife.message.core.jooq.Tables.MSG_SMS_TEMPLATE_PARAM;

/**
 * @ Author     : BaiZhongYing
 * @ Date       : Created in 16:37 2019/3/19
 * @ Description:
 * @ Modified By:
 * @ Version: $version
 */
@Repository
public class MsgSmsTemplateParamBaseDaoImpl extends BaseDaoImpl implements MsgSmsTemplateParamBaseDao {
    private static String EFFECTIVE = TerminologyConfigEnum.VALID_FLAG.effective.name();

    @Override
    public List<MsgSmsTemplateParamBo> getMsgSmsTemplateParam(String... msgSmsTemplateParamId) {
        SelectJoinStep<Record> selectJoinStep = this.getDslContext()
                .select(MSG_SMS_TEMPLATE_PARAM.fields())
                .from(MSG_SMS_TEMPLATE_PARAM);
        selectJoinStep.where(MSG_SMS_TEMPLATE_PARAM.MSG_SMS_TEMPLATE_PARAM_ID.in(msgSmsTemplateParamId).and(MSG_SMS_TEMPLATE_PARAM.VALID_FLAG.eq(EFFECTIVE)));
        return selectJoinStep.fetchInto(MsgSmsTemplateParamBo.class);
    }



}
