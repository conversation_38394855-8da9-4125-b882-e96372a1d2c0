package com.gclife.client.dao.impl;

import com.gclife.client.core.jooq.tables.daos.CustomerDao;
import com.gclife.client.core.jooq.tables.daos.CustomerRelationshipDao;
import com.gclife.client.core.jooq.tables.daos.FamilyDao;
import com.gclife.client.core.jooq.tables.daos.FamilyMemberDao;
import com.gclife.client.core.jooq.tables.pojos.CustomerPo;
import com.gclife.client.core.jooq.tables.pojos.CustomerRelationshipPo;
import com.gclife.client.core.jooq.tables.pojos.FamilyMemberPo;
import com.gclife.client.core.jooq.tables.pojos.FamilyPo;
import com.gclife.client.dao.CustomerExtDao;
import com.gclife.client.dao.FamilyExtDao;
import com.gclife.client.model.bo.CustomerBo;
import com.gclife.client.model.bo.CustomerRelationshipBo;
import com.gclife.common.dao.base.impl.BaseDaoImpl;
import com.gclife.common.model.config.TerminologyConfigEnum;
import com.gclife.common.util.AssertUtils;
import com.gclife.common.util.UUIDUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static com.gclife.client.core.jooq.Tables.*;

/**
 * <AUTHOR>
 * @version v1.0
 * Description:
 * @date 2022-6-22
 */
@Repository
public class CustomerExtDaoImpl extends BaseDaoImpl implements CustomerExtDao {
    @Autowired
    private CustomerDao customerDao;
    @Autowired
    private CustomerRelationshipDao customerRelationshipDao;

    /**
     * 查询家人信息
     * @param familyMemberId 家人ID
     * @return
     */
    @Override
    public CustomerBo queryCustomerBo(String familyMemberId) {
        return this.getDslContext()
                .select(CUSTOMER.fields())
                .select(FAMILY_MEMBER.FAMILY_MEMBER_ID, FAMILY_MEMBER.RELATIONSHIP, FAMILY_MEMBER.FAMILY_ID)
                .from(CUSTOMER)
                .innerJoin(FAMILY_MEMBER).on(FAMILY_MEMBER.CUSTOMER_ID.eq(CUSTOMER.CUSTOMER_ID))
                .where(FAMILY_MEMBER.FAMILY_MEMBER_ID.eq(familyMemberId))
                .fetchOneInto(CustomerBo.class);
    }

    /**
     * 保存客户信息
     * @param customerPo 客户信息
     * @param userId 用户ID
     */
    @Override
    public void saveCustomer(CustomerPo customerPo, String userId) {
        if (!AssertUtils.isNotEmpty(customerPo.getCustomerId()) || customerPo.isForceSave()) {
            // 新增
            if (!customerPo.isForceSave()) {
                customerPo.setCustomerId(UUIDUtils.getUUIDShort());
            }
            customerPo.setCreatedUserId(userId);
            customerPo.setCreatedDate(System.currentTimeMillis());
            customerPo.setValidFlag(TerminologyConfigEnum.VALID_FLAG.effective.name());
            customerDao.insert(customerPo);
        } else {
            // 修改
            customerPo.setUpdatedUserId(userId);
            customerPo.setUpdatedDate(System.currentTimeMillis());
            customerDao.update(customerPo);
        }
    }

    /**
     * 查询客户信息
     * @param userId 用户id
     * @return
     */
    @Override
    public CustomerPo queryCustomerByUserId(String userId) {
        return this.getDslContext()
                .select(CUSTOMER.fields())
                .from(CUSTOMER)
                .where(CUSTOMER.USER_ID.eq(userId))
                .fetchOneInto(CustomerPo.class);
    }

    /**
     * 获取客户之间关系
     * @param customerId 客户ID
     * @param level 查询深度
     * @return
     */
    @Override
    public List<CustomerRelationshipBo> listMember(String customerId, Integer level) {
        List<CustomerRelationshipBo> relationshipBos = this.getDslContext()
                .selectFrom(CUSTOMER_RELATIONSHIP)
                .fetchInto(CustomerRelationshipBo.class);
        List<String> customerIds = new ArrayList<>();
        customerIds.add(customerId);
        return getRelationshipBos(relationshipBos, customerId, customerIds, level, 1);
    }

    /**
     * 批量获取客户之间关系
     * @param customerIds 客户ID
     * @param level 查询深度
     * @return
     */
    @Override
    public Map<String, List<CustomerRelationshipBo>> listMember(List<String> customerIds, Integer level) {
        List<CustomerRelationshipBo> relationshipBos = this.getDslContext()
                .selectFrom(CUSTOMER_RELATIONSHIP)
                .fetchInto(CustomerRelationshipBo.class);
        Map<String, List<CustomerRelationshipBo>> map = new HashMap<>();
        customerIds.forEach(customerId -> {
            List<String> customerIdList = new ArrayList<>();
            customerIdList.add(customerId);
            List<CustomerRelationshipBo> customerRelationshipBos = getRelationshipBos(relationshipBos, customerId, customerIdList, level, 1);
            map.put(customerId, customerRelationshipBos);
        });
        return map;
    }

    /**
     * 递归获取关系数据
     * @param relationshipBos 全部关系数据
     * @param customerId 客户ID
     * @param customerIds 存放家庭所有客户ID
     * @param level 查询层级
     * @param depth 深度
     * @return
     */
    private List<CustomerRelationshipBo> getRelationshipBos(List<CustomerRelationshipBo> relationshipBos, String customerId, List<String> customerIds, Integer level, int depth) {
        List<CustomerRelationshipBo> customerRelationshipBos = new ArrayList<>();
        List<String> tempCustomerIds = new ArrayList<>();
        for (CustomerRelationshipBo relationshipBo : relationshipBos) {
            if (!AssertUtils.isNotNull(level) || level >= depth) {
                if (relationshipBo.getCustomerId().equals(customerId) && !customerIds.contains(relationshipBo.getRelationCustomerId())) {
                    customerIds.add(relationshipBo.getRelationCustomerId());
                    tempCustomerIds.add(relationshipBo.getRelationCustomerId());
                    relationshipBo.setDepth(depth);
                    customerRelationshipBos.add(relationshipBo);
                }
            }
        }
        tempCustomerIds.forEach(tempCustomerId -> {
            customerRelationshipBos.addAll(getRelationshipBos(relationshipBos, tempCustomerId, customerIds, level, depth+1));
        });
        return customerRelationshipBos;
    }

    /**
     * 保存客户间关系
     * @param customerRelationshipPo 客户间关系
     * @param userId
     */
    @Override
    public void saveCustomerRelationship(CustomerRelationshipPo customerRelationshipPo, String userId) {
        if (AssertUtils.isNotEmpty(customerRelationshipPo.getCustomerRelationshipId())) {
            // 修改
            customerRelationshipPo.setUpdatedDate(System.currentTimeMillis());
            customerRelationshipPo.setUpdatedUserId(userId);
            customerRelationshipDao.update(customerRelationshipPo);
        } else {
            // 新增
            customerRelationshipPo.setCustomerRelationshipId(UUIDUtils.getUUIDShort());
            customerRelationshipPo.setCreatedDate(System.currentTimeMillis());
            customerRelationshipPo.setCreatedUserId(userId);
            customerRelationshipPo.setValidFlag(TerminologyConfigEnum.VALID_FLAG.effective.name());
            customerRelationshipDao.insert(customerRelationshipPo);
        }
    }
}
