package com.gclife.client.model.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * create 17-11-15
 * description: 用户注册成功的响应
 */
@Data
public class UserLoginResponse {
    @ApiModelProperty(value = "用户ID", example = "86")
    private String userId;
    @ApiModelProperty(value = "用户名", example = "abc")
    private String username;
    @ApiModelProperty(value = "密码", example = "4567")
    private String password;
    @ApiModelProperty(example = "密码修改标识")
    private String passwordFlag;

    @ApiModelProperty(value = "错误代码", example = "SUCCESS")
    private String error;

    @ApiModelProperty(value = "错误信息", example = "该用户已存在")
    private String message;

    @ApiModelProperty(value = "最近一次登录时间", example = "18898617356")
    private Long lastLogin;

    @ApiModelProperty(value = "手机区号", example = "86")
    private String countryCode;
    @ApiModelProperty(value = "手机号码", example = "18898617356")
    private String mobile;
    @ApiModelProperty(value = "手机号码", example = "18898617356")
    private String email;

    @ApiModelProperty(value = "访问token", example = "18898617356")
    private String accessToken;

    @ApiModelProperty(value = "刷新token", example = "18898617356")
    private String refreshToken;

    @ApiModelProperty(value = "多长时间后过期", example = "18898617356")
    private Long expiresIn;

}
