package com.gclife.client.model.request;

import com.gclife.common.annotation.Internation;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class ReportReporterRequest {
    @ApiModelProperty(example = "理赔ID")
    private String claimId;
    @ApiModelProperty(example = "报案ID")
    private String reportId;
    /******************报案人信息******************/
    @ApiModelProperty(example = "报案人ID")
    private String reportReporterId;
    @ApiModelProperty(example = "报案人关系")
    private String reportRelation;
    @ApiModelProperty(example = "报案人关系名称")
    @Internation(filed = "reportRelation", codeType = "RELATIONSHIP_WITH_THE_INSURED")
    private String reportRelationName;
    @ApiModelProperty(example = "报案人与被保人关系具体栏目")
    private String reportRelationSpecific;
    @ApiModelProperty(example = "报案人姓名")
    private String reporterName;
    @ApiModelProperty(example = "投保人姓",required = true)
    private String familyName;
    @ApiModelProperty(example = "投保人名",required = true)
    private String givenName;
    @ApiModelProperty(example = "报案证件类型")
    private String reporterIdType;
    @ApiModelProperty(example = "报案证件类型名称")
    @Internation(filed = "reporterIdType", codeType = "ID_TYPE")
    private String reporterIdTypeName;
    @ApiModelProperty(example = "报案证件号码")
    private String reporterIdNo;
    @ApiModelProperty(example = "报案方式")
    private String reportMode;
    @ApiModelProperty(example = "报案方式名称")
    @Internation(filed = "reportMode", codeType = "CLAIM_REPORT_MODE")
    private String reportModeName;
    @ApiModelProperty(example = "报案联系电话")
    private String reporterMobile;
    @ApiModelProperty(example = "报案邮箱")
    private String reporterEmail;
    @ApiModelProperty(example = "报案地址编码")
    private String reporterArea;
    @ApiModelProperty(example = "报案地址")
    private String reporterAddress;
    /******************委托人信息******************/
    @ApiModelProperty(example = "是否委托他人(YES:NO)")
    private String isEntrustOthers;
    @ApiModelProperty(example = "委托人姓名")
    private String reportReporterAssigneeId;
    @ApiModelProperty(example = "委托人姓名")
    private String assigneeName;
    @ApiModelProperty(example = "委托人性别")
    private String assigneeSex;
    @ApiModelProperty(example = "委托人性别名称")
    @Internation(filed = "assigneeSex", codeType = "GENDER")
    private String assigneeSexName;
    @ApiModelProperty(example = "委托人证件类型")
    private String assigneeIdType;
    @ApiModelProperty(example = "委托人证件类型名称")
    @Internation(filed = "assigneeIdType", codeType = "ID_TYPE")
    private String assigneeIdTypeName;
    @ApiModelProperty(example = "委托人证件号码")
    private String assigneeIdNo;
    @ApiModelProperty(example = "委托人电话号")
    private String assigneePhone;
    @ApiModelProperty(example = "委托人地址编码")
    private String assigneeArea;
    @ApiModelProperty(example = "委托人地址")
    private String assigneeAddress;
}
