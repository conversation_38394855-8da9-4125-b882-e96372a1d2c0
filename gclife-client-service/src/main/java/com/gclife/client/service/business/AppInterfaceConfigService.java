package com.gclife.client.service.business;

import com.gclife.client.model.response.InterfaceConfigResponse;
import com.gclife.common.model.ResultObject;
import com.gclife.common.service.BaseBusinessService;

/**
 * <AUTHOR>
 * create 18-6-7
 * description:
 */
public interface AppInterfaceConfigService extends BaseBusinessService {
    /**
     * 获取接口配置
     * @param version 版本号
     * @return ResultObject<InterfaceConfigResponse>
     */
    ResultObject<InterfaceConfigResponse>   getInterfaceConfigByVersion(String version);
}
