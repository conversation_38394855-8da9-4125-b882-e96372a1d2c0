package com.gclife.client.service.business;

import com.gclife.claim.model.respone.ClaimOnlineResponse;
import com.gclife.claim.model.respone.ReportDetailResponse;
import com.gclife.claim.model.request.ReportDetailRequest;
import com.gclife.common.model.ResultObject;
import com.gclife.common.model.base.Users;
import com.gclife.common.model.pojo.AppRequestHeads;
import com.gclife.common.service.BaseBusinessService;

import java.util.List;

/**
 * <AUTHOR>
 * create 22-05-19
 * description:APP理赔服务
 */
public interface ClaimBusinessService extends BaseBusinessService {

    /**
     * 获取理赔信息
     *
     * @param users 用户
     * @param appRequestHandler  请求头参数
     * @param customerUserId
     * @return ClaimInfoResponse
     */
    ResultObject<List<ReportDetailResponse>> getClaimInfo(Users users, AppRequestHeads appRequestHandler, String customerUserId);

    /**
     * 提交理赔申请
     *
     * @param users 用户
     * @param appRequestHandler  请求头参数
     * @return reportDetailRequest
     */
    ResultObject<ClaimOnlineResponse> claimAcceptPost(Users users, AppRequestHeads appRequestHandler, ReportDetailRequest reportDetailRequest);

    /**
     * 提交理赔申请
     *
     * @param users 用户
     * @param appRequestHandler  请求头参数
     * @return reportDetailRequest
     */
    ResultObject<ReportDetailResponse> claimAcceptGet(Users users, AppRequestHeads appRequestHandler, String claimId);
}
