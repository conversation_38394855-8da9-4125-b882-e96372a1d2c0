package com.gclife.agent.validate.sql.team.gh;

/**
 * <AUTHOR>
 * create 18-1-15
 * description:
 */
public class GhAgentTeamSql {

    /*****
     * 业务员推荐团队，不包含自己
     */
    public static String agentAllRecommendMemberNoSelfSql = "WITH RECURSIVE T (agent_id,user_id, agent_code, agent_name, agent_status,branch_id,agent_source,pay_mode_limit,recommend_agent_id,\n" +
            "     recommend_date,manager_agent_id,current_train_agent_id,current_train_date,reserve_flag,created_user_id,created_date,updated_user_id,updated_date,practitioner_year,agent_level_id, depth) AS (\n" +
            "                       SELECT\n" +
            "                         agent_id,user_id, agent_code, agent_name, agent_status,branch_id,agent_source,pay_mode_limit,recommend_agent_id,\n" +
            "     recommend_date,manager_agent_id,current_train_agent_id,current_train_date,reserve_flag,created_user_id,created_date,updated_user_id,updated_date,practitioner_year,agent_level_id,\n" +
            "                         0 AS DEPTH\n" +
            "                       FROM agent\n" +
            "                       WHERE agent_id = '?agentId?'\n" +
            "                       UNION ALL\n" +
            "                       SELECT\n" +
            "                          D.agent_id,D.user_id, D.agent_code, D.agent_name, D.agent_status,D.branch_id,D.agent_source,D.pay_mode_limit,D.recommend_agent_id,\n" +
            "     D.recommend_date,D.manager_agent_id,D.current_train_agent_id,D.current_train_date,D.reserve_flag,D.created_user_id,D.created_date,D.updated_user_id,D.updated_date,D.practitioner_year,D.agent_level_id,\n" +
            "                         T.DEPTH + 1 AS DEPTH\n" +
            "                       FROM agent D\n" +
            "                         JOIN T ON D.recommend_agent_id = T.agent_id\n" +
            "                     )\n" +
            "                     SELECT\n" +
            "                       agent_id,user_id, agent_code, agent_name, agent_status,branch_id,agent_source,pay_mode_limit,recommend_agent_id,\n" +
            "     recommend_date,manager_agent_id,current_train_agent_id,current_train_date,reserve_flag,created_user_id,created_date,updated_user_id,updated_date,practitioner_year,agent_level_id,\n" +
            "                       depth\n" +
            "                     FROM T WHERE  T.depth <>0";


    /*****
     * 业务员推荐指定层级业务员列表
     */
    public static String agentRecommendSql = "\n" +
            "WITH RECURSIVE T (agent_id, agent_code, agent_name, recommend_agent_id, depth) AS (\n" +
            "           SELECT\n" +
            "             agent_id,\n" +
            "             agent_code,\n" +
            "             agent_name,\n" +
            "             recommend_agent_id,\n" +
            "             0 AS DEPTH\n" +
            "           FROM agent\n" +
            "           WHERE user_id = '?userId?'\n" +
            "           UNION ALL\n" +
            "           SELECT\n" +
            "             D.agent_id,\n" +
            "             D.agent_code,\n" +
            "             D.agent_name,\n" +
            "             D.recommend_agent_id,\n" +
            "             T.DEPTH + 1 AS DEPTH\n" +
            "           FROM agent D\n" +
            "             JOIN T ON D.recommend_agent_id = T.agent_id\n" +
            "         )\n" +
            "         SELECT\n" +
            "           agent_id,\n" +
            "           agent_code,\n" +
            "           agent_name,\n" +
            "           recommend_agent_id,\n" +
            "           depth\n" +
            "         FROM T WHERE  T.depth =?depth?";


    /*****
     * 业务员我的团队业绩统计　sql
     */
//    public static String agentRecommendTearmMemberSql = "\n" +
//            "\n" +
//            "SELECT\n" +
//            "    T.agent_id,T.agent_code, T.agent_name,d.head_attach_id,\n" +
//            "    (SELECT count(*) FROM commission_policy AS cp WHERE cp.agent_id = T.agent_id) AS commissionPolicyNum,\n" +
//            "    sum(p.commission_amount)    AS fyc,\n" +
//            "    sum(p.period_total_premium) AS fyp\n" +
//            "  FROM (\n" +
//            "         WITH RECURSIVE T (agent_id, agent_code, agent_name, recommend_agent_id, depth) AS (\n" +
//            "           SELECT\n" +
//            "             agent_id,\n" +
//            "             agent_code,\n" +
//            "             agent_name,\n" +
//            "             recommend_agent_id,\n" +
//            "             0 AS DEPTH\n" +
//            "           FROM agent\n" +
//            "           WHERE user_id = '?userId?'\n" +
//            "           UNION ALL\n" +
//            "           SELECT\n" +
//            "             D.agent_id,\n" +
//            "             D.agent_code,\n" +
//            "             D.agent_name,\n" +
//            "             D.recommend_agent_id,\n" +
//            "             T.DEPTH + 1 AS DEPTH\n" +
//            "           FROM agent D\n" +
//            "             JOIN T ON D.recommend_agent_id = T.agent_id\n" +
//            "         )\n" +
//            "         SELECT\n" +
//            "           agent_id,\n" +
//            "           agent_code,\n" +
//            "           agent_name,\n" +
//            "           recommend_agent_id,\n" +
//            "           depth\n" +
//            "         FROM T WHERE T.depth=?depth?\n" +
//            "       ) T\n" +
//            "    INNER JOIN agent_detail d ON d.agent_id=T.agent_id\n" +
//            "    LEFT JOIN commission_policy p ON T.agent_id = p.agent_id\n" +
//            "\n" +
//            "GROUP BY T.agent_id,T.agent_code, T.agent_name,d.head_attach_id";


    public static String agentRecommendTearmMemberSql ="SELECT\n" +
            "                T.agent_id,T.agent_code, T.agent_name,T.mobile,d.head_attach_id,\n" +
            "                (SELECT count(*) FROM commission_policy AS cp WHERE cp.agent_id = T.agent_id) AS commissionPolicyNum, \n" +
            "                sum(p.commission_amount)    AS fyc, \n" +
            "                sum(p.period_total_premium) AS fyp \n" +
            "              FROM ( \n" +
            "                     WITH RECURSIVE T (agent_id, agent_code, agent_name, recommend_agent_id,mobile, depth) AS (\n" +
            "                       SELECT \n" +
            "                         a.agent_id,\n" +
            "                         a.agent_code,\n" +
            "                         a.agent_name,\n" +
            "                         a.recommend_agent_id,\n" +
            "                         b.mobile,\n" +
            "                         0 AS DEPTH \n" +
            "                       FROM agent a\n" +
            "                         INNER JOIN agent_detail b on a.agent_id=b.agent_id\n" +
            "                       WHERE user_id = '?userId?'\n" +
            "                       UNION ALL \n" +
            "                       SELECT \n" +
            "                         D.agent_id, \n" +
            "                         D.agent_code, \n" +
            "                         D.agent_name, \n" +
            "                         D.recommend_agent_id,\n" +
            "                         b.mobile,\n" +
            "                         T.DEPTH  +1 AS DEPTH\n" +
            "                       FROM agent D\n" +
            "                         INNER JOIN agent_detail b on D.agent_id=b.agent_id\n" +
            "                         JOIN T ON D.recommend_agent_id = T.agent_id \n" +
            "                     ) \n" +
            "                     SELECT \n" +
            "                       agent_id, \n" +
            "                       agent_code, \n" +
            "                       agent_name,\n" +
            "                       recommend_agent_id,\n" +
            "                       mobile,\n" +
            "                       depth \n" +
            "                     FROM T WHERE T.depth=?depth?\n" +
            "                   ) T \n" +
            "                INNER JOIN agent_detail d ON d.agent_id=T.agent_id \n" +
            "                LEFT JOIN commission_policy p ON T.agent_id = p.agent_id \n" +
            "             \n" +
            "            GROUP BY T.agent_id,T.agent_code,T.mobile, T.agent_name,d.head_attach_id";


    /**
     * 业务员推荐指定某层级之外的业务员团队曾远业绩统计列表
     */
//    public static String agentRecommendOutTearmMemberSql = "\n" +
//            "\n" +
//            "SELECT\n" +
//            "    T.agent_id,T.agent_code, T.agent_name,d.head_attach_id,\n" +
//            "    (SELECT count(*) FROM commission_policy AS cp WHERE cp.agent_id = T.agent_id) AS commissionPolicyNum,\n" +
//            "    sum(p.commission_amount)    AS fyc,\n" +
//            "    sum(p.period_total_premium) AS fyp\n" +
//            "  FROM (\n" +
//            "         WITH RECURSIVE T (agent_id, agent_code, agent_name, recommend_agent_id, depth) AS (\n" +
//            "           SELECT\n" +
//            "             agent_id,\n" +
//            "             agent_code,\n" +
//            "             agent_name,\n" +
//            "             recommend_agent_id,\n" +
//            "             0 AS DEPTH\n" +
//            "           FROM agent\n" +
//            "           WHERE user_id = '?userId?'\n" +
//            "           UNION ALL\n" +
//            "           SELECT\n" +
//            "             D.agent_id,\n" +
//            "             D.agent_code,\n" +
//            "             D.agent_name,\n" +
//            "             D.recommend_agent_id,\n" +
//            "             T.DEPTH + 1 AS DEPTH\n" +
//            "           FROM agent D\n" +
//            "             JOIN T ON D.recommend_agent_id = T.agent_id\n" +
//            "         )\n" +
//            "         SELECT\n" +
//            "           agent_id,\n" +
//            "           agent_code,\n" +
//            "           agent_name,\n" +
//            "           recommend_agent_id,\n" +
//            "           depth\n" +
//            "         FROM T WHERE T.depth>?depth?\n" +
//            "       ) T\n" +
//            "    INNER JOIN agent_detail d ON d.agent_id=T.agent_id\n" +
//            "    LEFT JOIN commission_policy p ON T.agent_id = p.agent_id\n" +
//            "\n" +
//            "GROUP BY T.agent_id,T.agent_code, T.agent_name,d.head_attach_id";
    ;

    public static String agentRecommendOutTearmMemberSql = "SELECT\n" +
            "                T.agent_id,T.agent_code, T.agent_name,T.mobile,d.head_attach_id,\n" +
            "                (SELECT count(*) FROM commission_policy AS cp WHERE cp.agent_id = T.agent_id) AS commissionPolicyNum, \n" +
            "                sum(p.commission_amount)    AS fyc, \n" +
            "                sum(p.period_total_premium) AS fyp \n" +
            "              FROM ( \n" +
            "                     WITH RECURSIVE T (agent_id, agent_code, agent_name, recommend_agent_id,mobile, depth) AS (\n" +
            "                       SELECT \n" +
            "                         a.agent_id,\n" +
            "                         a.agent_code,\n" +
            "                         a.agent_name,\n" +
            "                         a.recommend_agent_id,\n" +
            "                         b.mobile,\n" +
            "                         0 AS DEPTH \n" +
            "                       FROM agent a\n" +
            "                         INNER JOIN agent_detail b  on a.agent_id=b.agent_id\n" +
            "                       WHERE user_id = '?userId?'\n" +
            "                       UNION ALL \n" +
            "                       SELECT \n" +
            "                         D.agent_id, \n" +
            "                         D.agent_code, \n" +
            "                         D.agent_name, \n" +
            "                         D.recommend_agent_id,\n" +
            "                         b.mobile,\n" +
            "                         T.DEPTH + 1 AS DEPTH\n" +
            "                       FROM agent D\n" +
            "                         INNER JOIN agent_detail b  on D.agent_id=b.agent_id\n" +
            "                         JOIN T ON D.recommend_agent_id = T.agent_id \n" +
            "                     ) \n" +
            "                     SELECT \n" +
            "                       agent_id, \n" +
            "                       agent_code, \n" +
            "                       agent_name, \n" +
            "                       recommend_agent_id,\n" +
            "                       mobile,\n" +
            "                       depth \n" +
            "                     FROM T WHERE T.depth>?depth?\n" +
            "                   ) T \n" +
            "                INNER JOIN agent_detail d ON d.agent_id=T.agent_id \n" +
            "                LEFT JOIN commission_policy p ON T.agent_id = p.agent_id \n" +
            "             \n" +
            "            GROUP BY T.agent_id,T.agent_code, T.agent_name,T.mobile,d.head_attach_id";


    /**
     * 业务员推荐指定某层级之外的业务员列表
     */
    public static String agentRecommendOutSql = "\n" +
            "WITH RECURSIVE T (agent_id, agent_code, agent_name, recommend_agent_id, depth) AS (\n" +
            "           SELECT\n" +
            "             agent_id,\n" +
            "             agent_code,\n" +
            "             agent_name,\n" +
            "             recommend_agent_id,\n" +
            "             0 AS DEPTH\n" +
            "           FROM agent\n" +
            "           WHERE user_id = '?userId?'\n" +
            "           UNION ALL\n" +
            "           SELECT\n" +
            "             D.agent_id,\n" +
            "             D.agent_code,\n" +
            "             D.agent_name,\n" +
            "             D.recommend_agent_id,\n" +
            "             T.DEPTH + 1 AS DEPTH\n" +
            "           FROM agent D\n" +
            "             JOIN T ON D.recommend_agent_id = T.agent_id\n" +
            "         )\n" +
            "         SELECT\n" +
            "           agent_id,\n" +
            "           agent_code,\n" +
            "           agent_name,\n" +
            "           recommend_agent_id,\n" +
            "           depth\n" +
            "         FROM T WHERE  T.depth >?depth?";


    /****业务员我的团队sql***/
    public static String agentTeamSql = "\n" +
            "SELECT sum(B.fyc) as fyc ,sum(B.fyp) as fyp ,sum(B.head_count) as head_count from (\n" +
            "  SELECT\n" +
            "    sum(p.commission_amount)    AS fyc,\n" +
            "    sum(p.period_total_premium) AS fyp,\n" +
            "    0                           AS head_count\n" +
            "  FROM (\n" +
            "         WITH RECURSIVE T (agent_id, agent_code, agent_name, recommend_agent_id, depth) AS (\n" +
            "           SELECT\n" +
            "             agent_id,\n" +
            "             agent_code,\n" +
            "             agent_name,\n" +
            "             recommend_agent_id,\n" +
            "             0 AS DEPTH\n" +
            "           FROM agent\n" +
            "           WHERE user_id = '?agentId?'\n" +
            "           UNION ALL\n" +
            "           SELECT\n" +
            "             D.agent_id,\n" +
            "             D.agent_code,\n" +
            "             D.agent_name,\n" +
            "             D.recommend_agent_id,\n" +
            "             T.DEPTH + 1 AS DEPTH\n" +
            "           FROM agent D\n" +
            "             JOIN T ON D.manager_agent_id = T.agent_id\n" +
            "         )\n" +
            "         SELECT\n" +
            "           agent_id,\n" +
            "           agent_code,\n" +
            "           agent_name,\n" +
            "           recommend_agent_id,\n" +
            "           depth\n" +
            "         FROM T\n" +
            "       ) T LEFT JOIN commission_policy p ON T.agent_id = p.agent_id\n" +
            "   WHERE p.biz_year_month='?yearMonth?' " +
            "  UNION ALL\n" +
            "\n" +
            "  SELECT\n" +
            "    0                 AS fyc,\n" +
            "    0                 AS fyp,\n" +
            "    count(A.agent_id) AS head_count\n" +
            "  FROM (\n" +
            "         WITH RECURSIVE T (agent_id, agent_code, agent_name, recommend_agent_id, depth) AS (\n" +
            "           SELECT\n" +
            "             agent_id,\n" +
            "             agent_code,\n" +
            "             agent_name,\n" +
            "             recommend_agent_id,\n" +
            "             0 AS DEPTH\n" +
            "           FROM agent\n" +
            "           WHERE user_id = '?agentId?'\n" +
            "           UNION ALL\n" +
            "           SELECT\n" +
            "             D.agent_id,\n" +
            "             D.agent_code,\n" +
            "             D.agent_name,\n" +
            "             D.recommend_agent_id,\n" +
            "             T.DEPTH + 1 AS DEPTH\n" +
            "           FROM agent D\n" +
            "             JOIN T ON D.recommend_agent_id = T.agent_id\n" +
            "         )\n" +
            "         SELECT\n" +
            "           agent_id,\n" +
            "           agent_code,\n" +
            "           agent_name,\n" +
            "           recommend_agent_id,\n" +
            "           depth\n" +
            "         FROM T\n" +
            "       ) A\n" +
            ") B";

    /****业务员我各个层级推荐人数统计***/
    public static String agentLevelCountSql = "SELECT 'ONE_RECOMMEND_TEAM' as level_team_type, count(A.agent_id) head_count FROM (\n" +
            "        WITH RECURSIVE T (agent_id,agent_code,agent_name, recommend_agent_id, depth)  AS (\n" +
            "           SELECT agent_id,agent_code, agent_name,recommend_agent_id, 0 AS DEPTH\n" +
            "          FROM agent\n" +
            "          WHERE user_id ='?'\n" +
            "        UNION ALL\n" +
            "               SELECT  D.agent_id,D.agent_code, D.agent_name, D.recommend_agent_id,T.DEPTH + 1 AS DEPTH\n" +
            "          FROM agent D\n" +
            "          JOIN T ON D.recommend_agent_id = T.agent_id\n" +
            "          )\n" +
            "        SELECT   agent_id,agent_code,agent_name, recommend_agent_id, depth FROM T\n" +
            "          WHERE T.depth=1\n" +
            "        ) A\n" +
            "UNION ALL\n" +
            "SELECT 'TWO_RECOMMEND_TEAM' as level_team_type, count(A.agent_id) head_count  FROM (\n" +
            "        WITH RECURSIVE T (agent_id,agent_code,agent_name, recommend_agent_id, depth)  AS (\n" +
            "           SELECT agent_id,agent_code, agent_name,recommend_agent_id, 0 AS DEPTH\n" +
            "          FROM agent\n" +
            "          WHERE user_id ='?'\n" +
            "        UNION ALL\n" +
            "               SELECT  D.agent_id,D.agent_code, D.agent_name, D.recommend_agent_id,T.DEPTH + 1 AS DEPTH\n" +
            "          FROM agent D\n" +
            "          JOIN T ON D.recommend_agent_id = T.agent_id\n" +
            "          )\n" +
            "        SELECT   agent_id,agent_code,agent_name, recommend_agent_id, depth FROM T\n" +
            "          WHERE T.depth=2\n" +
            "        ) A\n" +
            "UNION ALL\n" +
            "SELECT 'OTHER_RECOMMEND_TEAM' as level_team_type, count(A.agent_id) head_count  FROM (\n" +
            "        WITH RECURSIVE T (agent_id,agent_code,agent_name, recommend_agent_id, depth)  AS (\n" +
            "           SELECT agent_id,agent_code, agent_name,recommend_agent_id, 0 AS DEPTH\n" +
            "          FROM agent\n" +
            "          WHERE user_id ='?'\n" +
            "        UNION ALL\n" +
            "               SELECT  D.agent_id,D.agent_code, D.agent_name, D.recommend_agent_id,T.DEPTH + 1 AS DEPTH\n" +
            "          FROM agent D\n" +
            "          JOIN T ON D.recommend_agent_id = T.agent_id\n" +
            "          )\n" +
            "        SELECT   agent_id,agent_code,agent_name, recommend_agent_id, depth FROM T\n" +
            "          WHERE T.depth>2\n" +
            "        ) A";






}