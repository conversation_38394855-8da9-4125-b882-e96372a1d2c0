package com.gclife.agent.service.law.impl;

import com.gclife.agent.base.model.bo.law.LawItemBo;
import com.gclife.agent.base.util.LanguageUtils;
import com.gclife.agent.core.jooq.tables.daos.LawDao;
import com.gclife.agent.core.jooq.tables.pojos.LawItemAlgorithmPo;
import com.gclife.agent.core.jooq.tables.pojos.LawItemPo;
import com.gclife.agent.core.jooq.tables.pojos.LawPo;
import com.gclife.agent.dao.law.AlgorithmExtDao;
import com.gclife.agent.dao.law.LawItemAlgorithmExtDao;
import com.gclife.agent.dao.law.LawItemAlgorithmParamValueExtDao;
import com.gclife.agent.dao.law.LawItemExtDao;
import com.gclife.agent.base.interfaces.impl.PlatformServiceInterfaceImpl;
import com.gclife.agent.model.bo.AlgorithmBo;
import com.gclife.agent.model.bo.LawItemAlgorithmParamValueBo;
import com.gclife.agent.model.bo.ParamValueResponse;
import com.gclife.agent.model.bo.report.LawItemParamBo;
import com.gclife.agent.base.model.config.AgentErrorConfigEnum;
import com.gclife.agent.base.model.config.AgentTermEnum;
import com.gclife.agent.model.request.law.SaveLawItemAlgorithmRequest;
import com.gclife.agent.model.response.law.ItemParamResponse;
import com.gclife.agent.model.response.law.LawItemParamResponse;
import com.gclife.agent.model.response.law.LawItemResponse;
import com.gclife.agent.model.response.law.RowParamResponse;
import com.gclife.agent.service.data.AgentService;
import com.gclife.agent.service.law.LawItemBusinessService;
import com.gclife.common.exception.RequestException;
import com.gclife.common.model.ResultObject;
import com.gclife.common.model.base.Users;
import com.gclife.common.model.config.TerminologyConfigEnum;
import com.gclife.common.service.impl.BaseBusinessServiceImpl;
import com.gclife.common.util.AssertUtils;
import com.gclife.common.util.UUIDUtils;
import com.gclife.platform.api.PlatformInternationalBaseApi;
import com.gclife.platform.model.request.SyscodeRequest;
import com.gclife.platform.model.response.SyscodeResponse;
import org.modelmapper.TypeToken;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * create 18-1-8
 * description:
 */
@Repository
public class LawItemBusinessServiceImpl extends BaseBusinessServiceImpl implements LawItemBusinessService {

    @Autowired
    private AlgorithmExtDao algorithmExtDao;
    @Autowired
    private LawItemAlgorithmExtDao lawItemAlgorithmExtDao;
    @Autowired
    private LawItemExtDao lawItemExtDao;
    @Autowired
    private LawItemAlgorithmParamValueExtDao lawItemAlgorithmParamValueExtDao;
    @Autowired
    PlatformServiceInterfaceImpl platformServiceInterface;
    @Autowired
    private AgentService agentService;

    @Autowired
    private LawDao lawDao;

    @Autowired
    private PlatformInternationalBaseApi platformInternationalBaseApi;

    @Autowired
    private LanguageUtils languageUtils;

    @Override
    public ResultObject<LawItemParamResponse> getLawParamList(String lawItemId) {
        ResultObject<LawItemParamResponse> resultObject = new ResultObject<>();
        try {
            LawItemParamResponse lawItemParamResponse = new LawItemParamResponse();
            //查询基本法项目信息
            LawItemBo lawItemBo = lawItemExtDao.getLawItemById(lawItemId);
            AssertUtils.isNotNull(this.getLogger(), lawItemBo, AgentErrorConfigEnum.AGENT_QUERY_LAW_ITEM_IS_NULL);
            lawItemParamResponse.setAlgorithmName(lawItemBo.getAlgorithmName());
            lawItemParamResponse.setLawItemId(lawItemBo.getLawItemId());
            lawItemParamResponse.setItemName(lawItemBo.getItemName());
            lawItemParamResponse.setDescription(lawItemBo.getDescription());
            lawItemParamResponse.setHasParamFlag(lawItemBo.getHasParamFlag());
            lawItemParamResponse.setCloneFlag(lawItemBo.getCloneFlag());

            //查出 算法参数 list   参数名称国际化
            List<LawItemParamBo> lawItemParamList = lawItemExtDao.getLawItemParamList(lawItemBo.getLawItemId());
            // 生成列对象
            List<ItemParamResponse> itemParamResponseList = (List<ItemParamResponse>) this.converterList(lawItemParamList, new TypeToken<List<ItemParamResponse>>() {
            }.getType());
            if (AssertUtils.isNotEmpty(itemParamResponseList)) {
                //获取参数国际化 名称
                List<String> paramCodeList = itemParamResponseList.stream().map(itemParamResponse -> itemParamResponse.getParamCode()).collect(Collectors.toList());
                SyscodeRequest syscodeReq = new SyscodeRequest();
                syscodeReq.setCodeType(AgentTermEnum.BASE_INTERNATIONAL_TEXT_TYPE.PARAM_TYPE.name());
                syscodeReq.setCodeKeys(paramCodeList);
                //获取算法编码国际化
                List<SyscodeResponse> internationalList = platformServiceInterface.getInternationalList(syscodeReq);

                itemParamResponseList.forEach(itemParamResponse -> {
                    //国际化
                    if (AssertUtils.isNotEmpty(internationalList)) {
                        internationalList.forEach(syscodeRespFc -> {
                            if (syscodeRespFc.getCodeKey().equals(itemParamResponse.getParamCode())) {
                                itemParamResponse.setParamName(syscodeRespFc.getCodeName());
                            }
                        });
                    }
                });
                lawItemParamResponse.setListParamTitle(itemParamResponseList);
                //查出参数值
                List<LawItemAlgorithmParamValueBo> lawItemAlgorithmParamValueBoList = lawItemAlgorithmParamValueExtDao.getParamValueByLawItemId(lawItemBo.getLawItemId());
                if (AssertUtils.isNotEmpty(lawItemAlgorithmParamValueBoList)) {
                    //生成行数据
                    List<RowParamResponse> listRowParam = new ArrayList<>();

                    Map<String, List<LawItemAlgorithmParamValueBo>> lawItemAlgorithmParamValueGroupBy = lawItemAlgorithmParamValueBoList.stream().collect(Collectors.groupingBy(LawItemAlgorithmParamValueBo::getLawItemAlgorithmId));
                    lawItemAlgorithmParamValueGroupBy.forEach((lawItemAlgorithmId, LawItemAlgorithmParamValueList) -> {
                        //生成 列 数据
                        List<ParamValueResponse> listParamValue = (List<ParamValueResponse>) this.converterList(itemParamResponseList, new TypeToken<List<ParamValueResponse>>() {
                        }.getType());

                        RowParamResponse rowParamResponse = new RowParamResponse();
                        rowParamResponse.setLawItemAlgorithmId(lawItemAlgorithmId);
                        //把值设为 0
                        listParamValue.forEach(paramValueResponse -> paramValueResponse.setParamValue(null));
                        LawItemAlgorithmParamValueList.forEach(lawItemAlgorithmParamValueBo -> {
                            listParamValue.forEach(paramValueResponse -> {
                                if (paramValueResponse.getParamCode().equals(lawItemAlgorithmParamValueBo.getParamCode())) {
                                    paramValueResponse.setParamValue(lawItemAlgorithmParamValueBo.getParamValue());
                                }
                            });
                        });
                        rowParamResponse.setListParamValue(listParamValue);
                        listRowParam.add(rowParamResponse);
                    });
                    lawItemParamResponse.setListRowParam(listRowParam);
                }

            }
            //设置返回参数
            resultObject.setData(lawItemParamResponse);
        } catch (Exception e) {
            e.printStackTrace();
            if (e instanceof RequestException) {
                RequestException error = (RequestException) e;
                //错误设置
                resultObject.setIenum(error.getiEnum());
            } else {
                resultObject.setIenum(AgentErrorConfigEnum.AGENT_QUERY_LAW_ITEM_PARAM_DETAIL_ERROR);
            }
        }
        return resultObject;
    }

    @Override
    @Transactional
    public ResultObject deleteLawItem(String lawItemId, Users users) {
        ResultObject resultObject = new ResultObject();
        try {
            //项目算法 必须是 顶级父算法
            LawItemBo lawItemBo = lawItemExtDao.getParentAlgorithm(lawItemId);
            //是否有 父算法 没有 父算法进行 无效 修改操作
            if (AssertUtils.isNotNull(lawItemBo) && AssertUtils.isNotEmpty(lawItemBo.getParentAlgorithmCode())) {
                throw new RequestException(AgentErrorConfigEnum.AGENT_DELETE_LAW_ITEM_ERROR);
            }
            //设置基本发项目失效 项
            lawItemExtDao.deleteLawItem(lawItemId, users);
            //设置基本发项目 算法失效
            lawItemAlgorithmExtDao.deleteLawItemAlgorithm(lawItemId, null, users);
        } catch (Exception e) {
            e.printStackTrace();
            if (e instanceof RequestException) {
                RequestException error = (RequestException) e;
                //错误设置
                resultObject.setIenum(error.getiEnum());
            } else {
                resultObject.setIenum(AgentErrorConfigEnum.AGENT_DELETE_LAW_ITEM_ERROR);
            }
        }
        return resultObject;
    }

    @Override
    @Transactional
    public ResultObject saveLawItemAlgorithm(SaveLawItemAlgorithmRequest saveLawItemAlgorithmRequest, Users users) {
        ResultObject resultObject = new ResultObject();
        try {
            AssertUtils.isNotEmpty(this.getLogger(), saveLawItemAlgorithmRequest.getAlgorithmIds(), AgentErrorConfigEnum.AGENT_SAVE_LAW_ITEM_ALGORITHM_ERROR);
            AssertUtils.isNotEmpty(this.getLogger(), saveLawItemAlgorithmRequest.getLawId(), AgentErrorConfigEnum.AGENT_SAVE_LAW_ITEM_ALGORITHM_ERROR);
            List<String> algorithmIds = saveLawItemAlgorithmRequest.getAlgorithmIds();
            String lawId = saveLawItemAlgorithmRequest.getLawId();
            LawPo lawPo = lawDao.findById(lawId);
            AssertUtils.isNotNull(this.getLogger(), lawPo, AgentErrorConfigEnum.AGENT_SAVE_LAW_ITEM_ALGORITHM_ERROR);
            //查询出项目算法信息
            List<AlgorithmBo> algorithmList = algorithmExtDao.getAlgorithmList(algorithmIds, lawPo.getLawId());
            AssertUtils.isNotEmpty(this.getLogger(), algorithmList, AgentErrorConfigEnum.AGENT_SAVE_LAW_ITEM_ALGORITHM_ERROR);
            this.getLogger().error("algorithmIds:" + algorithmIds.size() + " :: " + "algorithmList:" + algorithmList.size());
            //添加项目
            List<LawItemPo> lawItemPoList = new ArrayList<>();
            //添加 项目算法 关联数据
            List<LawItemAlgorithmPo> lawItemAlgorithmList = new ArrayList<>();
            algorithmList.forEach(algorithmBo -> {
                //项目设置参数
                LawItemPo lawItemPo = new LawItemPo();
                lawItemPo.setLawItemId(UUIDUtils.getUUIDShort());
                lawItemPo.setLawId(lawPo.getLawId());
                lawItemPo.setItemName(algorithmBo.getAlgorithmName());
                lawItemPo.setValidFlag(TerminologyConfigEnum.VALID_FLAG.effective.name());
                lawItemPo.setCalculateTypeCode(algorithmBo.getAlgorithmTypeCode());
                lawItemPo.setParamConfigFlag(algorithmBo.getHasParamFlag());
                lawItemPoList.add(lawItemPo);
                //项目 算法 关联信息添加
                LawItemAlgorithmPo lawItemAlgorithm = new LawItemAlgorithmPo();
                lawItemAlgorithm.setLawItemAlgorithmId(UUIDUtils.getUUIDShort());
                lawItemAlgorithm.setLawItemId(lawItemPo.getLawItemId());
                lawItemAlgorithm.setAlgorithmCode(algorithmBo.getAlgorithmCode());
                lawItemAlgorithm.setValidFlag(TerminologyConfigEnum.VALID_FLAG.effective.name());
                lawItemAlgorithmList.add(lawItemAlgorithm);
            });
            agentService.saveLawItem(lawItemPoList);
            //添加项目算法关联信息
            agentService.saveLawItemAlgorithm(lawItemAlgorithmList);

        } catch (Exception e) {
            e.printStackTrace();
            if (e instanceof RequestException) {
                RequestException error = (RequestException) e;
                //错误设置
                resultObject.setIenum(error.getiEnum());
            } else {
                resultObject.setIenum(AgentErrorConfigEnum.AGENT_SAVE_LAW_ITEM_ALGORITHM_ERROR);
            }
        }
        return resultObject;
    }

    @Override
    public ResultObject<List<LawItemResponse>> gitLawItem(Users users, String lawId) {
        ResultObject<List<LawItemResponse>> resultObject = new ResultObject<>();
        try {
            //项目信息列表
            List<LawItemBo> lawItemList = lawItemExtDao.getLawItemListByLawId(lawId);
            //算法名称国际化
            if (AssertUtils.isNotEmpty(lawItemList)) {
                //获取所有算法编码
//                List<String> algorithmCodeList = lawItemList.stream().map(lawItemBo -> lawItemBo.getAlgorithmCode()).collect(Collectors.toList());
//                SyscodeRequest syscodeReq = new SyscodeRequest();
//                syscodeReq.setCodeType(AgentTermEnum.BASE_INTERNATIONAL_TEXT_TYPE.ALGORITHM_TYPE.name());
//                syscodeReq.setCodeKeys(algorithmCodeList);

                //批量查询国际化
                List<String> types = Arrays.asList(AgentTermEnum.BASE_INTERNATIONAL_TEXT_TYPE.ALGORITHM_TYPE.name(),
                        AgentTermEnum.BASE_INTERNATIONAL_TEXT_TYPE.FLAG_TYPE.name(),
                        AgentTermEnum.BASE_INTERNATIONAL_TEXT_TYPE.PARAM_CONFIG_FLAG_TYPE.name());
                Map<String, List<SyscodeResponse>> internationalListAll = platformInternationalBaseApi.queryBatchInternationalByCodeKeys(users.getLanguage(), types).getData();

                //获取算法编码国际化
                List<SyscodeResponse> internationalList = internationalListAll.get(AgentTermEnum.BASE_INTERNATIONAL_TEXT_TYPE.ALGORITHM_TYPE.name());
                //获取  是否国际化
                List<SyscodeResponse> flagTypeList = internationalListAll.get(AgentTermEnum.BASE_INTERNATIONAL_TEXT_TYPE.FLAG_TYPE.name());
                //获取  配置状态国际化
                List<SyscodeResponse> paramConfigFlagList = internationalListAll.get(AgentTermEnum.BASE_INTERNATIONAL_TEXT_TYPE.PARAM_CONFIG_FLAG_TYPE.name());
                if (AssertUtils.isNotEmpty(internationalList) || AssertUtils.isNotEmpty(flagTypeList)) {
                    lawItemList.forEach(lawItemBo -> {
//                        if (AssertUtils.isNotEmpty(internationalList)) {
                            lawItemBo.setAlgorithmName(languageUtils.findCodeName(internationalList, lawItemBo.getAlgorithmCode()));
//                        }
//                        if (AssertUtils.isNotEmpty(paramConfigFlagList)) {
                            lawItemBo.setParamConfigFlag(languageUtils.findCodeName(paramConfigFlagList, lawItemBo.getParamConfigFlag()));
//                        }
//                        if (AssertUtils.isNotEmpty(flagTypeList)) {
                            //设置 是否有附件
                            if (AssertUtils.isNotEmpty(lawItemBo.getAlgorithmAttachmentId())) {
                                lawItemBo.setAlgorithmAttachment(languageUtils.findCodeName(flagTypeList, AgentTermEnum.FLAG.TRUE.name()));
                            } else {
                                lawItemBo.setAlgorithmAttachment(languageUtils.findCodeName(flagTypeList, AgentTermEnum.FLAG.FALSE.name()));
                            }
                            //设置 是否有依赖项
                            if (AssertUtils.isNotEmpty(lawItemBo.getAlgorithmRelationshipId())) {
                                lawItemBo.setParentAlgorithm(languageUtils.findCodeName(flagTypeList, AgentTermEnum.FLAG.TRUE.name()));
                            } else {
                                lawItemBo.setParentAlgorithm(languageUtils.findCodeName(flagTypeList, AgentTermEnum.FLAG.FALSE.name()));
                            }
//                        }

                    });
                }
            }

            List<LawItemResponse> lawItemResponseList = (List<LawItemResponse>) this.converterList(lawItemList, new TypeToken<List<LawItemResponse>>() {
            }.getType());
            resultObject.setData(lawItemResponseList);

        } catch (Exception e) {
            e.printStackTrace();
            if (e instanceof RequestException) {
                RequestException error = (RequestException) e;
                //错误设置
                resultObject.setIenum(error.getiEnum());
            } else {
                resultObject.setIenum(AgentErrorConfigEnum.AGENT_SAVE_LAW_ITEM_ALGORITHM_ERROR);
            }
        }
        return resultObject;
    }

    /**
     * 获取是否  国际化
     *
     * @param flagTypeList
     * @param type
     * @return
     */
//    public String getFlagByType(List<SyscodeResponse> flagTypeList, String type) {
//        String typeName = "";
//        if (AssertUtils.isNotEmpty(flagTypeList)) {
//            for (SyscodeResponse syscodeRespFc : flagTypeList) {
//                if (syscodeRespFc.getCodeKey().equals(type)) {
//                    typeName = syscodeRespFc.getCodeName();
//                    break;
//                }
//            }
//        }
//        return typeName;
//    }

    @Override
    public ResultObject<List<LawItemParamResponse>> gitLawItemDetail(String lawId) {
        ResultObject<List<LawItemParamResponse>> resultObject = new ResultObject<>();
        try {



        } catch (Exception e) {
            e.printStackTrace();
            if (e instanceof RequestException) {
                RequestException error = (RequestException) e;
                //错误设置
                resultObject.setIenum(error.getiEnum());
            } else {
                resultObject.setIenum(AgentErrorConfigEnum.AGENT_QUERY_LAW_ITEM_DETAIL_ERROR);
            }
        }
        return resultObject;
    }
}
