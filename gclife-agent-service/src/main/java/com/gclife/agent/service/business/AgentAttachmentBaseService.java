package com.gclife.agent.service.business;

import com.gclife.agent.base.model.config.AgentTermEnum;
import com.gclife.agent.core.jooq.tables.pojos.AgentAttachmentPo;
import com.gclife.common.service.BaseService;

/**
 * <AUTHOR>
 * @date 2022/4/18
 */
public interface AgentAttachmentBaseService extends BaseService {

    /**
     * 生成附件
     *
     * @param agentId
     * @param language
     * @param agentAttachmentPo
     * @param contract
     * @param isShowCompanySeal
     * @return
     */
    String generateAgentAttachment(String agentId, String language, AgentAttachmentPo agentAttachmentPo, AgentTermEnum.AGENT_ATTACHMENT_TYPE contract, Boolean isShowCompanySeal);
}
