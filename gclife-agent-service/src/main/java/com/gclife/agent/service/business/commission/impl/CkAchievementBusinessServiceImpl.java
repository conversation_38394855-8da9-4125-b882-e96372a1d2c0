package com.gclife.agent.service.business.commission.impl;

import com.gclife.agent.base.model.config.AgentErrorConfigEnum;
import com.gclife.agent.dao.AgentHistoryExtDao;
import com.gclife.agent.dao.commission.CkAchievementExtDao;
import com.gclife.agent.model.bo.CkAchievementExtBo;
import com.gclife.agent.model.bo.CkAchievementExtBo2;
import com.gclife.agent.model.bo.CkAchievementExtListBo;
import com.gclife.agent.service.business.commission.CkAchievementBusinessService;
import com.gclife.attachment.api.AttachmentApi;
import com.gclife.attachment.model.response.AttachmentResponse;
import com.gclife.common.exception.RequestException;
import com.gclife.common.model.ResultObject;
import com.gclife.common.model.base.Users;
import com.gclife.common.service.impl.BaseBusinessServiceImpl;
import com.gclife.common.util.AssertUtils;
import com.gclife.common.util.DateUtils;
import com.gclife.platform.api.PlatformBranchBaseApi;
import com.gclife.platform.model.response.BranchResponse;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.xssf.usermodel.XSSFSheet;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.servlet.http.HttpServletResponse;
import java.io.ByteArrayOutputStream;
import java.io.InputStream;
import java.io.OutputStream;
import java.net.URL;
import java.net.URLEncoder;
import java.util.*;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import static com.gclife.agent.base.model.config.AgentTermEnum.AGENT_TYPE.EMPLOYEE;
import static com.gclife.agent.base.model.config.AgentTermEnum.AGENT_TYPE.LIFE_CONSULTANT;

/**
 * <AUTHOR>
 *         create 19-7-9
 *         description:
 */
@Service
public class CkAchievementBusinessServiceImpl extends BaseBusinessServiceImpl implements CkAchievementBusinessService {
    @Autowired
    private CkAchievementExtDao ckAchievementExtDao;
    @Autowired
    private AttachmentApi attachmentApi;
    @Autowired
    private PlatformBranchBaseApi platformBranchBaseApi;
    @Autowired
    private AgentHistoryExtDao agentHistoryExtDao;

    @Override
    public void exportCkAchievement(HttpServletResponse httpServletResponse, Users users, String agentTypeCode) {

        try {
            ResultObject<AttachmentResponse> attachmentRespFcResultObject = null;
            if (agentTypeCode.equals(LIFE_CONSULTANT.toString())){
                //由输入流得到工作簿
                attachmentRespFcResultObject = attachmentApi.templateGet("KPI_LIFE_CONSULTANT");
                httpServletResponse.addHeader("Content-Disposition", "attachment;filename=" + URLEncoder.encode("KPI_LIFE_CONSULTANT.xls", "UTF-8"));
            }else if (agentTypeCode.equals(EMPLOYEE.toString())){
                //由输入流得到工作簿
                attachmentRespFcResultObject = attachmentApi.templateGet("KPI_SALES_MANAGER");
                httpServletResponse.addHeader("Content-Disposition", "attachment;filename=" + URLEncoder.encode("KPI_SALES_MANAGER.xls", "UTF-8"));
            }
            URL url = new URL(attachmentRespFcResultObject.getData().getUrl());
            InputStream inputStream = url.openStream();
            XSSFWorkbook workbook = new XSSFWorkbook(inputStream);
            //得到工作表
            XSSFSheet sheet = workbook.getSheetAt(0);
            List<CkAchievementExtBo> ckAchievementExtBos = ckAchievementExtDao.queryListCkAchievementExtBo(agentTypeCode);
            List<String> branchIds = new ArrayList<>();
            if (AssertUtils.isNotEmpty(ckAchievementExtBos)){
                Map<CkAchievementExtBo2, List<CkAchievementExtBo>> map = ckAchievementExtBos.stream()
                        .collect(Collectors.groupingBy(CkAchievementExtBo::getCkAchievementExtBo2, Collectors.toList()));
                List<CkAchievementExtListBo> list=new ArrayList<>();
                for (Map.Entry<CkAchievementExtBo2, List<CkAchievementExtBo>> m : map.entrySet()) {
                    CkAchievementExtListBo achievementExtListBo =new CkAchievementExtListBo();
                    achievementExtListBo.setAgentId(m.getKey().getAgentId());
                    achievementExtListBo.setAgentCode(m.getKey().getAgentCode());
                    achievementExtListBo.setAgentName(m.getKey().getAgentName());
                    achievementExtListBo.setAgentLevelId(m.getKey().getAgentLevelId());
                    achievementExtListBo.setAgentLevelName(m.getKey().getAgentLevelName());
                    achievementExtListBo.setBranchId(m.getKey().getBranchId());
                    branchIds.add(m.getKey().getBranchId());
                    achievementExtListBo.setHireDate(m.getKey().getHireDate());
                    achievementExtListBo.setDismissDate(m.getKey().getDismissDate());
                    achievementExtListBo.setBizYearMonth(m.getKey().getBizYearMonth());
                    achievementExtListBo.setList(m.getValue());
                    list.add(achievementExtListBo);
                }
                try {
                    int num = 0;
                    for (int i = 3; i < sheet.getLastRowNum(); i++) {
                        if (!(list.size() > num && AssertUtils.isNotNull(list.get(num)))) {
                            break;
                        }
                        CkAchievementExtListBo achievementExtListBo = list.get(num);
                        num++;
                        Row writeRow = sheet.getRow(i);
                        writeRow.getCell(0).setCellValue(num);
                        String yearMonth = achievementExtListBo.getBizYearMonth();
                        if (!AssertUtils.isNotEmpty(yearMonth)) {
                            yearMonth = "--";
                        }
                        writeRow.getCell(1).setCellValue(yearMonth);

                        String agentName = achievementExtListBo.getAgentName();
                        if (!AssertUtils.isNotEmpty(agentName)) {
                            agentName = "--";
                        }
                        writeRow.getCell(2).setCellValue(agentName);


                        String agentCode = achievementExtListBo.getAgentCode();
                        if (!AssertUtils.isNotEmpty(agentCode)) {
                            agentCode = "--";
                        }
                        writeRow.getCell(3).setCellValue(agentCode);

                        List<BranchResponse> listResultObject = platformBranchBaseApi.queryBranchByIds(branchIds).getData();
                        Optional<BranchResponse> optional = listResultObject.stream().filter(branchBaseRespFc -> (branchBaseRespFc.getBranchId().equals(achievementExtListBo.getBranchId()))).findFirst();
                        if (optional.isPresent()){
                            String branchName = optional.get().getBranchName();
                            if (!AssertUtils.isNotEmpty(branchName)) {
                                branchName = "--";
                            }
                            writeRow.getCell(4).setCellValue(branchName);
                        }

                        String agentLevelName = achievementExtListBo.getAgentLevelName();
                        if (!AssertUtils.isNotEmpty(agentLevelName)) {
                            agentLevelName = "--";
                        }
                        writeRow.getCell(5).setCellValue(agentLevelName);

                        String hireDate = String.valueOf(DateUtils.timeStrToString(achievementExtListBo.getHireDate(), "yyyy-MM-dd"));
                        if (hireDate.equals("null")) {
                            hireDate = "--";
                        }
                        writeRow.getCell(6).setCellValue(hireDate);

                        String dismissDate = String.valueOf(DateUtils.timeStrToString(achievementExtListBo.getDismissDate(), "yyyy-MM-dd"));
                        if (dismissDate.equals("null")) {
                            dismissDate = "--";
                        }
                        writeRow.getCell(7).setCellValue(dismissDate);

                        if (agentTypeCode.equals(LIFE_CONSULTANT.toString())){
                            writeRow.getCell(8).setCellValue("--");
                            writeRow.getCell(9).setCellValue("--");
                            writeRow.getCell(10).setCellValue("--");
                            writeRow.getCell(11).setCellValue("--");
                            writeRow.getCell(12).setCellValue("--");
                            achievementExtListBo.getList().forEach( ckAchievementExtBo -> {
                                if (ckAchievementExtBo.getAchievementTypeCode().equals("LC_TEAM_GOVERNED_MANPOWER")){
                                    String value = ckAchievementExtBo.getValue();
                                    if (!AssertUtils.isNotEmpty(value)) {
                                        value = "--";
                                    }
                                    writeRow.getCell(8).setCellValue(value);
                                }
                                if (ckAchievementExtBo.getAchievementTypeCode().equals("LC_TEAM_EFFECTIVE_MANPOWER")){
                                    String value = ckAchievementExtBo.getValue();
                                    if (!AssertUtils.isNotEmpty(value)) {
                                        value = "--";
                                    }
                                    writeRow.getCell(9).setCellValue(value);
                                }
                                if (ckAchievementExtBo.getAchievementTypeCode().equals("NEW_POLICY_NUMBER")){
                                    String value = ckAchievementExtBo.getValue();
                                    if (!AssertUtils.isNotEmpty(value)) {
                                        value = "--";
                                    }
                                    writeRow.getCell(10).setCellValue(value);
                                }
                                if (ckAchievementExtBo.getAchievementTypeCode().equals("FYP")){
                                    String value = ckAchievementExtBo.getValue();
                                    if (!AssertUtils.isNotEmpty(value)) {
                                        value = "--";
                                    }
                                    writeRow.getCell(11).setCellValue(value);
                                }
                                if (ckAchievementExtBo.getAchievementTypeCode().equals("RENEWAL_RATE")){
                                    String value = ckAchievementExtBo.getValue();
                                    if (!AssertUtils.isNotEmpty(value)) {
                                        value = "--";
                                    }
                                    writeRow.getCell(12).setCellValue(value);
                                }
                            });
                        }else if (agentTypeCode.equals(EMPLOYEE.toString())){
                            writeRow.getCell(8).setCellValue("--");
                            writeRow.getCell(9).setCellValue("--");
                            writeRow.getCell(10).setCellValue("--");
                            writeRow.getCell(11).setCellValue("--");
                            writeRow.getCell(12).setCellValue("--");
                            writeRow.getCell(13).setCellValue("--");
                            achievementExtListBo.getList().forEach( ckAchievementExtBo -> {
                                if (ckAchievementExtBo.getAchievementTypeCode().equals("SM_MANAGER_DIRECTOR_NUMBER")){
                                    String value = ckAchievementExtBo.getValue();
                                    if (!AssertUtils.isNotEmpty(value)) {
                                        value = "--";
                                    }
                                    writeRow.getCell(8).setCellValue(value);
                                }
                                if (ckAchievementExtBo.getAchievementTypeCode().equals("SM_MANAGER_SENIOR_DIRECTOR_NUMBER")){
                                    String value = ckAchievementExtBo.getValue();
                                    if (!AssertUtils.isNotEmpty(value)) {
                                        value = "--";
                                    }
                                    writeRow.getCell(9).setCellValue(value);
                                }
                                if (ckAchievementExtBo.getAchievementTypeCode().equals("SM_MANAGER_HIGH_LEVEL_DIRECTOR_NUMBER")){
                                    String value = ckAchievementExtBo.getValue();
                                    if (!AssertUtils.isNotEmpty(value)) {
                                        value = "--";
                                    }
                                    writeRow.getCell(10).setCellValue(value);
                                }
                                if (ckAchievementExtBo.getAchievementTypeCode().equals("NEW_POLICY_NUMBER")){
                                    String value = ckAchievementExtBo.getValue();
                                    if (!AssertUtils.isNotEmpty(value)) {
                                        value = "--";
                                    }
                                    writeRow.getCell(11).setCellValue(value);
                                }
                                if (ckAchievementExtBo.getAchievementTypeCode().equals("FYP")){
                                    String value = ckAchievementExtBo.getValue();
                                    if (!AssertUtils.isNotEmpty(value)) {
                                        value = "--";
                                    }
                                    writeRow.getCell(12).setCellValue(value);
                                }
                                if (ckAchievementExtBo.getAchievementTypeCode().equals("RENEWAL_RATE")){
                                    String value = ckAchievementExtBo.getValue();
                                    if (!AssertUtils.isNotEmpty(value)) {
                                        value = "--";
                                    }
                                    writeRow.getCell(13).setCellValue(value);
                                }
                            });
                        }
                    }
                } catch (Exception e) {
                    e.printStackTrace();
                }
            }
            ByteArrayOutputStream byteArrayOutputStream = new ByteArrayOutputStream();
            workbook.write(byteArrayOutputStream);
            httpServletResponse.setCharacterEncoding("UTF-8");
            httpServletResponse.setContentType("application/x-download");
            OutputStream outputStream = httpServletResponse.getOutputStream();
            outputStream.write(byteArrayOutputStream.toByteArray());
            outputStream.close();
            inputStream.close();
            byteArrayOutputStream.close();

        } catch (Exception e) {
            e.printStackTrace();
            if (e instanceof RequestException) {
                RequestException error = (RequestException) e;
                //错误设置
                throw new RequestException(error.getiEnum());
            } else {
                throw new RequestException();
            }
        }
    }

    /**
     * 月度考核批处理
     * @param bizYearMonth 考核月份
     * @return
     */
    @Override
    public ResultObject batchCk(String bizYearMonth) {
        ResultObject resultObject = new ResultObject();
        try {
            // 查询业务员
            List<String> branchIds = agentHistoryExtDao.listCkBranch(bizYearMonth);
            branchIds.forEach(branchId -> {
                try {
                    agentHistoryExtDao.branchCk(branchId, bizYearMonth);
                } catch (Exception e) {
                    this.getLogger().error("机构：" + branchId + "考核出错");
                    throw new RequestException("机构：" + branchId + "考核出错");
                }
            });
        } catch (Exception e) {
            e.printStackTrace();
            setResultObjectException(getLogger(), resultObject, e, AgentErrorConfigEnum.AGENT_BUSINESS_BATAH_CHECK_ERROR);
        }
        return resultObject;
    }
}