package com.gclife.agent.dao.neo4j;

import com.gclife.agent.model.bo.AgentBo;
import com.gclife.agent.model.neo4j.AgentNode;
import com.gclife.common.dao.base.BaseDao;

import java.util.List;

/**
 * \* Created with IntelliJ IDEA.
 * \* User: deep
 * \* Date: 17-9-18
 * \* Time: 下午5:43
 * \* To change this template use File | Settings | File Templates.
 * \* Description:
 *
 * <AUTHOR>
 */
public interface AgentNodeDao extends BaseDao {

    /**
     * 查询上月业务员架构信息
     * @return
     */
    public List<AgentNode> loadLastMonthAgentNodes(List<String> branchIds,String bizYearMonth);

}