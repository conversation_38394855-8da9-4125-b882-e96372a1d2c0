package com.gclife.agent.dao.commission;

import com.gclife.agent.base.model.bo.commission.CommissionPolicyBo;
import com.gclife.agent.core.jooq.tables.pojos.CommissionPolicyCoveragePo;
import com.gclife.agent.core.jooq.tables.pojos.CommissionPolicyPo;
import com.gclife.agent.form.commission.CommissionPolicyUnlockRequest;
import com.gclife.agent.model.bo.*;
import com.gclife.agent.model.request.commission.PolicyCommissionsRequest;
import com.gclife.common.dao.base.BaseDao;
import com.gclife.common.model.base.Users;

import java.util.List;

/**
 * <AUTHOR>
 * create 17-11-12
 * description:
 */
public interface CommissionPolicyExtDao extends BaseDao {

    /**
     *上月筛选符合保单
     * @param yearMonth 年月
     * @param bizDate　业绩日期
     * @param branchIds　机构集合
     * @return  List<CommissionPolicyPo>
     */
    public List<CommissionPolicyPo> loadCoincideCommissionPolicyPoList(String yearMonth,Long bizDate,List<String> branchIds);


    /**
     *上月筛选符合保单
     * @param yearMonth 年月
     * @param branchIds　机构集合
     * @return  List<CommissionPolicyPo>
     */
    public List<CommissionPolicyPo> loadCoincideCommissionPolicyPoList(String yearMonth,List<String> branchIds);

    /**
     * 获取发佣保单明细对象
     *
     * @param yearMonth 年月
     * @param policyIds 　保单IDS
     * @return  List<CommissionPolicyBo>
     */
    public List<CommissionPolicyBo> getCommissionPolicyBoList(String yearMonth, List<String> policyIds);


    /**
     * 根据ID查询发佣保单
     *
     * @param commissionPolicyId 佣金保单ID
     * @return  CommissionPolicyPo
     */
    public CommissionPolicyPo getCommissionPolicyPoByCommissionPolicyId(String commissionPolicyId);


    /**
     * 获取发佣开始流程
     *
     * @param yearMonth 年月
     * @param policyId  　保单ID
     * @return  CommissionPolicyBo
     */
    public CommissionPolicyBo getCommissionPolicyBo(String yearMonth, String policyId);



    public List<CommissionPolicyPo> getCommissionPolicyBoByPayMentId(String policyId, String policyPaymentId);

    /**
     * 根据保单ID,保单支付ID期查询算佣保单
     *
     * @param policyId        保单ID
     * @param policyPaymentId 　保单支付ID
     * @return  CommissionPolicyBo
     */
    public CommissionPolicyBo getCommissionPolicyBoByPayMentIdAndCommissionTypeCode(String policyId, String policyPaymentId,String CommissionTypeCode);

    /**
     * 根据businessId查询算佣保单
     *
     * @param businessId        保单ID
     * @return  CommissionPolicyBo
     */
    public CommissionPolicyBo getCommissionPolicyBoByBusinessId(String businessId);

    /**
     * 根据businessIds查询算佣保单
     *
     * @param businessIds        保单IDs
     * @return  CommissionPolicyBo
     */
    List<CommissionPolicyBo> getCommissionPolicyByBusinessIds(List<String> businessIds);



    /**
     * 查询可以发佣解冻的保单
     * １．过了犹豫期
     * ２．发佣状态为冻结
     *
     * @param commissionPolicyIds 　IDS
     * @param status              状态
     * @return  List<CommissionPolicyPo>
     */
    public List<CommissionPolicyBo> getCommissionPolicyPoPassHesitateListByStatus(String status, List<String> commissionPolicyIds);


    /**
     * 查询可以发佣的保单
     * １．过了犹豫期
     * ２．发佣状态为冻结
     *
     * @param branchIds 　机构IDs
     * @param status    状态
     * @return  List<CommissionPolicyPo>
     */
    public List<CommissionPolicyPo> getCommissionPolicyPoPassHesitateListByBranchIds(String status, List<String> branchIds);



    /**
     * 　佣金保单列表 分页
     *
     * @param branchIds 　机构IDs
     * @return  List<CommissionPolicyBo>
     */
    public List<CommissionPolicyBo> queryPageCommissionPolicyBoListByBranchIds(List<String> branchIds,PolicyCommissionsRequest policyCommissionsRequest);


    /**
     * 待解冻佣金保单列表 分页
     * @param policyCommissionsRequest
     * @return
     */
    public List<CommissionPolicyBo> getThawCommissionPolicyBoListByBranchIds(PolicyCommissionsRequest policyCommissionsRequest);

    /**
     * 　待解冻佣金保单列表 分页
     * １．过了犹豫期
     *
     * @param commissionPolicyIds 　机构IDs
     * @return  List<CommissionPolicyBo>
     */
    public List<CommissionPolicyCoveragePo> getCommissionPolicyCoveragePoListByCommissionIds(List<String> commissionPolicyIds);
    /**
     * 　待解冻佣金保单列表 分页
     * １．过了犹豫期
     *
     * @param commissionPolicyId 　机构IDs
     * @return  List<CommissionPolicyBo>
     */
    public List<CommissionPolicyCoverageBo> getCommissionPolicyCoverageBoListByCommissionId(String commissionPolicyId);


    /**
     * 　业管审核佣金保单列表 分页
     * １．过了犹豫期
     *
     * @param branchIds 　机构IDs
     * @return  List<CommissionPolicyBo>
     */
    public List<CommissionPolicyBo> getBusinessCommissionPolicyBoListByBranchIds(List<String> branchIds,String keyword,Integer offset,Integer pageSize);


    /**
     * 　财务审核佣金保单列表 分页
     * １．过了犹豫期
     *
     * @param branchIds 　机构IDs
     * @return  List<CommissionPolicyBo>
     */
    public List<CommissionPolicyBo> getFinanceCommissionPolicyBoListByBranchIds(List<String> branchIds,String keyword,Integer offset,Integer pageSize);

    /**
     * 查询没有过犹豫期的保单
     * １．过了犹豫期
     * ２．发佣状态为冻结
     *
     * @param commissionPolicyIds 　IDS
     * @return  List<CommissionPolicyPo>
     */
    public List<CommissionPolicyPo> getCommissionPolicyPoNoPassHesitateList(List<String> commissionPolicyIds);


    /**
     * 根据机构ID统计 机构保单佣金信息
     *
     * @param completeBranchIds　完成机构IDs
     * @return  List<BranchCommissionPolicyBo>
     */
    List<BranchCommissionPolicyBo> statisticsByBranchIdGet(String yearMonth, Long bizDate, List<String> completeBranchIds);

    /**
     * 获取保单佣金列表
     *
     * @param yearMonth 年月
     * @param bizDate　业绩日期
     * @param policyCommissionsRequest  保单请求参数
     * @return  List<PolicyCommissionsBo>
     */
    List<PolicyCommissionsBo> commissionPolicys(String yearMonth, Long bizDate, PolicyCommissionsRequest policyCommissionsRequest,List<String> branchIds);


    /**
     * 根据佣金保单Id获取佣金信息
     *
     * @param users 当前用户
     * @param commissionPolicyId    佣金保单ID
     * @return  PolicyCommissionsBo
     */
    PolicyCommissionsBo commissionPolicyByIdGet(Users users, String commissionPolicyId);

    /**
     * 获取业务员月分 佣金保单 信息
     *
     * @param yearMonth 业绩年月
     * @return
     */
    List<PolicyCommissionsBo> getCommissionPolicyListBy(String yearMonth, String agentId,String commissionStatus);

    /**
     * 获取业务员月分 佣金保单 信息
     *
     * @param yearMonth 业绩年月
     * @return
     */
    List<PolicyCommissionsBo> getCommissionPolicyListBy(String yearMonth, String agentId,List<String> commissionStatus);


    /**
     * 查询机构下的佣金保单统计信息
     *
     * @param commissionPolicyIds 佣金保单IDS
     * @return List<CommissionPolicyExceptionBo>
     */
    public List<CommissionPolicyStatisticBo> getCommissionPolicyStatisticBoList(List<String> commissionPolicyIds);

    /**
     *
     * @param yearMonth　业绩年月
     * @param bizDate   业务日期
     * @param branchIds 业务归属机构IDS
     * @return
     */
    public List<PolicyCommissionsBo> commissionPolicys(String yearMonth, Long bizDate,List<String> branchIds);



    /**
     *
     * @param commissionPolicyId　根据佣金保单主键查询佣金保单险种集合
     * @return  List<CommissionPolicyCoveragePo>
     */
    public List<CommissionPolicyCoveragePo> queryCommissionPolicyCoveragePo(String commissionPolicyId);




    /**
     * 查询可以发佣解冻的保单
     * １．过了犹豫期
     * ２．发佣状态为冻结
     *
     * @param policyId 　保单ID
     * @return  List<CommissionPolicyPo>
     */
    public List<CommissionPolicyPo> queryCommissionPolicyByPolicy(String policyId);

    /**
     * 查询未解锁的保单
     *
     * @param businessType 　业务类型
     * @param businessType 　业务状态
     * @return  List<CommissionPolicyPo>
     */
    public List<CommissionPolicyPo> queryByBusinessTypeStatus(String businessType, String businessStatus);

    /**
     * 解锁commission_policy
     *
     * @param businessIds 　业务ID集
     * @return  int
     */
    public int commissionPolicyUnlock(List<CommissionPolicyUnlockRequest> commissionPolicyUnlockRequests);

    public CommissionPolicyBo getNewContractCommissionPolicy(String policyId);

    /**
     * 导出算用保单数据
     * @param leafBranchIds
     * @param policyCommissionsRequest
     * @return
     */
    List<CommissionPolicyBo> queryExportPageCommissionPolicyBoListByBranchIds(List<String> leafBranchIds, PolicyCommissionsRequest policyCommissionsRequest);

}