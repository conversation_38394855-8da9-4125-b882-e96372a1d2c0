package com.gclife.agent.dao.law;

import com.gclife.agent.model.bo.LawBranchBo;
import com.gclife.common.model.base.Users;

import java.util.List;

/**
 * <AUTHOR>
 * create 18-1-5
 * description:
 */
public interface LawBranchExtDao {

    /***
     * 获取 基本法机构信息
     * @param lawId
     * @return
     */
    List<LawBranchBo> getLawBranch(String lawId);

    /**
     * 删除销售机构
     *
     * @param lawId
     * @param users
     * @return
     */
    int deleteLawBranch(String lawId, Users users);

    /***
     * 获取 基本法机构信息
     * @param lawId
     * @return
     */
    List<LawBranchBo> getNotThisLawBranch(String lawId);
}
