package com.gclife.agent.dao;

import com.gclife.agent.core.jooq.tables.pojos.AgentHistoryExceptionPo;
import com.gclife.agent.model.bo.AgentHistoryBo;
import com.gclife.common.dao.base.BaseDao;

import java.util.List;

/**
 * \* Created with IntelliJ IDEA.
 * \* User: deep
 * \* Date: 17-9-18
 * \* Time: 下午5:43
 * \* To change this template use File | Settings | File Templates.
 * \* Description: 架构历史异常
 *
 * <AUTHOR>
 */
public interface AgentHistoryExceptionExtDao extends BaseDao {
    /**
     * 查询机构有下有异常架构的机构集合
     *
     * @param yearMonth 代理ID
     * @param branchIds 业绩年月
     * @return AgentHistoryBo
     */
    public List<String> loadHistoryExceptionBranchIds(String yearMonth ,List<String> branchIds);

    /**
     * 查询机构有下有异常集合
     *
     * @param yearMonth 代理ID
     * @param branchIds 业绩年月
     * @return AgentHistoryBo
     */
    List<AgentHistoryExceptionPo> loadHistoryExceptionPoList(String yearMonth ,List<String> branchIds);
}