package com.gclife.agent.dao;

import com.gclife.agent.core.jooq.tables.pojos.AgentHistoryBatchPo;
import com.gclife.common.dao.base.BaseDao;

/**
 * \* Created with IntelliJ IDEA.
 * \* User: 邓凯献
 * \* Date: 17-9-18
 * \* Time: 下午5:43
 * \* To change this template use File | Settings | File Templates.
 * \* Description:批处理查询
 *
 * <AUTHOR>
 */
public interface AgentHistoryBatchExtDao extends BaseDao {

    /**
     * 根据批次ID查询批次
     * @param batchId　批次ID
     * @return  AgentHistoryBatchPo
     */
    public AgentHistoryBatchPo loadAgentHistoryBatchPo(String batchId);

}