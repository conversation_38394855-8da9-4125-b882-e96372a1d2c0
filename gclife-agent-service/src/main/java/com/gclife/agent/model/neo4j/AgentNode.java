package com.gclife.agent.model.neo4j;


//import org.neo4j.ogm.annotation.GraphId;
import org.springframework.data.neo4j.core.schema.GeneratedValue;
import org.springframework.data.neo4j.core.schema.Id;
//import org.neo4j.ogm.annotation.NodeEntity;
//import org.neo4j.ogm.annotation.Relationship;
import org.springframework.data.neo4j.core.schema.Node;
import org.springframework.data.neo4j.core.schema.Relationship;

import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

/**
 * <AUTHOR>
 *         create 17-11-5
 *         description:　业务员图节点
 */
//@NodeEntity
@Node
public class AgentNode {
    /**图主键*/
//    @GraphId
    @Id
    @GeneratedValue
    private Long agentSeq;
    /**签约日期*/
    private Long hireDate;
    /**业务年月*/
    private String bizYearMonth;
    /**业务员ID*/
    private String agentId;
    /**业务员名称*/
    private String agentName;
    /**推荐人ID*/
    private String recommendAgentId;
    /**管理人ID*/
    private String managerAgentId;
    /**育成人ID*/
    private String currentTrainAgentId;
    /**机构ID*/
    private String branchId;
    /**业务员职级编码*/
    private String agentLevelCode;
    /**业务员职级名称*/
    private String agentLevelName;
    /**业务员类型*/
    private String agentTypeCode;
    /**层级*/
    private long level;
    /**客户数未达标标识*/
    private String customerNoFinishFlag;
    /**有效人力标志*/
    private String effectiveManpowerFlag;
    /**业务员状态*/
    private String agentStatus;
    /**离职日期*/
    private Long dismissDate;

    //推荐关系
    @Relationship(type = "RECOMMEND",direction = Relationship.Direction.INCOMING)
    private Set<AgentNode> listRecommends=new HashSet<>();

    //管理关系
    @Relationship(type = "MANAGER",direction = Relationship.Direction.INCOMING)
    private Set<AgentNode> listManagers=new HashSet<>();

    //育成关系
    @Relationship(type = "TRAIN",direction = Relationship.Direction.INCOMING)
    private Set<AgentNode> listTrains=new HashSet<>();

    /**保单关系*/
    @Relationship(type = "POLICY_BELONG",direction = Relationship.Direction.INCOMING)
    private List<PolicyNode> listPolicyNodes= new ArrayList<>();

    /**保单间接奖励关系*/
    @Relationship(type = "POLICY_BONUS_BELONG",direction = Relationship.Direction.INCOMING)
    private List<PolicyBonusNode> listPolicyBonusNode = new ArrayList<>();

    /**业绩*/
    @Relationship(type = "ACHIEVEMENT",direction = Relationship.Direction.INCOMING)
    private List<AchievementNode> listAchievements=new ArrayList<>();

    /**
     * 成为推荐人
     * @return
     */
    public void beRecommends(AgentNode agentNode){
        listRecommends.add(agentNode);
    }

    /**
     * 成为管理人
     * @return
     */
    public void beManagers(AgentNode agentNode){
        listManagers.add(agentNode);
    }

    /**
     * 成为育成人
     * @return
     */
    public void beTrains(AgentNode agentNode){
        listTrains.add(agentNode);
    }

    /**
     * 成为保单
     * @return
     */
    public void bePolicy(PolicyNode policyNode){
        listPolicyNodes.add(policyNode);
    }

    /**
     * 成为保单
     * @return
     */
    public void bePolicyList(List<PolicyNode> policyNodes){
        listPolicyNodes.addAll(policyNodes);
    }

    /**
     * 成为保单间接奖励
     * @return
     */
    public void bePolicyBonusList(List<PolicyBonusNode> policyBonusNodes){
        listPolicyBonusNode.addAll(policyBonusNodes);
    }


    /**
     * 成为业绩
     * @return
     */
    public void beAchievement(AchievementNode achievementNode){
        listAchievements.add(achievementNode);
    }




    public String getBizYearMonth() {
        return bizYearMonth;
    }

    public void setBizYearMonth(String bizYearMonth) {
        this.bizYearMonth = bizYearMonth;
    }

    public Long getAgentSeq() {
        return agentSeq;
    }

    public void setAgentSeq(Long agentSeq) {
        this.agentSeq = agentSeq;
    }

    public Set<AgentNode> getListRecommends() {
        return listRecommends;
    }

    public void setListRecommends(Set<AgentNode> listRecommends) {
        this.listRecommends = listRecommends;
    }

    public Set<AgentNode> getListManagers() {
        return listManagers;
    }

    public void setListManagers(Set<AgentNode> listManagers) {
        this.listManagers = listManagers;
    }


    public String getAgentId() {
        return agentId;
    }

    public void setAgentId(String agentId) {
        this.agentId = agentId;
    }

    public String getAgentName() {
        return agentName;
    }

    public void setAgentName(String agentName) {
        this.agentName = agentName;
    }

    public String getRecommendAgentId() {
        return recommendAgentId;
    }

    public void setRecommendAgentId(String recommendAgentId) {
        this.recommendAgentId = recommendAgentId;
    }

    public String getManagerAgentId() {
        return managerAgentId;
    }

    public void setManagerAgentId(String managerAgentId) {
        this.managerAgentId = managerAgentId;
    }

    public String getCurrentTrainAgentId() {
        return currentTrainAgentId;
    }

    public void setCurrentTrainAgentId(String currentTrainAgentId) {
        this.currentTrainAgentId = currentTrainAgentId;
    }

    public String getBranchId() {
        return branchId;
    }

    public void setBranchId(String branchId) {
        this.branchId = branchId;
    }


    public Set<AgentNode> getListTrains() {
        return listTrains;
    }

    public void setListTrains(Set<AgentNode> listTrains) {
        this.listTrains = listTrains;
    }

    public List<PolicyNode> getListPolicyNodes() {
        return listPolicyNodes;
    }

    public void setListPolicyNodes(List<PolicyNode> listPolicyNodes) {
        this.listPolicyNodes = listPolicyNodes;
    }

    public List<PolicyBonusNode> getListPolicyBonusNode() {
        return listPolicyBonusNode;
    }

    public void setListPolicyBonusNode(List<PolicyBonusNode> listPolicyBonusNode) {
        this.listPolicyBonusNode = listPolicyBonusNode;
    }

    public String getAgentLevelCode() {
        return agentLevelCode;
    }

    public void setAgentLevelCode(String agentLevelCode) {
        this.agentLevelCode = agentLevelCode;
    }

    public String getAgentLevelName() {
        return agentLevelName;
    }

    public void setAgentLevelName(String agentLevelName) {
        this.agentLevelName = agentLevelName;
    }

    public long getLevel() {
        return level;
    }

    public void setLevel(long level) {
        this.level = level;
    }

    public List<AchievementNode> getListAchievements() {
        return listAchievements;
    }

    public void setListAchievements(List<AchievementNode> listAchievements) {
        this.listAchievements = listAchievements;
    }

    public Long getHireDate() {
        return hireDate;
    }

    public void setHireDate(Long hireDate) {
        this.hireDate = hireDate;
    }

    public String getAgentTypeCode() {
        return agentTypeCode;
    }

    public void setAgentTypeCode(String agentTypeCode) {
        this.agentTypeCode = agentTypeCode;
    }

    public String getCustomerNoFinishFlag() {
        return customerNoFinishFlag;
    }

    public void setCustomerNoFinishFlag(String customerNoFinishFlag) {
        this.customerNoFinishFlag = customerNoFinishFlag;
    }

    public String getEffectiveManpowerFlag() {
        return effectiveManpowerFlag;
    }

    public void setEffectiveManpowerFlag(String effectiveManpowerFlag) {
        this.effectiveManpowerFlag = effectiveManpowerFlag;
    }

    public String getAgentStatus() {
        return agentStatus;
    }

    public void setAgentStatus(String agentStatus) {
        this.agentStatus = agentStatus;
    }

    public Long getDismissDate() {
        return dismissDate;
    }

    public void setDismissDate(Long dismissDate) {
        this.dismissDate = dismissDate;
    }
}