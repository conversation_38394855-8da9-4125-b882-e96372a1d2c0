package com.gclife.agent.model.request.sign;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @version v1.0
 * Description:
 * @date 2022-04-15
 */
@Data
public class ErrorExportRequest {
    @ApiModelProperty(example = "所属机构ID")
    private String branchId;
    @ApiModelProperty(example = "异常日期开始日期")
    private String errorDateStart;
    @ApiModelProperty(example = "异常日期截止日期")
    private String errorDateEnd;
    @ApiModelProperty(example = "待定期间(LE_30_DAY:≤30;LE_60_DAY:≤60;GT_60_DAY:＞60)")
    private String pendingPeriod;
}
