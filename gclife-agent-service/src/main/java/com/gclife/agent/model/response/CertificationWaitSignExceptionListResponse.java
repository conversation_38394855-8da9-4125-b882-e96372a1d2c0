package com.gclife.agent.model.response;

import com.gclife.common.annotation.Internation;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * create 17-12-23
 * description:
 */
@Data
public class CertificationWaitSignExceptionListResponse {
    @ApiModelProperty(value = "机构ID", example = "100100S1000000")
    private String branchId;
    @ApiModelProperty(value = "所属机构", example = "深圳分公司")
    private String branchName;
    @ApiModelProperty(value = "推荐人姓名", example = "罗斌")
    private String recommendName;
    @ApiModelProperty(value = "推荐人手机号", example = "18898617356")
    private String recommendMobile;
    @ApiModelProperty(value = "推荐人职级", example = "初级业务员")
    private String recommendLevelCode;
    @ApiModelProperty(value = "推荐人职级", example = "初级业务员")
    @Internation(codeType = "AGENT_LEVEL", filed = "recommendLevelCode")
    private String recommendLevel;
    @ApiModelProperty(value = "业务员姓名", example = "张三")
    private String agentName;
    @ApiModelProperty(value = "业务员ID", example = "1")
    private String agentId;
    @ApiModelProperty(value = "业务员编码", example = "200236")
    private String agentCode;
    @ApiModelProperty(value = "业务员职级", example = "团队经理")
    private String agentLevelCode;
    @ApiModelProperty(value = "业务员职级", example = "团队经理")
    @Internation(codeType = "AGENT_LEVEL", filed = "agentLevelCode")
    private String agentLevel;
    @ApiModelProperty(value = "业务员手机号", example = "18888888888")
    private String agentMobile;
    @ApiModelProperty(value = "业务员注册日期", example = "1513734615000")
    private Long registerDate;
    @ApiModelProperty(value = "业务员实名认证日期", example = "1513734615000")
    private Long identityDate;
    @ApiModelProperty(value = "业务员签约日期", example = "1513734615000")
    private Long signDate;
    @ApiModelProperty(example = "异常日期")
    private Long errorDate;
    @ApiModelProperty(value = "等待时长", example = "856590000")
    private Long waitTime;
    @ApiModelProperty(value = "异常原因", example = "照片模糊")
    private String exceptionReason;
}
