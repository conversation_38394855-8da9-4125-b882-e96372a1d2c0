package com.gclife.agent.model.bo;

import com.gclife.agent.core.jooq.tables.pojos.ProcessManagerPo;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * create 17-12-2
 * description:
 */
public class ProcessManagerBo extends ProcessManagerPo {

    /**
     * 佣金保单笔数
     */
    Integer commissionPolicyNum;
    /**
     * 首拥保单笔数
     */
    Integer firstCommissionPolicyNum;
    /**
     * 无用 FYP 佣金合计
     */
    BigDecimal fypCommissionSum;
    /**
     * 发佣总人数
     */
    Integer commissionAgentNum;
    /**
     * 佣金合计合计
     */
    BigDecimal commissionSum;

    BigDecimal fyp;

    BigDecimal cyp;

    BigDecimal fyc;

    BigDecimal cyc;

    BigDecimal first;

    BigDecimal second;
    /**
     * 续佣保单笔数
     */
    Integer rycNum;

    BigDecimal valuePremiumSum;

    public Integer getCommissionPolicyNum() {
        return commissionPolicyNum;
    }

    public void setCommissionPolicyNum(Integer commissionPolicyNum) {
        this.commissionPolicyNum = commissionPolicyNum;
    }

    public Integer getFirstCommissionPolicyNum() {
        return firstCommissionPolicyNum;
    }

    public void setFirstCommissionPolicyNum(Integer firstCommissionPolicyNum) {
        this.firstCommissionPolicyNum = firstCommissionPolicyNum;
    }

    public BigDecimal getFypCommissionSum() {
        return fypCommissionSum;
    }

    public void setFypCommissionSum(BigDecimal fypCommissionSum) {
        this.fypCommissionSum = fypCommissionSum;
    }

    public BigDecimal getCommissionSum() {
        return commissionSum;
    }

    public void setCommissionSum(BigDecimal commissionSum) {
        this.commissionSum = commissionSum;
    }

    public BigDecimal getFyp() {
        return fyp;
    }

    public void setFyp(BigDecimal fyp) {
        this.fyp = fyp;
    }

    public BigDecimal getFyc() {
        return fyc;
    }

    public void setFyc(BigDecimal fyc) {
        this.fyc = fyc;
    }

    public BigDecimal getCyc() {
        return cyc;
    }

    public void setCyc(BigDecimal cyc) {
        this.cyc = cyc;
    }

    public BigDecimal getCyp() {
        return cyp;
    }

    public void setCyp(BigDecimal cyp) {
        this.cyp = cyp;
    }

    public BigDecimal getValuePremiumSum() {
        return valuePremiumSum;
    }

    public void setValuePremiumSum(BigDecimal valuePremiumSum) {
        this.valuePremiumSum = valuePremiumSum;
    }

    public Integer getRycNum() {
        return rycNum;
    }

    public void setRycNum(Integer rycNum) {
        this.rycNum = rycNum;
    }

    public BigDecimal getFirst() {
        return first;
    }

    public void setFirst(BigDecimal first) {
        this.first = first;
    }

    public BigDecimal getSecond() {
        return second;
    }

    public void setSecond(BigDecimal second) {
        this.second = second;
    }

    public Integer getCommissionAgentNum() {
        return commissionAgentNum;
    }

    public void setCommissionAgentNum(Integer commissionAgentNum) {
        this.commissionAgentNum = commissionAgentNum;
    }
}