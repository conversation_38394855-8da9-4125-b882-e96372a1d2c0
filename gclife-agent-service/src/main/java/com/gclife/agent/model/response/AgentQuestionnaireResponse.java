package com.gclife.agent.model.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * create 2022/3/11 10:34
 * description:
 */
@Data
public class AgentQuestionnaireResponse {
    @ApiModelProperty(example = "问题对象(PART_ONE:第一部分,PART_TWO:第二部分)")
    private String questionObject;

    /**
     * 问题答案列表
     */
    private List<AgentQuestionnaireAnswerResponse> listAnswers;

    public AgentQuestionnaireResponse(String questionObject, List<AgentQuestionnaireAnswerResponse> listAnswers) {
        this.questionObject = questionObject;
        this.listAnswers = listAnswers;
    }
}
