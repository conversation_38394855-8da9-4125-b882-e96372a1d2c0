package com.gclife.agent.model.response;

import com.gclife.common.annotation.Internation;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * create 2022/3/31 14:43
 * description:我的增员
 */
@Data
public class AgentIncreaseStaffResponse {
    @ApiModelProperty(example = "业务员ID", value = "业务员ID")
    private String agentId;
    @ApiModelProperty(example = "业务员头像附件ID", value = "业务员头像附件ID")
    private String headAttachId;
    @ApiModelProperty(example = "业务员头像附件URL", value = "业务员头像附件URL")
    private String headAttachURL;
    @ApiModelProperty(example = "业务员姓名", value = "业务员姓名")
    private String agentName;
    @ApiModelProperty(example = "签约审核状态", value = "签约审核状态")
    private String auditStatus;
    @ApiModelProperty(example = "签约审核状态", value = "签约审核状态")
    @Internation(filed = "auditStatus", codeType = "CERTIFICATION_STATUS")
    private String auditStatusName;
    @ApiModelProperty(example = "签约状态", value = "签约状态")
    private String signStatus;
    @ApiModelProperty(example = "签约状态", value = "签约状态")
    @Internation(filed = "signStatus", codeType = "SIGN_STATUS")
    private String signStatusName;
    @ApiModelProperty(example = "等待时长", value = "等待时长")
    private long waitTime = 0;
}
