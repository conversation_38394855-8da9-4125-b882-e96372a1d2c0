package com.gclife.agent.model.response.agent.base;

import com.gclife.common.model.BaseResponse;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

/**
 * \* Created with IntelliJ IDEA.
 * \* User: deep
 * \* Date: 17-9-18
 * \* Time: 下午2:16
 * \* To change this template use File | Settings | File Templates.
 * \* Description: 业务员投保带
 *
 * <AUTHOR>
 */
@ApiModel(value = "queryAgent", description = "业务员")
public class AgentListResponse extends BaseResponse {

    @ApiModelProperty(example = "业务员ID")
    private String agentId;
    @ApiModelProperty(example = "用户ID")
    private String userId;
    @ApiModelProperty(example = "业务员编码")
    private String agentCode;
    @ApiModelProperty(example = "业务员名称")
    private String agentName;
    @ApiModelProperty(example = "业务员在职状态")
    private String agentStatus;
    @ApiModelProperty(example = "机构ID")
    private String branchId;
    @ApiModelProperty(example = "业务员来源")
    private String agentSource;
    @ApiModelProperty(example = "支付限制")
    private String payModeLimit;
    @ApiModelProperty(example = "业务员推荐人")
    private String recommendAgentId;
    @ApiModelProperty(example = "推荐日期")
    private Long   recommendDate;
    @ApiModelProperty(example = "管理人")
    private String managerAgentId;
    @ApiModelProperty(example = "育成人")
    private String currentTrainAgentId;
    @ApiModelProperty(example = "育成时间")
    private Long   currentTrainDate;
    @ApiModelProperty(example = "创建时间")
    private Long   createdDate;
    @ApiModelProperty(value = "主键", example = "主键")
    private String agentDetailId;
    @ApiModelProperty(value = "头像", example = "主键")
    private String headAttachId;
    @ApiModelProperty(value = "币种", example = "币种")
    private String currencyCode;
    @ApiModelProperty(value = "区域编码", example = "区域编码")
    private String areaCode;
    @ApiModelProperty(value = "年份", example = "年份")
    private Long   practitionerYear;
    @ApiModelProperty(value = "手机号码", example = "手机号码")
    private String mobile;
    @ApiModelProperty(value = "二维码", example = "二维码")
    private String qrCodeAttachId;
    @ApiModelProperty(value = "性别", example = "男")
    private String sex;
    @ApiModelProperty(value = "出生日期", example = "762105600000")
    private Long   birthday;
    @ApiModelProperty(value = "邮箱", example = "762105600000")
    private String email;
    @ApiModelProperty(value = "民族", example = "汉")
    private String nation;
    @ApiModelProperty(value = "证件地址", example = "湖南省邵东县XXX")
    private String idAddress;
    @ApiModelProperty(value = "证件有效期限", example = "1827158400000")
    private Long   idExpDate;
    @ApiModelProperty(example = "用户实名认证ID")
    private String identityId;
    @ApiModelProperty(example = "证件姓名")
    private String name;
    @ApiModelProperty(example = "证件类型")
    private String idTypeCode;
    @ApiModelProperty(example = "证件号码")
    private String idNo;
    @ApiModelProperty(example = "证件正面附件(对应附件系统的附件ID)")
    private String idFrontAttachId;
    @ApiModelProperty(example = "证件反面附件(对应附件系统的附件ID)")
    private String idBackAttachId;
    @ApiModelProperty(example = "审核状态")
    private String auditStatus;
    @ApiModelProperty(example = "审核消息(失败时，具体的原因)")
    private String auditMessage;
    @ApiModelProperty(example = "审核人")
    private String auditUserId;
    @ApiModelProperty(example = "审核时间")
    private Long   auditDate;
    @ApiModelProperty(example = "审核失败编码（国际化）")
    private String auditFailedCode;
    @ApiModelProperty(example = "主键")
    private String agentSignedId;
    @ApiModelProperty(example = "承诺书签署日期")
    private Long   commitmentSignDate;
    @ApiModelProperty(example = "签约日期")
    private Long   hireDate;
    @ApiModelProperty(example = "解约日期")
    private Long   dismissDate;
    @ApiModelProperty(example = "再次签约日期")
    private Long   resumeDate;
    @ApiModelProperty(example = "聘用次数")
    private String hiretimes;
    @ApiModelProperty(example = "原工作单位名称")
    private String preCompanyName;
    @ApiModelProperty(example = "同业工作年限")
    private String agentYear;
    @ApiModelProperty(example = "合同档案编号")
    private String contractFileno;
    @ApiModelProperty(example = "合同签署日期")
    private Long   contractSignDate;
    @ApiModelProperty(example = "合同确认日期")
    private Long   contractConfirmDate;
    @ApiModelProperty(example = "保证金收缴日期")
    private Long   depositrecvDate;
    @ApiModelProperty(example = "保证金金额")
    private String depositAmount;
    @ApiModelProperty(example = "保证金返还日期")
    private Long   depositretDate;
    @ApiModelProperty(example = "保证金返还金额")
    private String depositretAmount;
    @ApiModelProperty(example = "保证金收据号")
    private String depositNo;
    @ApiModelProperty(example = "合同ID")
    private String contractFileId;
    @ApiModelProperty(example = "IN_SIGN:签约中，SIGN_ERROR:签约异常　SIGN_COMPLETE:签约完成")
    private String signStatus;
    @ApiModelProperty(example = "ID")
    private String agentLevelId;
    @ApiModelProperty(example = "编码")
    private String agentLevelCode;
    @ApiModelProperty(example = "编码名称")
    private String agentLevelCodeName;


    public String getAgentId() {
        return agentId;
    }

    public void setAgentId(String agentId) {
        this.agentId = agentId;
    }

    public String getUserId() {
        return userId;
    }

    public void setUserId(String userId) {
        this.userId = userId;
    }

    public String getAgentCode() {
        return agentCode;
    }

    public void setAgentCode(String agentCode) {
        this.agentCode = agentCode;
    }

    public String getAgentName() {
        return agentName;
    }

    public void setAgentName(String agentName) {
        this.agentName = agentName;
    }

    public String getAgentStatus() {
        return agentStatus;
    }

    public void setAgentStatus(String agentStatus) {
        this.agentStatus = agentStatus;
    }

    public String getBranchId() {
        return branchId;
    }

    public void setBranchId(String branchId) {
        this.branchId = branchId;
    }

    public String getAgentSource() {
        return agentSource;
    }

    public void setAgentSource(String agentSource) {
        this.agentSource = agentSource;
    }

    public String getPayModeLimit() {
        return payModeLimit;
    }

    public void setPayModeLimit(String payModeLimit) {
        this.payModeLimit = payModeLimit;
    }

    public String getRecommendAgentId() {
        return recommendAgentId;
    }

    public void setRecommendAgentId(String recommendAgentId) {
        this.recommendAgentId = recommendAgentId;
    }

    public Long getRecommendDate() {
        return recommendDate;
    }

    public void setRecommendDate(Long recommendDate) {
        this.recommendDate = recommendDate;
    }

    public String getManagerAgentId() {
        return managerAgentId;
    }

    public void setManagerAgentId(String managerAgentId) {
        this.managerAgentId = managerAgentId;
    }

    public String getCurrentTrainAgentId() {
        return currentTrainAgentId;
    }

    public void setCurrentTrainAgentId(String currentTrainAgentId) {
        this.currentTrainAgentId = currentTrainAgentId;
    }

    public Long getCurrentTrainDate() {
        return currentTrainDate;
    }

    public void setCurrentTrainDate(Long currentTrainDate) {
        this.currentTrainDate = currentTrainDate;
    }

    public Long getCreatedDate() {
        return createdDate;
    }

    public void setCreatedDate(Long createdDate) {
        this.createdDate = createdDate;
    }

    public String getAgentDetailId() {
        return agentDetailId;
    }

    public void setAgentDetailId(String agentDetailId) {
        this.agentDetailId = agentDetailId;
    }

    public String getHeadAttachId() {
        return headAttachId;
    }

    public void setHeadAttachId(String headAttachId) {
        this.headAttachId = headAttachId;
    }

    public String getCurrencyCode() {
        return currencyCode;
    }

    public void setCurrencyCode(String currencyCode) {
        this.currencyCode = currencyCode;
    }

    public String getAreaCode() {
        return areaCode;
    }

    public void setAreaCode(String areaCode) {
        this.areaCode = areaCode;
    }

    public Long getPractitionerYear() {
        return practitionerYear;
    }

    public void setPractitionerYear(Long practitionerYear) {
        this.practitionerYear = practitionerYear;
    }

    public String getMobile() {
        return mobile;
    }

    public void setMobile(String mobile) {
        this.mobile = mobile;
    }

    public String getQrCodeAttachId() {
        return qrCodeAttachId;
    }

    public void setQrCodeAttachId(String qrCodeAttachId) {
        this.qrCodeAttachId = qrCodeAttachId;
    }

    public String getSex() {
        return sex;
    }

    public void setSex(String sex) {
        this.sex = sex;
    }

    public Long getBirthday() {
        return birthday;
    }

    public void setBirthday(Long birthday) {
        this.birthday = birthday;
    }

    public String getEmail() {
        return email;
    }

    public void setEmail(String email) {
        this.email = email;
    }

    public String getNation() {
        return nation;
    }

    public void setNation(String nation) {
        this.nation = nation;
    }

    public String getIdAddress() {
        return idAddress;
    }

    public void setIdAddress(String idAddress) {
        this.idAddress = idAddress;
    }

    public Long getIdExpDate() {
        return idExpDate;
    }

    public void setIdExpDate(Long idExpDate) {
        this.idExpDate = idExpDate;
    }

    public String getIdentityId() {
        return identityId;
    }

    public void setIdentityId(String identityId) {
        this.identityId = identityId;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getIdTypeCode() {
        return idTypeCode;
    }

    public void setIdTypeCode(String idTypeCode) {
        this.idTypeCode = idTypeCode;
    }

    public String getIdNo() {
        return idNo;
    }

    public void setIdNo(String idNo) {
        this.idNo = idNo;
    }

    public String getIdFrontAttachId() {
        return idFrontAttachId;
    }

    public void setIdFrontAttachId(String idFrontAttachId) {
        this.idFrontAttachId = idFrontAttachId;
    }

    public String getIdBackAttachId() {
        return idBackAttachId;
    }

    public void setIdBackAttachId(String idBackAttachId) {
        this.idBackAttachId = idBackAttachId;
    }

    public String getAuditStatus() {
        return auditStatus;
    }

    public void setAuditStatus(String auditStatus) {
        this.auditStatus = auditStatus;
    }

    public String getAuditMessage() {
        return auditMessage;
    }

    public void setAuditMessage(String auditMessage) {
        this.auditMessage = auditMessage;
    }

    public String getAuditUserId() {
        return auditUserId;
    }

    public void setAuditUserId(String auditUserId) {
        this.auditUserId = auditUserId;
    }

    public Long getAuditDate() {
        return auditDate;
    }

    public void setAuditDate(Long auditDate) {
        this.auditDate = auditDate;
    }

    public String getAuditFailedCode() {
        return auditFailedCode;
    }

    public void setAuditFailedCode(String auditFailedCode) {
        this.auditFailedCode = auditFailedCode;
    }

    public String getAgentSignedId() {
        return agentSignedId;
    }

    public void setAgentSignedId(String agentSignedId) {
        this.agentSignedId = agentSignedId;
    }

    public Long getCommitmentSignDate() {
        return commitmentSignDate;
    }

    public void setCommitmentSignDate(Long commitmentSignDate) {
        this.commitmentSignDate = commitmentSignDate;
    }

    public Long getHireDate() {
        return hireDate;
    }

    public void setHireDate(Long hireDate) {
        this.hireDate = hireDate;
    }

    public Long getDismissDate() {
        return dismissDate;
    }

    public void setDismissDate(Long dismissDate) {
        this.dismissDate = dismissDate;
    }

    public Long getResumeDate() {
        return resumeDate;
    }

    public void setResumeDate(Long resumeDate) {
        this.resumeDate = resumeDate;
    }

    public String getHiretimes() {
        return hiretimes;
    }

    public void setHiretimes(String hiretimes) {
        this.hiretimes = hiretimes;
    }

    public String getPreCompanyName() {
        return preCompanyName;
    }

    public void setPreCompanyName(String preCompanyName) {
        this.preCompanyName = preCompanyName;
    }

    public String getAgentYear() {
        return agentYear;
    }

    public void setAgentYear(String agentYear) {
        this.agentYear = agentYear;
    }

    public String getContractFileno() {
        return contractFileno;
    }

    public void setContractFileno(String contractFileno) {
        this.contractFileno = contractFileno;
    }

    public Long getContractSignDate() {
        return contractSignDate;
    }

    public void setContractSignDate(Long contractSignDate) {
        this.contractSignDate = contractSignDate;
    }

    public Long getContractConfirmDate() {
        return contractConfirmDate;
    }

    public void setContractConfirmDate(Long contractConfirmDate) {
        this.contractConfirmDate = contractConfirmDate;
    }

    public Long getDepositrecvDate() {
        return depositrecvDate;
    }

    public void setDepositrecvDate(Long depositrecvDate) {
        this.depositrecvDate = depositrecvDate;
    }

    public String getDepositAmount() {
        return depositAmount;
    }

    public void setDepositAmount(String depositAmount) {
        this.depositAmount = depositAmount;
    }

    public Long getDepositretDate() {
        return depositretDate;
    }

    public void setDepositretDate(Long depositretDate) {
        this.depositretDate = depositretDate;
    }

    public String getDepositretAmount() {
        return depositretAmount;
    }

    public void setDepositretAmount(String depositretAmount) {
        this.depositretAmount = depositretAmount;
    }

    public String getDepositNo() {
        return depositNo;
    }

    public void setDepositNo(String depositNo) {
        this.depositNo = depositNo;
    }

    public String getContractFileId() {
        return contractFileId;
    }

    public void setContractFileId(String contractFileId) {
        this.contractFileId = contractFileId;
    }

    public String getSignStatus() {
        return signStatus;
    }

    public void setSignStatus(String signStatus) {
        this.signStatus = signStatus;
    }

    public String getAgentLevelId() {
        return agentLevelId;
    }

    public void setAgentLevelId(String agentLevelId) {
        this.agentLevelId = agentLevelId;
    }

    public String getAgentLevelCode() {
        return agentLevelCode;
    }

    public void setAgentLevelCode(String agentLevelCode) {
        this.agentLevelCode = agentLevelCode;
    }

    public String getAgentLevelCodeName() {
        return agentLevelCodeName;
    }

    public void setAgentLevelCodeName(String agentLevelCodeName) {
        this.agentLevelCodeName = agentLevelCodeName;
    }
}