package com.gclife.client.controller;

import com.gclife.client.api.ClientIndexApi;
import com.gclife.client.model.response.AppPopupResponse;
import com.gclife.client.model.response.AppSearchResponse;
import com.gclife.client.model.response.AppSearchResultResponse;
import com.gclife.client.model.response.IndexResponse;
import com.gclife.client.vo.ResultObject;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiResponse;
import io.swagger.annotations.ApiResponses;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @date 5/19/2022
 */
@Api(tags = "首页区块", description = "首页区块")
@RestController
@RequestMapping(value = "v1/index")
public class IndexController {

    @Autowired
    private ClientIndexApi clientIndexApi;

    @ApiOperation(value = "获取首页区块", notes = "获取首页区块")
    @ApiResponses({
            @ApiResponse(code = 200, message = "请求成功")
    })
    @GetMapping("blocks")
    public ResultObject<IndexResponse> getIndexBlock() {
        return clientIndexApi.getIndexBlock();
    }

    @ApiOperation(value = "app首页弹窗标识", notes = "app首页弹窗标识")
    @ApiResponses({
            @ApiResponse(code = 200, message = "请求成功")
    })
    @GetMapping(value = "popup")
    public ResultObject<AppPopupResponse> getIndexAppPopup() {
        return clientIndexApi.getIndexAppPopup();
    }

    @ApiOperation(value = "app首页搜索记录", notes = "app首页搜索记录")
    @ApiResponses({
            @ApiResponse(code = 200, message = "请求成功")
    })
    @GetMapping(value = "search/record")
    public ResultObject<AppSearchResponse> getIndexAppSearchRecord() {
        return clientIndexApi.getIndexAppSearchRecord();
    }

    @ApiOperation(value = "app首页搜索", notes = "app首页搜索")
    @ApiResponses({
            @ApiResponse(code = 200, message = "请求成功")
    })
    @GetMapping(value = "search")
    public ResultObject<AppSearchResultResponse> getIndexAppSearch(String keyword) {
        if (keyword == null || keyword.equals("")) {
            return ResultObject.success();
        }
        return clientIndexApi.getIndexAppSearch(keyword);
    }

    @ApiOperation(value = "删除搜索记录", notes = "删除搜索记录")
    @ApiResponses({
            @ApiResponse(code = 200, message = "请求成功")
    })
    @DeleteMapping(value = "search/delete")
    public ResultObject<Void> deleteIndexAppSearch() {
        return clientIndexApi.deleteIndexAppSearch();
    }
}
