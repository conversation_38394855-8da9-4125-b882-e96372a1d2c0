package com.gclife.platform.api;

import com.gclife.common.model.ResultObject;
import com.gclife.platform.form.SyscodeForm;
import com.gclife.platform.vo.SyscodeResponse;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiResponse;
import io.swagger.annotations.ApiResponses;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;
import java.util.Map;

/**
 * @Auther: chenjinrong
 * @Date: 19-4-1 17:43
 * @Description:
 */
@FeignClient(value = "gclife-platform-service")
public interface InternationalBaseApi {


    @ApiOperation(value = "查询国际化类型集合", notes = "查询国际化类型集合")
    @ApiResponses({
            @ApiResponse(code = 200, message = "请求成功")
    })
    @GetMapping(value = "v1/base/international/list")
    public ResultObject<List<SyscodeResponse>> queryInternational(@RequestParam("codeType") String codeType, @RequestParam(value = "language", required = false) String language);


    @ApiOperation(value = "查询国际化对象", notes = "查询国际化对象")
    @ApiResponses({
            @ApiResponse(code = 200, message = "请求成功")
    })
    @GetMapping(value = "v1/base/international")
    public ResultObject<SyscodeResponse> queryOneInternational(@RequestParam("codeType") String codeType, @RequestParam("codeKey") String codeKey, @RequestParam(value = "language", required = false) String language);


    @ApiOperation(value = "查询国际化对象", notes = "查询国际化对象")
    @ApiResponses({
            @ApiResponse(code = 200, message = "请求成功")
    })
    @PostMapping(value = "v1/base/international/list")
    public ResultObject<List<SyscodeResponse>> queryInternationalByCodeKeys(@RequestBody SyscodeForm syscodeReqFc);

    @ApiOperation(value = "批量查询国际化对象", notes = "批量查询国际化对象")
    @ApiResponses({
            @ApiResponse(code = 200, message = "请求成功")
    })
    @PostMapping(value = "v1/base/international/batch")
    ResultObject<Map<String, List<SyscodeResponse>>> queryBatchInternationalByCodeKeys(@RequestParam(value = "language", required = false) String language, @RequestBody List<String> codeTypes);

}
