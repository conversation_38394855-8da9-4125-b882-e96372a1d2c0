package com.gclife.platform.dao;


import com.gclife.common.dao.base.BaseDao;
import com.gclife.platform.core.jooq.tables.pojos.UserWeixinPo;
import com.gclife.platform.core.jooq.tables.pojos.UserWeixinRelationPo;
import com.gclife.platform.model.bo.UserLoginLogBo;

import java.util.List;
//

/**
 * \* Created with IntelliJ IDEA.
 * \* User: deep
 * \* Date: 17-9-10.
 * \* Time: 下午1:56
 * \* To change this template use File | Settings | File Templates.
 * \* Description:
 *
 * <AUTHOR>
 */
public interface UsersWeixinExtDao extends BaseDao {

    /**
     * 查询用户微信关联关系
     *
     * @param userId userId
     * @return UserWeixinRelationPo
     */
    public UserWeixinRelationPo loadUserWeixinRelationPoByOpenId(String userId);


    /**
     * 查询用户微信信息
     *
     * @param userId 用户ID
     * @return UserWeixinPo
     */
    public UserWeixinPo loadUserWeixinPo(String userId);


    /**
     * 查询用户微信信息
     *
     * @param userId 用户ID
     * @return UserWeixinPo
     */
    public UserWeixinRelationPo loadUserWeixinRelationPoByUserId(String userId);

    /**
     * 查询第三方开放平台的用户信息
     * @param unionId
     * @param channelId 渠道id
     * @return
     */
    public List<UserWeixinRelationPo> findUserWeixinRelationPo(String unionId, String channelId);
}