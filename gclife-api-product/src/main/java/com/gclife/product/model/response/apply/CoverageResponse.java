package com.gclife.product.model.response.apply;

import com.gclife.common.annotation.Internation;
import com.gclife.common.model.config.TerminologyConfigEnum;
import com.gclife.product.model.response.duty.DutyResponse;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

/**
 * @Description: 投保单险种
 * <AUTHOR>
 * @date 2017-11-23
 * @version V1.0
 */
@Data
public class CoverageResponse {

    @ApiModelProperty(example = "产品主键")
    private String productId;
    @ApiModelProperty(example = "产品名称")
    @Internation(filed = "productId", codeType = "PRODUCT_ID")
    private String productName;
    @ApiModelProperty(example = "产品编码")
    private String productCode;
    @ApiModelProperty(example = "主附险标记")
    private String primaryFlag;
    @ApiModelProperty(example = "期缴标准保费")
    private BigDecimal periodStandardPremium;
    @ApiModelProperty(example = "年缴标准保费")
    private BigDecimal yearStandardPremium;
    @ApiModelProperty(example = "年缴弱体加费")
    private BigDecimal yearWeakAddPremium;
    @ApiModelProperty(example = "年缴职业加费")
    private BigDecimal yearCareerAddPremium;
    @ApiModelProperty(example = "期缴职业加费")
    private BigDecimal periodCareerAddPremium;
    @ApiModelProperty(example = "期缴弱体加费")
    private BigDecimal periodWeakAddPremium;
    @ApiModelProperty(example = "加费期限")
    private Long addPremiumPeriod;
    @ApiModelProperty(example = "加费开始日期")
    private Long addPremiumStartDate;

    @ApiModelProperty(example = "保费(期缴保费单份)")
    private BigDecimal premium;
    @ApiModelProperty(example = "原始保费(premium*份数)")
    private BigDecimal originalPremium;
    @ApiModelProperty(example = "总保费(original_premium+加费)")
    private BigDecimal totalPremium;
    @ApiModelProperty(example = "实缴保费")
    private BigDecimal actualPremium;

    @ApiModelProperty(example = "保费折扣")
    private BigDecimal premiumDiscount;
    @ApiModelProperty(example = "缴费周期(缴别)(年缴、月缴)")
    private String premiumFrequency;
    @ApiModelProperty(example = "缴费期限类型（年、周岁、终身）")
    private String premiumPeriodUnit;
    @ApiModelProperty(example = "缴费期限(具体数值，如缴至60岁)")
    private long premiumPeriod;
    @ApiModelProperty(example = "保障年期类型（年、周岁、终身）")
    private String coveragePeriodUnit;
    @ApiModelProperty(example = "保障年期(具体数值，如缴至60岁)")
    private Long coveragePeriod;
    @ApiModelProperty(example = "保障开始日期")
    private String coveragePeriodStartDate;
    @ApiModelProperty(example = "保障结束日期")
    private String coveragePeriodEndDate;
    @ApiModelProperty(example = "红利领取方式")
    private String dividendReceiveMode;
    @ApiModelProperty(example = "份数")
    private long mult;
    @ApiModelProperty(example = "养老年金开始领取时间类型")
    private String pensionReceiveDateUnit;
    @ApiModelProperty(example = "养老年金开始领取时间(具体数值)")
    private Long pensionReceiveDate;
    @ApiModelProperty(example = "红利领取周期(年、月、其它)")
    private String dividendReceiveFrequency;
    @ApiModelProperty(example = "红利领取周期值(10,20)")
    private String dividendReceivePeriod;
    @ApiModelProperty(example = "养老金给付方式类型")
    private String pensionReceiveMode;
    @ApiModelProperty(example = "养老年金保证给付年限(具体数值:20,30,40)")
    private long pensionReceivePeriod;
    @ApiModelProperty(example = "给付频度(一次性给付方式（如为趸缴保险费，不得选择一次性给付方式)")
    private String pensionReceiveFrequency;
    @ApiModelProperty(example = "观察期(等待期)")
    private String waitPeriod;
    @ApiModelProperty(example = "观察期截止日期")
    private Long waitPeriodEndDate;
    @ApiModelProperty(example = "保额")
    private BigDecimal amount;
    @ApiModelProperty(example = "基础保额")
    private BigDecimal baseAmount;
    @ApiModelProperty(example = "红利保额")
    private BigDecimal dividendAmount;

    @ApiModelProperty(example = "产品档次")
    private String productLevel;

    @ApiModelProperty(example = "交费方式(自付:贷款付)")
    private String financingMethod = "SELF-FINANCED";

    @ApiModelProperty(example = "交费方式(自付:贷款付)")
    @Internation(filed = "financingMethod", codeType = "PRODUCT_FINANCING_METHOD")
    private String financingMethodName;

    @ApiModelProperty(example = "额外意外保额的倍数")
    private Long accSiMultiple;
    @ApiModelProperty(example = "额外的意外保额")
    private BigDecimal additionalAccAmount;
    @ApiModelProperty(example = "销售组合编码")
    private String packageCode;
    @ApiModelProperty(example = "销售组合名称")
    @Internation(filed = "packageCode", codeType = "PRODUCT_SALES_PACKAGES")
    private String packageName;
    @ApiModelProperty(example = "计划编码")
    private String planCode;
    @ApiModelProperty(example = "计划名称")
    @Internation(filed = "planCode", codeType = "PRODUCT_SALES_PLAN")
    private String planName;

    @ApiModelProperty(example = "本次险种缴费期数")
    private int paymentInstallments = 1;

    @ApiModelProperty(example = "网销产品缴费年期")
    private String premiumPaymentTerm;
    @ApiModelProperty(example = "网销产品缴费年期")
    @Internation(filed = "premiumPaymentTerm", codeType = "PREMIUM_PAYMENT_TERM")
    private String premiumPaymentTermName;

    /**
     * 险种责任
     */
    private List<DutyResponse> listCoverageDuty=new ArrayList<>();

    /**
     * 不同缴费周期对应的保费
     */
    List<CoveragePremiumFrequencyResponse> coveragePremiumFrequency;

    /**
     * 多档次
     */
    private List<CoverageLevelResponse> listCoverageLevel=new ArrayList<>();

}
