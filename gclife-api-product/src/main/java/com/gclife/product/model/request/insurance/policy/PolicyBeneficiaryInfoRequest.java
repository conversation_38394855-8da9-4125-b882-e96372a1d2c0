package com.gclife.product.model.request.insurance.policy;


import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 *         create 17-10-20
 *         description:
 */
@Data
public class PolicyBeneficiaryInfoRequest implements Serializable{
    private String     policyBeneficiaryId;
    private String     beneficiaryId;
    private String     beneficiaryType;
    private Long       beneficiaryNo;
    private String     beneficiaryNoOrder;
    private BigDecimal beneficiaryProportion;
    private String     beneficiaryGrade;
    private String     bankCode;
    private String     bankAccountNo;
    private String     bankAccountName;
    private String     transferGrantFlag;
    private String     claimPremDrawType;
    private BigDecimal promiseRate;
    private Long       promiseAge;
    private String     validFlag;
    private String     createdUserId;
    private Long       createdDate;
    private String     updatedUserId;
    private Long       updatedDate;
    private String     insuredId;

    private PolicyBeneficiaryRequest policyBeneficiary;


}
