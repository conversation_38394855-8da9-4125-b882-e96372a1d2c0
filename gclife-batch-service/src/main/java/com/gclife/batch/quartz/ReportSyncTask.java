package com.gclife.batch.quartz;

import com.alibaba.fastjson.JSON;
import com.gclife.batch.model.config.BatchErrorConfigEnum;
import com.gclife.common.model.ResultObject;
import com.gclife.common.model.config.TerminologyConfigEnum;
import com.gclife.common.util.DateUtils;
import com.gclife.common.util.JackSonUtils;
import com.gclife.common.util.UUIDUtils;
import com.gclife.report.api.ReportBaseApi;
import org.apache.commons.lang.exception.ExceptionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * create 18-11-9
 * description:
 */
@Component
public class ReportSyncTask {
    private static final Logger LOGGER = LoggerFactory.getLogger(ReportSyncTask.class);

    @Autowired
    private ReportBaseApi reportBaseApi;

    /**
     * 每一个小时执行财务报表批处理
     */
    @Scheduled(cron = "0 0 0/1 * * ?")
    public void syncReportPaymentTask() {
        String tmpUUID = UUIDUtils.getUUIDShort();
        try {
            LOGGER.info("syncReportPaymentTask -- start uuid:" + tmpUUID);
            int currentPage = 1;
            String result = "";
            do {
                result = reportBaseApi.syncReportPayment(10000, currentPage);
                currentPage++;
                LOGGER.info("syncReportPaymentTask -- process uuid:" + tmpUUID);
            } while (TerminologyConfigEnum.WHETHER.NO.name().equals(result));

            LOGGER.info("syncReportPaymentTask -- end uuid:" + tmpUUID);
        } catch (Exception e) {
            LOGGER.info("syncReportPaymentTask -- error uuid:" + tmpUUID);
            LOGGER.error(ExceptionUtils.getFullStackTrace(e));
            LOGGER.error(BatchErrorConfigEnum.BATCH_SYNC_REPORT_ERROR.getValue());
        }
    }

    /**
     * 每一个小时执行批量同步投保人资料
     */
    @Scheduled(cron = "0 0 0/1 * * ?")
    public void syncReportCustomerTask() {
        String tmpUUID = UUIDUtils.getUUIDShort();
        try {
            LOGGER.info("syncReportCustomerTask -- start uuid:" + tmpUUID);
            int currentPage = 1;
            String result = "";
            do {
                result = reportBaseApi.syncReportCustomer(10000, currentPage);
                currentPage++;
                LOGGER.info("syncReportCustomerTask -- process uuid:" + tmpUUID);
            } while (TerminologyConfigEnum.WHETHER.NO.name().equals(result));

            LOGGER.info("syncReportCustomerTask -- end uuid:" + tmpUUID);
        } catch (Exception e) {
            LOGGER.info("syncReportCustomerTask -- error uuid:" + tmpUUID);
            LOGGER.error(ExceptionUtils.getFullStackTrace(e));
            LOGGER.error(BatchErrorConfigEnum.BATCH_SYNC_REPORT_ERROR.getValue());
        }
    }

    /**
     * 每一个小时执行批量同步查询承保清单
     */
    @Scheduled(cron = "0 0 0/1 * * ?")
    public void syncReportPolicyTask() {
        String tmpUUID = UUIDUtils.getUUIDShort();
        try {
            LOGGER.info("syncReportPolicyTask -- start uuid:" + tmpUUID);
            int currentPage = 1;
            String result = "";
            do {
                result = reportBaseApi.syncReportPolicy(10000, currentPage);
                currentPage++;
                LOGGER.info("syncReportPolicyTask -- process uuid:" + tmpUUID);
            } while (TerminologyConfigEnum.WHETHER.NO.name().equals(result));

            LOGGER.info("syncReportPolicyTask -- end uuid:" + tmpUUID);
        } catch (Exception e) {
            LOGGER.info("syncReportPolicyTask -- error uuid:" + tmpUUID);
            LOGGER.error(ExceptionUtils.getFullStackTrace(e));
            LOGGER.error(BatchErrorConfigEnum.BATCH_SYNC_REPORT_ERROR.getValue());
        }
    }

    /**
     * 每一个小时执行批量同步查询团险承保清单
     */
    @Scheduled(cron = "0 0 0/1 * * ?")
    public void syncReportGroupPolicyTask() {
        String tmpUUID = UUIDUtils.getUUIDShort();
        try {
            LOGGER.info("syncReportGroupPolicyTask -- start uuid:" + tmpUUID);
            int currentPage = 1;
            String result = "";
            do {
                result = reportBaseApi.syncReportGroupPolicy(10000, currentPage);
                currentPage++;
                LOGGER.info("syncReportGroupPolicyTask -- process uuid:" + tmpUUID);
            } while (TerminologyConfigEnum.WHETHER.NO.name().equals(result));

            LOGGER.info("syncReportGroupPolicyTask -- end uuid:" + tmpUUID);
        } catch (Exception e) {
            LOGGER.info("syncReportGroupPolicyTask -- error uuid:" + tmpUUID);
            LOGGER.error(ExceptionUtils.getFullStackTrace(e));
            LOGGER.error(BatchErrorConfigEnum.BATCH_SYNC_REPORT_ERROR.getValue());
        }
    }

    /**
     * 每一个小时执行批量同步监管报表-承保清单
     */
    @Scheduled(cron = "0 0 0/1 * * ?")
    public void syncReportRegulatoryPolicyTask() {
        String tmpUUID = UUIDUtils.getUUIDShort();
        try {
            LOGGER.info("syncReportRegulatoryPolicyTask -- start uuid:" + tmpUUID);
            int currentPage = 1;
            String result = "";
            do {
                result = reportBaseApi.syncReportRegulatoryPolicy(10000, currentPage);
                currentPage++;
                LOGGER.info("syncReportRegulatoryPolicyTask -- process uuid:" + tmpUUID);
            } while (TerminologyConfigEnum.WHETHER.NO.name().equals(result));

            LOGGER.info("syncReportRegulatoryPolicyTask -- end uuid:" + tmpUUID);
        } catch (Exception e) {
            LOGGER.info("syncReportRegulatoryPolicyTask -- error uuid:" + tmpUUID);
            LOGGER.error(ExceptionUtils.getFullStackTrace(e));
            LOGGER.error(BatchErrorConfigEnum.BATCH_SYNC_REPORT_ERROR.getValue());
        }
    }

    /**
     * 每一个小时执行批量同步续期清单
     */
    @Scheduled(cron = "0 0 0/1 * * ?")
    public void syncReportRenewalTask() {
        String tmpUUID = UUIDUtils.getUUIDShort();
        try {
            LOGGER.info("syncReportRenewalTask -- start uuid:" + tmpUUID);
            int currentPage = 1;
            String result = "";
            do {
                result = reportBaseApi.syncReportRenewal(10000, currentPage);
                currentPage++;
                LOGGER.info("syncReportRenewalTask -- process uuid:" + tmpUUID);
            } while (TerminologyConfigEnum.WHETHER.NO.name().equals(result));

            LOGGER.info("syncReportRenewalTask -- end uuid:" + tmpUUID);
        } catch (Exception e) {
            LOGGER.info("syncReportRenewalTask -- error uuid:" + tmpUUID);
            LOGGER.error(ExceptionUtils.getFullStackTrace(e));
            LOGGER.error(BatchErrorConfigEnum.BATCH_SYNC_REPORT_ERROR.getValue());
        }
    }


    /**
     * 每一个小时执行批量同步团险续保清单
     */
    @Scheduled(cron = "0 0 0/1 * * ?")
    public void syncReportGroupRenewalTask() {
        String tmpUUID = UUIDUtils.getUUIDShort();
        try {
            LOGGER.info("syncReportGroupRenewalTask -- start uuid:" + tmpUUID);
            int currentPage = 1;
            String result = "";
            do {
                result = reportBaseApi.syncReportGroupRenewal(1000, currentPage);
                currentPage++;
                LOGGER.info("syncReportGroupRenewalTask -- process uuid:" + tmpUUID);
            } while (TerminologyConfigEnum.WHETHER.NO.name().equals(result));

            LOGGER.info("syncReportGroupRenewalTask -- end uuid:" + tmpUUID);
        } catch (Exception e) {
            LOGGER.info("syncReportGroupRenewalTask -- error uuid:" + tmpUUID);
            LOGGER.error(ExceptionUtils.getFullStackTrace(e));
            LOGGER.error(BatchErrorConfigEnum.BATCH_SYNC_REPORT_ERROR.getValue());
        }
    }

    /**
     * 每一个小时执行批量同步理赔报表
     */
    @Scheduled(cron = "0 0 0/1 * * ?")
    public void syncReportClaimTask() {
        String tmpUUID = UUIDUtils.getUUIDShort();
        try {
            LOGGER.info("syncReportClaimTask -- start uuid:" + tmpUUID);
            int currentPage = 1;
            String result = "";
            do {
                result = reportBaseApi.syncReportClaim(10000, currentPage);
                currentPage++;
                LOGGER.info("syncReportClaimTask -- process uuid:" + tmpUUID);
            } while (TerminologyConfigEnum.WHETHER.NO.name().equals(result));

            LOGGER.info("syncReportClaimTask -- end uuid:" + tmpUUID);
        } catch (Exception e) {
            LOGGER.info("syncReportClaimTask -- error uuid:" + tmpUUID);
            LOGGER.error(ExceptionUtils.getFullStackTrace(e));
            LOGGER.error(BatchErrorConfigEnum.BATCH_SYNC_REPORT_ERROR.getValue());
        }
    }

    /**
     * 每一个小时执行批量同步业务员报表
     */
    @Scheduled(cron = "0 0 0/1 * * ?")
    public void syncReportAgentTask() {
        String tmpUUID = UUIDUtils.getUUIDShort();
        try {
            LOGGER.info("syncReportAgentTask -- start uuid:" + tmpUUID);
            int currentPage = 1;
            String result = "";
            do {
                result = reportBaseApi.syncReportAgent(10000, currentPage);
                currentPage++;
                LOGGER.info("syncReportAgentTask -- process uuid:" + tmpUUID);
            } while (TerminologyConfigEnum.WHETHER.NO.name().equals(result));

            LOGGER.info("syncReportAgentTask -- end uuid:" + tmpUUID);
        } catch (Exception e) {
            LOGGER.info("syncReportAgentTask -- error uuid:" + tmpUUID);
            LOGGER.error(ExceptionUtils.getFullStackTrace(e));
            LOGGER.error(BatchErrorConfigEnum.BATCH_SYNC_REPORT_ERROR.getValue());
        }
    }

    /**
     * 每一个小时执行批量同步客户回访报表
     */
    @Scheduled(cron = "0 0 0/1 * * ?")
    public void syncReportReturnVisitTask() {
        String tmpUUID = UUIDUtils.getUUIDShort();
        try {
            LOGGER.info("syncReportReturnVisitTask -- start uuid:" + tmpUUID);
            int currentPage = 1;
            String result = "";
            do {
                result = reportBaseApi.syncReportReturnVisit(10000, currentPage);
                currentPage++;
                LOGGER.info("syncReportReturnVisitTask -- process uuid:" + tmpUUID);
            } while (TerminologyConfigEnum.WHETHER.NO.name().equals(result));

            LOGGER.info("syncReportReturnVisitTask -- end uuid:" + tmpUUID);
        } catch (Exception e) {
            LOGGER.info("syncReportReturnVisitTask -- error uuid:" + tmpUUID);
            LOGGER.error(ExceptionUtils.getFullStackTrace(e));
            LOGGER.error(BatchErrorConfigEnum.BATCH_SYNC_REPORT_ERROR.getValue());
        }
    }

    /**
     * 每一个小时执行批量同步被保人报表
     */
    @Scheduled(cron = "0 0 0/1 * * ?")
    public void syncReportInsuredTask() {
        String tmpUUID = UUIDUtils.getUUIDShort();
        try {
            LOGGER.info("syncReportInsuredTask -- start uuid:" + tmpUUID);
            int currentPage = 1;
            String result = "";
            do {
                result = reportBaseApi.syncReportInsured(10000, currentPage);
                currentPage++;
                LOGGER.info("syncReportInsuredTask -- process uuid:" + tmpUUID);
            } while (TerminologyConfigEnum.WHETHER.NO.name().equals(result));

            LOGGER.info("syncReportInsuredTask -- end uuid:" + tmpUUID);
        } catch (Exception e) {
            LOGGER.info("syncReportInsuredTask -- error uuid:" + tmpUUID);
            LOGGER.error(ExceptionUtils.getFullStackTrace(e));
            LOGGER.error(BatchErrorConfigEnum.BATCH_SYNC_REPORT_ERROR.getValue());
        }
    }

    /**
     * 每一个小时执行批量同步保全报表
     */
    @Scheduled(cron = "0 0 0/1 * * ?")
    public void syncReportEndorseTask() {
        String tmpUUID = UUIDUtils.getUUIDShort();
        try {
            LOGGER.info("syncReportEndorseTask -- start uuid:" + tmpUUID);
            int currentPage = 1;
            String result = "";
            do {
                result = reportBaseApi.syncReportEndorse(10000, currentPage);
                currentPage++;
                LOGGER.info("syncReportEndorseTask -- process uuid:" + tmpUUID);
            } while (TerminologyConfigEnum.WHETHER.NO.name().equals(result));

            LOGGER.info("syncReportEndorseTask -- end uuid:" + tmpUUID);
        } catch (Exception e) {
            LOGGER.info("syncReportEndorseTask -- error uuid:" + tmpUUID);
            LOGGER.error(ExceptionUtils.getFullStackTrace(e));
            LOGGER.error(BatchErrorConfigEnum.BATCH_SYNC_REPORT_ERROR.getValue());
        }
    }

    /**
     * 每月一号凌晨两点执行月度统计报表
     */
    @Scheduled(cron = "0 0 2 1 * ?")
    public void syncMonthlyStatisticsTask() {
        String tmpUUID = UUIDUtils.getUUIDShort();
        try {
            LOGGER.info("syncMonthlyStatisticsTask -- start uuid:" + tmpUUID);
            ResultObject<Void> resultObject = reportBaseApi.syncMonthlyStatistics();
            LOGGER.info("resultObject:{}", JackSonUtils.toJson(resultObject));
            LOGGER.info("syncMonthlyStatisticsTask -- end uuid:" + tmpUUID);
        } catch (Exception e) {
            LOGGER.info("syncMonthlyStatisticsTask -- error uuid:" + tmpUUID);
            LOGGER.error(ExceptionUtils.getFullStackTrace(e));
            LOGGER.error(BatchErrorConfigEnum.BATCH_SYNC_REPORT_ERROR.getValue());
        }
    }

    /**
     * 每月一号凌晨两点十分 发送月度报表统计到钉钉群
     */
    @Scheduled(cron = "0 10 2 1 * ?")
    public void sendMonthlyStatisticsDingtalkMsgTask() {
        String tmpUUID = UUIDUtils.getUUIDShort();
        try {
            LOGGER.info("sendMonthlyStatisticsDingtalkMsgTask -- start uuid:" + tmpUUID);
            ResultObject<Void> resultObject = reportBaseApi.sendMonthlyStatisticsDingtalkMsg(DateUtils.getCurrentTime());
            LOGGER.info("resultObject:{}", JackSonUtils.toJson(resultObject));
            LOGGER.info("sendMonthlyStatisticsDingtalkMsgTask -- end uuid:" + tmpUUID);
        } catch (Exception e) {
            LOGGER.info("sendMonthlyStatisticsDingtalkMsgTask -- error uuid:" + tmpUUID);
            LOGGER.error(ExceptionUtils.getFullStackTrace(e));
            LOGGER.error(BatchErrorConfigEnum.BATCH_SYNC_REPORT_ERROR.getValue());
        }
    }

    /**
     * 银保渠道手续费费用明细表
     * 保单和投保单  不包括手续费明细手续费明细 syncServiceChargeBanChannelPaymentTask
     * 每个月的月底统计 累计基础数据
     * 每个月月底 22点30分同部当月的数据
     */
    @Scheduled(cron = "0 30 22 28-31 * ?")
    public void syncServiceChargeBanChannelTask() {
        String thisMonthLastDay = DateUtils.timeStrToString(DateUtils.getThisMonthLastDay(), DateUtils.FORMATE3);
        String currentTimeDay = DateUtils.timeStrToString(DateUtils.getCurrentTime(), DateUtils.FORMATE3);
        if (!thisMonthLastDay.equals(currentTimeDay)) {
            return;
        }
        String tmpUUID = UUIDUtils.getUUIDShort();
        try {
            LOGGER.info("syncServiceChargeBanChannelTask -- start uuid:" + tmpUUID);
            ResultObject<Void> resultObject = reportBaseApi.syncServiceChargeBankChannel();
            LOGGER.info("resultObject:{}", JackSonUtils.toJson(resultObject));
            LOGGER.info("syncServiceChargeBanChannelTask -- end uuid:" + tmpUUID);
        } catch (Exception e) {
            LOGGER.info("syncServiceChargeBanChannelTask -- error uuid:" + tmpUUID);
            LOGGER.error(ExceptionUtils.getFullStackTrace(e));
            LOGGER.error(BatchErrorConfigEnum.BATCH_SYNC_REPORT_ERROR.getValue());
        }
    }

    /**
     * 银保渠道手续费费用明细表
     * 每小时同步支付数据  不重复同步
     * 每小时同步
     */
    @Scheduled(cron = "0 0 0/1 * * ?")
    public void syncServiceChargeBanChannelPaymentTask() {
        String tmpUUID = UUIDUtils.getUUIDShort();
        try {
            LOGGER.info("syncServiceChargeBanChannelPaymentTask -- start uuid:" + tmpUUID);
            ResultObject<Void> resultObject = reportBaseApi.syncServiceChargeBankChannelPayment();
            LOGGER.info("resultObject:{}", JackSonUtils.toJson(resultObject));
            LOGGER.info("syncServiceChargeBanChannelPaymentTask -- end uuid:" + tmpUUID);
        } catch (Exception e) {
            LOGGER.info("syncServiceChargeBanChannelPaymentTask -- error uuid:" + tmpUUID);
            LOGGER.error(ExceptionUtils.getFullStackTrace(e));
            LOGGER.error(BatchErrorConfigEnum.BATCH_SYNC_REPORT_ERROR.getValue());
        }
    }


    /**
     * 一小时 同步一次销售报表
     */
    @Scheduled(cron = "0 0 0/1 * * ?")
    public void syncSaleApplyPolicyTask() {
        String tmpUUID = UUIDUtils.getUUIDShort();
        try {
            LOGGER.info("syncSaleApplyPolicyTask -- start uuid:" + tmpUUID);
            ResultObject<Void> resultObject = reportBaseApi.syncSaleApplyPolicy();
            LOGGER.info("resultObject:{}", JackSonUtils.toJson(resultObject));
            LOGGER.info("syncSaleApplyPolicyTask -- end uuid:" + tmpUUID);
        } catch (Exception e) {
            LOGGER.info("syncSaleApplyPolicyTask -- error uuid:" + tmpUUID);
            LOGGER.error(ExceptionUtils.getFullStackTrace(e));
            LOGGER.error(BatchErrorConfigEnum.BATCH_SYNC_REPORT_ERROR.getValue());
        }
    }

    /**
     * 每个月的也低步暂收保费报表
     */
    @Scheduled(cron = "0 50 23 28-31 * ?")
    public void syncSuspense() {
        String thisMonthLastDay = DateUtils.timeStrToString(DateUtils.getThisMonthLastDay(), DateUtils.FORMATE3);
        String currentTimeDay = DateUtils.timeStrToString(DateUtils.getCurrentTime(), DateUtils.FORMATE3);
        if (!thisMonthLastDay.equals(currentTimeDay)) {
            return;
        }
        String tempUUID = UUIDUtils.getUUIDShort();
        try {
            Long startTime = DateUtils.getCurrentTime();
            LOGGER.info("quarterlyStatisticsReserveWithdrawalReport -- start uuid:" + tempUUID);
            //　请求微服务
            ResultObject resultObject = reportBaseApi.syncSuspense();
            LOGGER.info("quarterlyStatisticsReserveWithdrawalReport -- process uuid:" + tempUUID + "Result" + JSON.toJSONString(resultObject));
        } catch (Exception e) {
            LOGGER.error("quarterlyStatisticsReserveWithdrawalReport -- error uuid:" + tempUUID);
            LOGGER.error(ExceptionUtils.getFullStackTrace(e));
            LOGGER.error(BatchErrorConfigEnum.BATCH_SUSPENSE_ATTACHMENT_REPORT_ERROR.getValue());
        }
    }

    /**
     * 半小时 同步一次系统预警
     */
    @Scheduled(cron = "0 0/30 * * * ?")
    public void doSystemWarningTask() {
        System.out.println("系统预警开始执行 -----------------1111------------------111");
        String tmpUUID = UUIDUtils.getUUIDShort();
        try {
            LOGGER.info("checkApproveData 投保单和保单数据校验 -- start uuid:" + tmpUUID);
            ResultObject resultObject = reportBaseApi.checkApproveData();
            LOGGER.info("resultObject:{}", JackSonUtils.toJson(resultObject));
            LOGGER.info("checkApproveData 投保单和保单数据校验 -- end uuid:" + tmpUUID);

            LOGGER.info("checkApplyPaymentData 投保单支付数据和财务支付数据校验 -- start uuid:" + tmpUUID);
            ResultObject resultObject1 = reportBaseApi.checkApplyPaymentData();
            LOGGER.info("resultObject:{}", JackSonUtils.toJson(resultObject1));
            LOGGER.info("checkApplyPaymentData 投保单支付数据和财务支付数据校验 -- end uuid:" + tmpUUID);

            LOGGER.info("checkRenewalPaymentData 个团险续期续保支付数据和财务支付数据校验 -- start uuid:" + tmpUUID);
            ResultObject resultObject2 = reportBaseApi.checkRenewalPaymentData();
            LOGGER.info("resultObject:{}", JackSonUtils.toJson(resultObject2));
            LOGGER.info("checkRenewalPaymentData 个团险续期续保支付数据和财务支付数据校验 -- end uuid:" + tmpUUID);

            LOGGER.info("checkEndorsePaymentData 系统预警查询保全支付数据和财务支付数据校验 -- start uuid:" + tmpUUID);
            ResultObject resultObject4 = reportBaseApi.checkEndorsePaymentData();
            LOGGER.info("resultObject:{}", JackSonUtils.toJson(resultObject4));
            LOGGER.info("checkEndorsePaymentData 系统预警查询保全支付数据和财务支付数据校验 -- end uuid:" + tmpUUID);

            LOGGER.info("checkPaymentDataStatus 系统预警查询保全支付数据和财务支付数据校验状态校验 -- start uuid:" + tmpUUID);
            ResultObject resultObject5 = reportBaseApi.checkPaymentDataStatus();
            LOGGER.info("resultObject:{}", JackSonUtils.toJson(resultObject5));
            LOGGER.info("checkPaymentDataStatus 系统预警查询保全支付数据和财务支付数据校验状态校验 -- end uuid:" + tmpUUID);
        } catch (Exception e) {
            LOGGER.info("doSystemWarningTask -- error uuid:" + tmpUUID);
            LOGGER.error(ExceptionUtils.getFullStackTrace(e));
            LOGGER.error(BatchErrorConfigEnum.SYSTEM_WARNING_ERROR.getValue());
        }
    }

    /**
     * 一小时 同步学校发展基金报表
     */
    @Scheduled(cron = "0 0 0/1 * * ?")
    public void syncSdfReportTask() {
        System.out.println("同步学校发展基金报表 -----------------1111------------------111");
        String tmpUUID = UUIDUtils.getUUIDShort();
        try {
            LOGGER.info("同步学校发展基金报表 -- start uuid:" + tmpUUID);
            int currentPage = 1;
            String result = "";
            do {
                result = reportBaseApi.syncReportGroupPolicySdf(10000, currentPage);
                currentPage++;
                LOGGER.info("同步学校发展基金报表 -- process uuid:" + tmpUUID);
            } while (TerminologyConfigEnum.WHETHER.NO.name().equals(result));

            LOGGER.info("同步学校发展基金报表 -- end uuid:" + tmpUUID);
        } catch (Exception e) {
            LOGGER.info("同步学校发展基金报表失败 -- error uuid:" + tmpUUID);
            LOGGER.error(ExceptionUtils.getFullStackTrace(e));
            LOGGER.error(BatchErrorConfigEnum.BATCH_SYNC_REPORT_ERROR.getValue());
        }
    }
}
