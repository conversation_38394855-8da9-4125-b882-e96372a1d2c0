package com.gclife.certify.model.response;

import com.gclife.common.model.BaseResponse;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

/**
 * \* Created with IntelliJ IDEA.
 * \* User: deep
 * \* Date: 17-9-20
 * \* Time: 下午3:47
 * \* To change this template use File | Settings | File Templates.
 * \* Description:
 * <AUTHOR>
 */
@Getter
@Setter
@ApiModel(value = "CertifyAttachment",description = "系统单证附件")
public class CertifyAttachmentResponse extends BaseResponse {


    @ApiModelProperty(example = "附件ID")
    private String certifyAttachmentId;
    @ApiModelProperty(example = "附件类型")
    private String attachmentTypeCode;
    @ApiModelProperty(example = "附件类型名称")
    private String attachmentTypeName;
    @ApiModelProperty(example = "最大页数")
    private Long maxWeight;
    @ApiModelProperty(example = "排序")
    private Long   attachmentTypeIndex;
}