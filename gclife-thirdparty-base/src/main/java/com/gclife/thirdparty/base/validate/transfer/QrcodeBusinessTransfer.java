package com.gclife.thirdparty.base.validate.transfer;

import com.gclife.attachment.api.AttachmentQrcodeApi;
import com.gclife.attachment.model.request.QRCodeRequest;
import com.gclife.attachment.model.response.AttachmentByteResponse;
import com.gclife.common.model.ResultObject;
import com.gclife.common.model.config.TerminologyConfigEnum;
import com.gclife.common.util.AssertUtils;
import com.gclife.common.util.UUIDUtils;
import com.gclife.thirdparty.base.dao.QrcodeTypeBaseDao;
import com.gclife.thirdparty.base.model.bo.QrcodeDo;
import com.gclife.thirdparty.base.model.config.ThirdpartyErrorConfigEnum;
import com.gclife.thirdparty.base.model.vo.QrcodeOptionVo;
import com.gclife.thirdparty.base.service.data.ThirdpartyService;
import com.gclife.thirdparty.base.validate.base.BaseParameterValidate;
import com.gclife.thirdparty.core.jooq.tables.pojos.QrcodeTypePo;
import com.gclife.thirdparty.model.config.ThirdpartyTermEnum;
import me.chanjar.weixin.mp.api.WxMpService;
import me.chanjar.weixin.mp.bean.result.WxMpQrCodeTicket;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * create 17-12-22
 * description:
 */
@Component
public class QrcodeBusinessTransfer extends BaseParameterValidate {

    @Autowired
    private QrcodeTypeBaseDao qrcodeTypeBaseDao;

    @Autowired
    private WxMpService wxService;

    @Autowired
    private ThirdpartyService thirdpartyService;

    @Autowired
    private AttachmentQrcodeApi attachmentQrcodeApi;

    /**
     * 转换数据产生二维码
     *
     * @param qrcodeOptionVo 产生二维码处理类
     */

    public void transferQrcode(QrcodeDo qrcodeDo, QrcodeOptionVo qrcodeOptionVo) throws Exception {
        QrcodeTypePo qrcodeTypePo = qrcodeTypeBaseDao.queryOneQRCodeType(qrcodeOptionVo.getQrcodeType(), qrcodeOptionVo.getDeviceChannel());
        AssertUtils.isNotNull(this.getLogger(), qrcodeTypePo, ThirdpartyErrorConfigEnum.THIRDPARTY_BUSINESS_QUERY_TYPE_QRCODE_ERROR);
        //初始化二维码对象
        if (!AssertUtils.isNotEmpty(qrcodeDo.getQrcodeId())) {
            //初始的需要重新生成
            qrcodeDo.setQrcodeNo((System.currentTimeMillis() / 1000));
            qrcodeDo.setUpdate(false);
            qrcodeDo.setGenerateQrcode(true);
            qrcodeDo.setDeviceChannel(qrcodeOptionVo.getDeviceChannel());
            qrcodeDo.setIsHandler(TerminologyConfigEnum.WHETHER.NO.name());
            qrcodeDo.setIsError("INIT");
            //保存
            thirdpartyService.saveQrcodePo(qrcodeDo);
        } else {
            if (!qrcodeDo.getParseType().equals(qrcodeTypePo.getParseType()) || !qrcodeDo.getScanExpire().equals(qrcodeTypePo.getScanExpireType())) {
                //从一种类型到另一种类型，需要重新生成
                qrcodeDo.setGenerateQrcode(true);
            } else {
                if (ThirdpartyTermEnum.parseType.WECHAT.name().equals(qrcodeDo.getParseType())) {
                    //失效的
                    if (qrcodeDo.isQrcodeExpire()) {
                        //重新生成二唯码
                        qrcodeDo.setGenerateQrcode(true);
                    }
                }
            }
        }

        //转换数据
        if (qrcodeDo.isGenerateQrcode()) {
            qrcodeDo.setCreatedDate(System.currentTimeMillis());
            //业务类型
            qrcodeDo.setQrcodeTypeCode(qrcodeOptionVo.getQrcodeType());
            //转换类型
            qrcodeDo.setParseType(qrcodeTypePo.getParseType());
            //扫码类型
            qrcodeDo.setScanExpire(qrcodeTypePo.getScanExpireType());
            //业务字符串
            qrcodeDo.setBizStr(qrcodeOptionVo.getBizStr());
            //有效时长
            qrcodeDo.setExpireIn(qrcodeTypePo.getExpiresIn());
            //是否失效
            qrcodeDo.setIsExpire(TerminologyConfigEnum.WHETHER.NO.name());
            //设置失效时间
            if (ThirdpartyTermEnum.scanExpireType.FOREVER.name().equals(qrcodeDo.getScanExpire())) {
                qrcodeDo.setExpiresEndIn(System.currentTimeMillis() + (1000 * 60 * 60 * 24 * 365 * 200));
            } else {
                //如果为有效时长的
                if (qrcodeTypePo.getExpiresIn() > 0) {
                    //计算失效时间
                    qrcodeDo.setExpiresEndIn(System.currentTimeMillis() + (qrcodeTypePo.getExpiresIn()));
                } else {
                    //微信的最大时长一个月，即2592000秒
                    qrcodeDo.setExpiresEndIn(System.currentTimeMillis() + (1000 * 2592000));
                    qrcodeDo.setExpireIn(2592000000L);
                }
            }
        }
        qrcodeDo.setServiceName(qrcodeTypePo.getServiceName());
        //生产二维码场景字符串
        if (qrcodeDo.isGenerateQrcode()) {
            //设置解析链接
            if (ThirdpartyTermEnum.parseType.WECHAT.name().equals(qrcodeDo.getParseType())) {
                //如果是微信，则重新生成
                genderWechatQrcode(qrcodeDo);
            } else if (ThirdpartyTermEnum.parseType.BIZ_SERVICE.name().equals(qrcodeDo.getParseType()) || ThirdpartyTermEnum.parseType.BIZ_URL.name().equals(qrcodeDo.getParseType())) {
                //如果是B,C两种业务类型
                qrcodeDo.setParseStr("https://dev-api.zjlh.lan/thirdparty/v1/qrcode/business/handle" + "/" + qrcodeDo.getQrcodeId());
            } else {
                //如果是D,直接输出源字符
                qrcodeDo.setParseStr(qrcodeOptionVo.getBizStr());
            }
        }

        //生成二维码
        QRCodeRequest qrCodeRequest = new QRCodeRequest();
        qrCodeRequest.setSceneStr(qrcodeDo.getParseStr());
        qrCodeRequest.setExpiresIn(qrcodeDo.getExpireIn());
        ResultObject<AttachmentByteResponse> respFcResultObject = attachmentQrcodeApi.generateQrcode(qrCodeRequest);
        AssertUtils.isResultObjectDataNull(this.getLogger(), respFcResultObject);
        //设置数据
        qrcodeDo.setQrcodeAttachmentId(respFcResultObject.getData().getMediaId());
        qrcodeDo.setSourceUrl(respFcResultObject.getData().getUrl());

    }

    public void transferDingQrcode(QrcodeDo qrcodeDo, QrcodeOptionVo qrcodeOptionVo) throws Exception {
        QrcodeTypePo qrcodeTypePo = qrcodeTypeBaseDao.queryOneQRCodeType(qrcodeOptionVo.getQrcodeType(), qrcodeOptionVo.getDeviceChannel());
        AssertUtils.isNotNull(this.getLogger(), qrcodeTypePo, ThirdpartyErrorConfigEnum.THIRDPARTY_BUSINESS_QUERY_TYPE_QRCODE_ERROR);
        //初始化二维码对象
        if (!AssertUtils.isNotEmpty(qrcodeDo.getQrcodeId())) {
            //初始的需要重新生成
            qrcodeDo.setQrcodeNo((System.currentTimeMillis() / 1000));
            qrcodeDo.setUpdate(false);
            qrcodeDo.setGenerateQrcode(true);
            qrcodeDo.setDeviceChannel(qrcodeOptionVo.getDeviceChannel());
            qrcodeDo.setIsHandler(TerminologyConfigEnum.WHETHER.NO.name());
            qrcodeDo.setIsError("INIT");
            //保存
            thirdpartyService.saveQrcodePo(qrcodeDo);
        } else {
            if (!qrcodeDo.getParseType().equals(qrcodeTypePo.getParseType()) || !qrcodeDo.getScanExpire().equals(qrcodeTypePo.getScanExpireType())) {
                //从一种类型到另一种类型，需要重新生成
                qrcodeDo.setGenerateQrcode(true);
            } else {
                if (ThirdpartyTermEnum.parseType.WECHAT.name().equals(qrcodeDo.getParseType())) {
                    //失效的
                    if (qrcodeDo.isQrcodeExpire()) {
                        //重新生成二唯码
                        qrcodeDo.setGenerateQrcode(true);
                    }
                }
                if (ThirdpartyTermEnum.parseType.DING.name().equals(qrcodeDo.getParseType())) {
                    //失效的
                    if (qrcodeDo.isQrcodeExpire()) {
                        //重新生成二唯码
                        qrcodeDo.setGenerateQrcode(true);
                    }
                }
            }
        }

        //转换数据
        if (qrcodeDo.isGenerateQrcode()) {
            qrcodeDo.setCreatedDate(System.currentTimeMillis());
            //业务类型
            qrcodeDo.setQrcodeTypeCode(qrcodeOptionVo.getQrcodeType());
            //转换类型
            qrcodeDo.setParseType(qrcodeTypePo.getParseType());
            //扫码类型
            qrcodeDo.setScanExpire(qrcodeTypePo.getScanExpireType());
            //业务字符串
            qrcodeDo.setBizStr(qrcodeOptionVo.getBizStr());
            //有效时长
            qrcodeDo.setExpireIn(qrcodeTypePo.getExpiresIn());
            //是否失效
            qrcodeDo.setIsExpire(TerminologyConfigEnum.WHETHER.NO.name());
            //设置失效时间
            if (ThirdpartyTermEnum.scanExpireType.FOREVER.name().equals(qrcodeDo.getScanExpire())) {
                qrcodeDo.setExpiresEndIn(System.currentTimeMillis() + (1000 * 60 * 60 * 24 * 365 * 200));
            } else {
                //如果为有效时长的
                if (qrcodeTypePo.getExpiresIn() > 0) {
                    //计算失效时间
                    qrcodeDo.setExpiresEndIn(System.currentTimeMillis() + (qrcodeTypePo.getExpiresIn()));
                } else {
                    //微信的最大时长一个月，即2592000秒
                    qrcodeDo.setExpiresEndIn(System.currentTimeMillis() + (1000 * 2592000));
                    qrcodeDo.setExpireIn(2592000000L);
                }
            }
        }
        qrcodeDo.setServiceName(qrcodeTypePo.getServiceName());
        //生产二维码场景字符串
        if (qrcodeDo.isGenerateQrcode()) {
            //设置解析链接
            if (ThirdpartyTermEnum.parseType.WECHAT.name().equals(qrcodeDo.getParseType())) {
                //如果是微信，则重新生成
                genderWechatQrcode(qrcodeDo);
            } else if (ThirdpartyTermEnum.parseType.BIZ_SERVICE.name().equals(qrcodeDo.getParseType()) || ThirdpartyTermEnum.parseType.BIZ_URL.name().equals(qrcodeDo.getParseType())) {
                //如果是B,C两种业务类型
                qrcodeDo.setParseStr("https://dev-api.zjlh.lan/thirdparty/v1/qrcode/business/handle" + "/" + qrcodeDo.getQrcodeId());
            }  else if (ThirdpartyTermEnum.parseType.DING.name().equals(qrcodeDo.getParseType())) {
                //如果是钉钉业务类型
                qrcodeDo.setParseStr("https://dev-api.zjlh.lan/thirdparty/v1/qrcode/business/handle" + "/" + qrcodeDo.getQrcodeId());
            }
            else {
                //如果是D,直接输出源字符
                qrcodeDo.setParseStr(qrcodeOptionVo.getBizStr());
            }
        }

        //生成二维码
       /* QRCodeRequest qrCodeRequest = new QRCodeRequest();
        qrCodeRequest.setSceneStr(qrcodeDo.getParseStr());
        qrCodeRequest.setExpiresIn(qrcodeDo.getExpireIn());
        ResultObject<AttachmentByteResponse> respFcResultObject = attachmentQrcodeApi.generateQrcode(qrCodeRequest);
        AssertUtils.isResultObjectDataNull(this.getLogger(), respFcResultObject);
        //设置数据
        qrcodeDo.setQrcodeAttachmentId(respFcResultObject.getData().getMediaId());
        qrcodeDo.setSourceUrl(respFcResultObject.getData().getUrl());*/

    }


    /**
     * 生产微信ticket
     *
     * @param qrcodeDo 二维码对象
     * @throws Exception 抛出错误
     */
    private void genderWechatQrcode(QrcodeDo qrcodeDo) throws Exception {
        WxMpQrCodeTicket ticket = null;
        //临时和或带有效时长的，都采用临时二唯码
        if (ThirdpartyTermEnum.scanExpireType.SINGLE.name().equals(qrcodeDo.getScanExpire()) || ThirdpartyTermEnum.scanExpireType.EFFECTIVETIME.name().equals(qrcodeDo.getScanExpire())) {
            //临时二维码统一使用字符串
            qrcodeDo.setActionName(ThirdpartyTermEnum.QrcodeActionName.QR_STR_SCENE.name());
            qrcodeDo.setSceneStr(qrcodeDo.getQrcodeId());
            qrcodeDo.setSceneId(null);
            //临时二维码
            Integer expireIn = qrcodeDo.getExpireIn().intValue() / 1000;
            ticket = this.wxService.getQrcodeService().qrCodeCreateTmpTicket(qrcodeDo.getSceneStr(), expireIn);
        } else {
            //永久二维码(使用字符串)
            qrcodeDo.setActionName(ThirdpartyTermEnum.QrcodeActionName.QR_LIMIT_STR_SCENE.name());
            qrcodeDo.setSceneStr(qrcodeDo.getQrcodeId());
            qrcodeDo.setSceneId(null);
            //永久二维码
            ticket = this.wxService.getQrcodeService().qrCodeCreateLastTicket(qrcodeDo.getSceneStr());
        }
        if (AssertUtils.isNotNull(ticket)) {
            qrcodeDo.setTicket(ticket.getTicket());
            qrcodeDo.setParseStr(ticket.getUrl());
        }
    }

}