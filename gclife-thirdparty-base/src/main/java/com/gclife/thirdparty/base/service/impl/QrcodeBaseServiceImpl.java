package com.gclife.thirdparty.base.service.impl;

import com.gclife.common.service.impl.BaseBusinessServiceImpl;
import com.gclife.common.util.AssertUtils;
import com.gclife.thirdparty.base.dao.QrcodeBaseDao;
import com.gclife.thirdparty.base.model.bo.QrcodeDo;
import com.gclife.thirdparty.base.model.config.ThirdpartyErrorConfigEnum;
import com.gclife.thirdparty.base.model.vo.QrcodeOptionVo;
import com.gclife.thirdparty.base.service.QrcodeBaseService;
import com.gclife.thirdparty.base.service.data.ThirdpartyService;
import com.gclife.thirdparty.base.validate.business.QrcodeBusinessValidate;
import com.gclife.thirdparty.base.validate.transfer.QrcodeBusinessTransfer;
import com.gclife.thirdparty.core.jooq.tables.daos.QrcodeDao;
import com.gclife.thirdparty.core.jooq.tables.pojos.QrcodePo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * \* Created with IntelliJ IDEA.
 * \* User: deep
 * \* Date: 17-9-20
 * \* Time: 下午4:38
 * \* To change this template use File | Settings | File Templates.
 * \* Description:
 *
 * <AUTHOR>
 */
@Service
public class QrcodeBaseServiceImpl extends BaseBusinessServiceImpl implements QrcodeBaseService {

    @Autowired
    private QrcodeBusinessValidate qrcodeBusinessValidate;

    @Autowired
    private QrcodeBusinessTransfer qrcodeBusinessTransfer;

    @Autowired
    private QrcodeBaseDao qrcodeBaseDao;

    @Autowired
    private ThirdpartyService thirdpartyService;

    @Autowired
    private QrcodeDao qrcodeDao;

    /**
     * 生成二唯码
     *
     * @param qrcodeOptionVo 产生二维码处理类
     * @return String 附件ID
     */
    @Override
    @Transactional
    public String generatedQrcode(QrcodeOptionVo qrcodeOptionVo) {
        try {
            //业务校验
            qrcodeBusinessValidate.vaildBusinessgeneratedQrcode(qrcodeOptionVo);
            //查询
            QrcodeDo qrcodeDo = qrcodeBaseDao.queryOneQRCodeDoByBizStrAndQrcodeTypeAndDeviceChannel(qrcodeOptionVo.getBizStr(), qrcodeOptionVo.getQrcodeType(), qrcodeOptionVo.getDeviceChannel());
            if (!AssertUtils.isNotNull(qrcodeDo)) {
                qrcodeDo = new QrcodeDo();
            }
            //转换数据
            qrcodeBusinessTransfer.transferQrcode(qrcodeDo, qrcodeOptionVo);
            //保存数据
            thirdpartyService.saveQrcodePo(qrcodeDo);
            return qrcodeDo.getQrcodeAttachmentId();
        } catch (Exception e) {
            e.printStackTrace();
            this.throwsTransactionalException(this.getLogger(), e, ThirdpartyErrorConfigEnum.THIRDPARTY_BUSINESS_GENERATE_QRCODE_IS_ERROR);
        }
        return null;
    }

    @Override
    @Transactional
    public QrcodeDo generatedDingQrcode(QrcodeOptionVo qrcodeOptionVo) {
        try {
            //业务校验
            qrcodeBusinessValidate.vaildBusinessgeneratedQrcode(qrcodeOptionVo);
            //查询
            QrcodeDo qrcodeDo = qrcodeBaseDao.queryOneQRCodeDoByBizStrAndQrcodeTypeAndDeviceChannel(qrcodeOptionVo.getBizStr(), qrcodeOptionVo.getQrcodeType(), qrcodeOptionVo.getDeviceChannel());
            if (!AssertUtils.isNotNull(qrcodeDo)) {
                qrcodeDo = new QrcodeDo();
            }
            //转换数据
            qrcodeBusinessTransfer.transferDingQrcode(qrcodeDo, qrcodeOptionVo);
            //保存数据
            thirdpartyService.saveQrcodePo(qrcodeDo);
            return qrcodeDo;
        } catch (Exception e) {
            e.printStackTrace();
            this.throwsTransactionalException(this.getLogger(), e, ThirdpartyErrorConfigEnum.THIRDPARTY_BUSINESS_GENERATE_QRCODE_IS_ERROR);
        }
        return null;
    }

    @Override
    public QrcodePo getQrcodeByUserId(String userId, String qrcodeAttachmentId) {
        return qrcodeBaseDao.getQrcodeByUserId(userId,qrcodeAttachmentId);
    }

}