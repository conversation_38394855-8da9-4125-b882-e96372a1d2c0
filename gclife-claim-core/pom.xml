<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <groupId>com.gclife</groupId>
        <artifactId>gclife-business-core</artifactId>
        <version>0.0.1-SNAPSHOT</version>
    </parent>

    <modelVersion>4.0.0</modelVersion>
    <groupId>com.gclife</groupId>
    <artifactId>gclife-claim-core</artifactId>
    <version>0.0.1-SNAPSHOT</version>
    <packaging>jar</packaging>
    <name>${project.artifactId}</name>
    <description>数据库基础服务</description>

    <developers>
        <developer>
            <name>caoqinghua</name>
            <email><EMAIL></email>
        </developer>
    </developers>

    <properties>
        <!--jooq　配置-->
        <jooq.packerage.name>com.gclife.claim.core.jooq</jooq.packerage.name>
        <jooq.strategy.name>com.gclife.common.configuration.GeneratorStrategy</jooq.strategy.name>

        <!--代码生成数据库配置-->
        <jdbc.database>claim</jdbc.database>
        <!--suppress UnresolvedMavenProperty -->
        <jdbc.url>jdbc:postgresql://${db.server.addr}:${db.server.port}/${jdbc.database}</jdbc.url>
        <!--suppress UnresolvedMavenProperty -->
        <jdbc.user>${db.server.user}</jdbc.user>
        <!--suppress UnresolvedMavenProperty -->
        <jdbc.password>${db.server.password}</jdbc.password>
    </properties>

    <build>
        <finalName>${project.artifactId}</finalName>
        <plugins>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
                <configuration>
                    <classifier>exec</classifier>
                </configuration>
            </plugin>

            <!--jooq插件-->
            <plugin><!-- The jOOQ code generator plugin -->
                <groupId>org.jooq</groupId>
                <artifactId>jooq-codegen-maven</artifactId>
                <!-- The plugin should hook into the generate goal -->
                <executions>
                    <execution>
                        <goals>
                            <goal>generate</goal>
                        </goals>
                    </execution>
                </executions>

                <!-- Manage the plugin's dependency. In this example, we'll use a PostgreSQL database -->
                <dependencies>
                    <dependency><!-- https://mvnrepository.com/artifact/org.postgresql/postgresql -->
                        <groupId>org.postgresql</groupId>
                        <artifactId>postgresql</artifactId>
                        <version>${postgresql.version}</version>
                    </dependency>
                </dependencies>

                <!-- Specify the plugin configuration.
                     The configuration format is the same as for the standalone code generator -->
                <configuration>
                    <!-- JDBC connection parameters -->
                    <jdbc>
                        <driver>org.postgresql.Driver</driver>
                        <url>${jdbc.url}</url>
                        <username>${jdbc.user}</username>
                        <password>${jdbc.password}</password>
                    </jdbc>

                    <!-- Generator parameters -->
                    <generator>
                        <!--<strategy>
                             自定义产生器
                        </strategy>-->
                        <strategy>
                            <name>${jooq.strategy.name}</name>
                        </strategy>

                        <database>
                            <name>org.jooq.meta.postgres.PostgresDatabase</name>
                            <includes>.*</includes>
                            <excludes></excludes>
                            <!-- In case your database supports catalogs, e.g. SQL Server:
                            <inputCatalog>public</inputCatalog>
                              -->
                            <inputSchema>public</inputSchema>
                        </database>
                        <generate>
                            <deprecated>false</deprecated>
                            <pojos>true</pojos>
                            <daos>true</daos>
                            <pojosEqualsAndHashCode>true</pojosEqualsAndHashCode>
                            <!--加注解-->
                            <springAnnotations>true</springAnnotations>
                        </generate>
                        <target>
                            <packageName>${jooq.packerage.name}</packageName>
                            <directory>src/main/java</directory>
                        </target>
                    </generator>
                </configuration>
            </plugin>
        </plugins>
    </build>

    <profiles>
        <profile>
            <id>dev</id>
            <properties>
                <db.server.addr>pg-server</db.server.addr>
                <db.server.port>5432</db.server.port>
                <db.server.user>policy</db.server.user>
                <db.server.password>policy</db.server.password>
            </properties>
            <activation>
                <activeByDefault>false</activeByDefault>
            </activation>
        </profile>
        <profile>
            <!-- 服务器打包直接读取环境变量的值-->
            <id>server</id>
            <properties>
                <!--suppress UnresolvedMavenProperty -->
                <db.server.addr>${env.DB_SERVER_ADDR}</db.server.addr>
                <!--suppress UnresolvedMavenProperty -->
                <db.server.port>${env.DB_SERVER_PORT}</db.server.port>
                <!--suppress UnresolvedMavenProperty -->
                <db.server.user>${env.DB_USER_FOR_BUILD}</db.server.user>
                <!--suppress UnresolvedMavenProperty -->
                <db.server.password>${env.DB_PASSWORD_FOR_BUILD}</db.server.password>
            </properties>
            <activation>
                <activeByDefault>true</activeByDefault>
            </activation>
        </profile>
    </profiles>

</project>