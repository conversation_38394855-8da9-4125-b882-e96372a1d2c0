package com.gclife.party.model.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * create 2019/12/6
 * description:
 */
@Data
public class AreaTreeResponse {
    @ApiModelProperty(example = "地区ID")
    private String areaId;
    @ApiModelProperty(example = "地区名称")
    private String areaName;
    @ApiModelProperty(example = "父级地区ID")
    private String parentAreaId;
    @ApiModelProperty(example = "层级")
    private String depth;
}
