package com.gclife.agent.base.dao.law.impl;

import com.gclife.agent.base.dao.law.LawExtDao;
import com.gclife.agent.base.model.bo.law.LawBo;
import com.gclife.agent.base.model.bo.law.LawItemBo;
import com.gclife.agent.base.model.config.AgentErrorConfigEnum;
import com.gclife.agent.base.model.config.AgentTermEnum;
import com.gclife.agent.core.jooq.tables.pojos.LawBranchPo;
import com.gclife.common.dao.base.impl.BaseDaoImpl;
import com.gclife.common.exception.RequestException;
import com.gclife.common.model.base.Users;
import com.gclife.common.model.config.TerminologyConfigEnum;
import com.gclife.common.util.AssertUtils;
import com.gclife.common.util.DateUtils;
import com.gclife.platform.model.response.BranchResponse;
import org.jooq.Condition;
import org.jooq.SelectConditionStep;
import org.jooq.SelectJoinStep;
import org.jooq.UpdateSetMoreStep;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

import static com.gclife.agent.core.jooq.Tables.LAW_BRANCH_CONFIG;
import static com.gclife.agent.core.jooq.tables.Algorithm.ALGORITHM;
import static com.gclife.agent.core.jooq.tables.Law.LAW;
import static com.gclife.agent.core.jooq.tables.LawBranch.LAW_BRANCH;
import static com.gclife.agent.core.jooq.tables.LawItem.LAW_ITEM;
import static com.gclife.agent.core.jooq.tables.LawItemAlgorithm.LAW_ITEM_ALGORITHM;

/**
 * <AUTHOR>
 * create 17-11-12
 * description: 流程开始
 */
@Component
public class LawExtDaoImpl extends BaseDaoImpl implements LawExtDao {

    /**
     * 根据机构ID获取，销售机构上的基本法
     *
     * @param branchIds 机构树
     * @return LawPo 基本法对象
     */
    @Override
    public List<LawBranchPo> loadLawPoByBranchId(List<String> branchIds) {
        List<LawBranchPo> lawBranchPos = new ArrayList<>();
        try {
            lawBranchPos = this.getDslContext()
                    .select(LAW_BRANCH.fields())
                    .from(LAW_BRANCH)
                    .where(LAW_BRANCH.ENABLED.eq(TerminologyConfigEnum.ENABLED.ENABLED.name()))
                    .and(LAW_BRANCH.BRANCH_ID.in(branchIds))
                    .and(LAW_BRANCH.VALID_FLAG.eq(TerminologyConfigEnum.VALID_FLAG.effective.name()))
                    .and(LAW_BRANCH.START_DATE.le(DateUtils.getCurrentDateToTime()))
                    .and(LAW_BRANCH.END_DATE.gt(DateUtils.getCurrentDateToTime()))
                    .fetchInto(LawBranchPo.class);
        } catch (Exception e) {
            this.getLogger().error(AgentErrorConfigEnum.AGENT_QUERY_LAW_FAIL.getValue());
            throw new RequestException(AgentErrorConfigEnum.AGENT_QUERY_LAW_FAIL);
        }
        return lawBranchPos;
    }


    @Override
    public LawBranchPo loadLawPoByParentBranchs(List<BranchResponse> parentBranchs) {
        LawBranchPo lawBranchPo = null;
        try {
            List<LawBranchPo> lawBranchPos = this.getDslContext()
                    .select(LAW_BRANCH.fields())
                    .from(LAW_BRANCH)
                    .where(LAW_BRANCH.ENABLED.eq(TerminologyConfigEnum.ENABLED.ENABLED.name()))
                    .and(LAW_BRANCH.BRANCH_ID.in(parentBranchs.stream().map(parentBranch -> parentBranch.getBranchId()).collect(Collectors.toList())))
                    .and(LAW_BRANCH.VALID_FLAG.eq(TerminologyConfigEnum.VALID_FLAG.effective.name()))
                    .fetchInto(LawBranchPo.class);
            boolean flag = false;
            //找最近机构配置的基本法
            for (BranchResponse branchLeafRespFc : parentBranchs) {
                for (LawBranchPo lawBranch : lawBranchPos) {
                    if (branchLeafRespFc.getBranchId().equals(lawBranch.getBranchId())) {
                        lawBranchPo = lawBranch;
                        flag = true;
                        break;
                    }
                }
                if (flag) {
                    break;
                }
            }
            AssertUtils.isNotNull(this.getLogger(), lawBranchPo, AgentErrorConfigEnum.AGENT_QUERY_LAW_IS_NOT_FOUND);
        } catch (Exception e) {
            this.getLogger().error(AgentErrorConfigEnum.AGENT_QUERY_LAW_FAIL.getValue());
            throw new RequestException(AgentErrorConfigEnum.AGENT_QUERY_LAW_FAIL);
        }
        return lawBranchPo;
    }

    /**
     * 根据机构ID获取，基本法中的算法
     *
     * @param lawId             基本法ID
     * @param calculateTypeCode 计算类型
     * @return
     */
    @Override
    public List<LawItemBo> loadAlgorithmByLawId(String lawId, String calculateTypeCode) {
        List<LawItemBo> algorithmPos = null;
        try {
            SelectConditionStep selectConditionStep = this.getDslContext()
                    .select(LAW_ITEM.fields())
                    .select(ALGORITHM.fields())
                    .select(LAW_ITEM_ALGORITHM.INDEX, LAW_ITEM_ALGORITHM.MAX_INDEX, LAW_ITEM_ALGORITHM.LAW_ITEM_ALGORITHM_ID)
                    .from(LAW)
                    .innerJoin(LAW_ITEM).on(LAW.LAW_ID.eq(LAW_ITEM.LAW_ID))
                    .innerJoin(LAW_ITEM_ALGORITHM).on(LAW_ITEM.LAW_ITEM_ID.eq(LAW_ITEM_ALGORITHM.LAW_ITEM_ID))
                    .innerJoin(ALGORITHM).on(ALGORITHM.ALGORITHM_CODE.eq(LAW_ITEM_ALGORITHM.ALGORITHM_CODE))
                    .where(LAW.LAW_ID.eq(lawId))
                    .and(LAW_ITEM.VALID_FLAG.eq(TerminologyConfigEnum.VALID_FLAG.effective.name()))
                    .and(LAW_ITEM_ALGORITHM.VALID_FLAG.eq(TerminologyConfigEnum.VALID_FLAG.effective.name()))
                    .and(ALGORITHM.VALID_FLAG.eq(TerminologyConfigEnum.VALID_FLAG.effective.name()));
            Optional.ofNullable(calculateTypeCode).ifPresent(code -> {
                selectConditionStep.and(LAW_ITEM.CALCULATE_TYPE_CODE.eq(code));
            });
            //按算法编码排序，然后再按照排序字段排序
            selectConditionStep.orderBy(LAW_ITEM.ITEM_INDEX.asc(), LAW_ITEM_ALGORITHM.INDEX.asc());
            algorithmPos = selectConditionStep.fetchInto(LawItemBo.class);
        } catch (Exception e) {
            this.getLogger().error(AgentErrorConfigEnum.AGENT_QUERY_LAW_FAIL.getValue());
            throw new RequestException(AgentErrorConfigEnum.AGENT_QUERY_LAW_FAIL);
        }
        return algorithmPos;
    }

    @Override
    public List<LawBo> getLawList(String keyword, String belongBranchId) {
        List<LawBo> lawBoList = null;
        try {
            SelectJoinStep selectJoinStep = this.getDslContext()
                    .select(LAW.fields())
                    .select(LAW_ITEM.LAW_ID.count().as("lawItemNum"))
                    .from(LAW_BRANCH_CONFIG)
                    .leftJoin(LAW).on(LAW_BRANCH_CONFIG.LAW_ID.eq(LAW.LAW_ID))
                    .leftJoin(LAW_ITEM).on(LAW_ITEM.LAW_ID.eq(LAW.LAW_ID)
                            .and(LAW_ITEM.CALCULATE_TYPE_CODE
                                    //算法类型：　ACHIEVEMENT：计算业绩．BONUS:计算销奖.COMMISSION:佣金
                                    //过滤
                                    .eq(AgentTermEnum.CALCULATE_TYPE.BONUS.name()).or(LAW_ITEM.CALCULATE_TYPE_CODE.eq(AgentTermEnum.CALCULATE_TYPE.COMMISSION.name())))
                            .and(LAW_ITEM.VALID_FLAG.eq(AgentTermEnum.VALID_FLAG.effective.name())));
            List<Condition> conditionList = new ArrayList<>();
            conditionList.add(LAW.VALID_FLAG.eq(AgentTermEnum.VALID_FLAG.effective.name()));
            //关键字搜索  基本法名称
            if (AssertUtils.isNotEmpty(keyword)) {
                conditionList.add(LAW.LAW_NAME.like("%" + keyword + "%"));
            }
            conditionList.add(LAW_BRANCH_CONFIG.TOP_BRANCH_ID.eq(belongBranchId));
            selectJoinStep.where(conditionList);
            //分组
            selectJoinStep.groupBy(LAW.LAW_ID);
            //时间倒序
            selectJoinStep.orderBy(LAW.CREATED_DATE.desc());
            System.out.println(selectJoinStep.toString());
            lawBoList = selectJoinStep.fetchInto(LawBo.class);
        } catch (Exception e) {
            this.getLogger().error(AgentErrorConfigEnum.AGENT_QUERY_LAW_LIST_ERROR.getValue());
            throw new RequestException(AgentErrorConfigEnum.AGENT_QUERY_LAW_LIST_ERROR);
        }
        return lawBoList;
    }

    @Override
    public List<LawBo> fetchByLawName(String lawName) {
        List<LawBo> lawBoList = new ArrayList<>();
        try {
            SelectJoinStep selectJoinStep = this.getDslContext()
                    .select(LAW.fields())
                    .from(LAW);
            List<Condition> conditionList = new ArrayList<>();
            //关键字搜索  基本法名称
            conditionList.add(LAW.LAW_NAME.eq(lawName));
            conditionList.add(LAW.VALID_FLAG.eq(AgentTermEnum.VALID_FLAG.effective.name()));
            selectJoinStep.where(conditionList);
            System.out.println(selectJoinStep.toString());
            lawBoList = selectJoinStep.fetchInto(LawBo.class);
        } catch (Exception e) {
            this.getLogger().error(AgentErrorConfigEnum.AGENT_SAVE_LAW_ERROR.getValue());
            throw new RequestException(AgentErrorConfigEnum.AGENT_SAVE_LAW_ERROR);
        }
        return lawBoList;
    }

    @Override
    public LawBo getLawById(String lawId) {
        LawBo lawBo = null;
        try {
            SelectJoinStep selectJoinStep = this.getDslContext()
                    .select(LAW.fields())
                    .from(LAW);
            List<Condition> conditionList = new ArrayList<>();
            //关键字搜索  基本法名称
            conditionList.add(LAW.LAW_ID.eq(lawId));
            conditionList.add(LAW.VALID_FLAG.eq(AgentTermEnum.VALID_FLAG.effective.name()));
            selectJoinStep.where(conditionList);
            System.out.println(selectJoinStep.toString());
            lawBo = (LawBo) selectJoinStep.fetchOneInto(LawBo.class);
        } catch (Exception e) {
            this.getLogger().error(AgentErrorConfigEnum.AGENT_SAVE_LAW_ERROR.getValue());
            throw new RequestException(AgentErrorConfigEnum.AGENT_SAVE_LAW_ERROR);
        }
        return lawBo;
    }

    @Override
    public int deleteLaw(String lawId, Users users) {
        int execute = 0;
        try {
            UpdateSetMoreStep updateSetMoreStep = this.getDslContext()
                    .update(LAW)
                    .set(LAW.VALID_FLAG, AgentTermEnum.VALID_FLAG.invalid.name())
                    .set(LAW.UPDATED_USER_ID,users.getUserId())
                    .set(LAW.UPDATED_DATE,System.currentTimeMillis());
            List<Condition> conditionList = new ArrayList<>();
            conditionList.add(LAW.LAW_ID.eq(lawId));
            conditionList.add(LAW.VALID_FLAG.eq(AgentTermEnum.VALID_FLAG.effective.name()));
            updateSetMoreStep.where(conditionList);
            System.out.println(updateSetMoreStep.toString());
            execute = updateSetMoreStep.execute();
        } catch (Exception e) {
            this.getLogger().error(AgentErrorConfigEnum.AGENT_DELETE_LAW_ERROR.getValue());
            throw new RequestException(AgentErrorConfigEnum.AGENT_DELETE_LAW_ERROR);
        }
        return execute;
    }
}