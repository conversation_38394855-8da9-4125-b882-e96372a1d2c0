package com.gclife.agent.base.dao;

import com.gclife.agent.base.model.bo.AgentDetailDo;
import com.gclife.agent.base.model.bo.AgentDo;
import com.gclife.agent.base.model.vo.AgentPagingVo;
import com.gclife.agent.core.jooq.tables.pojos.*;
import com.gclife.common.dao.base.BaseDao;
import com.gclife.common.model.BasePageRequest;

import java.util.List;

/**
 * <AUTHOR>
 *         create 2018-05-02
 *         description:　业务员基础dao类
 */
public interface AgentDetailBaseDao extends BaseDao {


    /**
     * 根据业务员ID 查下详情对象
     * @param agentId 业务员ID
     * @return AgentDo
     */
    public AgentDetailPo queryOneAgentDetailPoByAgentId(String agentId);

    /**
     * 根据业务员ID 查下详情对象
     * @param agentIds 业务员ID
     * @return AgentDo
     */
    public List<AgentDetailPo> queryListAgentDetailPoByAgentIds(List<String> agentIds);


    /**
     * 根据业务员手机号查询
     * @param mobiles 业务员ID
     * @return AgentDo
     */
    public List<AgentDetailDo> queryAgentDetailDoByMobiles(String... mobiles);

    /**
     * 根据业务员 编号查询
     * @param agentCode
     * @return
     */
    List<AgentDetailDo> queryAgentDetailDoByCodes(String... agentCode);

    /**
     * 根据手机号查询业务员详细信息
     * @param mobile 手机号
     * @return 业务员详细信息
     */
    AgentDetailPo queryOneAgentDetailByMobile(String mobile);

    /**
     * 查询需要提醒的证件到期信息
     * @return
     */
    List<AgentDetailPo> queryListAgentDetailPo(BasePageRequest basePageRequest);
}