package com.gclife.message.model.respone;

import com.gclife.common.model.BaseResponse;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

/**
 * <AUTHOR>
 * create 17-11-3
 * description:短信验证码生成的响应消息结构
 */
@Getter
@Setter
public class SmsVerifyCodeGeratorResponse extends BaseResponse {
    @ApiModelProperty(value = "发送标志:(Y/N),成功、失败",example = "Y")
    private String sendFlag;

    @ApiModelProperty(value = "发送结果，第三方服务返回的详细信息",example = "此次操作成功")
    private String sendResult;

    @ApiModelProperty(value = "国家区号",example = "+86")
    private String countryCode;

    @ApiModelProperty(value = "接收用户(手机号码/邮箱地址)",example = "接收用户(手机号码/邮箱地址)")
    private String mobile;

    @ApiModelProperty(value = "短信类型，注册：REGISTER，登录：LOGIN",example = "REGISTER")
    private String typeCode;

    @ApiModelProperty(value = "验证码发送类型，默认MOBILE(MOBILE/EMAIL)", example = "验证码发送类型(MOBILE/EMAIL)")
    private String verificationType;
}
