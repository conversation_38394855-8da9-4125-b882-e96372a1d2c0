package com.gclife.apply.report.service.impl;

import com.gclife.apply.core.jooq.tables.daos.ApplyApplicantDao;
import com.gclife.apply.core.jooq.tables.pojos.ApplyApplicantPo;
import com.gclife.apply.model.bo.ApplyGroupReportSyncApplicantBo;
import com.gclife.apply.model.bo.ApplyReportBo;
import com.gclife.apply.model.config.ApplyTermEnum;
import com.gclife.apply.model.respone.ApplyReportResponse;
import com.gclife.apply.report.service.ApplyReportBusinessService;
import com.gclife.apply.service.ApplyBaseService;
import com.gclife.common.model.BasePageRequest;
import com.gclife.common.model.ResultObject;
import com.gclife.common.model.config.TerminologyConfigEnum;
import com.gclife.common.service.impl.BaseBusinessServiceImpl;
import com.gclife.common.util.AssertUtils;
import com.gclife.common.util.DateUtils;
import com.gclife.party.api.CustomerManageApi;
import com.gclife.party.model.request.CustomerBusinessRequest;
import com.gclife.party.model.response.UserCustomerResponse;
import com.gclife.product.model.config.ProductTermEnum;
import com.gclife.report.api.model.response.ActualPerformanceReportBo;
import com.gclife.report.api.model.response.SaleApplyPolicyBo;
import com.gclife.report.api.model.response.ServiceChargeBankChannelBo;
import org.modelmapper.TypeToken;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

/**
 * <AUTHOR>
 *         create 18-11-12
 *         description:
 */
@Service
public class ApplyReportBusinessServiceImpl extends BaseBusinessServiceImpl implements ApplyReportBusinessService {
    @Autowired
    private ApplyBaseService applyBaseService;
    @Autowired
    private CustomerManageApi customerManageApi;
    @Autowired
    private ApplyApplicantDao applyApplicantDao;

    /**
     * 查询投保单信息跟投保人信息（收付费明细报表）
     * @param businessId payment关联投保单id
     */
    @Override
    public ResultObject<List<ApplyReportResponse>> queryListApplyReportBo(List<String> businessId) {
        ResultObject<List<ApplyReportResponse>> resultObject = new ResultObject<>();
        List<ApplyReportBo> applyReportBos = applyBaseService.queryApplyReport(businessId);
        if (!AssertUtils.isNotEmpty(applyReportBos)) {
            return resultObject;
        }
        List<ApplyReportResponse> applyListResponses = (List<ApplyReportResponse>)this.converterList(applyReportBos,new TypeToken<List<ApplyReportResponse>>() {}.getType());
        resultObject.setData(applyListResponses);
        return resultObject;
    }

    /**
     * @param password
     * @param updateFlag
     * @return
     */
    @Override
    @Transactional
    public ResultObject syncApplicantCustomer(String password, String updateFlag) {
        ResultObject resultObject = new ResultObject();
        if (!"GCLIFE".equals(password)) {
            return resultObject;
        }

        List<ApplyGroupReportSyncApplicantBo> syncApplicantBos = applyBaseService.querySyncApplicantCustomer();
        if (!AssertUtils.isNotEmpty(syncApplicantBos)) {
            return resultObject;
        }
        List<CustomerBusinessRequest> customerBusinessReqFcList = new ArrayList<>();
        syncApplicantBos.forEach(policyGroupReportSyncApplicantBo -> {
            CustomerBusinessRequest customerBusinessRequest = new CustomerBusinessRequest();
            customerBusinessRequest.setName(policyGroupReportSyncApplicantBo.getDelegateName());
            customerBusinessRequest.setIdNo(policyGroupReportSyncApplicantBo.getDelegateIdNo());
            customerBusinessRequest.setIdType(policyGroupReportSyncApplicantBo.getDelegateIdType());
            customerBusinessRequest.setBirthday(policyGroupReportSyncApplicantBo.getDelegateBirthday());
            customerBusinessRequest.setMobile(policyGroupReportSyncApplicantBo.getDelegateMobile());
            customerBusinessRequest.setUserId(policyGroupReportSyncApplicantBo.getAgentId());
            //准客户不能设置勋章，否则会被当做客户存起来
//            customerBusinessRequest.setMedalNo(ApplyTermEnum.CUSTOMER_TYPE.APPLICANT_DELEGATE.name());
            customerBusinessReqFcList.add(customerBusinessRequest);
        });
        ResultObject<List<UserCustomerResponse>> listResultObject = customerManageApi.saveCustomerMessagePoList(customerBusinessReqFcList);
        List<ApplyApplicantPo> applyApplicantPos = applyBaseService.queryApplicantCustomer();

        if (!AssertUtils.isResultObjectListDataNull(listResultObject) && AssertUtils.isNotEmpty(applyApplicantPos)) {
            List<ApplyApplicantPo> updatePolicyApplicantPos = new ArrayList<>();
            listResultObject.getData().forEach(userCustomerResponse -> {
                applyApplicantPos.stream().filter(policyApplicantPo -> policyApplicantPo.getDelegateIdNo().equals(userCustomerResponse.getIdNo())
                        && policyApplicantPo.getDelegateIdType().equals(userCustomerResponse.getIdType())).findFirst().ifPresent(policyApplicantPo -> {
                    policyApplicantPo.setDelegateCustomerId(userCustomerResponse.getCustomerId());
                    if (AssertUtils.isNotEmpty(updateFlag) && TerminologyConfigEnum.WHETHER.YES.name().equals(updateFlag)) {
                        policyApplicantPo.setUpdatedDate(DateUtils.getCurrentTime());
                    }
                    updatePolicyApplicantPos.add(policyApplicantPo);
                });
            });
            applyApplicantDao.update(updatePolicyApplicantPos);
        }
        return resultObject;
    }


    @Override
    public ResultObject<List<ActualPerformanceReportBo>> queryActualPerformance(List<String> policyIdList) {
        ResultObject<List<ActualPerformanceReportBo>> resultObject = new ResultObject<>();
        List<ActualPerformanceReportBo> actualPerformanceReportBoList = applyBaseService.queryActualPerformance(policyIdList);
        // 设置产品名称
        if (AssertUtils.isNotEmpty(actualPerformanceReportBoList)) {
            List<ProductTermEnum.PRODUCT> products = Arrays.asList(ProductTermEnum.PRODUCT.values());
            actualPerformanceReportBoList.forEach(actualPerformanceReportBo -> {
                products.stream().filter(product -> product.id().equals(actualPerformanceReportBo.getProductId()))
                        .findFirst().ifPresent(product -> actualPerformanceReportBo.setProductName(product.desc()));
            });
        }
        resultObject.setData(actualPerformanceReportBoList);
        return resultObject;
    }

    @Override
    public ResultObject<List<ServiceChargeBankChannelBo>> syncApplyServiceChargeBankChannel(BasePageRequest basePageRequest, String syncDate) {
        ResultObject<List<ServiceChargeBankChannelBo>> resultObject = new ResultObject<>();
        List<ServiceChargeBankChannelBo> actualPerformanceReportBoList = applyBaseService.syncApplyServiceChargeBankChannel(basePageRequest,syncDate);
        resultObject.setData(actualPerformanceReportBoList);
        return resultObject;
    }

    @Override
    public ResultObject<List<SaleApplyPolicyBo>> syncSaleApplyPolicy(BasePageRequest basePageRequest, String syncDate) {
        ResultObject<List<SaleApplyPolicyBo>> resultObject = new ResultObject<>();
        List<SaleApplyPolicyBo> actualPerformanceReportBoList = applyBaseService.syncSaleApplyPolicy(basePageRequest,syncDate);
        resultObject.setData(actualPerformanceReportBoList);
        return resultObject;
    }
}