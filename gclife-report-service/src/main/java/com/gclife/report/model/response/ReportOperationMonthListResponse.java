package com.gclife.report.model.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * create 2020/3/31 上午10:46
 * description:
 */
@Data
public class ReportOperationMonthListResponse {
    @ApiModelProperty(example = "统计日期月份")
    private String statisticalDateFormat;

    // 运营人力合计 留空，线下统计当前当前运营部员工人数
    @ApiModelProperty(example = "人数合计")
    private Integer employeeSum = 0;
    @ApiModelProperty(example = "非柬员工")
    private Integer employeeCambodiaSum = 0;
    @ApiModelProperty(example = "柬籍员工")
    private Integer employeeNotCambodiaSum = 0;
    // 业务
    private BigDecimal applyPolicyGroupTotalPremiumSum = new BigDecimal(0);
    @ApiModelProperty(example = "新单业务量")
    private BigDecimal applyPolicyTotalPremiumSum = new BigDecimal(0);
    @ApiModelProperty(example = "其中短期业务量")
    private BigDecimal applyPolicyLongTotalPremiumSum = new BigDecimal(0);
    @ApiModelProperty(example = "其中长期寿险量")
    private BigDecimal applyPolicyShortTotalPremiumSum = new BigDecimal(0);
    @ApiModelProperty(example = "长寿续期业务量")
    private BigDecimal longTotalPremiumSum = new BigDecimal(0);
    @ApiModelProperty(example = "短期续期件数")
    private Integer shortRenewalInsuranceSum = 0;
    @ApiModelProperty(example = "长寿续期件数")
    private Integer longRenewalSum = 0;
    @ApiModelProperty(example = "短期续保件综合")
    private BigDecimal shortRenewalInsuranceTotalPremiumSum = new BigDecimal(0);
    @ApiModelProperty(example = "续期率（件数）")
    private BigDecimal renewalRatePremium = new BigDecimal(0);
    @ApiModelProperty(example = "续期率（保费）")
    private BigDecimal renewalRateCount= new BigDecimal(0);

    //工作量
    @ApiModelProperty(example = "工作量-新单处理件数")
    private Integer newPolicyNum = 0;
    @ApiModelProperty(example = "工作量-续单处理件数")
    private Integer newRenewalNum = 0;
    @ApiModelProperty(example = "工作量-撤单件数")
    private Integer workloadCancellationsNum = 0;
    @ApiModelProperty(example = "工作量-退保件数")
    private Integer workloadSurrenderNum = 0;
    @ApiModelProperty(example = "工作量-保全件数")
    private Integer workloadEndorseNum = 0;
    @ApiModelProperty(example = "工作量-理赔件数")
    private Integer workloadClaimsNum = 0;
    @ApiModelProperty(example = "工作量-生存给付件数")
    private Integer workloadSurvivalBenefitsNum = 0;
    @ApiModelProperty(example = "工作量-处理件数合计")
    private Integer processedTotalSum = 0;

    //有效保单积存
    @ApiModelProperty(example = "有效保单积存-长期寿险")
    private Integer longTermLifeInsuranceSum = 0;
    @ApiModelProperty(example = "有效保单积存-短期")
    private Integer shortTermInsuranceSum = 0;
    @ApiModelProperty(example = "有效保单积存-其它")
    private Integer otherInsuranceSum = 0;

    //理赔
    @ApiModelProperty(example = "理赔-理赔件数")
    private Integer claimsNum = 0;
    @ApiModelProperty(example = "理赔-理赔金额")
    private BigDecimal claimAmount = BigDecimal.ZERO;
    @ApiModelProperty(example = "理赔-生存给付件数")
    private Integer survivalBenefitsNum = 0;
    @ApiModelProperty(example = "理赔-生存给付金额")
    private BigDecimal survivalBenefitsAmount = BigDecimal.ZERO;

    //撤单
    @ApiModelProperty(example = "撤单-撤单件数")
    private Integer cancellationsNum = 0;
    @ApiModelProperty(example = "撤单-撤单返还金额")
    private BigDecimal refundAmountForCancellation = BigDecimal.ZERO;

    //退保
    @ApiModelProperty(example = "退保-退保件数")
    private Integer surrenderNum = 0;
    @ApiModelProperty(example = "退保-退保金额")
    private BigDecimal surrenderAmount = BigDecimal.ZERO;

    //客户量
    @ApiModelProperty(example = "客户量-历年投保人累计")
    private Integer applicantAccumulatedOverTheYears = 0;
    @ApiModelProperty(example = "客户量-历年被保险人累计")
    private Integer insuredAccumulatedOverTheYears = 0;


    public Integer getClaimsNum() {
        return workloadClaimsNum;
    }

    public Integer getCancellationsNum() {
        return workloadCancellationsNum;
    }


    public Integer getSurrenderNum() {
        return workloadSurrenderNum;
    }

}
