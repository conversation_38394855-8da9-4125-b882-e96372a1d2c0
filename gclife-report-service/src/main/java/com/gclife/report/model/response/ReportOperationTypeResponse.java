package com.gclife.report.model.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class ReportOperationTypeResponse {
    @ApiModelProperty(example = "件数")
    private Integer number;
    @ApiModelProperty(example = "保费")
    private String totalPremium;
    @ApiModelProperty(example = "新增投保人人数")
    private Integer newApplicantNumber;
    @ApiModelProperty(example = "新增被保人人数")
    private Integer newInsuredNumber;
}
