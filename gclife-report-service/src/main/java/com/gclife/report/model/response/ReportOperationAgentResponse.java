package com.gclife.report.model.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class ReportOperationAgentResponse {
    @ApiModelProperty(example = "业务员类型名称")
    private String agentTypeName;

    //新单承保
    @ApiModelProperty(example = "新单承保")
    private ReportOperationTypeResponse  approvePolicy;

    //犹豫期退保
    @ApiModelProperty(example = "犹豫期退保")
    private ReportOperationTypeResponse  hesitationRevoke;

    //退保
    @ApiModelProperty(example = "非犹豫期退保")
    private ReportOperationTypeResponse  surrender;

    //续期应收
    @ApiModelProperty(example = "续期应收")
    private ReportOperationTypeResponse  renewalReceivable;

    //续期实收
    @ApiModelProperty(example = "续期实收")
    private ReportOperationTypeResponse  renewalActual;

    //续保应收
    @ApiModelProperty(example = "续保应收")
    private ReportOperationTypeResponse  renewalInsuranceReceivable;

    //续保实收
    @ApiModelProperty(example = "续保实收")
    private ReportOperationTypeResponse  renewalInsuranceActual;
}
