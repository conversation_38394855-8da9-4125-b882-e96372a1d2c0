package com.gclife.agent.form;

import io.swagger.annotations.ApiModelProperty;

/**
 * <AUTHOR>
 *         create 17-11-6
 *         description:
 */
public class AgentRegisterRequest {

    @ApiModelProperty(value = "用户ID", example = "lb", required = true)
    private String userId;
    @ApiModelProperty(value = "手机号", example = "18898617356", required = true)
    private String mobile;
    @ApiModelProperty(value = "推荐人手机号", example = "18575500594", required = true)
    private String recommendAgentMobile;
    @ApiModelProperty(value = "渠道类型", example = "gclife_agent_app", required = true)
    private String deviceChannel;

    public String getDeviceChannel() {
        return deviceChannel;
    }

    public void setDeviceChannel(String deviceChannel) {
        this.deviceChannel = deviceChannel;
    }

    public String getUserId() {
        return userId;
    }

    public void setUserId(String userId) {
        this.userId = userId;
    }

    public String getMobile() {
        return mobile;
    }

    public void setMobile(String mobile) {
        this.mobile = mobile;
    }

    public String getRecommendAgentMobile() {
        return recommendAgentMobile;
    }

    public void setRecommendAgentMobile(String recommendAgentMobile) {
        this.recommendAgentMobile = recommendAgentMobile;
    }
}