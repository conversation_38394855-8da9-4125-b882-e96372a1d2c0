/*
 * This file is generated by jOOQ.
*/
package com.gclife.commission.core.jooq.tables;


import com.gclife.commission.core.jooq.Keys;
import com.gclife.commission.core.jooq.Public;
import com.gclife.commission.core.jooq.tables.records.ProcessCloseRecord;

import java.util.Arrays;
import java.util.List;

import javax.annotation.Generated;

import org.jooq.Field;
import org.jooq.Schema;
import org.jooq.Table;
import org.jooq.TableField;
import org.jooq.UniqueKey;
import org.jooq.impl.TableImpl;


/**
 * 关闭账户流程控制
 */
@Generated(
    value = {
        "http://www.jooq.org",
        "jOOQ version:3.9.4"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class ProcessClose extends TableImpl<ProcessCloseRecord> {

    private static final long serialVersionUID = -1295589979;

    /**
     * The reference instance of <code>public.process_close</code>
     */
    public static final ProcessClose PROCESS_CLOSE = new ProcessClose();

    /**
     * The class holding records for this type
     */
    @Override
    public Class<ProcessCloseRecord> getRecordType() {
        return ProcessCloseRecord.class;
    }

    /**
     * The column <code>public.process_close.process_close_id</code>. 	发佣控制记录ID	  
     */
    public final TableField<ProcessCloseRecord, String> PROCESS_CLOSE_ID = createField("process_close_id", org.jooq.impl.SQLDataType.VARCHAR.length(128).nullable(false), this, "	发佣控制记录ID	  ");

    /**
     * The column <code>public.process_close.branch_id</code>. 	机构ID	  
     */
    public final TableField<ProcessCloseRecord, String> BRANCH_ID = createField("branch_id", org.jooq.impl.SQLDataType.VARCHAR.length(128), this, "	机构ID	  ");

    /**
     * The column <code>public.process_close.biz_year_month</code>. 	业务年月	  
     */
    public final TableField<ProcessCloseRecord, String> BIZ_YEAR_MONTH = createField("biz_year_month", org.jooq.impl.SQLDataType.VARCHAR.length(128), this, "	业务年月	  ");

    /**
     * The column <code>public.process_close.close_status</code>. 	关账状态	  
     */
    public final TableField<ProcessCloseRecord, String> CLOSE_STATUS = createField("close_status", org.jooq.impl.SQLDataType.VARCHAR.length(128), this, "	关账状态	  ");

    /**
     * The column <code>public.process_close.close_date</code>. 	关账时间	  
     */
    public final TableField<ProcessCloseRecord, Long> CLOSE_DATE = createField("close_date", org.jooq.impl.SQLDataType.BIGINT, this, "	关账时间	  ");

    /**
     * The column <code>public.process_close.close_user</code>. 	关账用户	  
     */
    public final TableField<ProcessCloseRecord, String> CLOSE_USER = createField("close_user", org.jooq.impl.SQLDataType.VARCHAR.length(128), this, "	关账用户	  ");

    /**
     * The column <code>public.process_close.valid_flag</code>. 	有效标识(effective:有效，invalid:失效)	  
     */
    public final TableField<ProcessCloseRecord, String> VALID_FLAG = createField("valid_flag", org.jooq.impl.SQLDataType.VARCHAR.length(128), this, "	有效标识(effective:有效，invalid:失效)	  ");

    /**
     * The column <code>public.process_close.created_user_id</code>. 	创建人	  
     */
    public final TableField<ProcessCloseRecord, String> CREATED_USER_ID = createField("created_user_id", org.jooq.impl.SQLDataType.VARCHAR.length(128), this, "	创建人	  ");

    /**
     * The column <code>public.process_close.created_date</code>. 	创建时间	  
     */
    public final TableField<ProcessCloseRecord, Long> CREATED_DATE = createField("created_date", org.jooq.impl.SQLDataType.BIGINT, this, "	创建时间	  ");

    /**
     * The column <code>public.process_close.updated_user_id</code>. 	更新人	  
     */
    public final TableField<ProcessCloseRecord, String> UPDATED_USER_ID = createField("updated_user_id", org.jooq.impl.SQLDataType.VARCHAR.length(128), this, "	更新人	  ");

    /**
     * The column <code>public.process_close.updated_date</code>. 	更新时间	  
     */
    public final TableField<ProcessCloseRecord, Long> UPDATED_DATE = createField("updated_date", org.jooq.impl.SQLDataType.BIGINT, this, "	更新时间	  ");

    /**
     * Create a <code>public.process_close</code> table reference
     */
    public ProcessClose() {
        this("process_close", null);
    }

    /**
     * Create an aliased <code>public.process_close</code> table reference
     */
    public ProcessClose(String alias) {
        this(alias, PROCESS_CLOSE);
    }

    private ProcessClose(String alias, Table<ProcessCloseRecord> aliased) {
        this(alias, aliased, null);
    }

    private ProcessClose(String alias, Table<ProcessCloseRecord> aliased, Field<?>[] parameters) {
        super(alias, null, aliased, parameters, "关闭账户流程控制");
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Schema getSchema() {
        return Public.PUBLIC;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public UniqueKey<ProcessCloseRecord> getPrimaryKey() {
        return Keys.PROCESS_CLOSE_PROCESS_CLOSE_ID_PK;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public List<UniqueKey<ProcessCloseRecord>> getKeys() {
        return Arrays.<UniqueKey<ProcessCloseRecord>>asList(Keys.PROCESS_CLOSE_PROCESS_CLOSE_ID_PK);
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public ProcessClose as(String alias) {
        return new ProcessClose(alias, this);
    }

    /**
     * Rename this table
     */
    @Override
    public ProcessClose rename(String name) {
        return new ProcessClose(name, null);
    }
}
