/*
 * This file is generated by jOOQ.
*/
package com.gclife.commission.core.jooq.tables.records;


import com.gclife.commission.core.jooq.tables.CommissionBonus;

import java.math.BigDecimal;

import javax.annotation.Generated;

import org.jooq.Field;
import org.jooq.Record1;
import org.jooq.Record11;
import org.jooq.Row11;
import org.jooq.impl.UpdatableRecordImpl;


/**
 * 销奖表
 */
@Generated(
    value = {
        "http://www.jooq.org",
        "jOOQ version:3.9.4"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class CommissionBonusRecord extends UpdatableRecordImpl<CommissionBonusRecord> implements Record11<String, String, String, BigDecimal, String, BigDecimal, String, String, Long, String, Long> {

    private static final long serialVersionUID = 1451670534;

    /**
     * Setter for <code>public.commission_bonus.commission_bonus_id</code>. 	主键ID	  
     */
    public void setCommissionBonusId(String value) {
        set(0, value);
    }

    /**
     * Getter for <code>public.commission_bonus.commission_bonus_id</code>. 	主键ID	  
     */
    public String getCommissionBonusId() {
        return (String) get(0);
    }

    /**
     * Setter for <code>public.commission_bonus.branch_id</code>. 销售机构(营业部)
     */
    public void setBranchId(String value) {
        set(1, value);
    }

    /**
     * Getter for <code>public.commission_bonus.branch_id</code>. 销售机构(营业部)
     */
    public String getBranchId() {
        return (String) get(1);
    }

    /**
     * Setter for <code>public.commission_bonus.agent_id</code>. 	代理人ID	  
     */
    public void setAgentId(String value) {
        set(2, value);
    }

    /**
     * Getter for <code>public.commission_bonus.agent_id</code>. 	代理人ID	  
     */
    public String getAgentId() {
        return (String) get(2);
    }

    /**
     * Setter for <code>public.commission_bonus.bonus_amount</code>. 	奖励金额	  
     */
    public void setBonusAmount(BigDecimal value) {
        set(3, value);
    }

    /**
     * Getter for <code>public.commission_bonus.bonus_amount</code>. 	奖励金额	  
     */
    public BigDecimal getBonusAmount() {
        return (BigDecimal) get(3);
    }

    /**
     * Setter for <code>public.commission_bonus.bonus_year_month</code>. 	奖励归属年月	  
     */
    public void setBonusYearMonth(String value) {
        set(4, value);
    }

    /**
     * Getter for <code>public.commission_bonus.bonus_year_month</code>. 	奖励归属年月	  
     */
    public String getBonusYearMonth() {
        return (String) get(4);
    }

    /**
     * Setter for <code>public.commission_bonus.rate</code>. 	奖励率(%)	  
     */
    public void setRate(BigDecimal value) {
        set(5, value);
    }

    /**
     * Getter for <code>public.commission_bonus.rate</code>. 	奖励率(%)	  
     */
    public BigDecimal getRate() {
        return (BigDecimal) get(5);
    }

    /**
     * Setter for <code>public.commission_bonus.law_item_id</code>. 	基本法项目ID	  
     */
    public void setLawItemId(String value) {
        set(6, value);
    }

    /**
     * Getter for <code>public.commission_bonus.law_item_id</code>. 	基本法项目ID	  
     */
    public String getLawItemId() {
        return (String) get(6);
    }

    /**
     * Setter for <code>public.commission_bonus.created_user_id</code>. 	创建人	  
     */
    public void setCreatedUserId(String value) {
        set(7, value);
    }

    /**
     * Getter for <code>public.commission_bonus.created_user_id</code>. 	创建人	  
     */
    public String getCreatedUserId() {
        return (String) get(7);
    }

    /**
     * Setter for <code>public.commission_bonus.created_date</code>. 	创建时间	  
     */
    public void setCreatedDate(Long value) {
        set(8, value);
    }

    /**
     * Getter for <code>public.commission_bonus.created_date</code>. 	创建时间	  
     */
    public Long getCreatedDate() {
        return (Long) get(8);
    }

    /**
     * Setter for <code>public.commission_bonus.updated_user_id</code>. 	更新人	  
     */
    public void setUpdatedUserId(String value) {
        set(9, value);
    }

    /**
     * Getter for <code>public.commission_bonus.updated_user_id</code>. 	更新人	  
     */
    public String getUpdatedUserId() {
        return (String) get(9);
    }

    /**
     * Setter for <code>public.commission_bonus.updated_date</code>. 	更新时间	  
     */
    public void setUpdatedDate(Long value) {
        set(10, value);
    }

    /**
     * Getter for <code>public.commission_bonus.updated_date</code>. 	更新时间	  
     */
    public Long getUpdatedDate() {
        return (Long) get(10);
    }

    // -------------------------------------------------------------------------
    // Primary key information
    // -------------------------------------------------------------------------

    /**
     * {@inheritDoc}
     */
    @Override
    public Record1<String> key() {
        return (Record1) super.key();
    }

    // -------------------------------------------------------------------------
    // Record11 type implementation
    // -------------------------------------------------------------------------

    /**
     * {@inheritDoc}
     */
    @Override
    public Row11<String, String, String, BigDecimal, String, BigDecimal, String, String, Long, String, Long> fieldsRow() {
        return (Row11) super.fieldsRow();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Row11<String, String, String, BigDecimal, String, BigDecimal, String, String, Long, String, Long> valuesRow() {
        return (Row11) super.valuesRow();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<String> field1() {
        return CommissionBonus.COMMISSION_BONUS.COMMISSION_BONUS_ID;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<String> field2() {
        return CommissionBonus.COMMISSION_BONUS.BRANCH_ID;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<String> field3() {
        return CommissionBonus.COMMISSION_BONUS.AGENT_ID;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<BigDecimal> field4() {
        return CommissionBonus.COMMISSION_BONUS.BONUS_AMOUNT;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<String> field5() {
        return CommissionBonus.COMMISSION_BONUS.BONUS_YEAR_MONTH;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<BigDecimal> field6() {
        return CommissionBonus.COMMISSION_BONUS.RATE;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<String> field7() {
        return CommissionBonus.COMMISSION_BONUS.LAW_ITEM_ID;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<String> field8() {
        return CommissionBonus.COMMISSION_BONUS.CREATED_USER_ID;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<Long> field9() {
        return CommissionBonus.COMMISSION_BONUS.CREATED_DATE;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<String> field10() {
        return CommissionBonus.COMMISSION_BONUS.UPDATED_USER_ID;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<Long> field11() {
        return CommissionBonus.COMMISSION_BONUS.UPDATED_DATE;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public String value1() {
        return getCommissionBonusId();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public String value2() {
        return getBranchId();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public String value3() {
        return getAgentId();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public BigDecimal value4() {
        return getBonusAmount();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public String value5() {
        return getBonusYearMonth();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public BigDecimal value6() {
        return getRate();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public String value7() {
        return getLawItemId();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public String value8() {
        return getCreatedUserId();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Long value9() {
        return getCreatedDate();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public String value10() {
        return getUpdatedUserId();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Long value11() {
        return getUpdatedDate();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public CommissionBonusRecord value1(String value) {
        setCommissionBonusId(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public CommissionBonusRecord value2(String value) {
        setBranchId(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public CommissionBonusRecord value3(String value) {
        setAgentId(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public CommissionBonusRecord value4(BigDecimal value) {
        setBonusAmount(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public CommissionBonusRecord value5(String value) {
        setBonusYearMonth(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public CommissionBonusRecord value6(BigDecimal value) {
        setRate(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public CommissionBonusRecord value7(String value) {
        setLawItemId(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public CommissionBonusRecord value8(String value) {
        setCreatedUserId(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public CommissionBonusRecord value9(Long value) {
        setCreatedDate(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public CommissionBonusRecord value10(String value) {
        setUpdatedUserId(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public CommissionBonusRecord value11(Long value) {
        setUpdatedDate(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public CommissionBonusRecord values(String value1, String value2, String value3, BigDecimal value4, String value5, BigDecimal value6, String value7, String value8, Long value9, String value10, Long value11) {
        value1(value1);
        value2(value2);
        value3(value3);
        value4(value4);
        value5(value5);
        value6(value6);
        value7(value7);
        value8(value8);
        value9(value9);
        value10(value10);
        value11(value11);
        return this;
    }

    // -------------------------------------------------------------------------
    // Constructors
    // -------------------------------------------------------------------------

    /**
     * Create a detached CommissionBonusRecord
     */
    public CommissionBonusRecord() {
        super(CommissionBonus.COMMISSION_BONUS);
    }

    /**
     * Create a detached, initialised CommissionBonusRecord
     */
    public CommissionBonusRecord(String commissionBonusId, String branchId, String agentId, BigDecimal bonusAmount, String bonusYearMonth, BigDecimal rate, String lawItemId, String createdUserId, Long createdDate, String updatedUserId, Long updatedDate) {
        super(CommissionBonus.COMMISSION_BONUS);

        set(0, commissionBonusId);
        set(1, branchId);
        set(2, agentId);
        set(3, bonusAmount);
        set(4, bonusYearMonth);
        set(5, rate);
        set(6, lawItemId);
        set(7, createdUserId);
        set(8, createdDate);
        set(9, updatedUserId);
        set(10, updatedDate);
    }
}
