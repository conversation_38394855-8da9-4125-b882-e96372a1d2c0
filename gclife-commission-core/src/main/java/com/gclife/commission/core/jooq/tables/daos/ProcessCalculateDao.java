/*
 * This file is generated by jOOQ.
*/
package com.gclife.commission.core.jooq.tables.daos;


import com.gclife.commission.core.jooq.tables.ProcessCalculate;
import com.gclife.commission.core.jooq.tables.pojos.ProcessCalculatePo;
import com.gclife.commission.core.jooq.tables.records.ProcessCalculateRecord;

import java.util.List;

import javax.annotation.Generated;

import org.jooq.Configuration;
import org.jooq.impl.DAOImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;


/**
 * 佣金计算流程控制
 */
@Generated(
    value = {
        "http://www.jooq.org",
        "jOOQ version:3.9.4"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
@Repository
public class ProcessCalculateDao extends DAOImpl<ProcessCalculateRecord, ProcessCalculatePo, String> {

    /**
     * Create a new ProcessCalculateDao without any configuration
     */
    public ProcessCalculateDao() {
        super(ProcessCalculate.PROCESS_CALCULATE, ProcessCalculatePo.class);
    }

    /**
     * Create a new ProcessCalculateDao with an attached configuration
     */
    @Autowired
    public ProcessCalculateDao(Configuration configuration) {
        super(ProcessCalculate.PROCESS_CALCULATE, ProcessCalculatePo.class, configuration);
    }

    /**
     * {@inheritDoc}
     */
    @Override
    protected String getId(ProcessCalculatePo object) {
        return object.getProcessCalculateId();
    }

    /**
     * Fetch records that have <code>process_calculate_id IN (values)</code>
     */
    public List<ProcessCalculatePo> fetchByProcessCalculateId(String... values) {
        return fetch(ProcessCalculate.PROCESS_CALCULATE.PROCESS_CALCULATE_ID, values);
    }

    /**
     * Fetch a unique record that has <code>process_calculate_id = value</code>
     */
    public ProcessCalculatePo fetchOneByProcessCalculateId(String value) {
        return fetchOne(ProcessCalculate.PROCESS_CALCULATE.PROCESS_CALCULATE_ID, value);
    }

    /**
     * Fetch records that have <code>branch_id IN (values)</code>
     */
    public List<ProcessCalculatePo> fetchByBranchId(String... values) {
        return fetch(ProcessCalculate.PROCESS_CALCULATE.BRANCH_ID, values);
    }

    /**
     * Fetch records that have <code>biz_year_month IN (values)</code>
     */
    public List<ProcessCalculatePo> fetchByBizYearMonth(String... values) {
        return fetch(ProcessCalculate.PROCESS_CALCULATE.BIZ_YEAR_MONTH, values);
    }

    /**
     * Fetch records that have <code>calculate_status IN (values)</code>
     */
    public List<ProcessCalculatePo> fetchByCalculateStatus(String... values) {
        return fetch(ProcessCalculate.PROCESS_CALCULATE.CALCULATE_STATUS, values);
    }

    /**
     * Fetch records that have <code>calculate_date IN (values)</code>
     */
    public List<ProcessCalculatePo> fetchByCalculateDate(Long... values) {
        return fetch(ProcessCalculate.PROCESS_CALCULATE.CALCULATE_DATE, values);
    }

    /**
     * Fetch records that have <code>calculate_user IN (values)</code>
     */
    public List<ProcessCalculatePo> fetchByCalculateUser(String... values) {
        return fetch(ProcessCalculate.PROCESS_CALCULATE.CALCULATE_USER, values);
    }

    /**
     * Fetch records that have <code>valid_flag IN (values)</code>
     */
    public List<ProcessCalculatePo> fetchByValidFlag(String... values) {
        return fetch(ProcessCalculate.PROCESS_CALCULATE.VALID_FLAG, values);
    }

    /**
     * Fetch records that have <code>created_user_id IN (values)</code>
     */
    public List<ProcessCalculatePo> fetchByCreatedUserId(String... values) {
        return fetch(ProcessCalculate.PROCESS_CALCULATE.CREATED_USER_ID, values);
    }

    /**
     * Fetch records that have <code>created_date IN (values)</code>
     */
    public List<ProcessCalculatePo> fetchByCreatedDate(Long... values) {
        return fetch(ProcessCalculate.PROCESS_CALCULATE.CREATED_DATE, values);
    }

    /**
     * Fetch records that have <code>updated_user_id IN (values)</code>
     */
    public List<ProcessCalculatePo> fetchByUpdatedUserId(String... values) {
        return fetch(ProcessCalculate.PROCESS_CALCULATE.UPDATED_USER_ID, values);
    }

    /**
     * Fetch records that have <code>updated_date IN (values)</code>
     */
    public List<ProcessCalculatePo> fetchByUpdatedDate(Long... values) {
        return fetch(ProcessCalculate.PROCESS_CALCULATE.UPDATED_DATE, values);
    }
}
