/*
 * This file is generated by jOOQ.
*/
package com.gclife.commission.core.jooq.tables.pojos;


import com.gclife.common.model.pojo.BasePojo;

import java.io.Serializable;

import javax.annotation.Generated;


/**
 * 基本法机构关联表
 */
@Generated(
    value = {
        "http://www.jooq.org",
        "jOOQ version:3.9.4"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class LawBranchPo extends BasePojo implements Serializable {

    private static final long serialVersionUID = -154691808;

    private String lawBranchId;
    private String lawId;
    private String branchId;
    private String enabled;
    private String validFlag;
    private String description;
    private String createdUserId;
    private Long   createdDate;
    private String updatedUserId;
    private Long   updatedDate;

    public LawBranchPo() {}

    public LawBranchPo(LawBranchPo value) {
        this.lawBranchId = value.lawBranchId;
        this.lawId = value.lawId;
        this.branchId = value.branchId;
        this.enabled = value.enabled;
        this.validFlag = value.validFlag;
        this.description = value.description;
        this.createdUserId = value.createdUserId;
        this.createdDate = value.createdDate;
        this.updatedUserId = value.updatedUserId;
        this.updatedDate = value.updatedDate;
    }

    public LawBranchPo(
        String lawBranchId,
        String lawId,
        String branchId,
        String enabled,
        String validFlag,
        String description,
        String createdUserId,
        Long   createdDate,
        String updatedUserId,
        Long   updatedDate
    ) {
        this.lawBranchId = lawBranchId;
        this.lawId = lawId;
        this.branchId = branchId;
        this.enabled = enabled;
        this.validFlag = validFlag;
        this.description = description;
        this.createdUserId = createdUserId;
        this.createdDate = createdDate;
        this.updatedUserId = updatedUserId;
        this.updatedDate = updatedDate;
    }

    public String getLawBranchId() {
        return this.lawBranchId;
    }

    public void setLawBranchId(String lawBranchId) {
        this.lawBranchId = lawBranchId;
    }

    public String getLawId() {
        return this.lawId;
    }

    public void setLawId(String lawId) {
        this.lawId = lawId;
    }

    public String getBranchId() {
        return this.branchId;
    }

    public void setBranchId(String branchId) {
        this.branchId = branchId;
    }

    public String getEnabled() {
        return this.enabled;
    }

    public void setEnabled(String enabled) {
        this.enabled = enabled;
    }

    public String getValidFlag() {
        return this.validFlag;
    }

    public void setValidFlag(String validFlag) {
        this.validFlag = validFlag;
    }

    public String getDescription() {
        return this.description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public String getCreatedUserId() {
        return this.createdUserId;
    }

    public void setCreatedUserId(String createdUserId) {
        this.createdUserId = createdUserId;
    }

    public Long getCreatedDate() {
        return this.createdDate;
    }

    public void setCreatedDate(Long createdDate) {
        this.createdDate = createdDate;
    }

    public String getUpdatedUserId() {
        return this.updatedUserId;
    }

    public void setUpdatedUserId(String updatedUserId) {
        this.updatedUserId = updatedUserId;
    }

    public Long getUpdatedDate() {
        return this.updatedDate;
    }

    public void setUpdatedDate(Long updatedDate) {
        this.updatedDate = updatedDate;
    }

    @Override
    public boolean equals(Object obj) {
        if (this == obj)
            return true;
        if (obj == null)
            return false;
        if (getClass() != obj.getClass())
            return false;
        final LawBranchPo other = (LawBranchPo) obj;
        if (lawBranchId == null) {
            if (other.lawBranchId != null)
                return false;
        }
        else if (!lawBranchId.equals(other.lawBranchId))
            return false;
        if (lawId == null) {
            if (other.lawId != null)
                return false;
        }
        else if (!lawId.equals(other.lawId))
            return false;
        if (branchId == null) {
            if (other.branchId != null)
                return false;
        }
        else if (!branchId.equals(other.branchId))
            return false;
        if (enabled == null) {
            if (other.enabled != null)
                return false;
        }
        else if (!enabled.equals(other.enabled))
            return false;
        if (validFlag == null) {
            if (other.validFlag != null)
                return false;
        }
        else if (!validFlag.equals(other.validFlag))
            return false;
        if (description == null) {
            if (other.description != null)
                return false;
        }
        else if (!description.equals(other.description))
            return false;
        if (createdUserId == null) {
            if (other.createdUserId != null)
                return false;
        }
        else if (!createdUserId.equals(other.createdUserId))
            return false;
        if (createdDate == null) {
            if (other.createdDate != null)
                return false;
        }
        else if (!createdDate.equals(other.createdDate))
            return false;
        if (updatedUserId == null) {
            if (other.updatedUserId != null)
                return false;
        }
        else if (!updatedUserId.equals(other.updatedUserId))
            return false;
        if (updatedDate == null) {
            if (other.updatedDate != null)
                return false;
        }
        else if (!updatedDate.equals(other.updatedDate))
            return false;
        return true;
    }

    @Override
    public int hashCode() {
        final int prime = 31;
        int result = 1;
        result = prime * result + ((this.lawBranchId == null) ? 0 : this.lawBranchId.hashCode());
        result = prime * result + ((this.lawId == null) ? 0 : this.lawId.hashCode());
        result = prime * result + ((this.branchId == null) ? 0 : this.branchId.hashCode());
        result = prime * result + ((this.enabled == null) ? 0 : this.enabled.hashCode());
        result = prime * result + ((this.validFlag == null) ? 0 : this.validFlag.hashCode());
        result = prime * result + ((this.description == null) ? 0 : this.description.hashCode());
        result = prime * result + ((this.createdUserId == null) ? 0 : this.createdUserId.hashCode());
        result = prime * result + ((this.createdDate == null) ? 0 : this.createdDate.hashCode());
        result = prime * result + ((this.updatedUserId == null) ? 0 : this.updatedUserId.hashCode());
        result = prime * result + ((this.updatedDate == null) ? 0 : this.updatedDate.hashCode());
        return result;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder("LawBranchPo (");

        sb.append(lawBranchId);
        sb.append(", ").append(lawId);
        sb.append(", ").append(branchId);
        sb.append(", ").append(enabled);
        sb.append(", ").append(validFlag);
        sb.append(", ").append(description);
        sb.append(", ").append(createdUserId);
        sb.append(", ").append(createdDate);
        sb.append(", ").append(updatedUserId);
        sb.append(", ").append(updatedDate);

        sb.append(")");
        return sb.toString();
    }
}
