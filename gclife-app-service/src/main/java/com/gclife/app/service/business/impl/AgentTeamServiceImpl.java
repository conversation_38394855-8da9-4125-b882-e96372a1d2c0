package com.gclife.app.service.business.impl;

import com.gclife.agent.api.*;
import com.gclife.agent.model.request.AgentDetailRequest;
import com.gclife.agent.model.response.*;
import com.gclife.app.model.config.AppErrorConfigEnum;
import com.gclife.app.model.config.AppTermEnum;
import com.gclife.app.model.response.AgentInfoResponse;
import com.gclife.app.service.business.AgentTeamService;
import com.gclife.app.validate.parameter.transform.AppLanguageCodeTransData;
import com.gclife.app.validate.parameter.transform.MineTransData;
import com.gclife.common.TerminologyConfigEnum;
import com.gclife.common.exception.RequestException;
import com.gclife.common.model.ResultObject;
import com.gclife.common.model.base.Users;
import com.gclife.common.TerminologyTypeEnum;
import com.gclife.common.model.pojo.AppRequestHeads;
import com.gclife.common.service.impl.BaseServiceImpl;
import com.gclife.common.util.AssertUtils;
import com.gclife.party.api.UserAccountApi;
import com.gclife.party.model.response.UserAccountResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Arrays;

/**
 * <AUTHOR>
 * create 17-11-10
 * description:
 */
@Service
public class AgentTeamServiceImpl extends BaseServiceImpl implements AgentTeamService {

    @Autowired
    private AgentApi agentApi;

    @Autowired
    private AgentAppTeamApi agentAppTeamApi;
    @Autowired
    private UserAccountApi userAccountApi;
    @Autowired
    private AgentDetailApi agentDetailApi;
    @Autowired
    private AgentSignAuditApi agentSignAuditApi;
    @Autowired
    private AgentBaseAgentApi agentBaseAgentApi;

    @Autowired
    private AppLanguageCodeTransData appLanguageCodeTransData;

    @Autowired
    private MineTransData mineTransData;

    @Override
    public ResultObject<AgentInfoResponse> agentInfoGet(Users users, AppRequestHeads appRequestHeads) {
        ResultObject<AgentInfoResponse> resultObject = new ResultObject<>();
        try {
            AgentInfoResponse agentInfoResponse = new AgentInfoResponse();
            //获取代理人信息
            ResultObject<AgentResponse> agentRespFcResultObject = agentApi.agentByIdGet(users.getUserId());
            if (!AssertUtils.isResultObjectDataNull(agentRespFcResultObject)) {
                agentInfoResponse.setAgentName(agentRespFcResultObject.getData().getAgentName());
                agentInfoResponse.setAgentTypeCode(agentRespFcResultObject.getData().getAgentTypeCode());
                if (AssertUtils.isNotEmpty(agentRespFcResultObject.getData().getAgentLevelId())) {
                    ResultObject<AgentLevelResponse> agentLevelRespFcResultObject = agentApi.agentLevelGet(agentRespFcResultObject.getData().getAgentLevelId());
                    if (!AssertUtils.isResultObjectDataNull(agentLevelRespFcResultObject)) {
                        agentInfoResponse.setAgentLevel(appLanguageCodeTransData.getCodeNameByKey(TerminologyTypeEnum.AGENT_PROFESSIONAL_LEVEL.name()
                                , agentLevelRespFcResultObject.getData().getAgentLevelCode()));
                    }
                }
            }
            //获取代理人头像
            ResultObject<AgentDetailResponse> userInfoRespFcResultObject = agentDetailApi.agentDetailGet();
            if (!AssertUtils.isResultObjectDataNull(userInfoRespFcResultObject)) {
                AgentDetailResponse agentDetailResponse = userInfoRespFcResultObject.getData();
                agentInfoResponse.setHeadImageUrl(AssertUtils.isNotEmpty(agentDetailResponse.getHeadAttachId())
                        ? userInfoRespFcResultObject.getData().getHeadAttachId() : "ATTACHMENT_84e8eeed-2241-4b89-a991-ce3714ad254f");
                //若二维码为空,重新生成二维码
                if (AssertUtils.isNotEmpty(agentDetailResponse.getQrCodeAttachId()) && TerminologyConfigEnum.WHETHER.YES.name().equals(agentDetailResponse.getQrCodeFlag())) {
                    agentInfoResponse.setQrCodeAttachId(agentDetailResponse.getQrCodeAttachId());
                } else {
                    String qrCodeAttachId = mineTransData.getQrCodeAttachId(agentRespFcResultObject.getData().getBranchId(), users, appRequestHeads);
                    agentInfoResponse.setQrCodeAttachId(qrCodeAttachId);
                    AgentDetailRequest agentDetailRequest = new AgentDetailRequest();
                    agentDetailRequest.setQrCodeAttachId(qrCodeAttachId);
                    agentDetailRequest.setQrCodeFlag(TerminologyConfigEnum.WHETHER.YES.name());
                    agentDetailApi.agentDetailPost(agentDetailRequest);
                }
            }

            //获取签证认证状态
            String certifyStatus = AppTermEnum.USER_CERTIFICATION_STATUS.NOT_CERTIFIED.name();
            //设置签约状态
            AgentSignBaseResponse agentSignBaseResponse = agentBaseAgentApi.queryOneAgentSignedAgentId(users.getUserId()).getData();
            if (AssertUtils.isNotNull(agentSignBaseResponse)) {
                if (AppTermEnum.SIGN_STATUS.SIGN_COMPLETE.name().equals(agentSignBaseResponse.getSignStatus())) {
                    certifyStatus = AppTermEnum.USER_CERTIFICATION_STATUS.CERTIFIED.name();
                } else if (AppTermEnum.SIGN_STATUS.IN_SIGN.name().equals(agentSignBaseResponse.getSignStatus())) {
                    certifyStatus = AppTermEnum.USER_CERTIFICATION_STATUS.UNDER_REVIEW.name();
                } else if (AppTermEnum.SIGN_STATUS.SIGN_ERROR.name().equals(agentSignBaseResponse.getSignStatus())) {
                    certifyStatus = AppTermEnum.USER_CERTIFICATION_STATUS.CERTIFIED_FAILED.name();
                } else if (AppTermEnum.SIGN_STATUS.RESCIND.name().equals(agentSignBaseResponse.getSignStatus())) {
                    certifyStatus = AppTermEnum.USER_CERTIFICATION_STATUS.NOT_CERTIFIED.name();
                }
            }
            agentInfoResponse.setCertifyStatus(appLanguageCodeTransData.getCodeNameByKey(AppTermEnum.USER_CERTIFICATION_STATUS.CERTIFICATION_STATUS.name(), certifyStatus));

            //获取用户账户
            ResultObject<UserAccountResponse> userAccountResponseResultObject = userAccountApi.userAccountGet(users.getUserId(), AppTermEnum.USER_ACCOUNT_TYPE.CASH_ACCOUNT.name());
            if (!AssertUtils.isResultObjectDataNull(userAccountResponseResultObject)) {
                UserAccountResponse userAccountResponse = userAccountResponseResultObject.getData();
                agentInfoResponse.setCumulativeIncome(userAccountResponse.getTotalAmount().toString());
                agentInfoResponse.setAvailableBalance(userAccountResponse.getCashWithdrawalAmount().toString());

            }

            //获取用户积分
            ResultObject<UserAccountResponse> userAccountGet = userAccountApi.userAccountGet(users.getUserId(), AppTermEnum.USER_ACCOUNT_TYPE.INTEGRAL_ACCOUNT.name());
            if (!AssertUtils.isResultObjectDataNull(userAccountGet) && AssertUtils.isNotNull(userAccountGet.getData().getTotalAmount())) {
                agentInfoResponse.setCumulativePoints(Float.valueOf(userAccountGet.getData().getTotalAmount().toString()).intValue());
            }
            //获取我的团队动态链接
            ResultObject<TeamPageResponse> respFcResultObject = agentAppTeamApi.getTeamUrl();
            if (!AssertUtils.isResultObjectDataNull(respFcResultObject)) {
                agentInfoResponse.setLinkUrl(respFcResultObject.getData().getUrl());
            }
            resultObject.setData(agentInfoResponse);
        } catch (Exception e) {
            if (e instanceof RequestException) {
                RequestException error = (RequestException) e;
                //错误设置
                resultObject.setIenum(error.getiEnum());
            } else {
                resultObject.setIenum(AppErrorConfigEnum.APP_AGENT_QUERY_ERROR);
            }
            e.printStackTrace();
        }
        return resultObject;
    }

}
