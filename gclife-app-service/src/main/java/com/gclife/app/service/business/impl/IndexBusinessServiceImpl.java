package com.gclife.app.service.business.impl;

import com.gclife.agent.api.AgentApi;
import com.gclife.agent.model.response.AgentResponse;
import com.gclife.app.dao.IndexExtDao;
import com.gclife.app.model.bo.BlockPropertyBo;
import com.gclife.app.model.config.AppErrorConfigEnum;
import com.gclife.app.model.config.AppTermEnum;
import com.gclife.app.model.response.BlockPropertyResponse;
import com.gclife.app.model.response.IndexResponse;
import com.gclife.app.service.business.AppUseRecordService;
import com.gclife.app.service.business.IndexBusinessService;
import com.gclife.app.validate.parameter.transform.IndexTransData;
import com.gclife.common.exception.RequestException;
import com.gclife.common.model.ResultObject;
import com.gclife.common.model.base.Users;
import com.gclife.common.model.config.BaseErrorConfigEnum;
import com.gclife.common.model.config.InternationalTypeEnum;
import com.gclife.common.model.pojo.AppRequestHeads;
import com.gclife.common.service.impl.BaseBusinessServiceImpl;
import com.gclife.common.util.AssertUtils;
import com.gclife.platform.api.PlatformBranchApi;
import com.gclife.platform.api.PlatformInternationalBaseApi;
import com.gclife.platform.api.PlatformUsersApi;
import com.gclife.platform.api.UserLoginBaseApi;
import com.gclife.platform.model.request.UserInfoRequest;
import com.gclife.platform.model.response.BranchResponse;
import com.gclife.platform.model.response.SyscodeResponse;
import org.modelmapper.TypeToken;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 * create 17-11-3
 * description:
 */
@Service
public class IndexBusinessServiceImpl extends BaseBusinessServiceImpl implements IndexBusinessService {
    @Autowired
    private AgentApi agentApi;

    @Autowired
    private IndexExtDao indexExtDao;

    @Autowired
    private PlatformUsersApi platformUsersApi;

    @Autowired
    private IndexTransData indexTransData;
    @Autowired
    private AppUseRecordService appUseRecordService;

    @Autowired
    private UserLoginBaseApi userLoginBaseApi;

    @Autowired
    private PlatformBranchApi platformBranchApi;

    @Autowired
    private PlatformInternationalBaseApi platformInternationalBaseApi;

    @Override
    public ResultObject<IndexResponse> getIndexBlock(Users users, AppRequestHeads appRequestHeads) {
        ResultObject<IndexResponse> resultObject = new ResultObject<>();
        try {
            // 记录用户使用天数
            appUseRecordService.saveAppUseRecord(users);
            //用户登录日志:TODO
            userLoginBaseApi.userLoginLogPost(appRequestHeads,users.getUserId(), BaseErrorConfigEnum.SUCCESS.name());

            //修改用户语言
            UserInfoRequest userInfoReqFc = new UserInfoRequest();
            userInfoReqFc.setLanguage(appRequestHeads.getLanguage());
            platformUsersApi.userInfoPut(userInfoReqFc);

            IndexResponse indexResponse = new IndexResponse();
            AgentResponse agentRespFc = agentApi.agentByIdGet(users.getUserId()).getData();
            AssertUtils.isNotNull(this.getLogger(), agentRespFc, AppErrorConfigEnum.APP_QUERY_USER_INFO_IS_NULL);
            String agentBranchId = agentRespFc.getBranchId();
            AssertUtils.isNotNull(this.getLogger(), agentBranchId, AppErrorConfigEnum.APP_QUERY_USER_INFO_BRANCH_IS_NULL);

            //当前用户的父类机构集合,从下往上
            ResultObject<List<BranchResponse>> parentBranches = platformBranchApi.userParentBranchs(agentBranchId);
            AssertUtils.isResultObjectListDataNull(this.getLogger(), parentBranches, AppErrorConfigEnum.APP_QUERY_USER_INFO_BRANCH_IS_NULL);
            List<BranchResponse> branchRespFcs = parentBranches.getData();
            List<String> blockBranches = indexExtDao.loadBlockBranches();

            String branchId = this.loadBranch(branchRespFcs, blockBranches);

            List<BlockPropertyBo> blockPropertyBos = indexExtDao.loadBlocks(branchId);
            AssertUtils.isNotEmpty(this.getLogger(), blockPropertyBos, AppErrorConfigEnum.APP_QUERY_INDEX_BLOCKS_IS_NULL);
            //数据转换
            List<BlockPropertyResponse> blockPropertyResponseList = (List<BlockPropertyResponse>) this.converterList(blockPropertyBos, new TypeToken<List<BlockPropertyResponse>>() {
            }.getType());

            // 数据标题
            List<SyscodeResponse> appPropertyDataTitleSyscodes = platformInternationalBaseApi
                    .queryInternational(InternationalTypeEnum.APP_PROPERTY_DATA_TITLE.name(), appRequestHeads.getLanguage()).getData();
            // 数据副标题
            List<SyscodeResponse> appPropertyDataSubTitleSyscodes = platformInternationalBaseApi
                    .queryInternational(InternationalTypeEnum.APP_PROPERTY_DATA_SUB_TITLE.name(), appRequestHeads.getLanguage()).getData();

            blockPropertyResponseList.forEach(blockPropertyResponse -> {
                if (AssertUtils.isNotEmpty(blockPropertyResponse.getListBlockPropertyData())) {
                    blockPropertyResponse.getListBlockPropertyData().forEach(blockPropertyDataResponse -> {
                        // 数据标题
                        if (AssertUtils.isNotEmpty(appPropertyDataTitleSyscodes)
                                && AssertUtils.isNotNull(blockPropertyDataResponse.getPropertyDataTitleCode())) {
                            appPropertyDataTitleSyscodes.stream()
                                    .filter(syscode -> syscode.getCodeKey().equals(blockPropertyDataResponse.getPropertyDataTitleCode()))
                                    .findFirst().ifPresent(syscode -> blockPropertyDataResponse.setPropertyDataTitle(syscode.getCodeName()));
                        }
                        // 数据副标题
                        if (AssertUtils.isNotEmpty(appPropertyDataSubTitleSyscodes)
                                && AssertUtils.isNotNull(blockPropertyDataResponse.getPropertyDataSubTitleCode())) {
                            appPropertyDataSubTitleSyscodes.stream()
                                    .filter(syscode -> syscode.getCodeKey().equals(blockPropertyDataResponse.getPropertyDataSubTitleCode()))
                                    .findFirst().ifPresent(syscode -> blockPropertyDataResponse.setPropertyDataSubTitle(syscode.getCodeName()));
                        }
                    });
                }

                if (AppTermEnum.BLOCK.BANNER.name().equals(blockPropertyResponse.getBlockPropertyCode())) {
                    indexResponse.setBanner(blockPropertyResponse);
                } else if (AppTermEnum.BLOCK.INFORMATION.name().equals(blockPropertyResponse.getBlockPropertyCode())) {
                    indexResponse.setInformation(blockPropertyResponse);
                } else if (AppTermEnum.BLOCK.MENU.name().equals(blockPropertyResponse.getBlockPropertyCode())) {
                    indexTransData.transMyTeamData(blockPropertyResponse);
                    indexResponse.setMenu(blockPropertyResponse);
                } else if (AppTermEnum.BLOCK.POPULAR_ACTIVITIES.name().equals(blockPropertyResponse.getBlockPropertyCode())) {
                    indexResponse.setPopularActivities(blockPropertyResponse);
                }
            });

            resultObject.setData(indexResponse);
        } catch (Exception e) {
            e.printStackTrace();
            if (e instanceof RequestException) {
                RequestException error = (RequestException) e;
                //错误设置
                resultObject.setIenum(error.getiEnum());
            } else {
                resultObject.setIenum(AppErrorConfigEnum.APP_QUERY_INDEX_BLOCKS_ERROR);
            }
        }
        return resultObject;
    }

    private String loadBranch(List<BranchResponse> branchRespFcs, List<String> blockBranches) {
        for (BranchResponse branchRespFc : branchRespFcs) {
            for (String blockBranch : blockBranches) {
                if (branchRespFc.getBranchId().equals(blockBranch)) {
                    return blockBranch;
                }
            }
        }
        return null;
    }
}
