package com.gclife.app.model.response;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

/**
 * <AUTHOR>
 * @version v1.0
 * Description:
 * @date 18-4-2
 */
@ApiModel(description = "附件")
public class AttachResponse {
    @ApiModelProperty(example = "附件明细ID")
    private String policyAttachmentId;
    @ApiModelProperty(example = "附件id,对应附件系统id")
    private String attachmentId;
    @ApiModelProperty(example = "附件语言")
    private String language;

    public String getPolicyAttachmentId() {
        return policyAttachmentId;
    }

    public void setPolicyAttachmentId(String policyAttachmentId) {
        this.policyAttachmentId = policyAttachmentId;
    }

    public String getAttachmentId() {
        return attachmentId;
    }

    public void setAttachmentId(String attachmentId) {
        this.attachmentId = attachmentId;
    }

    public String getLanguage() {
        return language;
    }

    public void setLanguage(String language) {
        this.language = language;
    }
}
