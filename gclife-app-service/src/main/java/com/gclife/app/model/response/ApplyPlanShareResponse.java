package com.gclife.app.model.response;

import io.swagger.annotations.ApiModelProperty;

/**
 * <AUTHOR>
 *         create 17-11-30
 *         description:
 */
public class ApplyPlanShareResponse {

    @ApiModelProperty(example = "计划书id")
    private String applyPlanId;

    @ApiModelProperty(example = "标题")
    private String title;
    @ApiModelProperty(example = "描述")
    private String description;
    @ApiModelProperty(example = "缩略图")
    private String thumbnailImageSrc;
    @ApiModelProperty(example = "图片")
    private String url;
    @ApiModelProperty(example = "类型")
    private String type;

    public String getApplyPlanId() {
        return applyPlanId;
    }

    public void setApplyPlanId(String applyPlanId) {
        this.applyPlanId = applyPlanId;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public String getThumbnailImageSrc() {
        return thumbnailImageSrc;
    }

    public void setThumbnailImageSrc(String thumbnailImageSrc) {
        this.thumbnailImageSrc = thumbnailImageSrc;
    }

    public String getUrl() {
        return url;
    }

    public void setUrl(String url) {
        this.url = url;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }
}
