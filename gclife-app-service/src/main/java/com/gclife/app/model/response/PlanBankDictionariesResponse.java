package com.gclife.app.model.response;

import com.gclife.platform.model.response.BankResponse;
import com.gclife.platform.model.response.SyscodeResponse;
import io.swagger.annotations.ApiModelProperty;

import java.util.List;

/**
 * <AUTHOR>
 * create 17-11-28
 * description:
 */
public class PlanBankDictionariesResponse {
    @ApiModelProperty(example = "账户类型")
    private List<SyscodeResponse> accountType;
    @ApiModelProperty(example = "工商银行")
    private List<BankResponse> banks;

    public List<SyscodeResponse> getAccountType() {
        return accountType;
    }

    public void setAccountType(List<SyscodeResponse> accountType) {
        this.accountType = accountType;
    }

    public List<BankResponse> getBanks() {
        return banks;
    }

    public void setBanks(List<BankResponse> banks) {
        this.banks = banks;
    }
}