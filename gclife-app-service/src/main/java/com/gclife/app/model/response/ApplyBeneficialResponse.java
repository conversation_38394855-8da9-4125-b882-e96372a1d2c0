package com.gclife.app.model.response;

import com.gclife.apply.model.request.ApplyBeneficialAttachmentRequest;
import com.gclife.common.annotation.Internation;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 *         create 17-12-1
 *         description:
 */
@Data
public class ApplyBeneficialResponse {

    @ApiModelProperty(value = "受益顺序", example = "受益顺序")
    private String beneficiaryNoOrder;

    @ApiModelProperty(value = "受益顺序", example = "受益顺序")
    @Internation(codeType = "BENEFICIARY_NO", filed = "beneficiaryNoOrder")
    private String beneficiaryNoOrderName;

    @ApiModelProperty(value = "受益人姓名", example = "受益人姓名")
    private String name;

    @ApiModelProperty(example = "受益人姓",required = true)
    private String familyName;
    @ApiModelProperty(example = "受益人名",required = true)
    private String givenName;

    @ApiModelProperty(value = "性别", example = "性别")
    private String sex;
    @ApiModelProperty(value = "性别名称", example = "性别名称")
    @Internation(filed = "sex", codeType = "GENDER")
    private String sexName;

    private String idType;
    @ApiModelProperty(value = "证件类型", example = "证件类型")
    @Internation(filed = "idType", codeType = "ID_TYPE")
    private String idTypeName;

    @ApiModelProperty(value = "证件号码", example = "证件号码")
    private String idNo;

    @ApiModelProperty(value = "证件有效期至", example = "证件有效期至")
    private String idExpDate;

    @ApiModelProperty(value = "受益顺序", example = "受益顺序")
    private String beneficiaryNo;

    @ApiModelProperty(value = "受益份额", example = "受益份额")
    private String beneficiaryProportion;

    @ApiModelProperty(value = "出生日期", example = "出生日期")
    private String birthday;

    @ApiModelProperty(example = "是否有社保")
    private String socialSecurity;
    @ApiModelProperty(example = "是否有社保名称")
    @Internation(filed = "socialSecurity", codeType = "SOCIAL_SECURITY")
    private String socialSecurityName;

    @ApiModelProperty(value = "职业",example = "职业")
    private String occupationCode;

    @ApiModelProperty(value = "职业名称",example = "职业名称")
    private String occupationCodeName;

    @ApiModelProperty(value = "与被保人的关系",example = "与被保人的关系")
    private String relationship;
    @ApiModelProperty(value = "与被保人的关系名称",example = "与被保人的关系名称")
    @Internation(filed = "relationship", codeType = "RELATIONSHIP_WITH_THE_INSURED")
    private String relationshipName;

    @ApiModelProperty(example = "与被保人的关系说明")
    private String relationshipInstructions;

    @ApiModelProperty(example = "受益人详细地址")
    private String addressDetail;

    @ApiModelProperty(example = "受益人手机号码")
    private String mobile;
    private ApplyBeneficialAttachmentRequest beneficialAttachmentList;
}
