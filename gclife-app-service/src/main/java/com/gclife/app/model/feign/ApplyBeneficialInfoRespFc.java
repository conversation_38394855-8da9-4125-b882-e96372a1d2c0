package com.gclife.app.model.feign;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 *         create 17-12-8
 *         description:
 */
@Data
public class ApplyBeneficialInfoRespFc {
    @ApiModelProperty(value = "投保单受益人ID", example = "004")
    private String     applyBeneficiaryId;
    @ApiModelProperty(value = "受益人ID", example = "004")
    private String     beneficiaryId;
    @ApiModelProperty(value = "被保人ID", example = "004")
    private String     insuredId;
    @ApiModelProperty(value = "投保单ID", example = "004")
    private String     applyId;
    @ApiModelProperty(value = "受益人类别(生存金，满期金)", example = "004")
    private String     beneficiaryType;
    @ApiModelProperty(value = "受益人顺位", example = "1")
    private Long       beneficiaryNo;
    @ApiModelProperty(value = "受益份额", example = "100")
    private BigDecimal beneficiaryProportion;
    @ApiModelProperty(value = "受益人级别", example = "1")
    private String     beneficiaryGrade;
    @ApiModelProperty(value = "银行编码", example = "1")
    private String     bankCode;
    @ApiModelProperty(value = "银行帐号", example = "************")
    private String     bankAccountNo;
    @ApiModelProperty(value = "银行帐户名", example = "李四")
    private String     bankAccountName;
    @ApiModelProperty(value = "转账授权标志", example = "TRUE")
    private String     transferGrantFlag;
    @ApiModelProperty(value = "理赔金领取方式", example = "004")
    private String     claimPremDrawType;
    @ApiModelProperty(value = "约定比例", example = "100")
    private BigDecimal promiseRate;
    @ApiModelProperty(value = "约定年龄", example = "18")
    private Long       promiseAge;
    @ApiModelProperty(value = "与被保人关系", example = "ONESELF")
    private String     relationship;
    @ApiModelProperty(example = "与被保人的关系说明")
    private String relationshipInstructions;
    private ApplyBeneficialRespFc applyBeneficiaryBo;

    /**
     * 受益人附件
     */
    private List<ApplyBeneficiaryAttachmentRespFc> listBeneficiaryAttachment;

    public String getApplyBeneficiaryId() {
        return applyBeneficiaryId;
    }

    public void setApplyBeneficiaryId(String applyBeneficiaryId) {
        this.applyBeneficiaryId = applyBeneficiaryId;
    }

    public String getBeneficiaryId() {
        return beneficiaryId;
    }

    public void setBeneficiaryId(String beneficiaryId) {
        this.beneficiaryId = beneficiaryId;
    }

    public String getInsuredId() {
        return insuredId;
    }

    public void setInsuredId(String insuredId) {
        this.insuredId = insuredId;
    }

    public String getApplyId() {
        return applyId;
    }

    public void setApplyId(String applyId) {
        this.applyId = applyId;
    }

    public String getBeneficiaryType() {
        return beneficiaryType;
    }

    public void setBeneficiaryType(String beneficiaryType) {
        this.beneficiaryType = beneficiaryType;
    }

    public Long getBeneficiaryNo() {
        return beneficiaryNo;
    }

    public void setBeneficiaryNo(Long beneficiaryNo) {
        this.beneficiaryNo = beneficiaryNo;
    }

    public BigDecimal getBeneficiaryProportion() {
        return beneficiaryProportion;
    }

    public void setBeneficiaryProportion(BigDecimal beneficiaryProportion) {
        this.beneficiaryProportion = beneficiaryProportion;
    }

    public String getBeneficiaryGrade() {
        return beneficiaryGrade;
    }

    public void setBeneficiaryGrade(String beneficiaryGrade) {
        this.beneficiaryGrade = beneficiaryGrade;
    }

    public String getBankCode() {
        return bankCode;
    }

    public void setBankCode(String bankCode) {
        this.bankCode = bankCode;
    }

    public String getBankAccountNo() {
        return bankAccountNo;
    }

    public void setBankAccountNo(String bankAccountNo) {
        this.bankAccountNo = bankAccountNo;
    }

    public String getBankAccountName() {
        return bankAccountName;
    }

    public void setBankAccountName(String bankAccountName) {
        this.bankAccountName = bankAccountName;
    }

    public String getTransferGrantFlag() {
        return transferGrantFlag;
    }

    public void setTransferGrantFlag(String transferGrantFlag) {
        this.transferGrantFlag = transferGrantFlag;
    }

    public String getClaimPremDrawType() {
        return claimPremDrawType;
    }

    public void setClaimPremDrawType(String claimPremDrawType) {
        this.claimPremDrawType = claimPremDrawType;
    }

    public BigDecimal getPromiseRate() {
        return promiseRate;
    }

    public void setPromiseRate(BigDecimal promiseRate) {
        this.promiseRate = promiseRate;
    }

    public Long getPromiseAge() {
        return promiseAge;
    }

    public void setPromiseAge(Long promiseAge) {
        this.promiseAge = promiseAge;
    }

    public String getRelationship() {
        return relationship;
    }

    public void setRelationship(String relationship) {
        this.relationship = relationship;
    }

    public ApplyBeneficialRespFc getApplyBeneficiaryBo() {
        return applyBeneficiaryBo;
    }

    public void setApplyBeneficiaryBo(ApplyBeneficialRespFc applyBeneficiaryBo) {
        this.applyBeneficiaryBo = applyBeneficiaryBo;
    }

    public List<ApplyBeneficiaryAttachmentRespFc> getListBeneficiaryAttachment() {
        return listBeneficiaryAttachment;
    }

    public void setListBeneficiaryAttachment(List<ApplyBeneficiaryAttachmentRespFc> listBeneficiaryAttachment) {
        this.listBeneficiaryAttachment = listBeneficiaryAttachment;
    }
}
