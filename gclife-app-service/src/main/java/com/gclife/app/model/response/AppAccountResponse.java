package com.gclife.app.model.response;

import com.gclife.common.annotation.Internation;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class AppAccountResponse {
    @ApiModelProperty(example = "授权帐号ID")
    private String accountId;
    @ApiModelProperty(example = "账户号码")
    private String accountNo;
    @ApiModelProperty(example = "银行代码")
    private String bankCode;
    @ApiModelProperty(example = "银行代码")
    @Internation(codeType = "BANK",filed = "bankCode")
    private String bankName;
    @ApiModelProperty(example = "支行名称")
    private String subbranch;
    @ApiModelProperty(example = "账户持有人")
    private String accountOwner;
    @ApiModelProperty(example = "主附卡标识")
    private String primaryFlag;
    @ApiModelProperty(example = "银行卡附件正面")
    private String bankFrontAttachId;
    @ApiModelProperty(example = "银行卡附件反面")
    private String bankBackAttachId;
    @ApiModelProperty(example = "失败原因code")
    private String failureReasonCode;
    @Internation(codeType = "ACCOUNT_AUDIT_EXCEPTION_QUESTION",filed = "failureReasonCode")
    @ApiModelProperty(example = "失败原因")
    private String failureReason;
    @ApiModelProperty(example = "审核状态")
    private String auditStatus;
}
