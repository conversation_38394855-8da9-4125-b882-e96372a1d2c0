package com.gclife.app.model.response;

import com.gclife.common.annotation.Internation;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * create 2021/3/1 下午2:00
 * description:职业性质
 */
@Data
public class ApplyOccupationNatureResponse {
    @ApiModelProperty(example = "投保单ID")
    private String applyId;
    @ApiModelProperty(example = "职业性质编码")
    private String occupationNature;
    @ApiModelProperty(example = "职业性质名称")
    @Internation(filed = "occupationNature", codeType = "OCCUPATION_NATURE")
    private String occupationNatureName;
    @ApiModelProperty(example = "职业性质其他详细说明")
    private String occupationNatureSpecific;
    @ApiModelProperty(example = "雇主姓名")
    private String employerName;
    @ApiModelProperty(example = "业务性质")
    private String businessNature;
    @ApiModelProperty(example = "职业")
    private String occupation;
    @ApiModelProperty(example = "准确职责")
    private String exactDuties;
    @ApiModelProperty(example = "序号")
    private String seq;
}
