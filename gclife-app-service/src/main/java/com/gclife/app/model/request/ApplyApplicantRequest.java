package com.gclife.app.model.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 *         create 17-12-1
 *         description:
 */
@Data
public class ApplyApplicantRequest {
    @ApiModelProperty(example = "影像附件ID")
    private String attachmentId;

    @ApiModelProperty(example = "护照资料影像附件ID")
    private String passportAttachmentId;

    @ApiModelProperty(example = "护照资料影像附件ID")
    private String passportVisaAttachmentId;

    @ApiModelProperty(example = "投保人姓名",required = true)
    private String name;

    @ApiModelProperty(example = "投保人姓",required = true)
    private String familyName;

    @ApiModelProperty(example = "投保人名",required = true)
    private String givenName;

    @ApiModelProperty(example = "身高",required = true)
    private String stature;

    @ApiModelProperty(example = "体重",required = true)
    private String avoirdupois;

    @ApiModelProperty(example = "婚姻状况",required = true)
    private String marriage;

    @ApiModelProperty(example = "证件类型",required = true)
    private String idType;

    @ApiModelProperty(example = "证件号码",required = true)
    private String idNo;

    @ApiModelProperty(example = "性别",required = true)
    private String sex;

    @ApiModelProperty(example = "出生日期",required = true)
    private String birthday;

    @ApiModelProperty(example = "证件有效期")
    private String   idExpDate;

    @ApiModelProperty(example = "国籍",required = true)
    private String nationality;

    @ApiModelProperty(example = "外国人国籍类型",required = true)
    private String nationalityType;

    @ApiModelProperty(example = "签证有效期")
    private String visaExpDate;

    @ApiModelProperty(example = "手机号码",required = true)
    private String mobile;

    @ApiModelProperty(example = "电子邮箱",required = true)
    private String email;

    @ApiModelProperty(example = "单位类型",required = true)
    private String companyType;

    @ApiModelProperty(example = "职业",required = true)
    private String occupationCode;

    @ApiModelProperty(example = "单位电话",required = true)
    private String belongsCompanyPhone;

    @ApiModelProperty(example = "单位邮编",required = true)
    private String belongsCompanyZipCode;

    @ApiModelProperty(example = "单位地区编码",required = true)
    private String companyAreaCode;

    @ApiModelProperty(example = "单位详细地址",required = true)
    private String companyAddress;

    @ApiModelProperty(example = "居住地区编码",required = true)
    private String homeAreaCode;

    @ApiModelProperty(example = "居住详细住址",required = true)
    private String homeAddress;

    @ApiModelProperty(example = "邮政编码",required = true)
    private String homeZipCode;

    @ApiModelProperty(example = "家庭电话",required = true)
    private String homePhone;

    @ApiModelProperty(example = "平均年收入",required = true)
    private String income;

    @ApiModelProperty(example = "是否有社保",required = true)
    private String socialSecurity;

    @ApiModelProperty(example = "收入来源",required = true)
    private String incomeSource;

    @ApiModelProperty(value = "公司名称", example = "公司名称")
    private String companyName;
    @ApiModelProperty(value = "学历", example = "学历")
    private String degree;

    @ApiModelProperty(value = "职务",example = "职务")
    private String position;

    @ApiModelProperty(value = "微信号",example = "微信号")
    private String wechatNo;

    @ApiModelProperty(example = "脸书号",required = true)
    private String facebookNo;

    @ApiModelProperty(example = "客户来源",required = true)
    private String customerSource;

    @ApiModelProperty(example = "手机号码2")
    private String mobile_2;

    @ApiModelProperty(example = "预期保费来源集合")
    private List<String> listExpectedPremiumSources;
    @ApiModelProperty(example = "预期保费来源其他详细说明")
    private String expectedPremiumSourcesSpecific;

    @ApiModelProperty(example = "咨询医生姓名")
    private String doctorName;
    @ApiModelProperty(example = "咨询医生所在地区编码")
    private String doctorAreaCode;
    @ApiModelProperty(example = "咨询医生所在地区详细地址")
    private String doctorAddress;

    private ApplyOccupationNatureRequest occupationNature;

    @ApiModelProperty(example = "地址类型")
    private String addressType;
    @ApiModelProperty(example = "ocr展示状态")
    private String ocrShowFlag;
    @ApiModelProperty(example = "网销暂存保存状态")
    private String saveFlag;
    @ApiModelProperty(example = "网销投保人附件名称")
    private String attachmentName;
    //网销计划书和投保单投保人信息不一致标识
    private String onlineInfoDiffFlag;
}
