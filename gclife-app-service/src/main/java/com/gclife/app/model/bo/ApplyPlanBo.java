package com.gclife.app.model.bo;

import com.gclife.attachment.model.policy.apply.ApplyPlanLoanBo;
import com.gclife.product.model.response.plan.PlanProductDetailResponse;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

;

/**
 * <AUTHOR>
 * @version v1.0
 * Description:
 * @date 18-2-7
 */
@Data
public class ApplyPlanBo {

    private String     applyPlanId;
    private String     applyPlanNo;
    private String     applyId;
    private String     agentId;
    private String     recipientName;
    private String     recipientSex;
    private String     status;
    private BigDecimal receivablePremium;
    private String     createdUserId;
    private Long       createdDate;
    private String     updatedUserId;
    private Long       updatedDate;
    private String     validFlag;
    private String     signature;
    private String     currencyCode;

    // 增加agentCode和agentName
    private String agentCode;
    private String agentName;
    private String agentMobile;
    @ApiModelProperty(value = "回溯日期", name = "回溯日期", example = "APPLY:RENEWAL:ENDORSE")
    private Long backTrackDate;

    private ApplyPlanApplicantBo applicant;
    private ApplyInsuredPlanBo insured;
    private List<ApplyCoveragePlanBo> coverages;
    /**
     * 现金价值
     */
    private List<ProductCashValueBo> listCashValue;
    /**
     * 计划书产品详情
     */
    private PlanProductDetailResponse planProductDetail;

    private ApplyPlanLoanBo planLoanContract;
}
