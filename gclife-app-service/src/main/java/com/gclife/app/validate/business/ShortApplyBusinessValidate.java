package com.gclife.app.validate.business;

import com.gclife.app.model.config.AppErrorConfigEnum;
import com.gclife.app.model.config.AppTermEnum;
import com.gclife.app.validate.parameter.transform.AppLanguageCodeTransData;
import com.gclife.apply.model.request.AppApplicantRequest;
import com.gclife.apply.model.request.AppInsuredRequest;
import com.gclife.apply.model.request.ShortApplyInputRequest;
import com.gclife.common.exception.RequestException;
import com.gclife.common.model.ResultObject;
import com.gclife.common.TerminologyTypeEnum;
import com.gclife.common.service.impl.BaseBusinessServiceImpl;
import com.gclife.common.util.AssertUtils;
import com.gclife.platform.api.PlatformCareerApi;
import com.gclife.platform.model.response.CareerResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <AUTHOR>
 * create 17-12-27
 * description:
 */
@Component
public class ShortApplyBusinessValidate extends BaseBusinessServiceImpl {
    @Autowired
    private AppLanguageCodeTransData appLanguageCodeTransData;

    @Autowired
    private PlatformCareerApi platformCareerApi;


    public void validParameterSaveAppInputData(com.gclife.apply.model.request.ShortApplyInputRequest shortApplyInputRequest) throws RequestException {

        //APP投保人信息
        com.gclife.apply.model.request.AppApplicantRequest applicant = shortApplyInputRequest.getApplicant();
        AssertUtils.isNotNull(this.getLogger(), applicant, AppErrorConfigEnum.APP_INPUT_APPLICANT_INFO_IS_NOT_NULL);
        AssertUtils.isNotEmpty(this.getLogger(), applicant.getName(), AppErrorConfigEnum.APP_INPUT_APPLICANT_NAME_IS_NOT_NULL);
        AssertUtils.isNotEmpty(this.getLogger(), applicant.getIdType(), AppErrorConfigEnum.APP_INPUT_APPLICANT_ID_TYPE_IS_NOT_NULL);
        AssertUtils.isNotEmpty(this.getLogger(), applicant.getIdNo(), AppErrorConfigEnum.APP_INPUT_APPLICANT_ID_NO_IS_NOT_NULL);
        AssertUtils.isNotEmpty(this.getLogger(), applicant.getSex(), AppErrorConfigEnum.APP_INPUT_APPLICANT_SEX_IS_NOT_NULL);
        AssertUtils.isNotNull(this.getLogger(), applicant.getBirthday(), AppErrorConfigEnum.APP_INPUT_APPLICANT_BIRTHDAY_IS_NOT_NULL);
        AssertUtils.isNotEmpty(this.getLogger(), applicant.getSocialSecurity(), AppErrorConfigEnum.APP_INPUT_APPLICANT_SOCIAL_SECURITY_IS_NOT_NULL);
        AssertUtils.isNotEmpty(this.getLogger(), applicant.getOccupationCode(), AppErrorConfigEnum.APP_INPUT_APPLICANT_OCCUPATION_CODE_IS_NOT_NULL);
        AssertUtils.isNotEmpty(this.getLogger(), applicant.getCountryCode(), AppErrorConfigEnum.APP_INPUT_APPLICANT_COUNTRY_CODE_IS_NOT_NULL);
        AssertUtils.isNotEmpty(this.getLogger(), applicant.getMobile(), AppErrorConfigEnum.APP_INPUT_APPLICANT_MOBILE_IS_NOT_NULL);
        AssertUtils.isNotEmpty(this.getLogger(), applicant.getVerifyCode(), AppErrorConfigEnum.APP_INPUT_APPLICANT_MOBILE_VERIFY_CODE_IS_NOT_NULL);
        AssertUtils.isNotEmpty(this.getLogger(), applicant.getEmail(), AppErrorConfigEnum.APP_INPUT_APPLICANT_EMAIL_IS_NOT_NULL);

        //APP被保人信息
        List<com.gclife.apply.model.request.AppInsuredRequest> listInsuredInfo = shortApplyInputRequest.getListInsured();
        AssertUtils.isNotEmpty(this.getLogger(), listInsuredInfo, AppErrorConfigEnum.APP_INPUT_INSURED_INFO_IS_NOT_NULL);
        listInsuredInfo.forEach(insuredInfo -> {
            if (!AppTermEnum.APP_INPUT_DICTIONARIES.ONESELF.name().equals(insuredInfo.getRelationship())) {
                //被保人
                AssertUtils.isNotNull(this.getLogger(), insuredInfo, AppErrorConfigEnum.APP_INPUT_INSURED_INFO_IS_NOT_NULL);
                AssertUtils.isNotEmpty(this.getLogger(), insuredInfo.getSex(), AppErrorConfigEnum.APP_INPUT_INSURED_SEX_IS_NOT_NULL);
                AssertUtils.isNotNull(this.getLogger(), insuredInfo.getBirthday(), AppErrorConfigEnum.APP_INPUT_INSURED_BIRTHDAY_IS_NOT_NULL);
                AssertUtils.isNotEmpty(this.getLogger(), insuredInfo.getName(), AppErrorConfigEnum.APP_INPUT_INSURED_NAME_IS_NOT_NULL);
                AssertUtils.isNotEmpty(this.getLogger(), insuredInfo.getSocialSecurity(), AppErrorConfigEnum.APP_INPUT_INSURED_SOCIAL_SECURITY_IS_NOT_NULL);
                AssertUtils.isNotEmpty(this.getLogger(), insuredInfo.getOccupationCode(), AppErrorConfigEnum.APP_INPUT_INSURED_OCCUPATION_CODE_IS_NOT_NULL);
                AssertUtils.isNotEmpty(this.getLogger(), insuredInfo.getRelationship(), AppErrorConfigEnum.APP_INPUT_INSURED_RELATIONSHIP_IS_NOT_NULL);
                AssertUtils.isNotEmpty(this.getLogger(), insuredInfo.getIdType(), AppErrorConfigEnum.APP_INPUT_INSURED_ID_TYPE_IS_NOT_NULL);
                AssertUtils.isNotEmpty(this.getLogger(), insuredInfo.getIdNo(), AppErrorConfigEnum.APP_INPUT_INSURED_ID_NO_IS_NOT_NULL);
            }
        });
    }

    public void validBusinessShortApply(ShortApplyInputRequest shortApplyInputRequest) throws RequestException {

        //-------------验证投保人信息-------------
        AppApplicantRequest applicant = shortApplyInputRequest.getApplicant();
        if (!AssertUtils.isTimestamp(applicant.getBirthday())) {
            throw new RequestException(AppErrorConfigEnum.APP_APP_APPLICANT_BIRTHDAY_FORMAT_ERROR);
        }
        AssertUtils.isNotPureDigital(getLogger(), applicant.getMobile(), AppErrorConfigEnum.APP_APP_APPLICANT_MOBILE_FORMAT_ERROR);
        AssertUtils.isNotPureDigital(getLogger(), applicant.getVerifyCode(), AppErrorConfigEnum.APP_APP_APPLICANT_VERIFY_CODE_FORMAT_ERROR);
        AssertUtils.isEmail(getLogger(), applicant.getEmail(), AppErrorConfigEnum.APP_APP_APPLICANT_EMAIL_FORMAT_ERROR);

        AssertUtils.isNotEmpty(getLogger(), appLanguageCodeTransData.verifyCodeNameByKey(TerminologyTypeEnum.GENDER.name(), applicant.getSex()), AppErrorConfigEnum.APP_APP_APPLICANT_SEX_FORMAT_ERROR);
        AssertUtils.isNotEmpty(getLogger(), appLanguageCodeTransData.verifyCodeNameByKey(TerminologyTypeEnum.ID_TYPE.name(), applicant.getIdType()), AppErrorConfigEnum.APP_APP_APPLICANT_ID_TYPE_FORMAT_ERROR);
        AssertUtils.isNotEmpty(getLogger(), appLanguageCodeTransData.verifyCodeNameByKey(TerminologyTypeEnum.SOCIAL_SECURITY.name(), applicant.getSocialSecurity()), AppErrorConfigEnum.APP_APP_APPLICANT_SOCIAL_SECURITY_FORMAT_ERROR);
        AssertUtils.isNotEmpty(getLogger(), appLanguageCodeTransData.verifyCodeNameByKey(TerminologyTypeEnum.SMS_TYPE.name(), applicant.getTypeCode()), AppErrorConfigEnum.APP_APP_APPLICANT_SMS_TYPE_FORMAT_ERROR);

        //验证职业
        ResultObject<CareerResponse> respFcResultObject = platformCareerApi.careerInfoGet(applicant.getOccupationCode(),null);
        AssertUtils.isResultObjectDataNull(getLogger(), respFcResultObject, AppErrorConfigEnum.APP_APP_APPLICANT_OCCUPATION_FORMAT_ERROR);

        //-------------验证被保人信息-------------
        List<AppInsuredRequest> listInsured = shortApplyInputRequest.getListInsured();
        listInsured.forEach(insured -> {
            if (!AppTermEnum.APP_INPUT_DICTIONARIES.ONESELF.name().equals(insured.getRelationship())) {
                if (!AssertUtils.isTimestamp(insured.getBirthday())) {
                    throw new RequestException(AppErrorConfigEnum.APP_APP_INSURED_BIRTHDAY_FORMAT_ERROR);
                }
                AssertUtils.isNotEmpty(getLogger(), appLanguageCodeTransData.verifyCodeNameByKey(TerminologyTypeEnum.GENDER.name(), insured.getSex()), AppErrorConfigEnum.APP_APP_INSURED_SEX_FORMAT_ERROR);
                AssertUtils.isNotEmpty(getLogger(), appLanguageCodeTransData.verifyCodeNameByKey(TerminologyTypeEnum.ID_TYPE.name(), insured.getIdType()), AppErrorConfigEnum.APP_APP_INSURED_ID_TYPE_FORMAT_ERROR);
                AssertUtils.isNotEmpty(getLogger(), appLanguageCodeTransData.verifyCodeNameByKey(TerminologyTypeEnum.SOCIAL_SECURITY.name(), insured.getSocialSecurity()), AppErrorConfigEnum.APP_APP_INSURED_SOCIAL_SECURITY_FORMAT_ERROR);
                AssertUtils.isNotEmpty(getLogger(), appLanguageCodeTransData.verifyCodeNameByKey(TerminologyTypeEnum.RELATIONSHIP_WITH_THE_APPLICANT.name(), insured.getRelationship()), AppErrorConfigEnum.APP_APP_INSURED_RELATIONSHIP_WITH_THE_APPLICANT_FORMAT_ERROR);
                //验证职业
                ResultObject<CareerResponse> respFcResultObjectInsured = platformCareerApi.careerInfoGet(insured.getOccupationCode(),null);
                AssertUtils.isResultObjectDataNull(getLogger(), respFcResultObjectInsured, AppErrorConfigEnum.APP_APP_INSURED_OCCUPATION_FORMAT_ERROR);
            }
        });
    }
}
