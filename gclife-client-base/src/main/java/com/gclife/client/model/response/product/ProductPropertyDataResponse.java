package com.gclife.client.model.response.product;

import com.gclife.client.vo.BaseResponse;
import io.swagger.annotations.ApiModelProperty;

/**
 * Created by cqh on 17-11-6.
 * <AUTHOR>
 * @desction 产品属性数据对象
 */
public class ProductPropertyDataResponse extends BaseResponse {


    @ApiModelProperty(value = "propertyDataId", name = "属性数据Id",example = "属性数据Id")
    private String propertyDataId;

    @ApiModelProperty(value = "propertyDataTypeCode", name = "属性数据类别编码",example = "属性数据类别编码(TEXT,IMAGE,ARTICLE)")
    private String propertyDataTypeCode;

    @ApiModelProperty(value = "propertyDataTitle", name = "属性数据标题",example = "属性数据标题")
    private String propertyDataTitle;

    @ApiModelProperty(value = "propertyDataSubTitle", name = "属性数据副标题",example = "属性数据副标题")
    private String propertyDataSubTitle;

    @ApiModelProperty(value = "propertyDataThumbnailUrl", name = "属性数据缩略图",example = "属性数据缩略图")
    private String propertyDataThumbnailUrl;

    @ApiModelProperty(value = "propertyDataImageUrl", name = "属性数据大图",example = "属性数据大图")
    private String propertyDataImageUrl;

    @ApiModelProperty(value = "propertyDataUrl", name = "属性数据链接",example = "属性数据链接")
    private String propertyDataUrl;

    @ApiModelProperty(value = "propertyDataContent", name = "属性数据内容",example = "属性数据内容")
    private String propertyDataContent;

    @ApiModelProperty(value = "propertyDataLogoUrl", name = "属性数据logoUrl",example = "属性数据logoUrl")
    private String propertyDataLogoUrl;

    public String getPropertyDataId() {
        return propertyDataId;
    }

    public void setPropertyDataId(String propertyDataId) {
        this.propertyDataId = propertyDataId;
    }

    public String getPropertyDataTypeCode() {
        return propertyDataTypeCode;
    }

    public void setPropertyDataTypeCode(String propertyDataTypeCode) {
        this.propertyDataTypeCode = propertyDataTypeCode;
    }

    public String getPropertyDataTitle() {
        return propertyDataTitle;
    }

    public void setPropertyDataTitle(String propertyDataTitle) {
        this.propertyDataTitle = propertyDataTitle;
    }

    public String getPropertyDataSubTitle() {
        return propertyDataSubTitle;
    }

    public void setPropertyDataSubTitle(String propertyDataSubTitle) {
        this.propertyDataSubTitle = propertyDataSubTitle;
    }

    public String getPropertyDataThumbnailUrl() {
        return propertyDataThumbnailUrl;
    }

    public void setPropertyDataThumbnailUrl(String propertyDataThumbnailUrl) {
        this.propertyDataThumbnailUrl = propertyDataThumbnailUrl;
    }

    public String getPropertyDataImageUrl() {
        return propertyDataImageUrl;
    }

    public void setPropertyDataImageUrl(String propertyDataImageUrl) {
        this.propertyDataImageUrl = propertyDataImageUrl;
    }

    public String getPropertyDataUrl() {
        return propertyDataUrl;
    }

    public void setPropertyDataUrl(String propertyDataUrl) {
        this.propertyDataUrl = propertyDataUrl;
    }

    public String getPropertyDataContent() {
        return propertyDataContent;
    }

    public void setPropertyDataContent(String propertyDataContent) {
        this.propertyDataContent = propertyDataContent;
    }

    public String getPropertyDataLogoUrl() {
        return propertyDataLogoUrl;
    }

    public void setPropertyDataLogoUrl(String propertyDataLogoUrl) {
        this.propertyDataLogoUrl = propertyDataLogoUrl;
    }
}