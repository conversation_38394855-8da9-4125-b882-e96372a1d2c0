package com.gclife.client.model.request.product;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * create 2020/7/16 下午2:36
 * description:贷款合同信息
 */
@Data
public class ReferralInfoRequest {
    @ApiModelProperty(example = "推荐类型")
    private String referralSources;
    @ApiModelProperty(example = "银行分支机构ID")
    private String bankBranchId;
    @ApiModelProperty(example = "推荐人姓名")
    private String introducerName;
    @ApiModelProperty(example = "推荐人岗位")
    private String introducerPosition;
}
