package com.gclife.client.model.response.apply;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * create 17-12-1
 * description:
 */
@Data
public class AppApplyAddCoverageResponse {
    @ApiModelProperty(value = "附加险名称", example = "大中华人寿一生健康险")
    private String productName;
    @ApiModelProperty(value = "保险期间", example = "20年")
    private String guaranteePeriod;
    @ApiModelProperty(value = "缴费期间", example = "5年缴")
    private String premiumPeriod;
    @ApiModelProperty(example = "折扣保费信息展示标识")
    private String discountPremiumFlag;
    @ApiModelProperty(example = "折扣前保费")
    private BigDecimal premiumBeforeDiscount = BigDecimal.ZERO;
    @ApiModelProperty(example = "折扣系数(%)")
    private BigDecimal specialDiscount;
    @ApiModelProperty(example = "折扣类型")
    private String discountType;
    @ApiModelProperty(value = "折扣类型 国际化", example = "按比例承担")
    private String discountTypeName;
    @ApiModelProperty(example = "优惠活动类型(CASH_DEDUCTION:现金抵扣，CCC_DISCOUNT_CAMPAIGN:CCC折扣活动)")
    private String promotionType;
    @ApiModelProperty(example = "优惠活动类型")
    private String promotionTypeName;
    @ApiModelProperty(value = "折扣计算模式(PERCENTAGE:百分比，FIXED_AMOUNT:固定数额)")
    private String discountModel;
    @ApiModelProperty(value = "折扣计算模式展示字段名称")
    private String discountModelLabel;
    @ApiModelProperty(value = "保费", example = "10")
    private BigDecimal premium;

}