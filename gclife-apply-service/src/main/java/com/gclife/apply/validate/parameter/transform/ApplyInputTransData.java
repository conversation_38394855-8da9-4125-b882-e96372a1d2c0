package com.gclife.apply.validate.parameter.transform;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.gclife.agent.api.AgentApi;
import com.gclife.agent.model.response.AgentBaseResponse;
import com.gclife.agent.model.response.AgentResponse;
import com.gclife.apply.core.jooq.tables.pojos.*;
import com.gclife.apply.dao.ApplyExtDao;
import com.gclife.apply.dao.app.ApplyPlanBusinessDao;
import com.gclife.apply.model.bo.*;
import com.gclife.apply.model.bo.app.ApplyPlanBo;
import com.gclife.apply.model.config.ApplyErrorConfigEnum;
import com.gclife.apply.model.config.ApplyTermEnum;
import com.gclife.apply.model.config.ModelConstantEnum;
import com.gclife.apply.model.request.*;
import com.gclife.apply.service.*;
import com.gclife.apply.service.data.ApplyBoService;
import com.gclife.apply.validate.ClazzBusinessService;
import com.gclife.apply.validate.parameter.InputParameterValidate;
import com.gclife.apply.validate.parameter.app.transform.AppApplyTransData;
import com.gclife.certify.api.CertifyApplyApi;
import com.gclife.certify.model.response.CertifyNumberResponse;
import com.gclife.common.function.GcFactorFunction;
import com.gclife.common.model.ResultObject;
import com.gclife.common.model.config.BaseTermEnum;
import com.gclife.common.model.config.TerminologyConfigEnum;
import com.gclife.common.service.impl.BaseBusinessServiceImpl;
import com.gclife.common.util.AssertUtils;
import com.gclife.common.util.ClazzUtils;
import com.gclife.common.util.DateUtils;
import com.gclife.common.util.JackSonUtils;
import com.gclife.party.api.CustomerBaseApi;
import com.gclife.party.api.CustomerManageApi;
import com.gclife.party.model.request.CustomerBusinessRequest;
import com.gclife.party.model.request.UserCustomerBusinessRequest;
import com.gclife.party.model.response.CustomerMessageResponse;
import com.gclife.party.model.response.UserCustomerResponse;
import com.gclife.platform.api.PlatformAreaApi;
import com.gclife.platform.api.PlatformBranchApi;
import com.gclife.platform.api.PlatformBranchBaseApi;
import com.gclife.platform.model.response.AreaNameResponse;
import com.gclife.platform.model.response.BranchResponse;
import com.gclife.product.api.ProductApi;
import com.gclife.product.api.ProductSalesApi;
import com.gclife.product.model.config.ProductTermEnum;
import com.gclife.product.model.request.calculate.LoanContractRequest;
import com.gclife.product.model.response.manager.ProductDetailedInfoResponse;
import com.gclife.product.model.response.sales.ProductDiscountActivityResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * create 17-9-18
 * description
 */
@Component
public class ApplyInputTransData extends BaseBusinessServiceImpl {

    @Autowired
    private PlatformBranchApi platformBranchApi;
    @Autowired
    private AgentApi agentApi;
    @Autowired
    private PlatformAreaApi platformAreaApi;
    @Autowired
    private ClazzBusinessService clazzBusinessService;

    @Autowired
    private AppApplyTransData appApplyTransData;

    @Autowired
    private ApplyExtDao applyExtDao;
    @Autowired
    private CertifyApplyApi certifyApplyApi;
    @Autowired
    private ProductApi productApi;
    @Autowired
    private ProductSalesApi productSalesApi;
    @Autowired
    private ApplyPlanBaseService applyPlanBaseService;
    @Autowired
    private ApplyCoverageBaseService applyCoverageBaseService;
    @Autowired
    private ApplyPlanBusinessDao applyPlanBusinessDao;

    @Autowired
    private InputParameterValidate inputParameterValidate;

    @Autowired
    private CustomerManageApi customerManageApi;
    @Autowired
    private CustomerBaseApi customerBaseApi;
    @Autowired
    private ApplyCustomerMergerBaseService applyCustomerMergerBaseService;
    @Autowired
    private PlatformBranchBaseApi platformBranchBaseApi;
    @Autowired
    private ApplyBoService applyBoService;
    @Autowired
    private ApplyBaseService applyBaseService;
    @Autowired
    private ApplyOtherInfoBaseService applyOtherInfoBaseService;
    @Autowired
    private ApplyPremiumBaseService applyPremiumBaseService;


    public void transApplyApplicantBo(String applyId, AgentBaseResponse agentBaseRespFc, ApplyApplicantBo applyApplicantBo, ApplicantInfoRequest applicantInfoRequest, String userId) {
        ClazzUtils.copyPropertiesIgnoreNull(applicantInfoRequest, applyApplicantBo);
        if (ApplyTermEnum.APPLICANT_TYPE.PERSONAL.name().equals(applyApplicantBo.getApplicantType())) {
            applyApplicantBo.setName(applicantInfoRequest.getName().toUpperCase().trim());
        }
        if (AssertUtils.isNotEmpty(applicantInfoRequest.getListExpectedPremiumSources())) {
            applyApplicantBo.setExpectedPremiumSources(JackSonUtils.toJson(applicantInfoRequest.getListExpectedPremiumSources()));
        }
        //保存职业性质
        if (AssertUtils.isNotNull(applicantInfoRequest.getOccupationNature()) && AssertUtils.isNotEmpty(applicantInfoRequest.getOccupationNature().getOccupationNature())) {
            applyOtherInfoBaseService.deleteApplyOccupationNaturePo(applyId, ApplyTermEnum.CUSTOMER_TYPE.APPLICANT.name());
            ApplyOccupationNaturePo applyOccupationNaturePo = new ApplyOccupationNaturePo();
            ClazzUtils.copyPropertiesIgnoreNull(applicantInfoRequest.getOccupationNature(), applyOccupationNaturePo);
            applyOccupationNaturePo.setSeq("100");
            applyOccupationNaturePo.setApplyId(applyId);
            applyOccupationNaturePo.setCustomerType(ApplyTermEnum.CUSTOMER_TYPE.APPLICANT.name());
            applyOtherInfoBaseService.saveApplyOccupationNaturePo(userId, applyOccupationNaturePo);
            applyApplicantBo.setOccupationNature(applyOccupationNaturePo);
        }

        applyApplicantBo.setApplyId(applyId);
        //生日数据转换
        if (AssertUtils.isNotEmpty(applicantInfoRequest.getBirthday())) {
            if (AssertUtils.isDateFormat(applicantInfoRequest.getBirthday(), DateUtils.FORMATE3)) {
                applyApplicantBo.setBirthday(DateUtils.stringToTime(applicantInfoRequest.getBirthday(), DateUtils.FORMATE3));
            } else {
                applyApplicantBo.setBirthday(Long.valueOf(applicantInfoRequest.getBirthday()));
            }
        }
        applyApplicantBo.setIdExpDate(applicantInfoRequest.getIdExpDate());
        if (AssertUtils.isNotEmpty(applicantInfoRequest.getIdExpDateFormat())) {
            applyApplicantBo.setIdExpDate(DateUtils.stringToTime(applicantInfoRequest.getIdExpDateFormat(), DateUtils.FORMATE3));
        }
        if (AssertUtils.isNotEmpty(applicantInfoRequest.getHomeAreaCode())) {
            ResultObject<AreaNameResponse> areaRespFcResultObject = platformAreaApi.areaNameGet(applicantInfoRequest.getHomeAreaCode());
            if (!AssertUtils.isResultObjectDataNull(areaRespFcResultObject) && applicantInfoRequest.getHomeAddress().contains(areaRespFcResultObject.getData().getAreaName())) {
                applyApplicantBo.setHomeAddress(applicantInfoRequest.getHomeAddress().replace(areaRespFcResultObject.getData().getAreaName(), ""));
            }
        }
        applyApplicantBo.setCompanyAddress(applicantInfoRequest.getCompanyAddress());
        if (AssertUtils.isNotEmpty(applicantInfoRequest.getCompanyAreaCode())) {
            ResultObject<AreaNameResponse> areaNameGet = platformAreaApi.areaNameGet(applicantInfoRequest.getCompanyAreaCode(), null);
            if (!AssertUtils.isResultObjectDataNull(areaNameGet)) {
                applyApplicantBo.setCompanyAddressWhole(AssertUtils.isNotEmpty(areaNameGet.getData().getAreaName())
                        ? (areaNameGet.getData().getAreaName() + " " + applicantInfoRequest.getCompanyAddress()) : null);
            }
        }

        if (AssertUtils.isNotEmpty(applicantInfoRequest.getOccupationCode())) {
            applyApplicantBo.setOccupationType(appApplyTransData.getOccupationType(applicantInfoRequest.getOccupationCode(), applyId));
        }
        if (AssertUtils.isNotEmpty(applicantInfoRequest.getCompanyLegalPersonIdExpDate())) {
            applyApplicantBo.setCompanyLegalPersonIdExpDate(DateUtils.stringToTime(applicantInfoRequest.getCompanyLegalPersonIdExpDate(), DateUtils.FORMATE3));
        }
        if (AssertUtils.isNotEmpty(applicantInfoRequest.getCompanyContractIdExpDate())) {
            applyApplicantBo.setCompanyContractIdExpDate(DateUtils.stringToTime(applicantInfoRequest.getCompanyContractIdExpDate(), DateUtils.FORMATE3));
        }

        if (AssertUtils.isNotEmpty(applicantInfoRequest.getDelegateBirthdayFormat())) {
            applyApplicantBo.setDelegateBirthday(DateUtils.stringToTime(applicantInfoRequest.getDelegateBirthdayFormat(), DateUtils.FORMATE3));
        }
        if (AssertUtils.isNotEmpty(applicantInfoRequest.getSchoolTypeList())) {
            applyApplicantBo.setSchoolType(JSON.toJSONString(applicantInfoRequest.getSchoolTypeList()));
        }
        //团险将投保人代表当成客户保存
        if (ApplyTermEnum.APPLICANT_TYPE.GROUP.name().equals(applyApplicantBo.getApplicantType())) {
            //保存客户
            CustomerBusinessRequest groupDelegateCustomer = new CustomerBusinessRequest();
            groupDelegateCustomer.setName(applicantInfoRequest.getDelegateName());
            groupDelegateCustomer.setIdNo(applicantInfoRequest.getDelegateIdNo());
            groupDelegateCustomer.setIdType(applicantInfoRequest.getDelegateIdType());
            groupDelegateCustomer.setMobile(applicantInfoRequest.getDelegateMobile());
            groupDelegateCustomer.setNationality(applicantInfoRequest.getDelegateNationality());
            groupDelegateCustomer.setSex(applicantInfoRequest.getDelegateSex());
            groupDelegateCustomer.setPosition(applicantInfoRequest.getDelegatePosition());
            groupDelegateCustomer.setEmail(applicantInfoRequest.getDelegateEmail());
            groupDelegateCustomer.setBirthday(DateUtils.stringToTime(applicantInfoRequest.getDelegateBirthdayFormat(), DateUtils.FORMATE3));
            groupDelegateCustomer.setUserId(agentBaseRespFc.getAgentId());
            ResultObject<UserCustomerResponse> respFcResultObject = customerManageApi.saveCustomerMessagePo(groupDelegateCustomer);
            AssertUtils.isResultObjectDataNull(getLogger(), respFcResultObject, ApplyErrorConfigEnum.APPLY_SAVE_CUSTOMER_ERROR);
            applyApplicantBo.setDelegateCustomerId(respFcResultObject.getData().getCustomerId());
        }

        //判断是否有客户合并数据 注意覆盖数据
        ApplyCustomerMergerBo mergerApplicant = applyCustomerMergerBaseService.queryApplyCustomerMerger(applyId, ApplyTermEnum.CUSTOMER_TYPE.APPLICANT.name());
        if (AssertUtils.isNotNull(mergerApplicant) && "MERGED".equals(mergerApplicant.getMergerStatus()) && AssertUtils.isNotEmpty(mergerApplicant.getDataCalibrationCustomerId())) {
            ResultObject<List<CustomerMessageResponse>> baseCustomerList = customerBaseApi.getBaseCustomerList(mergerApplicant.getDataCalibrationCustomerId());
            CustomerMessageResponse customerMessageResponse = baseCustomerList.getData().get(0);
            String originApplicantCustomerId = applyApplicantBo.getCustomerId();
            String originRealCustomerId = customerMessageResponse.getCustomerId();

            //用所选择的CustomerId数据覆盖当前保单数据
            if ("NO".equals(mergerApplicant.getCurrentPolicyFlag()) && AssertUtils.isNotEmpty(mergerApplicant.getDataCalibrationCustomerId())) {
                ClazzUtils.copyPropertiesIgnoreNull(customerMessageResponse, applyApplicantBo);
                applyApplicantBo.setCustomerId(originApplicantCustomerId);
                customerManageApi.updateCustomerAgentDataByCustomer(mergerApplicant.getDataCalibrationCustomerId(), originApplicantCustomerId);
            }
            //用当前保单数据覆盖所选择的CustomerId数据
            if ("YES".equals(mergerApplicant.getCurrentPolicyFlag()) && AssertUtils.isNotEmpty(mergerApplicant.getDataCalibrationCustomerId())) {
                UserCustomerBusinessRequest userCustomerBusinessRequest = new UserCustomerBusinessRequest();
                ClazzUtils.copyPropertiesIgnoreNull(applyApplicantBo, userCustomerBusinessRequest);
                userCustomerBusinessRequest.setCustomerId(originRealCustomerId);
                userCustomerBusinessRequest.setOriginCustomerAgentId(applyApplicantBo.getCustomerId());
                customerManageApi.updateRealCustomerBusinessSingle(userCustomerBusinessRequest);
            }
        }

        //保存客户
        if (AssertUtils.isNotEmpty(applyApplicantBo.getIdNo()) && AssertUtils.isNotNull(applyApplicantBo.getIdType())) {
            CustomerBusinessRequest customerBusinessRequest = (CustomerBusinessRequest) this.converterObject(applyApplicantBo, CustomerBusinessRequest.class);
            customerBusinessRequest.setUserId(agentBaseRespFc.getAgentId());
            ResultObject<UserCustomerResponse> respFcResultObject = customerManageApi.saveCustomerMessagePo(customerBusinessRequest);
            AssertUtils.isResultObjectDataNull(getLogger(), respFcResultObject, ApplyErrorConfigEnum.APPLY_SAVE_CUSTOMER_ERROR);
            applyApplicantBo.setCustomerId(respFcResultObject.getData().getCustomerId());
            //验证客户与代理人关系
            GcFactorFunction gcFuction = () -> {
                inputParameterValidate.validParameterCustomerAndAgent(customerBusinessRequest, agentBaseRespFc);
            };
            Map<String, Object> map = new HashMap<String, Object>();
            map.put(BaseTermEnum.BASE_FACTOR_CONFIG_VALUE.GC.name(), gcFuction);
            this.handleDifferent(map, ApplyTermEnum.BASE_FACTOR_CONFIG_CODE.APPLY_AGENT_SELF_PRESERVATION_VERIFICATION.name());

        }
    }

    public ApplyContactInfoBo transApplyContactInfoBo(String applyId, ApplyContactInfoBo applyContactInfoBo, ApplyContactInfoRequest applyContactInfoRequest) {
        if (!AssertUtils.isNotNull(applyContactInfoBo)) {
            applyContactInfoBo = new ApplyContactInfoBo();
        }
        applyContactInfoBo.setApplyId(applyId);
        if (AssertUtils.isNotEmpty(applyContactInfoRequest.getSendAddrAreaCode())) {
            ResultObject<AreaNameResponse> respFcResultObject = platformAreaApi.areaNameGet(applyContactInfoRequest.getSendAddrAreaCode());
            if (!AssertUtils.isResultObjectDataNull(respFcResultObject) && applyContactInfoRequest.getSendAddrContact().contains(respFcResultObject.getData().getAreaName())) {
                applyContactInfoRequest.setSendAddrContact(applyContactInfoRequest.getSendAddrContact().replace(respFcResultObject.getData().getAreaName(), ""));
            }
        }
        applyContactInfoBo.setContractName(applyContactInfoRequest.getContractName());
        applyContactInfoBo.setSendAddrContact(applyContactInfoRequest.getSendAddrContact());
        applyContactInfoBo.setPostcodes(applyContactInfoRequest.getPostcodes());
        applyContactInfoBo.setContractPhone(applyContactInfoRequest.getContractPhone());
        applyContactInfoBo.setSmsServiceFlag(applyContactInfoRequest.getSmsServiceFlag());
        applyContactInfoBo.setSendAddrAreaCode(applyContactInfoRequest.getSendAddrAreaCode());
        applyContactInfoBo.setContractMobile(applyContactInfoRequest.getContractMobile());
        return applyContactInfoBo;
    }


    public List<ApplyAccountBo> transApplyAccountInfoBo(String applyId, ApplyBo applyBo, ApplyRequest applyRequest, List<ApplyAccountRequest> listApplyAccountInfo) {
        List<ApplyAccountBo> listApplyAccountBo = new ArrayList<>();
        listApplyAccountInfo.forEach(applyAccountRequest -> {
            if (AssertUtils.isNotNull(applyAccountRequest) && !AssertUtils.checkObjFieldAllIsNull(applyAccountRequest)) {
                if (AssertUtils.isNotEmpty(applyAccountRequest.getInitialPaymentMode())) {
                    applyBo.setInitialPaymentMode(applyAccountRequest.getInitialPaymentMode());
                }
                ApplyAccountBo applyAccountBo = new ApplyAccountBo();
                applyAccountBo.setApplyId(applyId);
                applyAccountBo.setUseType(ApplyTermEnum.ACCOUNT_USE_TYPE.PAY.name());
                applyAccountBo.setAccountNo(applyAccountRequest.getAccountNo());
                applyAccountBo.setBankCode(applyAccountRequest.getBankCode());
                applyAccountBo.setSubbranch(applyAccountRequest.getSubbranch());
                applyAccountBo.setAccountOwner(applyAccountRequest.getAccountOwner());
                applyAccountBo.setIdType(applyAccountRequest.getIdType());
                applyAccountBo.setIdNo(applyAccountRequest.getIdNo());
                applyAccountBo.setAccountType(applyAccountRequest.getAccountType());
                if (AssertUtils.isNotNull(applyRequest.getApplicant())) {
                    applyAccountBo.setApplicantName(applyRequest.getApplicant().getName());
                }
                applyAccountBo.setRelationship(applyAccountRequest.getRelationship());
                applyAccountBo.setAuthorizedDate(DateUtils.getCurrentTime());
                applyAccountBo.setAreaCode(applyAccountRequest.getAreaCode());
                listApplyAccountBo.add(applyAccountBo);
            }
        });
        return listApplyAccountBo;
    }

    public ApplyBeneficiaryBo transApplyBeneficiaryBo(String applyId, BeneficiaryInfoRequest beneficiaryInfoRequest) {
        ApplyBeneficiaryBo applyBeneficiaryBo = new ApplyBeneficiaryBo();
        applyBeneficiaryBo.setApplyId(applyId);
        applyBeneficiaryBo.setName(beneficiaryInfoRequest.getName().toUpperCase().trim());
        applyBeneficiaryBo.setSex(beneficiaryInfoRequest.getSex());
        if (AssertUtils.isNotEmpty(beneficiaryInfoRequest.getBirthday())) {
            applyBeneficiaryBo.setBirthday(Long.valueOf(beneficiaryInfoRequest.getBirthday()));
        }
        if (AssertUtils.isNotEmpty(beneficiaryInfoRequest.getIdType())) {
            applyBeneficiaryBo.setIdType(beneficiaryInfoRequest.getIdType());
        }
        if (AssertUtils.isNotEmpty(beneficiaryInfoRequest.getIdNo())) {
            applyBeneficiaryBo.setIdNo(beneficiaryInfoRequest.getIdNo());
        }
        return applyBeneficiaryBo;
    }

    /**
     * 被保人数据转换
     *
     * @param applyId            投保单ID
     * @param insuredInfoRequest 被保人
     * @return ApplyInsuredBo
     */
    public ApplyInsuredBo transApplyInsuredBo(String applyId, InsuredInfoRequest insuredInfoRequest) {
        ApplyInsuredBo applyInsuredBo = (ApplyInsuredBo) this.converterObject(insuredInfoRequest, ApplyInsuredBo.class);
        applyInsuredBo.setName(insuredInfoRequest.getName().toUpperCase().trim());
        if (AssertUtils.isNotEmpty(insuredInfoRequest.getListExpectedPremiumSources())) {
            applyInsuredBo.setExpectedPremiumSources(JackSonUtils.toJson(insuredInfoRequest.getListExpectedPremiumSources()));
        }
        //保存职业性质
        if (AssertUtils.isNotNull(insuredInfoRequest.getOccupationNature()) && AssertUtils.isNotEmpty(insuredInfoRequest.getOccupationNature().getOccupationNature())) {
            applyOtherInfoBaseService.deleteApplyOccupationNaturePo(applyId, ApplyTermEnum.CUSTOMER_TYPE.INSURED.name());
            ApplyOccupationNaturePo applyOccupationNaturePo = new ApplyOccupationNaturePo();
            ClazzUtils.copyPropertiesIgnoreNull(insuredInfoRequest.getOccupationNature(), applyOccupationNaturePo);
            applyOccupationNaturePo.setSeq("100");
            applyOccupationNaturePo.setApplyId(applyId);
            applyOccupationNaturePo.setCustomerType(ApplyTermEnum.CUSTOMER_TYPE.INSURED.name());
            applyOtherInfoBaseService.saveApplyOccupationNaturePo(null, applyOccupationNaturePo);
            applyInsuredBo.setOccupationNature(applyOccupationNaturePo);
        }

        applyInsuredBo.setApplyId(applyId);
        //生日数据转换
        if (AssertUtils.isNotEmpty(insuredInfoRequest.getBirthday())) {
            applyInsuredBo.setBirthday(Long.valueOf(insuredInfoRequest.getBirthday()));
        }
        if (AssertUtils.isNotEmpty(insuredInfoRequest.getHomeAreaCode())) {
            ResultObject<AreaNameResponse> areaRespFcResultObject = platformAreaApi.areaNameGet(insuredInfoRequest.getHomeAreaCode());
            if (!AssertUtils.isResultObjectDataNull(areaRespFcResultObject) && insuredInfoRequest.getHomeAddress().contains(areaRespFcResultObject.getData().getAreaName())) {
                insuredInfoRequest.setHomeAddress(insuredInfoRequest.getHomeAddress().replace(areaRespFcResultObject.getData().getAreaName(), ""));
            }
        }
        if (AssertUtils.isNotEmpty(insuredInfoRequest.getOccupationCode())) {
            applyInsuredBo.setOccupationType(appApplyTransData.getOccupationType(insuredInfoRequest.getOccupationCode(), applyId));
        }
        return applyInsuredBo;
    }

    public ApplyBeneficiaryInfoPo transApplyBeneficiaryInfoBo(ApplyBeneficiaryBo applyBeneficiaryBo, String insuredId, String applyId, BeneficiaryInfoRequest beneficiaryInfoRequest) {
        ApplyBeneficiaryInfoPo applyBeneficiaryInfoPo = new ApplyBeneficiaryInfoPo();
        applyBeneficiaryInfoPo.setApplyId(applyId);
        applyBeneficiaryInfoPo.setBeneficiaryId(applyBeneficiaryBo.getBeneficiaryId());
        if (AssertUtils.isNotEmpty(beneficiaryInfoRequest.getBeneficiaryNo())) {
            applyBeneficiaryInfoPo.setBeneficiaryNo(Long.valueOf(beneficiaryInfoRequest.getBeneficiaryNo()));
        }
        if (AssertUtils.isNotEmpty(beneficiaryInfoRequest.getBeneficiaryNoOrder())) {
            applyBeneficiaryInfoPo.setBeneficiaryNoOrder(beneficiaryInfoRequest.getBeneficiaryNoOrder());
        }
        if (AssertUtils.isNotEmpty(beneficiaryInfoRequest.getBeneficiaryProportion())) {
            applyBeneficiaryInfoPo.setBeneficiaryProportion(new BigDecimal(beneficiaryInfoRequest.getBeneficiaryProportion()));
        }
        applyBeneficiaryInfoPo.setInsuredId(insuredId);
        applyBeneficiaryInfoPo.setRelationship(beneficiaryInfoRequest.getRelationship());
        applyBeneficiaryInfoPo.setModifyFlag(TerminologyConfigEnum.WHETHER.YES.name());
        applyBeneficiaryInfoPo.setRelationshipInstructions(beneficiaryInfoRequest.getRelationshipInstructions());
        return applyBeneficiaryInfoPo;
    }

    public ApplyCoverageBo transApplyCoverageBo(ApplyCoverageInfoRequest applyCoverageInfoRequest, String applyId) {
        ApplyCoverageBo applyCoverageBo = new ApplyCoverageBo();
        applyCoverageBo.setProductId(applyCoverageInfoRequest.getProductId());
        applyCoverageBo.setProductCode(applyCoverageInfoRequest.getProductCode());
        applyCoverageBo.setPrimaryFlag(applyCoverageInfoRequest.getPrimaryFlag());

        //动态属性设置
        List<ApplyProductFieldRequest> listApplyProductField = applyCoverageInfoRequest.getListProductFields();
        if (null != listApplyProductField) {
            List<ApplyCoverageDutyBo> listApplyCoverageDutyBo = new ArrayList<>();
            List<String> dutyIds = new ArrayList<>();
            listApplyProductField.forEach(applyProductFieldRequest -> {
                if (AssertUtils.isNotEmpty(applyProductFieldRequest.getFieldName())) {
                    if (!AssertUtils.isNotEmpty(applyProductFieldRequest.getDutyId())) {
                        //险种参数
                        clazzBusinessService.setFieldValueByName(applyProductFieldRequest.getFieldName(), applyProductFieldRequest.getParameterValue(), applyCoverageBo);
                        if (AssertUtils.isNotEmpty(applyProductFieldRequest.getParameterUnit())) {
                            clazzBusinessService.setFieldValueByName(applyProductFieldRequest.getFieldName() + "Unit", applyProductFieldRequest.getParameterUnit(), applyCoverageBo);
                        } else {
                            if (AssertUtils.isNotNull(applyProductFieldRequest.getParameterValue())) {
                                clazzBusinessService.setFieldValueByName(applyProductFieldRequest.getFieldName() + "Unit", "YEAR", applyCoverageBo);
                            }
                        }
                    } else {
                        //责任参数
                        if (dutyIds.contains(applyProductFieldRequest.getDutyId())) {
                            listApplyCoverageDutyBo.forEach(applyCoverageDutyBo -> {
                                if (applyCoverageDutyBo.getDutyId().equals(applyProductFieldRequest.getDutyId())) {
                                    clazzBusinessService.setFieldValueByName(applyProductFieldRequest.getFieldName(), applyProductFieldRequest.getParameterValue(), applyCoverageDutyBo);
                                    if (AssertUtils.isNotEmpty(applyProductFieldRequest.getParameterUnit())) {
                                        clazzBusinessService.setFieldValueByName(applyProductFieldRequest.getFieldName() + "Unit", applyProductFieldRequest.getParameterUnit(), applyCoverageDutyBo);
                                    } else {
                                        clazzBusinessService.setFieldValueByName(applyProductFieldRequest.getFieldName() + "Unit", "YEAR", applyCoverageDutyBo);
                                    }
                                }
                            });
                        } else {
                            dutyIds.add(applyProductFieldRequest.getDutyId());
                            ApplyCoverageDutyBo applyCoverageDutyBo = new ApplyCoverageDutyBo();
                            applyCoverageDutyBo.setDutyId(applyProductFieldRequest.getDutyId());
                            clazzBusinessService.setFieldValueByName(applyProductFieldRequest.getFieldName(), applyProductFieldRequest.getParameterValue(), applyCoverageDutyBo);
                            if (AssertUtils.isNotEmpty(applyProductFieldRequest.getParameterUnit())) {
                                clazzBusinessService.setFieldValueByName(applyProductFieldRequest.getFieldName() + "Unit", applyProductFieldRequest.getParameterUnit(), applyCoverageDutyBo);
                            } else {
                                clazzBusinessService.setFieldValueByName(applyProductFieldRequest.getFieldName() + "Unit", "YEAR", applyCoverageDutyBo);
                            }
                            listApplyCoverageDutyBo.add(applyCoverageDutyBo);
                        }
                    }
                }
            });
            if (Arrays.asList(ApplyTermEnum.PRODUCT.PRODUCT_5.id(), ApplyTermEnum.PRODUCT.PRODUCT_28.id()).contains(applyCoverageBo.getProductId()) && !AssertUtils.isNotNull(applyCoverageBo.getBaseAmount()) && AssertUtils.isNotNull(applyCoverageBo.getAmount())) {
                applyCoverageBo.setBaseAmount(new BigDecimal(applyCoverageBo.getAmount()));
            }
            if (ProductTermEnum.PRODUCT.PRODUCT_34.id().equals(applyCoverageBo.getProductId())){
                ApplyCoveragePlanBo applyCoveragePlan = applyPlanBusinessDao.getApplyCoveragePlan(applyId, applyCoverageBo.getProductId());
                if (AssertUtils.isNotNull(applyCoveragePlan)) {
                    applyCoverageBo.setPackageCode(applyCoveragePlan.getPackageCode());
                    applyCoverageBo.setPlanCode(applyCoveragePlan.getPlanCode());
                }
            }
            applyCoverageBo.setListCoverageDuty(listApplyCoverageDutyBo);
        }
        return applyCoverageBo;

    }


    public List<ApplyQuestionnaireAnswerBo> transApplyQuestionnaireAnswerBo(String applyId, String questionId, List<ApplyQuestionnaireAnswerRequest> listAnswers) {
        List<ApplyQuestionnaireAnswerBo> listApplyQuestionnaireAnswerBo = new ArrayList<>();
        listAnswers.forEach(applyQuestionnaireAnswerRequest -> {
            ApplyQuestionnaireAnswerBo applyQuestionnaireAnswerBo = new ApplyQuestionnaireAnswerBo();
            applyQuestionnaireAnswerBo.setApplyId(applyId);
            applyQuestionnaireAnswerBo.setQuestionId(questionId);
            applyQuestionnaireAnswerBo.setAnswer(applyQuestionnaireAnswerRequest.getAnswer());
            applyQuestionnaireAnswerBo.setCustomerType(applyQuestionnaireAnswerRequest.getCustomerType());
            listApplyQuestionnaireAnswerBo.add(applyQuestionnaireAnswerBo);
        });
        return listApplyQuestionnaireAnswerBo;
    }

    public ApplyInsuredBo transApplyInsuredBoFromApplicant(String applyId, ApplicantInfoRequest applicantInfo) {
        ApplyInsuredBo applyInsuredBo = new ApplyInsuredBo();
        ClazzUtils.copyPropertiesIgnoreNull(applicantInfo, applyInsuredBo);

        if (AssertUtils.isNotEmpty(applicantInfo.getListExpectedPremiumSources())) {
            applyInsuredBo.setExpectedPremiumSources(JackSonUtils.toJson(applicantInfo.getListExpectedPremiumSources()));
        }
        //保存职业性质
        if (AssertUtils.isNotNull(applicantInfo.getOccupationNature()) && AssertUtils.isNotEmpty(applicantInfo.getOccupationNature().getOccupationNature())) {
            applyOtherInfoBaseService.deleteApplyOccupationNaturePo(applyId, ApplyTermEnum.CUSTOMER_TYPE.INSURED.name());
            ApplyOccupationNaturePo applyOccupationNaturePo = new ApplyOccupationNaturePo();
            ClazzUtils.copyPropertiesIgnoreNull(applicantInfo.getOccupationNature(), applyOccupationNaturePo);
            applyOccupationNaturePo.setApplyOccupationNatureId(null);
            applyOccupationNaturePo.setSeq("100");
            applyOccupationNaturePo.setApplyId(applyId);
            applyOccupationNaturePo.setCustomerType(ApplyTermEnum.CUSTOMER_TYPE.INSURED.name());
            applyOtherInfoBaseService.saveApplyOccupationNaturePo(null, applyOccupationNaturePo);
        }

        applyInsuredBo.setApplyId(applyId);
        //生日数据转换
        if (AssertUtils.isNotEmpty(applicantInfo.getBirthday())) {
            applyInsuredBo.setBirthday(Long.valueOf(applicantInfo.getBirthday()));
        }
        if (AssertUtils.isNotEmpty(applicantInfo.getHomeAreaCode())) {
            ResultObject<AreaNameResponse> areaRespFcResultObject = platformAreaApi.areaNameGet(applicantInfo.getHomeAreaCode());
            if (!AssertUtils.isResultObjectDataNull(areaRespFcResultObject) && applicantInfo.getHomeAddress().contains(areaRespFcResultObject.getData().getAreaName())) {
                applicantInfo.setHomeAddress(applicantInfo.getHomeAddress().replace(areaRespFcResultObject.getData().getAreaName(), ""));
            }
        }
        if (AssertUtils.isNotEmpty(applicantInfo.getOccupationCode())) {
            applyInsuredBo.setOccupationType(appApplyTransData.getOccupationType(applicantInfo.getOccupationCode(), applyId));
        }
        applyInsuredBo.setRelationship(ModelConstantEnum.RELATION_TYPE.ONESELF.name());
        return applyInsuredBo;
    }

    public ApplyBo transAppApplyPo(String userId, AppApplyBo appApplyBo) {
        ApplyBo applyBo = new ApplyBo();
        //代理人信息
        AgentResponse agentResponse = agentApi.agentByUserIdGet(userId).getData();
        AssertUtils.isNotNull(this.getLogger(), agentResponse, ApplyErrorConfigEnum.APPLY_BUSINESS_AGENT_IS_NOT_FOUND_OBJECT);
        //机构信息
        // 查询机构信息
        ResultObject<BranchResponse> branchResultObject = platformBranchBaseApi.queryOneBranchById(agentResponse.getBranchId());
        AssertUtils.isResultObjectError(getLogger(), branchResultObject);
        BranchResponse branchResponse = branchResultObject.getData();

        //生成投保单号
        ResultObject<CertifyNumberResponse> certifyNumberRespFcResultObject = certifyApplyApi.certifyNumberGetNew(branchResponse.getChannelTypeCode(),
                ApplyTermEnum.BUSINESS_TYPE.APPLY.name(),
                ApplyTermEnum.APPLICANT_TYPE.PERSONAL.name(),
                AssertUtils.isNotNull(branchResponse.getBranchBank()) ? branchResponse.getBranchBank().getBankAbbreviation() : null, branchResponse.getBranchId(),
                null,null,null);
        AssertUtils.isResultObjectDataNull(getLogger(), certifyNumberRespFcResultObject, ApplyErrorConfigEnum.APPLY_QUERY_APPLY_CERTIFY_ERROR);

        applyBo.setApplyNo(certifyNumberRespFcResultObject.getData().getCertifyNumber());
        applyBo.setApplySource(ApplyTermEnum.APPLY_SOURCE.APP.name());
        applyBo.setSalesBranchId(agentResponse.getBranchId());
        applyBo.setManagerBranchId(branchResponse.getManagerBranchId());
        applyBo.setChannelTypeCode(branchResponse.getChannelTypeCode());
        applyBo.setApplyDate(DateUtils.getCurrentTime());
        applyBo.setCurrencyCode(appApplyBo.getCurrencyCode());
        applyBo.setReceivablePremium(appApplyBo.getActualPremium());
        applyBo.setApplyType(ApplyTermEnum.APPLY_STATUS_FLAG.APPLY.name());
        applyBo.setApplyStatus(ApplyTermEnum.APPLY_STATUS_FLAG.APPLY_STATUS_INITIAL_COMPLETE.name());
        return applyBo;
    }

    public AppApplyBo transApplyInputRequest(ApplyRequest applyRequest) {
        AppApplyBo appApplyBo = new AppApplyBo();
        appApplyBo.setBusinessType("APPLY");
        if (AssertUtils.isNotEmpty(applyRequest.getBackTrackDateFormat())) {
            appApplyBo.setBackTrackDate(DateUtils.stringToTime(applyRequest.getBackTrackDateFormat(), DateUtils.FORMATE3));
            applyRequest.setBackTrackDate(DateUtils.stringToTime(applyRequest.getBackTrackDateFormat(), DateUtils.FORMATE3));
        }
        //APPLY_TWO代表需要校验投保保费  保存或者提交时候才去校验投保保费
        if (ApplyTermEnum.YES_NO.NO.name().equals(applyRequest.getCalculateFlag())) {
            appApplyBo.setBusinessType("APPLY_TWO");
        }
        //设置机构
        ApplyPo applyPo = applyExtDao.loadApplyPoById(applyRequest.getApplyId());
        AssertUtils.isNotNull(getLogger(), applyPo, ApplyErrorConfigEnum.APPLY_BUSINESS_APPLY_IS_NOT_FOUND_OBJECT);
        AssertUtils.isNotNull(getLogger(), applyPo.getSalesBranchId(), ApplyErrorConfigEnum.APPLY_BUSINESS_AGENT_BRANCH_IS_NOT_FOUND_OBJECT);
        appApplyBo.setBackTrackDate(applyPo.getBackTrackDate());
        appApplyBo.setBranchId(applyPo.getSalesBranchId());
        appApplyBo.setApplyDate(applyPo.getApplyDate());
        //投保人
        System.out.println("=================投保人1:" + JSON.toJSONString(applyRequest.getApplicant()));
        ApplyApplicantBo applyApplicantBo = (ApplyApplicantBo) this.converterObject(applyRequest.getApplicant(), ApplyApplicantBo.class);
        System.out.println("=================投保人2:" + JSON.toJSONString(applyApplicantBo));
        applyApplicantBo.setOccupationType(appApplyTransData.getOccupationType(applyApplicantBo.getOccupationCode(), applyPo.getApplyId()));
        appApplyBo.setApplicant(applyApplicantBo);
        //被保人
        List<ApplyInsuredBo> listApplyInsuredBo = new ArrayList<>();
        String[] productId = {""};
        applyRequest.getListInsured().forEach(insuredInfoRequest -> {
            ApplyInsuredBo applyInsuredBo = (ApplyInsuredBo) this.converterObject(insuredInfoRequest, ApplyInsuredBo.class);
            if (AssertUtils.isNotEmpty(insuredInfoRequest.getBirthdayFormat())) {
                long birthday = DateUtils.stringToTime(insuredInfoRequest.getBirthdayFormat(), DateUtils.FORMATE3);
                insuredInfoRequest.setBirthday(birthday + "");
                applyInsuredBo.setBirthday(birthday);
            }
            if (AssertUtils.isNotEmpty(insuredInfoRequest.getIdExpDateFormat())) {
                long idExpDate = DateUtils.stringToTime(insuredInfoRequest.getIdExpDateFormat(), DateUtils.FORMATE3);
                insuredInfoRequest.setIdExpDate(idExpDate);
                applyInsuredBo.setIdExpDate(idExpDate);
            }
            if (AssertUtils.isNotEmpty(applyInsuredBo.getOccupationCode())) {
                String occupationType = appApplyTransData.getOccupationType(applyInsuredBo.getOccupationCode(), applyPo.getApplyId());
                applyInsuredBo.setOccupationType(occupationType);
                applyInsuredBo.setCareerType(occupationType);
            }
            //险种
            List<ApplyCoverageBo> listApplyCoverageBo = new ArrayList<>();
            if (AssertUtils.isNotEmpty(insuredInfoRequest.getListCoverage())) {
                insuredInfoRequest.getListCoverage().forEach(applyCoverageInfoRequest -> {
                    ApplyCoverageBo applyCoverageBo = this.transApplyCoverageBo(applyCoverageInfoRequest, applyPo.getApplyId());
                    if (ApplyTermEnum.PRODUCT_PRIMARY_FLAG.MAIN.name().equals(applyCoverageBo.getPrimaryFlag())) {
                        productId[0] = applyCoverageBo.getProductId();
                    }
                    listApplyCoverageBo.add(applyCoverageBo);
                });
            }
            applyInsuredBo.setListCoverage(listApplyCoverageBo);
            listApplyInsuredBo.add(applyInsuredBo);
        });
        appApplyBo.setListInsured(listApplyInsuredBo);
        //贷款信息
        LoanContractRequest loanContract = applyRequest.getLoanContract();
        if (AssertUtils.isNotNull(loanContract)) {
            //若贷款金额为空，手动计算转换后的金额
            if (!AssertUtils.isNotNull(loanContract.getLoanAmount()) &&
                    AssertUtils.isNotNull(loanContract.getExchangeRate()) &&
                    AssertUtils.isNotNull(loanContract.getNotConvertedLoanAmount())
            ) {
                loanContract.setLoanAmount(loanContract.getNotConvertedLoanAmount().multiply((BigDecimal.ONE.divide(loanContract.getExchangeRate(), 8, BigDecimal.ROUND_UP))));
            }
            appApplyBo.setLoanContract(loanContract);
        }
        String participationDiscountFlag = TerminologyConfigEnum.WHETHER.NO.name();
        Long discountDate = AssertUtils.isNotNull(applyPo.getAppSubmitUnderwritingDate()) ? applyPo.getAppSubmitUnderwritingDate() : DateUtils.getCurrentTime();
        ResultObject<List<ProductDiscountActivityResponse>> productSimpleInfo = productSalesApi.queryProductDiscountActivity(productId[0], applyPo.getSalesBranchId(), discountDate);
        ApplyPlanBo applyPlanBo = applyPlanBaseService.queryApplyPlan(applyPo.getApplyId());
        if (AssertUtils.isNotNull(applyPlanBo) && AssertUtils.isNotNull(applyPlanBo.getSpecialDiscount())
                && !AssertUtils.isResultObjectListDataNull(productSimpleInfo)
        ) {
            appApplyBo.setPromotionType(applyPlanBo.getPromotionType());
            participationDiscountFlag = TerminologyConfigEnum.WHETHER.YES.name();
        }
        appApplyBo.setParticipationDiscountFlag(participationDiscountFlag);
        appApplyBo.setDiscountDate(discountDate);

        System.out.println(JSONObject.toJSON(appApplyBo));
        return appApplyBo;
    }

    public ApplyAgentBo transApplyAgentBo(AgentResponse agentRespFc, ApplyBo applyBo) {
        ApplyAgentBo applyAgentBo = new ApplyAgentBo();
        applyAgentBo.setAgentId(agentRespFc.getAgentId());
        applyAgentBo.setAgentCode(agentRespFc.getAgentCode());
        applyAgentBo.setApplyId(applyBo.getApplyId());
        applyAgentBo.setPercent(100L);
        return applyAgentBo;
    }

    public void transAppInsuredBoFromApplicant(ApplyInsuredBo applyInsuredBo, ApplyApplicantBo applyApplicantBo) {
        applyInsuredBo.setName(AssertUtils.isNotEmpty(applyApplicantBo.getName()) ? applyApplicantBo.getName().toUpperCase().trim() : applyApplicantBo.getName());
        applyInsuredBo.setSex(applyApplicantBo.getSex());
        applyInsuredBo.setBirthday(Long.valueOf(applyApplicantBo.getBirthday()));
        applyInsuredBo.setIdType(applyApplicantBo.getIdType());
        applyInsuredBo.setIdNo(applyApplicantBo.getIdNo());
        applyInsuredBo.setIdExpDate(applyApplicantBo.getIdExpDate());
        applyInsuredBo.setPostalAddress(applyApplicantBo.getPostalAddress());
        applyInsuredBo.setZipCode(applyApplicantBo.getZipCode());
        applyInsuredBo.setPhone(applyApplicantBo.getPhone());
        applyInsuredBo.setFax(applyApplicantBo.getFax());
        applyInsuredBo.setHomeFax(applyApplicantBo.getHomeFax());
        applyInsuredBo.setNationality(applyApplicantBo.getNationality());
        applyInsuredBo.setRegisterAddress(applyApplicantBo.getRegisterAddress());
        applyInsuredBo.setHealth(applyApplicantBo.getHealth());
        applyInsuredBo.setDegree(applyApplicantBo.getDegree());
        applyInsuredBo.setCreditGrade(applyApplicantBo.getCreditGrade());
        applyInsuredBo.setSmokeFlag(applyApplicantBo.getSmokeFlag());
        applyInsuredBo.setBmi(applyApplicantBo.getBmi());
        applyInsuredBo.setLicense(applyApplicantBo.getLicense());
        applyInsuredBo.setLicenseType(applyApplicantBo.getLicenseType());
        applyInsuredBo.setOccupationType(applyApplicantBo.getOccupationType());
        applyInsuredBo.setOccupationCode(applyApplicantBo.getOccupationCode());
        applyInsuredBo.setWorkType(applyApplicantBo.getWorkType());
        applyInsuredBo.setPluralityType(applyApplicantBo.getPluralityType());
        applyInsuredBo.setSalary(applyApplicantBo.getSalary());
        applyInsuredBo.setSocialSecurity(applyApplicantBo.getSocialSecurity());
        applyInsuredBo.setBelongsCompanyAddress(applyApplicantBo.getBelongsCompanyAddress());
        applyInsuredBo.setBelongsCompanyZipCode(applyApplicantBo.getBelongsCompanyZipCode());
        applyInsuredBo.setBelongsCompanyPhone(applyApplicantBo.getBelongsCompanyPhone());
        applyInsuredBo.setBelongsCompanyFax(applyApplicantBo.getBelongsCompanyFax());
        applyInsuredBo.setJoinCompanyDate(applyApplicantBo.getJoinCompanyDate());
        applyInsuredBo.setStartWorkDate(applyApplicantBo.getStartWorkDate());
        applyInsuredBo.setIncomeSource(applyApplicantBo.getIncomeSource());
        applyInsuredBo.setFamilyIncome(applyApplicantBo.getFamilyIncome());
        applyInsuredBo.setFamilyIncomeSource(applyApplicantBo.getFamilyIncomeSource());
        applyInsuredBo.setBankCode(applyApplicantBo.getBankCode());
        applyInsuredBo.setBankAccountNo(applyApplicantBo.getBankAccountNo());
        applyInsuredBo.setBankAccountName(applyApplicantBo.getBankAccountName());
        applyInsuredBo.setCompanyIdType(applyApplicantBo.getCompanyIdType());
        applyInsuredBo.setCompanyIdNo(applyApplicantBo.getCompanyIdNo());
        applyInsuredBo.setCompanyContractMobile(applyApplicantBo.getCompanyContractMobile());
        applyInsuredBo.setCompanyContractName(applyApplicantBo.getCompanyContractName());
        applyInsuredBo.setCompanyContractAddress(applyApplicantBo.getCompanyContractAddress());
        applyInsuredBo.setOtherPhone(applyApplicantBo.getOtherPhone());
        applyInsuredBo.setHomeAreaCode(applyApplicantBo.getHomeAreaCode());
        applyInsuredBo.setBelongsCompanyAreaCode(applyApplicantBo.getBelongsCompanyAreaCode());
        applyInsuredBo.setCompanyAreaCode(applyApplicantBo.getCompanyAreaCode());
        applyInsuredBo.setCompanyType(applyApplicantBo.getCompanyType());
        applyInsuredBo.setMarriage(applyApplicantBo.getMarriage());
        applyInsuredBo.setNationality(applyApplicantBo.getNationality());
        applyInsuredBo.setStature(applyApplicantBo.getStature());
        applyInsuredBo.setAvoirdupois(applyApplicantBo.getAvoirdupois());
        applyInsuredBo.setHomeAddress(applyApplicantBo.getHomeAddress());
        applyInsuredBo.setHomePhone(applyApplicantBo.getHomePhone());
        applyInsuredBo.setHomeZipCode(applyApplicantBo.getHomeZipCode());
        applyInsuredBo.setCompanyName(applyApplicantBo.getCompanyName());
        applyInsuredBo.setPosition(applyApplicantBo.getPosition());
        applyInsuredBo.setIncome(applyApplicantBo.getIncome());
        applyInsuredBo.setCompanyAddress(applyApplicantBo.getCompanyAddress());
        applyInsuredBo.setCompanyZipCode(applyApplicantBo.getCompanyZipCode());
        applyInsuredBo.setCompanyPhone(applyApplicantBo.getCompanyPhone());
        applyInsuredBo.setOccupationCode(applyApplicantBo.getOccupationCode());
        applyInsuredBo.setEmail(applyApplicantBo.getEmail());
        applyInsuredBo.setRelationship(ModelConstantEnum.RELATION_TYPE.ONESELF.name());
        applyInsuredBo.setMobile(applyApplicantBo.getMobile());
    }

    public void saveLoanBeneficiary(ApplyBo applyBo, ApplyInsuredBo applyInsuredBo, ApplyLoanPo applyLoanPo) {
        //受益人
        ApplyBeneficiaryInfoBo applyBeneficiaryInfoBo = new ApplyBeneficiaryInfoBo();
        ApplyBeneficiaryBo applyBeneficiaryBo = new ApplyBeneficiaryBo();
        List<ApplyBeneficiaryInfoBo> applyBeneficiaryInfoBos = applyBaseService.queryApplyLoanBeneficiary(applyBo.getApplyId(), TerminologyConfigEnum.WHETHER.NO.name());
        if (AssertUtils.isNotEmpty(applyBeneficiaryInfoBos)) {
            applyBeneficiaryInfoBo = applyBeneficiaryInfoBos.get(0);
            applyBeneficiaryBo = applyBeneficiaryInfoBos.get(0).getApplyBeneficiaryBo();
        }
//        // 查询机构信息
//        ResultObject<BranchResponse> branchResultObject = platformBranchBaseApi.queryOneBranchById(applyBo.getSalesBranchId());
//        AssertUtils.isResultObjectError(getLogger(), branchResultObject);
//        BranchResponse branchResponse = branchResultObject.getData();
        final String[] productId = {""};
        List<ApplyCoveragePo> applyCoveragePos = applyCoverageBaseService.getApplyCoverageList(applyBo.getApplyId());
        if (AssertUtils.isNotEmpty(applyCoveragePos)) {
            applyCoveragePos.stream().filter(applyCoverageBo -> ApplyTermEnum.PRODUCT_PRIMARY_FLAG.MAIN.name().equals(applyCoverageBo.getPrimaryFlag()))
                    .findFirst().ifPresent(applyCoverageBo -> productId[0] = applyCoverageBo.getProductId());
        } else {
            ApplyPlanBo applyPlanBo = applyPlanBusinessDao.getApplyPlanBoById(null, applyBo.getApplyId(), null);
            if (AssertUtils.isNotNull(applyPlanBo) && AssertUtils.isNotEmpty(applyPlanBo.getCoverages())) {
                applyPlanBo.getCoverages().stream().filter(applyCoverageBo -> ApplyTermEnum.PRODUCT_PRIMARY_FLAG.MAIN.name().equals(applyCoverageBo.getPrimaryFlag()))
                        .findFirst().ifPresent(applyCoverageBo -> productId[0] = applyCoverageBo.getProductId());
            }
        }
//        if (!AssertUtils.isNotEmpty(productId[0])) {
//            productId[0] = ProductTermEnum.PRODUCT.PRODUCT_5.id();
//        }
        ResultObject<ProductDetailedInfoResponse> productDetailInfo = productApi.getProductDetailInfo(productId[0], applyBo.getSalesBranchId());
        if (AssertUtils.isResultObjectDataNull(productDetailInfo) ||
                !TerminologyConfigEnum.WHETHER.YES.name().equals(productDetailInfo.getData().getContractLoanFlag())) {
            return;
        }

        if (AssertUtils.isNotNull(applyLoanPo)) {
            applyBeneficiaryBo.setBeneficiaryBranchCode(applyLoanPo.getLoanBranchCode());
            applyBeneficiaryBo.setBeneficiaryBranchId(AssertUtils.isNotEmpty(applyLoanPo.getLoanBranchId()) ? applyLoanPo.getLoanBranchId() : null);
            applyBeneficiaryBo.setBeneficiaryBranchName(applyLoanPo.getLoanBranchName());
        } else {
            ResultObject<List<BranchResponse>> branchParentList = platformBranchBaseApi.queryBranchParentListById(applyBo.getSalesBranchId());
            AssertUtils.isResultObjectError(getLogger(), branchParentList);
            for (BranchResponse branchResponseLicense : branchParentList.getData()) {
                if (AssertUtils.isNotNull(branchResponseLicense.getBranchLicense())) {
                    applyBeneficiaryBo.setBeneficiaryBranchCode(branchResponseLicense.getBranchLicense().getLicenseNo());
                    applyBeneficiaryBo.setBeneficiaryBranchId(branchResponseLicense.getBranchId());
                    applyBeneficiaryBo.setBeneficiaryBranchName(branchResponseLicense.getBranchName());
                    break;
                }
            }
        }
        applyBeneficiaryBo.setApplyId(applyBo.getApplyId());
        applyBoService.saveApplyBeneficiaryPo(applyBeneficiaryBo);
        //受益人关系
        applyBeneficiaryInfoBo.setApplyId(applyBo.getApplyId());
        applyBeneficiaryInfoBo.setInsuredId(applyInsuredBo.getInsuredId());
        applyBeneficiaryInfoBo.setBeneficiaryId(applyBeneficiaryBo.getBeneficiaryId());
        applyBeneficiaryInfoBo.setRelationship(ApplyTermEnum.RELATIONSHIP_WITH_THE_INSURED.BORROW.name());
        applyBeneficiaryInfoBo.setModifyFlag(TerminologyConfigEnum.WHETHER.NO.name());
        applyBeneficiaryInfoBo.setBeneficiaryNoOrder(ApplyTermEnum.BENEFICIARY_NO.ORDER_ONE.name());
        applyBeneficiaryInfoBo.setBeneficiaryProportion(BigDecimal.valueOf(100));
        applyBoService.saveApplyBeneficiaryInfoPo(applyBeneficiaryInfoBo);
    }

}
