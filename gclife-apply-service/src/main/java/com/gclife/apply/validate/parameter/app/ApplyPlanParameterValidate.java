package com.gclife.apply.validate.parameter.app;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.gclife.apply.core.jooq.tables.pojos.ApplyInsuredPo;
import com.gclife.apply.core.jooq.tables.pojos.ApplyOccupationNaturePo;
import com.gclife.apply.core.jooq.tables.pojos.ApplyPo;
import com.gclife.apply.model.bo.*;
import com.gclife.apply.model.config.ApplyErrorConfigEnum;
import com.gclife.apply.model.config.ModelConstantEnum;
import com.gclife.apply.model.request.AppPlanBankRequest;
import com.gclife.apply.model.request.BankCardImageRequest;
import com.gclife.apply.model.request.IdImageRequest;
import com.gclife.apply.model.request.ImageAttachmentRequest;
import com.gclife.apply.model.request.app.AppAttachmentRequest;
import com.gclife.apply.model.request.app.CustomerAttachmentRequest;
import com.gclife.apply.service.ApplyBaseService;
import com.gclife.apply.service.business.base.BaseApplyBusinessService;
import com.gclife.apply.transform.ApplyDataTransform;
import com.gclife.common.exception.RequestException;
import com.gclife.common.model.ResultObject;
import com.gclife.common.model.pojo.AppRequestHeads;
import com.gclife.common.service.impl.BaseBusinessServiceImpl;
import com.gclife.common.util.AssertUtils;
import com.gclife.policy.api.PolicyApi;
import com.gclife.policy.model.response.PolicyAndInsuredResponse;
import com.gclife.policy.model.response.PolicyInsuredResponse;
import com.gclife.product.model.config.ProductTermEnum;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * create 17-11-28
 * description:
 */
@Component
public class ApplyPlanParameterValidate extends BaseBusinessServiceImpl {
    @Autowired
    private BaseApplyBusinessService baseApplyBusinessService;
    @Autowired
    private ApplyDataTransform applyDataTransform;
    @Autowired
    private ApplyBaseService applyBaseService;
    @Autowired
    private PolicyApi policyApi;

    public void validParameterAppPlanBank(AppPlanBankRequest appPlanBankRequest) throws RequestException {
        AssertUtils.isNotEmpty(this.getLogger(), appPlanBankRequest.getApplyId(), ApplyErrorConfigEnum.APPLY_PARAMETER_APPLY_APPLY_ID_IS_NOT_NULL);
        AssertUtils.isNotEmpty(this.getLogger(), appPlanBankRequest.getAccountNo(), ApplyErrorConfigEnum.APPLY_INPUT_ACCOUNT_NO_IS_NOT_NULL);
        AssertUtils.isNotEmpty(this.getLogger(), appPlanBankRequest.getAccountType(), ApplyErrorConfigEnum.APPLY_INPUT_ACCOUNT_TYPE_IS_NOT_NULL);
        AssertUtils.isNotEmpty(this.getLogger(), appPlanBankRequest.getAreaCode(), ApplyErrorConfigEnum.APPLY_INPUT_ACCOUNT_AREA_CODE_NO_IS_NOT_NULL);
        AssertUtils.isNotEmpty(this.getLogger(), appPlanBankRequest.getBankCode(), ApplyErrorConfigEnum.APPLY_INPUT_ACCOUNT_BANK_CODE_IS_NOT_NULL);
    }

    public void validParameterIdImage(IdImageRequest idImageRequest) throws RequestException {
        AssertUtils.isNotEmpty(this.getLogger(), idImageRequest.getApplyId(), ApplyErrorConfigEnum.APPLY_PARAMETER_APPLY_APPLY_ID_IS_NOT_NULL);
        AssertUtils.isNotEmpty(this.getLogger(), idImageRequest.getListIdAttachId(), ApplyErrorConfigEnum.APPLY_ATTACHMENT_ID_IS_NOT_NULL);
        idImageRequest.getListIdAttachId().forEach(applyAttachmentResp -> {
            AssertUtils.isNotEmpty(this.getLogger(), applyAttachmentResp.getAttachmentId(), ApplyErrorConfigEnum.APPLY_ATTACHMENT_ID_IS_NOT_NULL);
            AssertUtils.isNotEmpty(this.getLogger(), applyAttachmentResp.getAttachmentSeq() + "", ApplyErrorConfigEnum.APPLY_ATTACHMENT_SEQ_IS_NOT_NULL);
        });
    }

    public void validParameterBankCardImage(BankCardImageRequest bankCardImageRequest) throws RequestException {
        AssertUtils.isNotEmpty(this.getLogger(), bankCardImageRequest.getApplyId(), ApplyErrorConfigEnum.APPLY_PARAMETER_APPLY_APPLY_ID_IS_NOT_NULL);
        AssertUtils.isNotNull(this.getLogger(), bankCardImageRequest.getBankCardAttachFront(), ApplyErrorConfigEnum.APPLY_ATTACHMENT_ID_IS_NOT_NULL);
        AssertUtils.isNotNull(this.getLogger(), bankCardImageRequest.getBankCardAttachBack(), ApplyErrorConfigEnum.APPLY_ATTACHMENT_ID_IS_NOT_NULL);
        AssertUtils.isNotEmpty(this.getLogger(), bankCardImageRequest.getBankCardAttachFront().getAttachmentId(), ApplyErrorConfigEnum.APPLY_ATTACHMENT_ID_IS_NOT_NULL);
        AssertUtils.isNotEmpty(this.getLogger(), bankCardImageRequest.getBankCardAttachBack().getAttachmentId(), ApplyErrorConfigEnum.APPLY_ATTACHMENT_ID_IS_NOT_NULL);
    }

    public void validParameterImageAttachment(ImageAttachmentRequest imageAttachmentRequest) throws RequestException {
        AssertUtils.isNotEmpty(this.getLogger(), imageAttachmentRequest.getApplyId(), ApplyErrorConfigEnum.APPLY_PARAMETER_APPLY_APPLY_ID_IS_NOT_NULL);
        AssertUtils.isNotEmpty(this.getLogger(), imageAttachmentRequest.getApplyTips(), ApplyErrorConfigEnum.APPLY_ATTACHMENT_APPLY_TIPS_IS_NOT_NULL);
        AssertUtils.isNotEmpty(this.getLogger(), imageAttachmentRequest.getApplicationConfirmation(), ApplyErrorConfigEnum.APPLY_APPLICATION_CONFIRMATION_IS_NOT_NULL);
        AssertUtils.isNotEmpty(this.getLogger(), imageAttachmentRequest.getRiskTips(), ApplyErrorConfigEnum.APPLY_ATTACHMENT_RISK_TIPS_IS_NOT_NULL);
        //投保提示书
        imageAttachmentRequest.getApplyTips().forEach(applyAttachmentResp -> {
            AssertUtils.isNotEmpty(this.getLogger(), applyAttachmentResp.getAttachmentId(), ApplyErrorConfigEnum.APPLY_ATTACHMENT_ID_IS_NOT_NULL);
            AssertUtils.isNotEmpty(this.getLogger(), applyAttachmentResp.getAttachmentSeq() + "", ApplyErrorConfigEnum.APPLY_ATTACHMENT_SEQ_IS_NOT_NULL);
        });
        //风险提示书
        imageAttachmentRequest.getRiskTips().forEach(applyAttachmentResp -> {
            AssertUtils.isNotEmpty(this.getLogger(), applyAttachmentResp.getAttachmentId(), ApplyErrorConfigEnum.APPLY_ATTACHMENT_ID_IS_NOT_NULL);
            AssertUtils.isNotEmpty(this.getLogger(), applyAttachmentResp.getAttachmentSeq() + "", ApplyErrorConfigEnum.APPLY_ATTACHMENT_SEQ_IS_NOT_NULL);
        });
        //电子保单申请确认书
        imageAttachmentRequest.getApplicationConfirmation().forEach(applyAttachmentResp -> {
            AssertUtils.isNotEmpty(this.getLogger(), applyAttachmentResp.getAttachmentId(), ApplyErrorConfigEnum.APPLY_ATTACHMENT_ID_IS_NOT_NULL);
            AssertUtils.isNotEmpty(this.getLogger(), applyAttachmentResp.getAttachmentSeq() + "", ApplyErrorConfigEnum.APPLY_ATTACHMENT_SEQ_IS_NOT_NULL);
        });
    }

    public void validImageAttachment(List<AppAttachmentRequest> certifyRespFcs) throws RequestException {
        AssertUtils.isNotEmpty(this.getLogger(), certifyRespFcs, ApplyErrorConfigEnum.APPLY_IMAGE_ATTACHMENT_IS_NOT_NULL);
        System.out.println("影像件请求数据--" + JSONObject.toJSONString(certifyRespFcs));
        certifyRespFcs.forEach(productCertifyRespFc -> {
            AssertUtils.isNotEmpty(this.getLogger(), productCertifyRespFc.getAttachmentTypeCode(), ApplyErrorConfigEnum.APPLY_IMAGE_ATTACHMENT_CODE_IS_NOT_NULL);
            AssertUtils.isNotEmpty(this.getLogger(), productCertifyRespFc.getAttachmentId(), ApplyErrorConfigEnum.APPLY_ATTACHMENT_ID_IS_NOT_NULL);
        });
    }

    public void validImageOtherAttachment(List<AppAttachmentRequest> certifyRespFcs) throws RequestException {
        AssertUtils.isNotEmpty(this.getLogger(), certifyRespFcs, ApplyErrorConfigEnum.APPLY_IMAGE_ATTACHMENT_IS_NOT_NULL);
        System.out.println("影像件请求数据--" + JSONObject.toJSONString(certifyRespFcs));
        certifyRespFcs.forEach(productCertifyRespFc -> {
            //AssertUtils.isNotEmpty(this.getLogger(), productCertifyRespFc.getAttachmentTypeCode(), ApplyErrorConfigEnum.APPLY_IMAGE_ATTACHMENT_CODE_IS_NOT_NULL);
            AssertUtils.isNotEmpty(this.getLogger(), productCertifyRespFc.getAttachmentId(), ApplyErrorConfigEnum.APPLY_ATTACHMENT_ID_IS_NOT_NULL);
        });
    }

    public void validImageSignatureAttachment(List<AppAttachmentRequest> appAttachmentRequests) throws RequestException {
        AssertUtils.isNotEmpty(this.getLogger(), appAttachmentRequests, ApplyErrorConfigEnum.APPLY_IMAGE_ATTACHMENT_IS_NOT_NULL);
        System.out.println("影像件请求数据--" + JSONObject.toJSONString(appAttachmentRequests));
        appAttachmentRequests.forEach(appAttachmentRequest -> {
            AssertUtils.isNotEmpty(this.getLogger(), appAttachmentRequest.getAttachmentTypeCode(), ApplyErrorConfigEnum.APPLY_IMAGE_ATTACHMENT_CODE_IS_NOT_NULL);
            AssertUtils.isNotEmpty(this.getLogger(), appAttachmentRequest.getAttachmentId(), ApplyErrorConfigEnum.APPLY_ATTACHMENT_ID_IS_NOT_NULL);
            AssertUtils.isNotEmpty(this.getLogger(), appAttachmentRequest.getIsFlag(), ApplyErrorConfigEnum.APPLY_PLAN_IS_FLAG_IS_NOT_NULL);
        });
    }


    public void validParameterApplicantInfo(ApplyApplicantBo applyApplicantBo, AppRequestHeads appRequestHeads, String activationCode) throws RequestException {
        AssertUtils.isNotNull(getLogger(), applyApplicantBo, ApplyErrorConfigEnum.APPLY_INPUT_APPLICANT_INFO_IS_NOT_NULL);
        AssertUtils.isNotEmpty(getLogger(), applyApplicantBo.getName(), ApplyErrorConfigEnum.APPLY_INPUT_APPLICANT_NAME_IS_NOT_NULL);
        AssertUtils.isNotEmpty(getLogger(), applyApplicantBo.getSex(), ApplyErrorConfigEnum.APPLY_INPUT_APPLICANT_SEX_IS_NOT_NULL);
        AssertUtils.isNotNull(getLogger(), applyApplicantBo.getBirthday(), ApplyErrorConfigEnum.APPLY_INPUT_APPLICANT_BIRTHDAY_IS_NOT_NULL);
        AssertUtils.isNotEmpty(getLogger(), applyApplicantBo.getStature(), ApplyErrorConfigEnum.APPLY_INPUT_APPLICANT_STATURE_IS_NOT_NULL);
        AssertUtils.isNotEmpty(getLogger(), applyApplicantBo.getAvoirdupois(), ApplyErrorConfigEnum.APPLY_INPUT_APPLICANT_AVOIRDUPOIS_IS_NOT_NULL);
        boolean is20A = ProductTermEnum.PRODUCT.PRODUCT_20A.id().equals(applyDataTransform.getMainProductId(applyApplicantBo.getApplyId()));
        boolean scFlag = AssertUtils.isNotEmpty(activationCode);
        if (!is20A && !scFlag) {
            AssertUtils.isNotEmpty(getLogger(), applyApplicantBo.getMarriage(), ApplyErrorConfigEnum.APPLY_INPUT_APPLICANT_MARRIAGE_IS_NOT_NULL);
            AssertUtils.isNotEmpty(getLogger(), applyApplicantBo.getIncome(), ApplyErrorConfigEnum.APPLY_APP_APPLICANT_INCOME_IS_NOT_NULL);
        }
        AssertUtils.isNotEmpty(getLogger(), applyApplicantBo.getOccupationCode(), ApplyErrorConfigEnum.APPLY_INPUT_APPLICANT_OCCUPATION_CODE_IS_NOT_NULL);
        AssertUtils.isNotEmpty(getLogger(), applyApplicantBo.getIdType(), ApplyErrorConfigEnum.APPLY_INPUT_APPLICANT_ID_TYPE_IS_NOT_NULL);
        AssertUtils.isNotEmpty(getLogger(), applyApplicantBo.getIdNo(), ApplyErrorConfigEnum.APPLY_INPUT_APPLICANT_ID_NO_IS_NOT_NULL);

        AssertUtils.isNotEmpty(getLogger(), applyApplicantBo.getNationality(), ApplyErrorConfigEnum.APPLY_INPUT_APPLICANT_NATIVE_PLACE_IS_NOT_NULL);
        AssertUtils.isNotEmpty(getLogger(), applyApplicantBo.getMobile(), ApplyErrorConfigEnum.APPLY_INPUT_APPLICANT_MOBILE_IS_NOT_NULL);
        AssertUtils.isNotPureDigital(this.getLogger(),applyApplicantBo.getMobile(),ApplyErrorConfigEnum.APPLY_APP_APPLICANT_MOBILE_FORMAT_ERROR);
        AssertUtils.isNotEmpty(getLogger(), applyApplicantBo.getAddressType(), ApplyErrorConfigEnum.APPLY_APP_ADDRESS_TYPE_IS_NOT_NULL);
//        AssertUtils.isNotEmpty(getLogger(), applyApplicantBo.getEmail(), ApplyErrorConfigEnum.APPLY_INPUT_APPLICANT_EMAIL_IS_NOT_NULL);

//        //校验微信和facebook
//        GcFactorFunction gcFuction = () -> {
//            //客户来源判断
//            AssertUtils.isNotEmpty(getLogger(), applyApplicantBo.getCustomerSource(), ApplyErrorConfigEnum.APPLY_INPUT_APPLICANT_CUSTOMER_SOURCE_IS_NOT_NULL);
//
//            if (!AssertUtils.isNotEmpty(applyApplicantBo.getWechatNo()) && !AssertUtils.isNotEmpty(applyApplicantBo.getFacebookNo())) {
//                throw new RequestException(ApplyErrorConfigEnum.APPLY_APP_APPLICANT_WECHAT_OR_FACEBOOK_IS_NOT_NULL);
//            }
//        };
//        Map<String, Object> map = new HashMap<String, Object>();
//        map.put(BaseTermEnum.BASE_FACTOR_CONFIG_VALUE.GC.name(), gcFuction);
//        this.handleDifferent(map, ApplyTermEnum.BASE_FACTOR_CONFIG_CODE.APPLY_WECHAT_FACEBOOK_VERIFICATION.name());

        AssertUtils.isNotEmpty(getLogger(), applyApplicantBo.getHomeAreaCode(), ApplyErrorConfigEnum.APPLY_APP_APPLICANT_HOME_AREA_CODE_IS_NOT_NULL);
        //网销详细地址非必填
        if (!"browser".equals(appRequestHeads.getDeviceChannel()) && !scFlag) {
            AssertUtils.isNotEmpty(getLogger(), applyApplicantBo.getHomeAddress(), ApplyErrorConfigEnum.APPLY_INPUT_APPLICANT_HOME_ADDRESS_IS_NOT_NULL);
        }

        //非20A线上产品验证职业性质
//        ResultObject<ProductDetailedInfoResponse> baseApplyProductInfo = baseApplyBusinessService.getBaseApplyProductInfo(applyApplicantBo.getApplyId());
//        if (!AssertUtils.isResultObjectDataNull(baseApplyProductInfo)) {
//            ProductDetailedInfoResponse productInfoData = baseApplyProductInfo.getData();
        if (!is20A && !scFlag) {
            ApplyOccupationNaturePo occupationNature = applyApplicantBo.getOccupationNature();
            AssertUtils.isNotNull(getLogger(), occupationNature, ApplyErrorConfigEnum.APPLY_OCCUPATION_NATURE_IS_NOT_NULL);
            AssertUtils.isNotEmpty(getLogger(), occupationNature.getOccupationNature(), ApplyErrorConfigEnum.APPLY_OCCUPATION_NATURE_IS_NOT_NULL);
            List<String> requiredOccupationNature = Arrays.asList(ModelConstantEnum.OCCUPATION_NATURE.EMPLOYED_IN_GOVERNMENT_OR_PUBLIC_SECTOR.name(),
                    ModelConstantEnum.OCCUPATION_NATURE.EMPLOYED_IN_PRIVATE_SECTOR.name(),
                    ModelConstantEnum.OCCUPATION_NATURE.SELF_EMPLOYED.name());
            if (requiredOccupationNature.contains(occupationNature.getOccupationNature())) {
                AssertUtils.isNotEmpty(getLogger(), occupationNature.getOccupation(), ApplyErrorConfigEnum.APPLY_OCCUPATION_NATURE_OCCUPATION_IS_NOT_NULL);
                AssertUtils.isNotEmpty(getLogger(), occupationNature.getExactDuties(), ApplyErrorConfigEnum.APPLY_OCCUPATION_NATURE_EXACTDUTIES_IS_NOT_NULL);
            }
        }
//        }
//
    }

    public void validParameterInsuredInfo(ApplyInsuredBo applyInsuredBo, ApplyPo applyPo, AppRequestHeads appRequestHeads) throws RequestException {
        String applyId = applyPo.getApplyId();
        AssertUtils.isNotNull(getLogger(), applyInsuredBo, ApplyErrorConfigEnum.APPLY_INPUT_INSURED_INFO_IS_NOT_NULL);
        AssertUtils.isNotEmpty(getLogger(), applyInsuredBo.getName(), ApplyErrorConfigEnum.APPLY_INPUT_INSURED_NAME_IS_NOT_NULL);
        AssertUtils.isNotEmpty(getLogger(), applyInsuredBo.getSex(), ApplyErrorConfigEnum.APPLY_INPUT_INSURED_SEX_IS_NOT_NULL);
        AssertUtils.isNotNull(getLogger(), applyInsuredBo.getBirthday(), ApplyErrorConfigEnum.APPLY_INPUT_INSURED_BIRTHDAY_IS_NOT_NULL);
        AssertUtils.isNotEmpty(getLogger(), applyInsuredBo.getRelationship(), ApplyErrorConfigEnum.APPLY_INPUT_INSURED_RELATIONSHIP_IS_NOT_NULL);
        AssertUtils.isNotEmpty(getLogger(), applyInsuredBo.getStature(), ApplyErrorConfigEnum.APPLY_INPUT_INSURED_STATURE_IS_NOT_NULL);
        AssertUtils.isNotEmpty(getLogger(), applyInsuredBo.getAvoirdupois(), ApplyErrorConfigEnum.APPLY_INPUT_INSURED_AVOIRDUPOIS_IS_NOT_NULL);
        AssertUtils.isNotEmpty(getLogger(), applyInsuredBo.getIdType(), ApplyErrorConfigEnum.APPLY_INPUT_INSURED_ID_TYPE_IS_NOT_NULL);
        AssertUtils.isNotEmpty(getLogger(), applyInsuredBo.getIdNo(), ApplyErrorConfigEnum.APPLY_INPUT_INSURED_ID_NO_IS_NOT_NULL);
        AssertUtils.isNotEmpty(getLogger(), applyInsuredBo.getNationality(), ApplyErrorConfigEnum.APPLY_INPUT_INSURED_NATIVE_PLACE_IS_NOT_NULL);
        AssertUtils.isNotEmpty(getLogger(), applyInsuredBo.getHomeAreaCode(), ApplyErrorConfigEnum.APPLY_APP_INSURED_HOME_AREA_CODE_IS_NOT_NULL);
        String mainProductId = applyDataTransform.getMainProductId(applyId);
        boolean is20A = ProductTermEnum.PRODUCT.PRODUCT_20A.id().equals(mainProductId);
        boolean scFlag = AssertUtils.isNotEmpty(applyPo.getActivationCode());
        if (!is20A && !scFlag) {
            AssertUtils.isNotEmpty(getLogger(), applyInsuredBo.getIncome(), ApplyErrorConfigEnum.APPLY_APP_INSURED_INCOME_IS_NOT_NULL);
        }
        AssertUtils.isNotEmpty(getLogger(), applyInsuredBo.getAddressType(), ApplyErrorConfigEnum.APPLY_APP_ADDRESS_TYPE_IS_NOT_NULL);

        if (!"browser".equals(appRequestHeads.getDeviceChannel()) && !scFlag) {
            AssertUtils.isNotEmpty(getLogger(), applyInsuredBo.getHomeAddress(), ApplyErrorConfigEnum.APPLY_INPUT_INSURED_HOME_ADDRESS_IS_NOT_NULL);
        }
        //非20A线上产品验证职业性质
//        ResultObject<ProductDetailedInfoResponse> baseApplyProductInfo = baseApplyBusinessService.getBaseApplyProductInfo(applyId);
//        if (!AssertUtils.isResultObjectDataNull(baseApplyProductInfo)) {
//            ProductDetailedInfoResponse productInfoData = baseApplyProductInfo.getData();
        //被保人大于18岁，就必填职业性质
        int ageByBirthday = applyDataTransform.getAgeByBirthday(applyInsuredBo.getBirthday());
        if (!is20A && !scFlag && ageByBirthday >= 18) {
            ApplyOccupationNaturePo occupationNature = applyInsuredBo.getOccupationNature();
            AssertUtils.isNotNull(getLogger(), occupationNature, ApplyErrorConfigEnum.APPLY_OCCUPATION_NATURE_IS_NOT_NULL);
            AssertUtils.isNotEmpty(getLogger(), occupationNature.getOccupationNature(), ApplyErrorConfigEnum.APPLY_OCCUPATION_NATURE_IS_NOT_NULL);
            List<String> requiredOccupationNature = Arrays.asList(ModelConstantEnum.OCCUPATION_NATURE.EMPLOYED_IN_GOVERNMENT_OR_PUBLIC_SECTOR.name(),
                    ModelConstantEnum.OCCUPATION_NATURE.EMPLOYED_IN_PRIVATE_SECTOR.name(),
                    ModelConstantEnum.OCCUPATION_NATURE.SELF_EMPLOYED.name());
            if (requiredOccupationNature.contains(occupationNature.getOccupationNature())) {
                AssertUtils.isNotEmpty(getLogger(), occupationNature.getOccupation(), ApplyErrorConfigEnum.APPLY_OCCUPATION_NATURE_OCCUPATION_IS_NOT_NULL);
                AssertUtils.isNotEmpty(getLogger(), occupationNature.getExactDuties(), ApplyErrorConfigEnum.APPLY_OCCUPATION_NATURE_EXACTDUTIES_IS_NOT_NULL);
            }
        }

        // 7.6.11  #34产品限制
        if (ProductTermEnum.PRODUCT.PRODUCT_34.id().equals(mainProductId) && !scFlag) {
            BigDecimal powStature = new BigDecimal(applyInsuredBo.getStature()).divide(new BigDecimal("100")).pow(2);
            BigDecimal bmi = new BigDecimal(applyInsuredBo.getAvoirdupois()).divide(powStature, 2, BigDecimal.ROUND_HALF_UP);
            // 成年人可接受的BMI范围在17至30之间
            if (ageByBirthday >= 18 && (bmi.compareTo(new BigDecimal(17)) < 0 || bmi.compareTo(new BigDecimal(30)) > 0)) {
                throwsException(getLogger(), ApplyErrorConfigEnum.APPLY_INPUT_INSURED_BMI_IS_NOT_NORMAL);
            }
            // 每个被保人只能购买一份#34产品保单
            ApplyInsuredPo applyInsured = applyBaseService.querySokSanCustomer(applyId, applyInsuredBo.getName(), applyInsuredBo.getSex(), applyInsuredBo.getBirthday(), applyInsuredBo.getIdNo());
            ResultObject<List<PolicyAndInsuredResponse>> policyInsuredResponse = policyApi.getSokSanCustomer(applyInsuredBo.getIdNo());
            this.getLogger().info("查询保单信息：{}", JSON.toJSON(policyInsuredResponse));

            // 投保单存在
            if (applyInsured != null) {
                if (policyInsuredResponse.getStatus() == 200 && policyInsuredResponse.getData() != null) {
                    List<PolicyAndInsuredResponse> policyInsuredResponseList = policyInsuredResponse.getData();
                    // 提取保单状态
                    List<String> policyStatus = policyInsuredResponseList.stream()
                            .map(PolicyAndInsuredResponse::getPolicyStatus)
                            .collect(Collectors.toList());

                    // 获取未终止状态集合
                    List<String> nonTerminatedStatuses = getValidPolicyStatuses(false);

                    // 检查是否存在未终止的保单状态
                    boolean hasNonTerminatedPolicy = policyStatus.stream()
                            .anyMatch(nonTerminatedStatuses::contains);

                    this.getLogger().info("检查是否存在未终止的保单状态：{}", hasNonTerminatedPolicy);
                    // 如果存在未终止的保单，抛出异常
                    if (hasNonTerminatedPolicy) {
                        AssertUtils.isNull(getLogger(), applyInsured, ApplyErrorConfigEnum.APPLY_INSURED_CAN_ONLY_PURCHASE_ONE_POLICY);
                    }
                    // 如果所有保单状态均为已终止，则不抛异常，继续后续逻辑
                } else {
                    AssertUtils.isNull(getLogger(), applyInsured, ApplyErrorConfigEnum.APPLY_INSURED_CAN_ONLY_PURCHASE_ONE_POLICY);
                }
            }
        }
    }

    public void validParameterBeneficialList(List<ApplyBeneficiaryInfoBo> listBeneficiaryInfoBo, AppRequestHeads appRequestHeads, ApplyPo applyPo) throws RequestException {
        AssertUtils.isNotEmpty(getLogger(), listBeneficiaryInfoBo, ApplyErrorConfigEnum.APPLY_APP_BENEFICIAL_IS_NOT_NULL);

        listBeneficiaryInfoBo.forEach(applyBeneficiaryInfoBo -> {
            ApplyBeneficiaryBo applyBeneficiaryBo = applyBeneficiaryInfoBo.getApplyBeneficiaryBo();
            AssertUtils.isNotEmpty(getLogger(), applyBeneficiaryInfoBo.getBeneficiaryNoOrder(), ApplyErrorConfigEnum.APPLY_APP_BENEFICIAL_NO_IS_NOT_NULL);
            AssertUtils.isNotNull(getLogger(), applyBeneficiaryBo, ApplyErrorConfigEnum.APPLY_APP_BENEFICIAL_IS_NOT_NULL);
            AssertUtils.isNotEmpty(getLogger(), applyBeneficiaryBo.getName(), ApplyErrorConfigEnum.APPLY_APP_BENEFICIAL_NAME_IS_NOT_NULL);
            if (!"browser".equals(appRequestHeads.getDeviceChannel()) && !AssertUtils.isNotEmpty(applyPo.getActivationCode())) {
                AssertUtils.isNotEmpty(getLogger(), applyBeneficiaryBo.getSex(), ApplyErrorConfigEnum.APPLY_APP_BENEFICIAL_SEX_IS_NOT_NULL);
            }
            //AssertUtils.isNotNull(getLogger(), applyBeneficiaryBo.getBirthday(), ApplyErrorConfigEnum.APPLY_APP_BENEFICIAL_BIRTHDAY_IS_NOT_NULL);
            //AssertUtils.isDateTimestamp(getLogger(), applyBeneficiaryBo.getBirthday());
            //AssertUtils.isNotEmpty(getLogger(), applyBeneficiaryBo.getIdType(), ApplyErrorConfigEnum.APPLY_APP_BENEFICIAL_ID_TYPE_IS_NOT_NULL);
            //AssertUtils.isNotEmpty(getLogger(), applyBeneficiaryBo.getIdNo(), ApplyErrorConfigEnum.APPLY_APP_BENEFICIAL_ID_NO_IS_NOT_NULL);
            AssertUtils.isNotNull(getLogger(), applyBeneficiaryInfoBo.getBeneficiaryProportion(), ApplyErrorConfigEnum.APPLY_APP_BENEFICIAL_PROPORTION_IS_NOT_NULL);
            AssertUtils.isNotNull(getLogger(), applyBeneficiaryInfoBo.getRelationship(), ApplyErrorConfigEnum.APPLY_APP_BENEFICIAL_RELATIONSHIP_IS_NOT_NULL);
        });

    }

    public void validParameterApplyContactInfo(ApplyContactInfoBo applyContactInfoBo) throws RequestException {
        AssertUtils.isNotNull(getLogger(), applyContactInfoBo, ApplyErrorConfigEnum.APPLY_APP_CONTACT_INFO_IS_NOT_NULL);
        AssertUtils.isNotNull(getLogger(), applyContactInfoBo.getContractName(), ApplyErrorConfigEnum.APPLY_APP_CONTACT_INFO_NAME_IS_NOT_NULL);
        AssertUtils.isNotNull(getLogger(), applyContactInfoBo.getContractMobile(), ApplyErrorConfigEnum.APPLY_APP_CONTACT_INFO_MOBILE_IS_NOT_NULL);
        AssertUtils.isNotNull(getLogger(), applyContactInfoBo.getSendAddrAreaCode(), ApplyErrorConfigEnum.APPLY_APP_CONTACT_INFO_AREA_CODE_IS_NOT_NULL);
        AssertUtils.isNotNull(getLogger(), applyContactInfoBo.getSendAddrContact(), ApplyErrorConfigEnum.APPLY_APP_CONTACT_INFO_ADDRESS_INFO_IS_NOT_NULL);
    }

    public void validCustomerImageAttachment(CustomerAttachmentRequest customerAttachmentRequest) {
        if (!AssertUtils.isNotNull(customerAttachmentRequest.getCustomerImage()) && !AssertUtils.isNotNull(customerAttachmentRequest.getCustomerVideo())) {
            throwsException(ApplyErrorConfigEnum.APPLY_CUSTOMER_IMAGE_ATTACHMENT_IS_NOT_NULL);
        }
        if (AssertUtils.isNotNull(customerAttachmentRequest.getCustomerImage())) {
            AssertUtils.isNotEmpty(this.getLogger(), customerAttachmentRequest.getCustomerImage().getAttachmentTypeCode(), ApplyErrorConfigEnum.APPLY_IMAGE_ATTACHMENT_CODE_IS_NOT_NULL);
            AssertUtils.isNotEmpty(this.getLogger(), customerAttachmentRequest.getCustomerImage().getAttachmentId(), ApplyErrorConfigEnum.APPLY_ATTACHMENT_ID_IS_NOT_NULL);
        }
        if (AssertUtils.isNotNull(customerAttachmentRequest.getCustomerVideo())) {
            AssertUtils.isNotEmpty(this.getLogger(), customerAttachmentRequest.getCustomerVideo().getAttachmentTypeCode(), ApplyErrorConfigEnum.APPLY_IMAGE_ATTACHMENT_CODE_IS_NOT_NULL);
            AssertUtils.isNotEmpty(this.getLogger(), customerAttachmentRequest.getCustomerVideo().getAttachmentId(), ApplyErrorConfigEnum.APPLY_ATTACHMENT_ID_IS_NOT_NULL);
        }
    }

    // 定义保单终止状态
    private static final List<String> TERMINATED_STATUSES = Arrays.asList(
            "POLICY_STATUS_INDEMNITY_TERMINATION",
            "POLICY_STATUS_HESITATION_REVOKE",
            "POLICY_STATUS_INVALID_THOROUGH",
            "POLICY_STATUS_EFFECT_TERMINATION",
            "POLICY_STATUS_SURRENDER",
            "POLICY_STATUS_IEXPIRE"
    );

    // 定义保单未终止状态
    private static final List<String> NON_TERMINATED_STATUSES = Arrays.asList(
            "POLICY_STATUS_WAIT_RENEWAL",
            "POLICY_STATUS_PENDING_EFFECT",
            "POLICY_STATUS_INVALID",
            "POLICY_STATUS_EFFECTIVE",
            "POLICY_STATUS_REINSTATEMENT",
            "POLICY_STATUS_EXTEND",
            "POLICY_STATUS_WAIVER_PREMIUM",
            "POLICY_EFFECTIVE_HC"
    );

    /**
     * 根据终止标识返回相应的保单状态集合
     * @param isTerminated true返回终止状态集合，false返回未终止状态集合
     * @return 对应的保单状态集合
     */
    public List<String> getValidPolicyStatuses(boolean isTerminated) {
        return isTerminated ? TERMINATED_STATUSES : NON_TERMINATED_STATUSES;
    }

}