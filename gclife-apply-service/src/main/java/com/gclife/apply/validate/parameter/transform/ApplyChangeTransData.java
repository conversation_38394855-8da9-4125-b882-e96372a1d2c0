package com.gclife.apply.validate.parameter.transform;

import com.alibaba.fastjson.JSONObject;
import com.gclife.agent.api.AgentApi;
import com.gclife.agent.model.request.AgentApplyQueryRequest;
import com.gclife.agent.model.response.AgentResponse;
import com.gclife.apply.core.jooq.tables.pojos.ApplyChangeCoveragePo;
import com.gclife.apply.core.jooq.tables.pojos.ApplyChangePo;
import com.gclife.apply.core.jooq.tables.pojos.ApplyChangeRecordPo;
import com.gclife.apply.core.jooq.tables.pojos.ApplyCoveragePo;
import com.gclife.apply.dao.ApplyUnderWriteExtDao;
import com.gclife.apply.model.bo.*;
import com.gclife.apply.model.config.ApplyErrorConfigEnum;
import com.gclife.apply.model.config.ApplyTermEnum;
import com.gclife.apply.model.response.ApplyChangeListResponse;
import com.gclife.apply.model.response.ApplyChangeResponse;
import com.gclife.apply.model.response.ApplyCoverageResponse;
import com.gclife.apply.service.ApplyChangeBaseService;
import com.gclife.apply.service.ApplyCoverageBaseService;
import com.gclife.apply.service.ApplyPaymentTransactionBaseService;
import com.gclife.common.model.ResultObject;
import com.gclife.common.model.base.Users;
import com.gclife.common.model.config.TerminologyConfigEnum;
import com.gclife.common.service.impl.BaseBusinessServiceImpl;
import com.gclife.common.util.AssertUtils;
import com.gclife.common.util.ClazzUtils;
import com.gclife.common.util.DateUtils;
import com.gclife.platform.api.PlatformEmployeeApi;
import com.gclife.platform.model.response.EmployeResponse;
import com.gclife.product.api.ProductApi;
import com.gclife.product.model.config.ProductTermEnum;
import com.gclife.product.model.request.calculate.ApplyRequest;
import com.gclife.product.model.response.apply.ApplyResponse;
import com.gclife.product.model.response.manager.ProductDetailedInfoResponse;
import com.gclife.product.model.response.paramter.ParameterValueResponse;
import lombok.extern.slf4j.Slf4j;
import org.modelmapper.TypeToken;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * create 2022/1/10 10:01
 * description:
 */
@Slf4j
@Component
public class ApplyChangeTransData extends BaseBusinessServiceImpl {
    @Autowired
    private ApplyCoverageBaseService applyCoverageBaseService;
    @Autowired
    private ApplyChangeBaseService applyChangeBaseService;
    @Autowired
    private ApplyPaymentTransactionBaseService applyPaymentTransactionBaseService;
    @Autowired
    private PlatformEmployeeApi platformEmployeeApi;
    @Autowired
    private ApplyDetailTransData applyDetailTransData;
    @Autowired
    private AgentApi agentApi;
    @Autowired
    private ProductApi productApi;
    @Autowired
    private ApplyUnderWriteExtDao applyUnderWriteExtDao;

    public void initApplyChangeData(Users users, String applyId) {
        List<ApplyCoveragePo> applyCoveragePosOfInsured = applyCoverageBaseService.listApplyCoverageOfInsured(applyId);
        if (!AssertUtils.isNotEmpty(applyCoveragePosOfInsured)) {
            return;
        }
        Optional<ApplyCoveragePo> first = applyCoveragePosOfInsured.stream().filter(applyCoveragePo ->
                ApplyTermEnum.PRODUCT_PRIMARY_FLAG.MAIN.name().equals(applyCoveragePo.getPrimaryFlag()) &&
                        !Arrays.asList(ApplyTermEnum.PRODUCT.PRODUCT_5.id(), ApplyTermEnum.PRODUCT.PRODUCT_28.id()).contains(applyCoveragePo.getProductId())
        ).findFirst();

        ApplyPaymentTransactionBo applyPaymentTransactionBo = applyPaymentTransactionBaseService.queryApplyPaymentTransactionByType(applyId, ApplyTermEnum.CHARGE_TYPE.PREPAID_PREMIUM.name());

        boolean notPrepaidPremium = !AssertUtils.isNotNull(applyPaymentTransactionBo)
                || !Arrays.asList(ApplyTermEnum.PAYMENT_STATUS.PAYMENT_SUCCESS.name(), ApplyTermEnum.PAYMENT_STATUS.PAYMENT_FINISHED.name()).contains(applyPaymentTransactionBo.getPaymentStatus());
        //非五号产品且没有预交成功的投保单才能产生投保单变更数据
        if (first.isPresent() && notPrepaidPremium) {
            log.info("初始化投保单变更数据, applyId:{}", applyId);
            ApplyChangePo applyChangePo = applyChangeBaseService.queryApplyChangePo(null, applyId);
            if (!AssertUtils.isNotNull(applyChangePo)) {
                applyChangePo = new ApplyChangePo();
            }
            applyChangePo.setApplyId(applyId);
            applyChangePo.setMainProductId(first.get().getProductId());
            applyChangePo.setChangeStatus(ApplyTermEnum.CHANGE_STATUS.INITIAL.name());
            applyChangeBaseService.saveApplyChangePo(applyChangePo, users.getUserId());
        }
    }

    public List<ApplyChangeListResponse> transApplyChangeList(List<ApplyListBo> applyListBos) {
        List<ApplyChangeListResponse> applyChangeListResponses = new ArrayList<>();

        List<String> agentIds = applyListBos.stream().map(ApplyListBo::getAgentId)
                .filter(AssertUtils::isNotEmpty).collect(Collectors.toList());
        AgentApplyQueryRequest applyAgentRequest = new AgentApplyQueryRequest();
        applyAgentRequest.setListAgentId(agentIds);
        List<AgentResponse> agentResponses = agentApi.agentsGet(applyAgentRequest).getData();
        AssertUtils.isNotEmpty(this.getLogger(), agentResponses, ApplyErrorConfigEnum.APPLY_BUSINESS_AGENT_IS_NOT_FOUND_OBJECT);

        applyListBos.forEach(applyListBo -> {
            ApplyChangeListResponse applyChangeListResponse = new ApplyChangeListResponse();
            ClazzUtils.copyPropertiesIgnoreNull(applyListBo, applyChangeListResponse);
            agentResponses.stream().filter(agentResponse -> agentResponse.getAgentId().equals(applyListBo.getAgentId())).findFirst().ifPresent((agent) -> {
                applyChangeListResponse.setAgentCode(agent.getAgentCode());
                applyChangeListResponse.setAgentName(agent.getAgentName());
                applyChangeListResponse.setAgentIdNo(agent.getIdNo());
                applyChangeListResponse.setAgentNameByCode(agent.getAgentName() + "/" + agent.getAgentCode());
            });
            if (AssertUtils.isNotEmpty(applyListBo.getApplyChangeId())) {
                ApplyChangeRecordPo applyChangeRecordPo = applyChangeBaseService.queryNewApplyChangeRecord(applyListBo.getApplyChangeId());
                if (AssertUtils.isNotNull(applyChangeRecordPo) && AssertUtils.isNotEmpty(applyChangeRecordPo.getApplyUserId())) {
                    EmployeResponse employeResponse = platformEmployeeApi.employeGet(applyChangeRecordPo.getApplyUserId()).getData();
                    if (AssertUtils.isNotNull(employeResponse)) {
                        applyChangeListResponse.setApplyUserName(employeResponse.getEmployeName());
                    }
                }
            }
            applyChangeListResponses.add(applyChangeListResponse);
        });
        return applyChangeListResponses;
    }

    public List<ApplyCoverageResponse> transApplyCoverage(List<ApplyCoverageBo> applyCoverageBos, ApplyBo applyBo) {
        return applyCoverageBos.stream().map(applyCoverageBo -> {
            ApplyCoverageResponse applyCoverageResponse = new ApplyCoverageResponse();
            applyCoverageResponse.setCoverageId(applyCoverageBo.getCoverageId());
            applyCoverageResponse.setProductId(applyCoverageBo.getProductId());
            //查询产品信息
            ProductDetailedInfoResponse productDetailedInfoResponse = productApi.getProductDetailInfo(applyCoverageBo.getProductId(), applyBo.getSalesBranchId()).getData();
            AssertUtils.isNotNull(this.getLogger(), productDetailedInfoResponse, ApplyErrorConfigEnum.APPLY_INPUT_APPLY_COVERAGE_IS_NOT_FOUND_OBJECT);
            //转换险种字段信息
            applyDetailTransData.transferApplyCoverage(applyCoverageResponse, applyCoverageBo, productDetailedInfoResponse);
            return applyCoverageResponse;
        }).collect(Collectors.toList());
    }

    public String validatePremiumFrequency(ApplyBo applyBo, String premiumFrequency, String type) {
        String applyId = applyBo.getApplyId();
        ApplyPaymentTransactionBo applyPaymentTransactionBo = applyPaymentTransactionBaseService.queryApplyPaymentTransactionByType(applyId, ApplyTermEnum.CHARGE_TYPE.PREPAID_PREMIUM.name());
        if (AssertUtils.isNotNull(applyPaymentTransactionBo) &&
                Arrays.asList(ApplyTermEnum.PAYMENT_STATUS.PAYMENT_FINISHED.name(), ApplyTermEnum.PAYMENT_STATUS.PAYMENT_SUCCESS.name()).contains(applyPaymentTransactionBo.getPaymentStatus())) {
            throwsException(log, ApplyErrorConfigEnum.APPLY_CANNOT_BE_OPERATED_CHANGE_PREMIUM_FREQUENCY_BY_PAYMENT_ERROR);
        }
        if (!ApplyTermEnum.APPLY_STATUS_FLAG.APPLY_STATUS_UNDERWRITE_ARTIFICIAL.name().equals(applyBo.getApplyStatus()) && "UW".equals(type)) {
            throwsException(log, ApplyErrorConfigEnum.APPLY_CANNOT_BE_OPERATED_CHANGE_PREMIUM_FREQUENCY_ERROR);
        }
        if (!ApplyTermEnum.APPLY_STATUS_FLAG.APPLY_STATUS_UNDERWRITE_PASS.name().equals(applyBo.getApplyStatus()) && "CHANGE".equals(type)) {
            throwsException(log, ApplyErrorConfigEnum.APPLY_CANNOT_BE_OPERATED_CHANGE_PREMIUM_FREQUENCY_ERROR);
        }
        List<ApplyCoveragePo> applyCoveragePos = applyCoverageBaseService.listApplyCoverageOfInsured(applyId);
        AssertUtils.isNotEmpty(getLogger(), applyCoveragePos, ApplyErrorConfigEnum.APPLY_BASE_BUSINESS_APPLY_COVERAGE_IS_NOT_FOUND_OBJECT);
        Optional<ApplyCoveragePo> first = applyCoveragePos.stream().filter(applyCoveragePo -> ApplyTermEnum.PRODUCT_PRIMARY_FLAG.MAIN.name().equals(applyCoveragePo.getPrimaryFlag())).findFirst();
        if (first.isPresent() && premiumFrequency.equals(first.get().getPremiumFrequency())) {
            throwsException(log, ApplyErrorConfigEnum.APPLY_BUSINESS_PREMIUM_FREQUENCY_NO_MODIFY);
        }
        String originPremiumFrequency = first.get().getPremiumFrequency();
        ProductDetailedInfoResponse productDetailedInfoResponse = productApi.getProductDetailInfo(first.get().getProductId(), applyBo.getSalesBranchId()).getData();
        productDetailedInfoResponse.getParameterFields().stream().filter(parameterFieldResponse -> ApplyTermEnum.COVERAGE_TITLE.PREMIUM_FREQUENCY.name().equals(parameterFieldResponse.getFieldCode()))
                .findFirst().ifPresent(parameterFieldResponse -> {
            Optional<ParameterValueResponse> first1 = parameterFieldResponse.getParameterValues().stream().filter(parameterValueResponse -> parameterValueResponse.getParameterValue().equals(premiumFrequency)).findFirst();
            if (!first1.isPresent()) {
                throwsException(log, ApplyErrorConfigEnum.APPLY_CANNOT_BE_OPERATED_CHANGE_PREMIUM_FREQUENCY_ERROR);
            }
        });
        return originPremiumFrequency;
    }

    public List<ApplyChangeCoveragePo> transModifyModeOfPayment(ApplyChangeResponse applyChangeResponse, String changeTypeValue, ApplyBo applyBo, ApplyChangeRecordPo applyChangeRecordPo) {
        String originPremiumFrequency = this.validatePremiumFrequency(applyBo, changeTypeValue, "CHANGE");
        applyChangeRecordPo.setBeforeChange(originPremiumFrequency);
        applyChangeRecordPo.setAfterChange(changeTypeValue);
        applyChangeRecordPo.setChangeTypeValue(changeTypeValue);

        //设置变更后的险种信息
        List<ApplyCoverageBo> afterCoverage = new ArrayList<>();
        List<ApplyCoverageBo> beforeCoverages = new ArrayList<>();
        applyBo.getListInsuredCoverage().forEach(applyCoverageBo -> {
            ApplyCoverageBo originCoverage = new ApplyCoverageBo();
            ClazzUtils.copyPropertiesIgnoreNull(applyCoverageBo, originCoverage);
            originCoverage.setCoverageChangeStatus("BEFORE");
            beforeCoverages.add(originCoverage);
        });
        applyBo.getListInsured().forEach(applyInsuredBo -> {
            applyInsuredBo.getListCoverage().forEach(applyCoverageBo -> applyCoverageBo.setPremiumFrequency(changeTypeValue));
            afterCoverage.addAll(applyInsuredBo.getListCoverage());
        });

        ApplyRequest applyRequest = (ApplyRequest) this.converterObject(applyBo, ApplyRequest.class);
        applyRequest.setBranchId(applyBo.getSalesBranchId());
        applyRequest.setBusinessType("APPLY_TWO");
        ApplyPremiumBo applyPremiumBo = applyBo.getApplyPremiumBo();
        String participationDiscountFlag = TerminologyConfigEnum.WHETHER.NO.name();
        if (AssertUtils.isNotNull(applyPremiumBo) && (AssertUtils.isNotNull(applyPremiumBo.getSpecialDiscount()) || AssertUtils.isNotNull(applyPremiumBo.getPromotionType()))) {
            participationDiscountFlag = TerminologyConfigEnum.WHETHER.YES.name();
        }
        applyRequest.setParticipationDiscountFlag(participationDiscountFlag);
        applyRequest.setDiscountDate(applyBo.getAppSubmitUnderwritingDate());
        applyRequest.setPromotionType(applyPremiumBo.getPromotionType());
        log.info(JSONObject.toJSONString(applyRequest));

        ResultObject<ApplyResponse> resultObject1 = productApi.trialCalculation(applyRequest);
        log.info(JSONObject.toJSONString(resultObject1));
        AssertUtils.isResultObjectError(log, resultObject1);

        AppApplyBo appApplyBo1 = (AppApplyBo) this.converterObject(resultObject1.getData(), AppApplyBo.class);
        appApplyBo1.setBranchId(applyBo.getSalesBranchId());
        appApplyBo1.setUwAddPremiumFlag(true);

        List<ApplyInsuredBo> listApplyInsuredBoOrigin = appApplyBo1.getListInsured();
        List<ApplyCoverageBo> applyCoverageBoList = new ArrayList<>();
        if (AssertUtils.isNotEmpty(listApplyInsuredBoOrigin)) {
            listApplyInsuredBoOrigin.forEach(applyInsuredBo -> {
                if (AssertUtils.isNotEmpty(applyInsuredBo.getListCoverage())) {
                    applyCoverageBoList.addAll(applyInsuredBo.getListCoverage());
                }
            });
        }

        afterCoverage.forEach(applyCoverageBo ->
                applyCoverageBoList.stream().filter(coverageBo -> coverageBo.getProductId().equals(applyCoverageBo.getProductId())).findFirst().ifPresent(coverageBo -> {
                            ClazzUtils.copyPropertiesIgnoreNull(coverageBo, applyCoverageBo);
                            applyCoverageBo.setApplyId(applyBo.getApplyId());
                            applyCoverageBo.setApplyNo(applyBo.getApplyNo());
                            applyCoverageBo.setCoverageChangeStatus("AFTER");
                        }
                ));
        List<ApplyChangeCoveragePo> applyChangeCoveragePos = (List<ApplyChangeCoveragePo>) this.converterList(afterCoverage, new TypeToken<List<ApplyChangeCoveragePo>>() {
        }.getType());
        applyChangeCoveragePos.addAll((List<ApplyChangeCoveragePo>) this.converterList(beforeCoverages, new TypeToken<List<ApplyChangeCoveragePo>>() {
        }.getType()));

        //变更前的险种信息
        applyChangeResponse.setBeforeChangeCoverageList(this.transApplyCoverage(beforeCoverages, applyBo));
        //变更后的险种信息
        applyChangeResponse.setAfterChangeCoverageList(this.transApplyCoverage(afterCoverage, applyBo));
        applyChangeResponse.setPremiumFrequency(changeTypeValue);
        return applyChangeCoveragePos;
    }

    public void transExtendedPaymentDueTime(ApplyChangeResponse applyChangeResponse, String changeTypeValue, String applyId, ApplyChangeRecordPo applyChangeRecordPo) {
        ApplyUnderWriteTaskBo applyUnderWriteTaskBo = applyUnderWriteExtDao.loadUnderWriteTaskExist(applyId);
        ApplyUnderwriteDecisionBo applyUnderwriteDecisionBo = applyUnderWriteExtDao.loadUnderwriteDecisionBoExist(applyUnderWriteTaskBo.getUnderwriteTaskId());
        //5.8.3 作废时间默认14天
        long beforePaymentDueTime = DateUtils.addStringDayRT(applyUnderwriteDecisionBo.getAuditDecisionDate(), 14);
        int addDays = 7;
        //团险14天
        if (ApplyTermEnum.EXTENDED_PAYMENT_DUE_TIME.EXTENDED_PAYMENT_DUE_TIME_TWO.name().equals(changeTypeValue)) {
            addDays = 14;
        }
        long afterPaymentDueTime = DateUtils.addStringDayRT(beforePaymentDueTime, addDays);
        applyChangeRecordPo.setBeforeChange(beforePaymentDueTime + "");
        applyChangeRecordPo.setAfterChange(afterPaymentDueTime + "");
        applyChangeRecordPo.setChangeTypeValue(changeTypeValue);
        applyChangeResponse.setExtendedPaymentDueTime(changeTypeValue);
        applyChangeResponse.setBeforeChangeName(DateUtils.timeStrToString(applyChangeRecordPo.getBeforeChange(), DateUtils.FORMATE6));
        applyChangeResponse.setAfterChangeName(DateUtils.timeStrToString(applyChangeRecordPo.getAfterChange(), DateUtils.FORMATE6));
    }
}
