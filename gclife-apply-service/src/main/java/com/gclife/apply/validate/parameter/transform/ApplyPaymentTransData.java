package com.gclife.apply.validate.parameter.transform;

import com.alibaba.fastjson.JSON;
import com.gclife.apply.core.jooq.tables.pojos.ApplyPo;
import com.gclife.apply.model.bo.*;
import com.gclife.apply.model.config.ApplyErrorConfigEnum;
import com.gclife.apply.model.config.ApplyTermEnum;
import com.gclife.apply.model.config.PayNotifyTermEnum;
import com.gclife.apply.model.feign.payment.PaymentBusinessDataReqFc;
import com.gclife.apply.model.request.group.ApplyPaymentNotifyRequest;
import com.gclife.apply.model.request.group.GroupImmediatePaymentRequest;
import com.gclife.apply.model.request.group.GroupPaymentItemRequest;
import com.gclife.apply.model.response.app.ApplyPlanPaymentResponse;
import com.gclife.apply.service.ApplyBaseService;
import com.gclife.apply.service.ApplyPaymentBaseService;
import com.gclife.apply.service.ApplyPaymentTransactionBaseService;
import com.gclife.apply.service.business.MessageBusinessService;
import com.gclife.apply.service.business.group.GroupPaymentService;
import com.gclife.apply.service.business.group.GroupWaitPaymentService;
import com.gclife.apply.transform.ApplyPaymentDataTransform;
import com.gclife.common.TerminologyConfigEnum;
import com.gclife.common.model.ResultObject;
import com.gclife.common.model.base.Users;
import com.gclife.common.model.pojo.AppRequestHeads;
import com.gclife.common.service.impl.BaseBusinessServiceImpl;
import com.gclife.common.util.AssertUtils;
import com.gclife.common.util.ClazzUtils;
import com.gclife.common.util.DateUtils;
import com.gclife.common.util.JackSonUtils;
import com.gclife.payment.api.PaymentApi;
import com.gclife.payment.api.PaymentBaseApi;
import com.gclife.payment.model.request.StartPaymentRequest;
import com.gclife.payment.model.response.StartPaymentResponse;
import com.gclife.platform.api.PlatformConfigApi;
import com.gclife.platform.model.request.AccountRequest;
import com.gclife.platform.model.response.BaseFactorConfigResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * create 17-9-18
 * description
 */
@Component
@Slf4j
public class ApplyPaymentTransData extends BaseBusinessServiceImpl {

    @Autowired
    private GroupWaitPaymentService groupWaitPaymentService;

    @Autowired
    private MessageBusinessService messageBusinessService;
    @Autowired
    private PlatformConfigApi platformConfigApi;
    @Autowired
    private ApplyPaymentBaseService applyPaymentBaseService;
    @Autowired
    private GroupPaymentService groupPaymentService;
    @Autowired
    private ApplyPaymentDataTransform applyPaymentDataTransform;
    @Autowired
    private PaymentBaseApi paymentBaseApi;
    @Autowired
    private ApplyBaseService applyBaseService;
    @Autowired
    private ApplyPaymentTransactionBaseService applyPaymentTransactionBaseService;
    @Autowired
    private PaymentApi paymentApi;

    public StartPaymentRequest transStartPaymentReqFc(String signType, String paymentMode, ApplyPo applyPo, ApplyAccountBo applyAccountBo, BigDecimal payPremium, String currency) {
        StartPaymentRequest startPaymentRequest = new StartPaymentRequest();
        startPaymentRequest.setBusinessId(applyPo.getApplyId());
        startPaymentRequest.setBusinessNo(applyPo.getApplyNo());
        startPaymentRequest.setBusinessType(ApplyTermEnum.BUSINESS_TYPE.APPLY.name());
        if (AssertUtils.isNotNull(applyAccountBo)) {
            startPaymentRequest.setBankAccountNo(applyAccountBo.getAccountNo());
            startPaymentRequest.setBankAccountName(applyAccountBo.getAccountOwner());
            startPaymentRequest.setBankCode(applyAccountBo.getBankCode());
        }
        startPaymentRequest.setPaymentUserId("1");
        startPaymentRequest.setDuePayAmount(payPremium);
        startPaymentRequest.setCurrency(currency);
        startPaymentRequest.setPaymentType(paymentMode);
        startPaymentRequest.setSignType(signType);
        return startPaymentRequest;
    }


    public AccountRequest transAccountReqFc(ApplyAccountBo applyAccountBo) {
        AccountRequest accountRequest = new AccountRequest();
        accountRequest.setAccountNo(applyAccountBo.getAccountNo());
        accountRequest.setBankCode(applyAccountBo.getBankCode());
        accountRequest.setAccountOwner(applyAccountBo.getAccountOwner());
        accountRequest.setIdType(applyAccountBo.getIdType());
        accountRequest.setIdNo(applyAccountBo.getIdNo());
        accountRequest.setAccountType(applyAccountBo.getAccountType());
        accountRequest.setAuthorizedDate(applyAccountBo.getAuthorizedDate());
        accountRequest.setCity(applyAccountBo.getAreaCode());
        return accountRequest;
    }

    public ApplyPremiumPayBo transApplyPremiumPayBo(ApplyPo applyPo, String signType, String accountId, StartPaymentResponse startPaymentResponse) {
        ApplyPremiumPayBo applyPremiumPayBo = new ApplyPremiumPayBo();
        applyPremiumPayBo.setAccountId(accountId);
        applyPremiumPayBo.setApplyId(applyPo.getApplyId());
        applyPo.setSignType(signType);
        if (ApplyTermEnum.SIGN_TYPE.ISSUE_PREMIUM.name().equals(signType)) {
            applyPremiumPayBo.setFeeStatus(PayNotifyTermEnum.PAY_NOTIFY_STATUS.PAYMENT_SUCCESS.name());
            applyPremiumPayBo.setArrivalDate(DateUtils.getCurrentTime());
            applyPo.setApplyStatus(ApplyTermEnum.APPLY_STATUS_FLAG.APPLY_STATUS_APPROVE_SUCCESS.name());
        } else if (ApplyTermEnum.PAYMENT_METHODS.CASH.name().equals(applyPo.getInitialPaymentMode()) || ApplyTermEnum.PAYMENT_METHODS.BANK_TRANSFER.name().equals(applyPo.getInitialPaymentMode())) {
            applyPremiumPayBo.setFeeStatus(PayNotifyTermEnum.PAY_NOTIFY_STATUS.PAYMENT_FINISHED.name());
        } else {
            applyPremiumPayBo.setFeeStatus(PayNotifyTermEnum.PAY_NOTIFY_STATUS.PAYMENT_WAITTING.name());
            applyPo.setApplyStatus(ApplyTermEnum.APPLY_STATUS_FLAG.APPLY_STATUS_UNDERWRITE_PASS.name());
        }

        applyPremiumPayBo.setPaymentId(startPaymentResponse.getPaymentId());
        applyPremiumPayBo.setReceivableDte(DateUtils.getCurrentTime());
        applyPremiumPayBo.setCurrencyTypeCode(applyPo.getCurrencyCode());
        applyPremiumPayBo.setReceivablePremium(applyPo.getReceivablePremium());
        applyPremiumPayBo.setPaymentUrl(startPaymentResponse.getPaymentUrl());
        return applyPremiumPayBo;
    }

    public ApplyPremiumPayBo transApplyPaymentFailed(ApplyPo applyPo) {
        ApplyPremiumPayBo applyPremiumPayBo = new ApplyPremiumPayBo();
        applyPremiumPayBo.setApplyId(applyPo.getApplyId());
        applyPremiumPayBo.setFeeStatus(PayNotifyTermEnum.PAY_NOTIFY_STATUS.PAYMENT_FAILED.name());
        applyPremiumPayBo.setReceivableDte(DateUtils.getCurrentTime());
        applyPremiumPayBo.setCurrencyTypeCode(applyPo.getCurrencyCode());
        applyPremiumPayBo.setReceivablePremium(applyPo.getReceivablePremium());
        return applyPremiumPayBo;
    }

    /**
     * 发起默认支付或者付费，多退少补
     *
     * @param users                  用户
     * @param appRequestHandler      请求头
     * @param applyId                投保单ID
     * @param originApplyPaymentUWBo 标识
     * @return 是否已经发起支付
     */
    public boolean transUWPayment(Users users, AppRequestHeads appRequestHandler, String applyId, ApplyPaymentUWBo originApplyPaymentUWBo) {
        ApplyPaymentUWBo applyPaymentUWBo = new ApplyPaymentUWBo();
        ClazzUtils.copyPropertiesIgnoreNull(originApplyPaymentUWBo, applyPaymentUWBo);

        ApplyBo applyBo = applyBaseService.queryApply(applyId);
        AssertUtils.isNotNull(log, applyBo, ApplyErrorConfigEnum.APPLY_BUSINESS_APPLY_IS_NOT_FOUND_OBJECT);

        BigDecimal receivablePremium = applyBo.getReceivablePremium();

        // 是否收付费
        BigDecimal paymentAmount = BigDecimal.ZERO;
        BigDecimal receiptAmount = BigDecimal.ZERO;

        ApplyPaymentTransactionBo applyPaymentTransactionBo = applyPaymentTransactionBaseService.queryApplyPaymentTransactionByType(applyId, ApplyTermEnum.CHARGE_TYPE.PREPAID_PREMIUM.name());
        //预缴保费的单人工核保处理
        if (AssertUtils.isNotNull(applyPaymentTransactionBo)) {
            if (ApplyTermEnum.PAYMENT_STATUS.PAYMENT_SUCCESS.name().equals(applyPaymentTransactionBo.getPaymentStatus())) {
                //1.核保通过
                if (applyPaymentUWBo.isInitiatePaymentFlag()) {
                    int compareTo = receivablePremium.compareTo(applyPaymentTransactionBo.getPaymentAmount());
                    if (compareTo == 0) {
                        paymentAmount = receivablePremium;
                        applyPaymentUWBo.setApproveFlag(true);
                        log.info("核保通过且预交成功不加费，直接承保");
                    } else if (compareTo > 0) {
                        //应收保费大于已预缴保费 = 加费
                        paymentAmount = receivablePremium.subtract(applyPaymentTransactionBo.getPaymentAmount());
                        applyPaymentUWBo.setPaymentFlag(true);
                        applyPaymentUWBo.setPrepaidAddPremiumFlag(true);
                        log.info("核保通过且预交成功有加费，需要支付加费保费");
                    } else {
                        paymentAmount = receivablePremium;
                        applyPaymentUWBo.setApproveFlag(true);
                        //应收保费小于已预缴保费 = 减费
                        receiptAmount = applyPaymentTransactionBo.getPaymentAmount().subtract(receivablePremium);
                        applyPaymentUWBo.setReceiptFlag(true);
                        log.info("核保通过且已预缴保费大于应收保费，需要退还多余保费");
                    }
                } else {
                    //2.核保不通过  已预缴保费 = 退全额保费
                    receiptAmount = applyPaymentTransactionBo.getPaymentAmount();
                    applyPaymentUWBo.setReceiptFlag(true);
                    log.info("核保不通过且已预缴保费，需要退还全额保费");
                }
            } else {
                //不管需要收费还是付费，预缴不成功的订单需要再支付的时候都要失效掉  且本次交费需要交全额保费
                applyPaymentTransactionBo.setPaymentStatus(ApplyTermEnum.PAYMENT_STATUS.PAYMENT_INVALID.name());
                applyPaymentTransactionBo.setValidFlag(TerminologyConfigEnum.VALID_FLAG.invalid.name());
                applyPaymentTransactionBo.getApplyPaymentTransactionItemBos().forEach(applyPaymentTransactionItemBo -> {
                    applyPaymentTransactionItemBo.setPaymentStatus(ApplyTermEnum.PAYMENT_STATUS.PAYMENT_INVALID.name());
                    applyPaymentTransactionItemBo.setValidFlag(TerminologyConfigEnum.VALID_FLAG.invalid.name());
                });
                applyPaymentTransactionBaseService.saveApplyPaymentTransaction(users.getUserId(), applyPaymentTransactionBo);

                if (applyPaymentUWBo.isInitiatePaymentFlag()) {
                    log.info("不存在预缴成功的单且核保通过，此时发起应收保费的支付");
                    paymentAmount = receivablePremium;
                    applyPaymentUWBo.setPaymentFlag(true);
                    //存在加费且不存在预缴成功即是预交总保费（标准保费+加费保费） 反之新单
                    if (AssertUtils.isNotEmpty(applyBo.getListApplyAddPremiumPo())) {
                        applyPaymentUWBo.setPrepaidTotalPremiumFlag(true);
                    } else if (!applyPaymentUWBo.isPrepaidPremiumFlag()) { //预交标识默认false，若为预缴，则不是新单承保
                        applyPaymentUWBo.setNewApplyUWFlag(true);
                    }
                } else {
                    ResultObject<Void> voidResultObject = paymentApi.updatePaymentStatus(applyPaymentTransactionBo.getPaymentId(), ApplyTermEnum.PAYMENT_STATUS.PAYMENT_INVALID.name());
                    this.getLogger().info("拒保时作废发起的预交支付记录：{}", JackSonUtils.toJson(voidResultObject));
                }
            }
        } else if (applyPaymentUWBo.isInitiatePaymentFlag()) {
            log.info("不存在预缴成功的单且核保通过，此时发起应收保费的支付");
            paymentAmount = receivablePremium;
            applyPaymentUWBo.setPaymentFlag(true);
            //存在加费且不存在预缴成功即是预交总保费（标准保费+加费保费） 反之新单
            if (AssertUtils.isNotEmpty(applyBo.getListApplyAddPremiumPo())) {
                applyPaymentUWBo.setPrepaidTotalPremiumFlag(true);
            } else if (!applyPaymentUWBo.isPrepaidPremiumFlag()) { //预交标识默认false，若为预缴，则不是新单承保
                applyPaymentUWBo.setNewApplyUWFlag(true);
            }
        }

        log.info("核保时各个交费时间点标识:{}", JackSonUtils.toJson(applyPaymentUWBo));
        //收费
        if (applyPaymentUWBo.isPaymentFlag()) {
            log.info("正在发起支付");
            this.initiatePayment(applyBo, users, appRequestHandler, applyPaymentUWBo, paymentAmount);
        }
        //付费
        if (applyPaymentUWBo.isReceiptFlag()) {
            log.info("正在发起付费");
            applyPaymentBaseService.initiateReceipt(users, appRequestHandler, applyBo, receiptAmount);
        }
        //直接承保回调
        if (applyPaymentUWBo.isApproveFlag()) {
            log.info("正在直接承保回调");
            BigDecimal finalPaymentAmount = paymentAmount;
            new Thread(() -> {
                try {
                    Thread.sleep(2000);
                } catch (InterruptedException e) {
                    e.printStackTrace();
                }
                //将信息同步至支付中心json
                List<Map<String, String>> maps = new ArrayList<>();
                Map<String, String> map = new HashMap<>();
                map.put("businessId", applyBo.getApplyId());
                PaymentBusinessDataReqFc paymentBusinessDataReqFc = applyPaymentDataTransform.transferPaymentBusinessData(applyBo);
                if (AssertUtils.isNotNull(paymentBusinessDataReqFc)) {
                    map.put("businessJson", JSON.toJSONString(paymentBusinessDataReqFc));
                }
                map.put("isPolicyReview", TerminologyConfigEnum.WHETHER.YES.name());
                String paymentType = ApplyTermEnum.CHARGE_TYPE.NORMAL_PAYMENT.name().equals(applyBo.getApplyPremiumBo().getPaymentType()) ? ApplyTermEnum.PAYMENT_TYPE.PAYMENT.name() : ApplyTermEnum.PAYMENT_TYPE.SUSPENSE_PREMIUM.name();
                map.put("paymentType", paymentType);
                maps.add(map);
                ResultObject resultObject1 = paymentBaseApi.syncBusinessJson(maps);
                log.info("将信息同步至支付中心json:{}", JackSonUtils.toJson(resultObject1));

                ApplyPaymentNotifyRequest applyPaymentNotifyRequest = new ApplyPaymentNotifyRequest();
                applyPaymentNotifyRequest.setPaymentId(applyPaymentTransactionBo.getPaymentId());
                applyPaymentNotifyRequest.setPaymentMethodCode(applyPaymentTransactionBo.getApplyPaymentTransactionItemBos().get(0).getPaymentMethodCode());
                applyPaymentNotifyRequest.setBusinessId(applyId);
                applyPaymentNotifyRequest.setStatus(PayNotifyTermEnum.PAY_NOTIFY_STATUS.PAYMENT_SUCCESS.name());
                applyPaymentNotifyRequest.setUserId(applyBo.getApplyAgentBo().getAgentId());
                String actualPayDate = DateUtils.getCurrentTime() + "";
//                if (AssertUtils.isNotNull(applyPaymentTransactionBo.getActualPayDate())) {
//                    actualPayDate = applyPaymentTransactionBo.getActualPayDate() + "";
//                }
                applyPaymentNotifyRequest.setActualPayDate(actualPayDate);
                applyPaymentNotifyRequest.setAmount(finalPaymentAmount.toString());
                applyPaymentNotifyRequest.setApproveFlag(true);
                groupPaymentService.handPaymentNotify(applyBo.getApplyAgentBo().getAgentId(), applyPaymentNotifyRequest);
            }
            ).start();
        }
        return applyPaymentUWBo.isPaymentFlag();
    }

    /**
     * 发起默认支付动作
     *
     * @param applyBo           投保单详情
     * @param users             当前用户
     * @param appRequestHandler 请求头
     * @param applyPaymentUWBo  预缴保费标识
     * @param paymentAmount
     */
    public void initiatePayment(ApplyBo applyBo, Users users, AppRequestHeads appRequestHandler, ApplyPaymentUWBo applyPaymentUWBo, BigDecimal paymentAmount) {
        //发起ABA支付验证，未配置或者不为YES则不发起ABA支付
        ResultObject<BaseFactorConfigResponse> baseFactorConfigResponseResultObject = platformConfigApi.queryOneBaseFactorConfig("INITIATE_ABA_PAYMENT_VERIFICATION");
        if (AssertUtils.isResultObjectDataNull(baseFactorConfigResponseResultObject) ||
                !TerminologyConfigEnum.WHETHER.YES.name().equals(baseFactorConfigResponseResultObject.getData().getConfigValue())) {
            return;
        }
        GroupImmediatePaymentRequest groupImmediatePaymentRequest = new GroupImmediatePaymentRequest();
        if (applyPaymentUWBo.isPrepaidPremiumFlag()) {
            groupImmediatePaymentRequest.setPaymentType(ApplyTermEnum.CHARGE_TYPE.PREPAID_PREMIUM.name());
            groupImmediatePaymentRequest.setFeeType(ApplyTermEnum.PAYMENT_TYPE.SUSPENSE_PREMIUM.name());
        }
        groupImmediatePaymentRequest.setApplyId(applyBo.getApplyId());
        BigDecimal receivablePremium = applyBo.getReceivablePremium();
        if (AssertUtils.isNotNull(paymentAmount)) {
            receivablePremium = paymentAmount;
        }
        groupImmediatePaymentRequest.setReceivablePremium(receivablePremium);
        GroupPaymentItemRequest groupPaymentItemRequest = new GroupPaymentItemRequest();
        groupPaymentItemRequest.setPaymentTypeCode("ACTUAL");
//        groupPaymentItemRequest.setPaymentMethodCode("ABA_PAYMENTS");
        groupPaymentItemRequest.setPaymentAmount(receivablePremium);
        List<GroupPaymentItemRequest> paymentItem = new ArrayList<>();
        paymentItem.add(groupPaymentItemRequest);
        groupImmediatePaymentRequest.setPaymentItem(paymentItem);

        ResultObject<ApplyPlanPaymentResponse> resultObject = groupWaitPaymentService.postImmediatePayment(users, appRequestHandler, groupImmediatePaymentRequest);
        AssertUtils.isResultObjectError(log, resultObject);

        new Thread(() -> {
            try {
                Thread.sleep(2000);
            } catch (InterruptedException e) {
                e.printStackTrace();
            }
            //发送支付消息
            this.sendUWPaymentMessage(applyBo.getApplyId(), resultObject.getData().getPaymentId(), users, appRequestHandler, applyPaymentUWBo);
        }).start();
    }

    private void sendUWPaymentMessage(String applyId, String paymentId, Users users, AppRequestHeads appRequestHandler, ApplyPaymentUWBo applyPaymentUWBo) {
        ApplyBo applyBo = applyBaseService.queryApply(applyId);
        AssertUtils.isNotNull(log, applyBo, ApplyErrorConfigEnum.APPLY_BASE_BUSINESS_APPLY_IS_NOT_FOUND_OBJECT);
        ApplyPremiumBo applyPremiumBo = applyBo.getApplyPremiumBo();
        boolean isDiscountFlag = AssertUtils.isNotNull(applyPremiumBo.getSpecialDiscount())
                && AssertUtils.isNotNull(applyPremiumBo.getPremiumBeforeDiscount())
                && AssertUtils.isNotEmpty(applyPremiumBo.getDiscountType())
                && AssertUtils.isNotEmpty(applyPremiumBo.getDiscountModel());

        applyBo.setPaymentId(paymentId);

        if (applyPaymentUWBo.isNewApplyUWFlag()) {
            log.info("消息通知 -> 个险新单核保通过");
            //个险新单核保通过
            // 发送消息给微信/钉钉
            messageBusinessService.pushApplyMessageBatch(ApplyTermEnum.MSG_BUSINESS_TYPE.PASSED_THE_UNDERWRITING_OF_THE_NEW_SINGLE_INSURANCE_POLICY.name(), applyBo);

            // 发消息给业务员
            messageBusinessService.pushApplyMessageSingle(
                    ApplyTermEnum.MSG_BUSINESS_TYPE.PASSED_THE_UNDERWRITING_OF_THE_NEW_SINGLE_INSURANCE_POLICY.name(), applyBo, applyBo.getApplyAgentBo().getAgentId());

            // 发消息给客户
            String businessCode = ApplyTermEnum.MSG_BUSINESS_TYPE.PASSED_THE_UNDERWRITING_OF_THE_NEW_SINGLE_INSURANCE_POLICY_CUSTOMER.name();
            if (isDiscountFlag) {
                businessCode = ApplyTermEnum.MSG_BUSINESS_TYPE.PASSED_THE_UNDERWRITING_OF_THE_NEW_SINGLE_INSURANCE_POLICY_DISCOUNT_CUSTOMER.name();
            } else if (ApplyTermEnum.CHANNEL_TYPE.ONLINE.name().equals(applyBo.getChannelTypeCode())) {
                businessCode = ApplyTermEnum.MSG_BUSINESS_TYPE.PASSED_THE_UNDERWRITING_OF_THE_NEW_SINGLE_INSURANCE_POLICY_ONLINE_CUSTOMER.name();
            }
            messageBusinessService.pushApplyMessageCustomer(businessCode, applyBo, users, appRequestHandler);
        }

        if (applyPaymentUWBo.isNewGroupApplyUWFlag()) {
            log.info("消息通知 -> 团险新单核保通过");
            /*===================================================================*/
            //团险新单核保通过
            // 发送消息给微信/钉钉
            //messageBusinessService.pushApplyMessageBatch(ApplyTermEnum.MSG_BUSINESS_TYPE.GROUP_INSURANCE_NEW_SINGLE_UNDERWRITING_PASSED.name(), applyBo);

            // 发消息给业务员
            messageBusinessService.pushApplyMessageSingle(
                    ApplyTermEnum.MSG_BUSINESS_TYPE.GROUP_INSURANCE_NEW_SINGLE_UNDERWRITING_PASSED.name(), applyBo, applyBo.getApplyAgentBo().getAgentId());

            // 发消息给客户
            String businessCode = ApplyTermEnum.MSG_BUSINESS_TYPE.GROUP_INSURANCE_NEW_SINGLE_UNDERWRITING_PASSED_CUSTOMER.name();
            if (isDiscountFlag) {
                businessCode = ApplyTermEnum.MSG_BUSINESS_TYPE.GROUP_INSURANCE_NEW_SINGLE_UNDERWRITING_PASSED_DISCOUNT_CUSTOMER.name();
            }
            messageBusinessService.pushApplyGroupMessageCustomer(businessCode, applyBo, users, appRequestHandler);
        }

        if (applyPaymentUWBo.isPrepaidAddPremiumFlag()) {
            log.info("消息通知 -> 预交加费通知");
            /*====================================================================*/
            //预交加费通知 (备注：标准保费${{standardPremium}}，加费保费${{addPremium}}不知)
            // 发送消息给微信/钉钉
            messageBusinessService.pushApplyMessageBatch(ApplyTermEnum.MSG_BUSINESS_TYPE.NOTICE_OF_ADVANCE_PAYMENT_OF_FEES.name(), applyBo);

            // 发消息给业务员
            messageBusinessService.pushApplyMessageSingle(
                    ApplyTermEnum.MSG_BUSINESS_TYPE.NOTICE_OF_ADVANCE_PAYMENT_OF_FEES.name(), applyBo, applyBo.getApplyAgentBo().getAgentId());

            // 发消息给客户
            String businessCode = ApplyTermEnum.MSG_BUSINESS_TYPE.NOTICE_OF_ADVANCE_PAYMENT_OF_FEES_CUSTOMER.name();
            if (isDiscountFlag) {
                businessCode = ApplyTermEnum.MSG_BUSINESS_TYPE.NOTICE_OF_ADVANCE_PAYMENT_OF_FEES_DISCOUNT_CUSTOMER.name();
            }
            messageBusinessService.pushApplyPreMessageCustomer(businessCode, applyBo, users, appRequestHandler);
        }

        /*====================================================================*/
        if (applyPaymentUWBo.isPrepaidTotalPremiumFlag()) {
            log.info("消息通知 -> 预交加费总保费通知");
            //预交加费总保费通知
            // 发送消息给微信/钉钉
            messageBusinessService.pushApplyMessageBatchNew(ApplyTermEnum.MSG_BUSINESS_TYPE.TOTAL_PREMIUMS_PAID_IN_ADVANCE.name(), applyBo);

            // 发消息给业务员
            messageBusinessService.pushApplyMessagePreAllSingle(
                    ApplyTermEnum.MSG_BUSINESS_TYPE.TOTAL_PREMIUMS_PAID_IN_ADVANCE.name(), applyBo, applyBo.getApplyAgentBo().getAgentId());

            // 发消息给客户
            String businessCode = ApplyTermEnum.MSG_BUSINESS_TYPE.TOTAL_PREMIUMS_PAID_IN_ADVANCE_CUSTOMER.name();
            if (isDiscountFlag) {
                businessCode = ApplyTermEnum.MSG_BUSINESS_TYPE.TOTAL_PREMIUMS_PAID_IN_ADVANCE_DISCOUNT_CUSTOMER.name();
            }
            messageBusinessService.pushApplyPreAllMessageCustomer(businessCode, applyBo, users, appRequestHandler);
        }
        if (applyPaymentUWBo.isPrepaidPremiumFlag() && applyPaymentUWBo.isPaymentFlag()) {
            /*====================================================================*/
            log.info("消息通知 -> 智能核保未通过预交保费通知");
            // 发消息给业务员
            messageBusinessService.pushApplyMessagePreAllSingle(
                    ApplyTermEnum.MSG_BUSINESS_TYPE.SMART_UNDERWRITING_NOT_PASS_TO_SALEMAN.name(), applyBo, applyBo.getApplyAgentBo().getAgentId());

            // 发消息给客户
            String businessCode = ApplyTermEnum.MSG_BUSINESS_TYPE.SMART_UNDERWRITING_NOT_PASS_TO_CUSTOMERS.name();
            if (isDiscountFlag) {
                businessCode = ApplyTermEnum.MSG_BUSINESS_TYPE.SMART_UNDERWRITING_NOT_PASS_TO_CUSTOMERS_DISCOUNT.name();
            }
            messageBusinessService.pushApplyPreAllMessageCustomer(businessCode, applyBo, users, appRequestHandler);
        }
    }
}
