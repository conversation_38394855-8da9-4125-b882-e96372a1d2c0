package com.gclife.apply.model.response;

import com.gclife.common.annotation.Internation;
import com.gclife.platform.model.response.BranchSimpleResponse;
import com.gclife.platform.model.response.CareerResponse;
import com.gclife.platform.model.response.SyscodeResponse;
import com.gclife.product.model.response.sales.ProductDiscountActivityResponse;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * create 18-5-8
 * description:
 */
@Data
public class GroupInputDictionariesResponse {
    @ApiModelProperty(example = "受益人顺序", value = "受益人顺序:ORDER_ONE")
    private List<SyscodeResponse> beneficiaryNo;

    @ApiModelProperty(example = "所属行业")
    private List<CareerResponse> companyIndustry;

    @ApiModelProperty(example = "证件类型")
    private List<SyscodeResponse> idType;

    @ApiModelProperty(example = "单位类型")
    private List<SyscodeResponse> companyType;

    @ApiModelProperty(example = "单位证件类型")
    private List<SyscodeResponse> companyIdType;

    @ApiModelProperty(example = "性别")
    private List<SyscodeResponse> sex;

    @ApiModelProperty(example = "特别约定类型")
    private List<SyscodeResponse> specialContractType;

    @ApiModelProperty(example = "国籍")
    private List<SyscodeResponse> nationality;

    @ApiModelProperty(example = "与投被保人关系")
    private List<SyscodeResponse> relationship;
    @ApiModelProperty(example = "发票类型")
    private List<SyscodeResponse> invoiceType;
    @ApiModelProperty(example = "折扣类型")
    private List<SyscodeResponse> discountType;

    @ApiModelProperty(example = "团险保险金额的计算依据")
    private List<SyscodeResponse> basisOfSumInsured;

    @ApiModelProperty(example = "优惠活动名称")
    private List<ProductDiscountActivityResponse> promotionType;

    @ApiModelProperty(example = "推荐类型")
    private List<SyscodeResponse> referralSources;

    @ApiModelProperty(example = "销售方式")
    private List<SyscodeResponse> salesPlan;

    @ApiModelProperty(example = "学校性质")
    private List<SyscodeResponse> schoolProperties;

    @ApiModelProperty(example = "学校类型")
    private List<SyscodeResponse> schoolType;
}
