package com.gclife.apply.model.response;

import com.gclife.common.annotation.BigDecimalFormat;
import com.gclife.common.annotation.DateFormat;
import com.gclife.common.annotation.Internation;
import com.gclife.common.annotation.Internations;
import com.gclife.common.model.BaseResponse;
import com.gclife.common.model.config.DateFormatPatternEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * create 18-4-23
 * description:
 */
@ApiModel(value = "ApplyList", description = "投保单列表")
@Data
public class ApplyQueryListResponse extends BaseResponse {
    @ApiModelProperty(value = "投保单变更ID", example = "投保单变更ID")
    private String applyChangeId;
    @ApiModelProperty(example = "投保单Id")
    private String applyId;

    @ApiModelProperty(example = "投保单号")
    private String applyNo;

    @ApiModelProperty(example = "销售机构Id")
    private String salesBranchId;

    @ApiModelProperty(example = "销售机构名称")
    private String salesBranchName;

    @ApiModelProperty(example = "管理机构Id")
    private String managerBranchId;

    @ApiModelProperty(example = "管理机构名称")
    private String managerBranchName;

    @ApiModelProperty(example = "渠道类型编码")
    private String channelTypeCode;

    @ApiModelProperty(example = "渠道类型名称")
    @Internation(filed = "channelTypeCode", codeType = "CHANNEL_TYPE")
    private String channelTypeName;
    @ApiModelProperty(example = "销售机构/渠道")
    @Internations(joinPath = "/", value = {@Internation(filed = "salesBranchName"), @Internation(filed = "channelTypeCode", codeType = "CHANNEL_TYPE")})
    private String salesBranchByChanelType;
    @ApiModelProperty(value = "投保人姓名", example = "投保人姓名")
    private String applicantName;

    @ApiModelProperty(example = "投保人证件号码")
    private String applicantIdNo;
    @ApiModelProperty(example = "投保人证件类型/号码")
    @Internations(joinPath = "/", value = {@Internation(filed = "applicantIdType", codeType = "ID_TYPE"), @Internation(filed = "applicantIdNo")})
    private String idTypeByNo;
    @ApiModelProperty(example = "投保人手机号")
    private String applicantMobile;
    @ApiModelProperty(example = "投保人证件类型")
    @Internation(filed = "applicantIdType", codeType = "ID_TYPE")
    private String applicantIdType;
    @ApiModelProperty(value = "代理人编码", example = "代理人编码")
    private String agentCode;
    @ApiModelProperty(value = "代理人姓名", example = "投保人姓名")
    private String agentName;
    @ApiModelProperty(value = "代理人姓名/工号", example = "代理人姓名/工号")
    private String agentNameByCode;
    @ApiModelProperty(example = "代理人证件号码")
    private String agentIdNo;

    @ApiModelProperty(example = "代理人手机号")
    private String agentMobile;

    @ApiModelProperty(example = "投保人代表姓名")
    private String delegateName;
    @ApiModelProperty(example = "投保人代表手机号")
    private String delegateMobile;

    @ApiModelProperty(value = "投保单来源", example = "投保单来源")
    private String applySource;

    @ApiModelProperty(value = "投保单来源名称", example = "投保单来源名称")
    @Internation(filed = "applySource", codeType = "APPLY_SOURCE")
    private String applySourceName;

    @ApiModelProperty(example = "投保单状态")
    private String applyStatus;

    @ApiModelProperty(example = "投保单类型")
    private String applyType;

    @ApiModelProperty(example = "投保单状态名称")
    @Internation(filed = "applyStatus", codeType = "APPLY_STATUS")
    private String applyStatusName;

    @ApiModelProperty(example = "投保日期")
    private String applyDate;

    @ApiModelProperty(example = "投保日期")
    private String applyDateFormat;

    @ApiModelProperty(value = "productId", example = "productId")
    private String productId;
    @ApiModelProperty(value = "产品名称", example = "产品名称")
    @Internation(codeType = "PRODUCT_ID", filed = "productId")
    private String productName;

    @ApiModelProperty(example = "缴费周期")
    @Internation(filed = "premiumFrequency", codeType = "PRODUCT_PREMIUM_FREQUENCY")
    private String premiumFrequency;

    @ApiModelProperty(example = "保障期限")
    private String coveragePeriod;

    @ApiModelProperty(example = "保障期限单位")
    @Internation(filed = "coveragePeriodUnit", codeType = "PRODUCT_COVERAGE_PERIOD_UNIT")
    private String coveragePeriodUnit;

    @ApiModelProperty(example = "应收保费总计")
    @BigDecimalFormat
    private BigDecimal receivablePremium;

    @ApiModelProperty(example = "保障期限加单位")
    @Internations(joinPath = "", value = {@Internation(filed = "coveragePeriod"), @Internation(filed = "coveragePeriodUnit", codeType = "PRODUCT_COVERAGE_PERIOD_UNIT")})
    private String coveragePeriodName;

    @ApiModelProperty(example = "生效日期")
    private String effectiveDate;

    @ApiModelProperty(example = "提交核保日期")
    private Long commitApplyDate;

    @ApiModelProperty(example = "提交核保日期")
    @DateFormat(filed = "commitApplyDate", pattern = DateFormatPatternEnum.FORMATE6)
    private Long commitApplyDateFormat;

}
