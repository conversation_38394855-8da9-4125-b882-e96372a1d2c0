package com.gclife.apply.model.response;

/**
 * <AUTHOR>
 * create 18-1-2
 * description:
 */
public class DashboardResponse {
    /**
     * 待办受理
     */
    private DashboardWaitTaskResponse waitAccept;
    /**
     * 待办录入
     */
    private DashboardWaitTaskResponse waitInput;
    /**
     * 效能受理
     */
    private DashboardFinishTaskResponse finishAccept;
    /**
     * 效能录入
     */
    private DashboardFinishTaskResponse finishInput;

    public DashboardWaitTaskResponse getWaitAccept() {
        return waitAccept;
    }

    public void setWaitAccept(DashboardWaitTaskResponse waitAccept) {
        this.waitAccept = waitAccept;
    }

    public DashboardWaitTaskResponse getWaitInput() {
        return waitInput;
    }

    public void setWaitInput(DashboardWaitTaskResponse waitInput) {
        this.waitInput = waitInput;
    }

    public DashboardFinishTaskResponse getFinishAccept() {
        return finishAccept;
    }

    public void setFinishAccept(DashboardFinishTaskResponse finishAccept) {
        this.finishAccept = finishAccept;
    }

    public DashboardFinishTaskResponse getFinishInput() {
        return finishInput;
    }

    public void setFinishInput(DashboardFinishTaskResponse finishInput) {
        this.finishInput = finishInput;
    }
}
