package com.gclife.apply.model.response;

import com.gclife.common.annotation.Internation;
import com.gclife.common.model.config.TerminologyConfigEnum;
import com.gclife.common.util.AssertUtils;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * create 18-8-1
 * description:
 */
@Data
public class ApplyAbandonedListResponse {
    @ApiModelProperty(value = "投保单ID", example = "投保单ID")
    private String applyId;
    @ApiModelProperty(value = "投保单号", example = "投保单号")
    private String applyNo;
    @ApiModelProperty(value = "productId", example = "productId")
    private String productId;
    @ApiModelProperty(value = "产品名称", example = "产品名称")
    @Internation(codeType = "PRODUCT_ID", filed = "productId")
    private String productName;
    @ApiModelProperty(value = "缴费周期", example = "缴费周期")
    private String premiumFrequency;
    @ApiModelProperty(value = "保障期限", example = "保障期限")
    private String coveragePeriod;
    @ApiModelProperty(value = "保费", example = "保费")
    private BigDecimal totalPremium;
    @ApiModelProperty(value = "投保时间", example = "投保时间")
    private Long applyDate;
    @ApiModelProperty(value = "投保时间格式化", example = "投保时间格式化")
    private String applyDateFormat;
    @ApiModelProperty(value = "投保人姓名", example = "投保人姓名")
    private String applicantName;
    @ApiModelProperty(value = "投保人证件类型/证件号码", example = "证件类型/证件号码")
    private String applicantIdTypeNo;
    @ApiModelProperty(value = "投保人手机号", example = "投保人手机号")
    private String applicantMobile;
    @ApiModelProperty(value = "销售机构/渠道", example = "销售机构/渠道")
    private String salesBranchChannelType;
    @ApiModelProperty(value = "业务员姓名/工号", example = "业务员姓名/工号")
    private String agentNameCode;
    @ApiModelProperty(value = "业务员手机号", example = "业务员手机号")
    private String agentMobile;
    @ApiModelProperty(value = "失效时间", example = "失效时间")
    private Long abandonedDate;
    @ApiModelProperty(value = "失效时间", example = "失效时间")
    private String abandonedDateFormat;
    @ApiModelProperty(value = "失效原因", example = "失效原因")
    private String abandonedRemark;
    @ApiModelProperty(value = "作废标识", example = "作废标识")
    private String abandonedFlag = TerminologyConfigEnum.WHETHER.YES.name();
    @ApiModelProperty(value = "作废类型",example = "作废类型")
    private String abandonedType;
    @ApiModelProperty(value = "作废类型国际化", example = "作废类型国际化")
    @Internation(filed = "abandonedType", codeType = "ABANDONED_TYPE")
    private String abandonedTypeName;
    @ApiModelProperty(value = "投保单状态", example = "投保单状态")
    private String applyStatus;
    @ApiModelProperty(value = "投保单状态名称", example = "投保单状态名称")
    @Internation(filed = "applyStatus", codeType = "APPLY_STATUS")
    private String applyStatusName;
}
