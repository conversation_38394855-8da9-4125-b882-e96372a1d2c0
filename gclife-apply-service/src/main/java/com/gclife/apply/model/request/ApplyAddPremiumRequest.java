package com.gclife.apply.model.request;

import com.gclife.apply.model.response.AddPremiumResponse;
import com.gclife.apply.model.response.ApplyAddPremiumResponse;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * create 2021/3/10 17:16
 * description:
 */
@Data
public class ApplyAddPremiumRequest {
    @ApiModelProperty(example = "applyId")
    private String applyId;
    @ApiModelProperty(example = "险种ID")
    private String coverageId;

    private List<AddPremiumResponse> listAddPremium;
}
