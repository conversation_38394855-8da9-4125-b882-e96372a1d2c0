package com.gclife.apply.model.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

@Data
public class OnlineApplyBeneficialRequest {
    @ApiModelProperty(value = "受益人为投保人", example = "受益人为投保人(YES:是；NO:否)")
    private String sameWithApplicant;

    @ApiModelProperty(value = "受益人为被保人", example = "受益人为被保人(YES:是；NO:否)")
    private String sameWithInsured;

    @ApiModelProperty(value = "受益人姓名", example = "受益人姓名",required = true)
    private String name;

    @ApiModelProperty(example = "受益人姓",required = true)
    private String familyName;

    @ApiModelProperty(example = "受益人名",required = true)
    private String givenName;

    @ApiModelProperty(value = "受益顺序", example = "受益顺序",required = true)
    private String beneficiaryNoOrder;

    @ApiModelProperty(value = "性别", example = "性别",required = true)
    private String sex;

    @ApiModelProperty(value = "证件类型", example = "证件类型",required = true)
    private String idType;

    @ApiModelProperty(value = "证件号码", example = "证件号码",required = true)
    private String idNo;

    @ApiModelProperty(value = "证件有效期至", example = "证件有效期至",required = true)
    private String idExpDate;

    @ApiModelProperty(value = "受益顺序", example = "受益顺序",required = true)
    private String beneficiaryNo;

    @ApiModelProperty(value = "受益份额", example = "受益份额",required = true)
    private String beneficiaryProportion;

    @ApiModelProperty(value = "出生日期", example = "出生日期",required = true)
    private String birthday;

    @ApiModelProperty(value = "职业",example = "职业",required = true)
    private String occupationCode;

    @ApiModelProperty(example = "是否有社保",required = true)
    private String socialSecurity;

    @ApiModelProperty(value = "与被保人关系", example = "ONESELF")
    private String     relationship;

    @ApiModelProperty(example = "与被保人的关系说明")
    private String relationshipInstructions;

    @ApiModelProperty(example = "受益人详细地址")
    private String addressDetail;

    @ApiModelProperty(example = "受益人手机号码")
    private String mobile;

    private List<OnlineApplyBeneficiaryAttachmentRequest> listBeneficialAttachment;
}
