package com.gclife.apply.service.business.impl;

import com.alibaba.fastjson.JSONObject;
import com.gclife.agent.api.AgentApi;
import com.gclife.agent.api.AgentBaseAgentApi;
import com.gclife.agent.api.AgentBaseApi;
import com.gclife.agent.model.request.AgentApplyQueryRequest;
import com.gclife.agent.model.response.AgentBaseResponse;
import com.gclife.agent.model.response.AgentResponse;
import com.gclife.agent.model.response.AgentSignBaseResponse;
import com.gclife.apply.core.jooq.tables.daos.*;
import com.gclife.apply.core.jooq.tables.pojos.*;
import com.gclife.apply.dao.*;
import com.gclife.apply.dao.app.ApplyPlanBusinessDao;
import com.gclife.apply.model.bo.*;
import com.gclife.apply.model.config.*;
import com.gclife.apply.model.request.*;
import com.gclife.apply.model.response.*;
import com.gclife.apply.service.*;
import com.gclife.apply.service.business.ApplyAutoUnderWriteBusinessService;
import com.gclife.apply.service.business.ApplyUnderWriteBusinessService;
import com.gclife.apply.service.business.MessageBusinessService;
import com.gclife.apply.service.business.PaymentBusinessService;
import com.gclife.apply.service.data.ApplyBoService;
import com.gclife.apply.service.data.ApplyUnderWriteBoService;
import com.gclife.apply.service.data.impl.ApplyDataSaveCommonService;
import com.gclife.apply.transform.ApplyDataTransform;
import com.gclife.apply.validate.parameter.ApplyUnderWriteParameterValidate;
import com.gclife.apply.validate.parameter.group.LanguageUtils;
import com.gclife.apply.validate.parameter.transform.*;
import com.gclife.attachment.model.policy.policy.PolicyCoverageBo;
import com.gclife.common.exception.RequestException;
import com.gclife.common.model.BasePageResponse;
import com.gclife.common.model.BaseResponse;
import com.gclife.common.model.ResultObject;
import com.gclife.common.model.base.Users;
import com.gclife.common.model.config.TerminologyConfigEnum;
import com.gclife.common.model.config.TerminologyTypeEnum;
import com.gclife.common.model.feign.SyscodeRespFc;
import com.gclife.common.model.pojo.AppRequestHeads;
import com.gclife.common.service.impl.BaseBusinessServiceImpl;
import com.gclife.common.util.AssertUtils;
import com.gclife.common.util.ClazzUtils;
import com.gclife.common.util.DateUtils;
import com.gclife.common.util.UUIDUtils;
import com.gclife.platform.api.PlatformBaseInternationServiceApi;
import com.gclife.platform.api.PlatformBranchApi;
import com.gclife.platform.api.PlatformCareerApi;
import com.gclife.platform.api.PlatformEmployeeApi;
import com.gclife.platform.model.response.BranchResponse;
import com.gclife.platform.model.response.CareerNameResponse;
import com.gclife.platform.model.response.EmployeResponse;
import com.gclife.policy.api.PolicyApi;
import com.gclife.policy.model.vo.PolicyVo;
import com.gclife.product.api.ProductApi;
import com.gclife.product.model.config.ProductTermEnum;
import com.gclife.product.model.request.calculate.ApplyRequest;
import com.gclife.product.model.response.apply.ApplyResponse;
import com.gclife.product.model.response.manager.ProductDetailedInfoResponse;
import com.gclife.product.model.response.paramter.ParameterValueResponse;
import com.gclife.workflow.api.WorkFlowApi;
import com.gclife.workflow.model.request.CompleteTaskParam;
import com.gclife.workflow.model.request.WaitingTaskRequest;
import com.gclife.workflow.model.response.WorkItemResponse;
import lombok.extern.slf4j.Slf4j;
import org.modelmapper.TypeToken;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.PlatformTransactionManager;
import org.springframework.transaction.TransactionStatus;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.interceptor.TransactionAspectSupport;
import org.springframework.transaction.support.DefaultTransactionDefinition;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

import static com.gclife.apply.model.config.ApplyTermEnum.UNDER_WRITE_PROMPT.*;
import static com.gclife.apply.model.config.ApplyTermEnum.YES_NO.YES;

/**
 * <AUTHOR>
 * create 17-10-9
 * description:核保接口实现类
 */
@Service
@Slf4j
public class ApplyUnderWriteBusinessServiceImpl extends BaseBusinessServiceImpl implements ApplyUnderWriteBusinessService {

    @Autowired
    private WorkFlowApi workFlowApi;
    @Autowired
    private ApplyUnderWriteExtDao applyUnderWriteExtDao;
    @Autowired
    private ApplyReviewExtDao applyReviewExtDao;
    @Autowired
    private PlatformBaseInternationServiceApi platformBaseInternationServiceApi;
    @Autowired
    private PlatformCareerApi platformCareerApi;
    @Autowired
    private PlatformEmployeeApi platformEmployeeApi;
    @Autowired
    private PlatformBranchApi platformBranchApi;
    @Autowired
    private ApplyExtDao applyExtDao;
    @Autowired
    private ApplyPlanBusinessDao applyPlanBusinessDao;
    @Autowired
    private ProductApi productApi;
    @Autowired
    private ApplyBoService applyBoService;
    @Autowired
    private ApplyUnderWriteParameterValidate applyUnderWriteParameterValidate;
    @Autowired
    private ApplyParameterTransData applyParameterTransData;
    @Autowired
    private ApplyUnderWriteBoService applyUnderWriteBoService;
    @Autowired
    private ApplyUnderwriteCoverageTaskTransData applyUnderwriteCoverageTaskTransData;
    @Autowired
    private ApplyUnderwriteDecisionTransData applyUnderwriteDecisionTransData;
    @Autowired
    private CompleteTaskReqFcTransData completeTaskReqFcTransData;
    @Autowired
    private ApplyConfigBranchExtDao applyConfigBranchExtDao;
    @Autowired
    private ApplyToPolicyTransData applyToPolicyTransData;
    @Autowired
    private AgentApi agentApi;
    @Autowired
    private AgentBaseApi agentBaseApi;
    @Autowired
    private AgentBaseAgentApi baseAgentApi;
    @Autowired
    private ApplyDao applyDao;
    @Autowired
    private MessageBusinessService messageBusinessService;
    @Autowired
    private PolicyApi policyApi;
    @Autowired
    private PlatformTransactionManager platformTransactionManager;
    @Autowired
    private ApplyFactAgentDao applyFactAgentDao;
    @Autowired
    private ApplyRemarkBaseService applyRemarkBaseService;
    @Autowired
    private ApplyPremiumBaseService applyPremiumBaseService;
    @Autowired
    private ApplyCoverageBaseDao applyCoverageBaseDao;
    @Autowired
    private ApplyUnderwriteBaseService applyUnderwriteBaseService;
    @Autowired
    private ApplyCoverageBaseService applyCoverageBaseService;
    @Autowired
    private ApplyPaymentTransData applyPaymentTransData;
    @Autowired
    private ApplyInputGetTransData applyInputGetTransData;
    @Autowired
    private ApplyInsuredDao applyInsuredDao;
    @Autowired
    private ApplyBaseService applyBaseService;
    @Autowired
    private ApplyDataSaveCommonService applyDataSaveCommonService;
    @Autowired
    private ApplyAddPremiumDao applyAddPremiumDao;
    @Autowired
    private ApplyCoverageDao applyCoverageDao;

    @Autowired
    private PaymentBusinessService paymentBusinessService;
    @Autowired
    private ApplyDataTransform applyDataTransform;
    @Autowired
    private ApplyAutoUnderWriteBusinessService applyAutoUnderWriteBusinessService;
    @Autowired
    private ApplyPaymentTransactionBaseService applyPaymentTransactionBaseService;
    @Autowired
    private ApplyChangeTransData applyChangeTransData;

    /**
     * 分页查询核保列表
     *
     * @param userId                     投保单ID
     * @param applyUnderWriteListRequest 核保列表请求参数
     * @return UnderWriteListResponse
     */
    @Override
    public ResultObject<BasePageResponse<UnderWriteListResponse>> getUnderWrites(String userId, ApplyUnderWriteListRequest applyUnderWriteListRequest) {
        ResultObject<BasePageResponse<UnderWriteListResponse>> resultObject = new ResultObject<>();
        List<UnderWriteListResponse> underWriteListResponses = new ArrayList<>();
        try {

            WaitingTaskRequest waitingTaskRequest = new WaitingTaskRequest();
            waitingTaskRequest.setWorkflowItemType(ModelConstantEnum.WORKFLOW_STATUS.ARTIFICIAL_UNDERWRITING_TASK.name());
            waitingTaskRequest.setWorkflowType(ModelConstantEnum.WORKFLOW_STATUS.NEW_CONTRACT.name());
            waitingTaskRequest.setUserId(userId);
            waitingTaskRequest.setAcceptStatusDec(applyUnderWriteListRequest.getUnderWriteStatus());
            if (AssertUtils.isNotEmpty(applyUnderWriteListRequest.getInputStartDateFormat())) {
                waitingTaskRequest.setHandStartDate(DateUtils.timeToTimeTop(DateUtils.stringToTime(applyUnderWriteListRequest.getInputStartDateFormat(), DateUtils.FORMATE3)) + "");
            }
            if (AssertUtils.isNotEmpty(applyUnderWriteListRequest.getInputEndDateFormat())) {
                waitingTaskRequest.setHandEndDate(DateUtils.timeToTimeTop(DateUtils.stringToTime(applyUnderWriteListRequest.getInputEndDateFormat(), DateUtils.FORMATE3)) + "");
            }

            //获取当前用户的流程中的待办任务
            ResultObject<List<WorkItemResponse>> resultObject1 = workFlowApi.queryWaitingTasks(waitingTaskRequest);
            AssertUtils.isResultObjectError(log, resultObject1, ApplyErrorConfigEnum.APPLY_QUERY_WORKFLOW_FAIL);
            List<WorkItemResponse> listWorkflowTaskResponse = resultObject1.getData();
            List<String> listPolicyIds = new ArrayList<>();
            if (AssertUtils.isNotEmpty(listWorkflowTaskResponse)) {
                listPolicyIds = listWorkflowTaskResponse.stream().map(WorkItemResponse::getBusinessId).distinct().collect(Collectors.toList());
            }

            if (!AssertUtils.isNotEmpty(listPolicyIds)) {
                return resultObject;
            }

            List<ApplyUnderWriteListBo> applyUnderWriteListBos = applyUnderWriteExtDao.loadUnderWriteList(applyUnderWriteListRequest, listPolicyIds);

            if (!AssertUtils.isNotEmpty(applyUnderWriteListBos)) {
                return resultObject;
            }
            List<String> allBranchIds = new ArrayList<>();
            List<String> agentIds = new ArrayList<>();
            applyUnderWriteListBos.forEach(applyUnderWriteListBo -> {
                allBranchIds.add(applyUnderWriteListBo.getManagerBranchId());
                allBranchIds.add(applyUnderWriteListBo.getSalesBranchId());
                if (AssertUtils.isNotEmpty(applyUnderWriteListBo.getAgentId())) {
                    agentIds.add(applyUnderWriteListBo.getAgentId());
                }
            });

            //调取代理人接口获取代理人信息
            AgentApplyQueryRequest applyAgentRequest = new AgentApplyQueryRequest();
            applyAgentRequest.setListAgentId(agentIds);
            List<AgentResponse> agentResponses = agentApi.agentsGet(applyAgentRequest).getData();
            AssertUtils.isNotEmpty(log, agentResponses, ApplyErrorConfigEnum.APPLY_BUSINESS_AGENT_IS_NOT_FOUND_OBJECT);

            List<BranchResponse> allBranchResponses = platformBranchApi.branchsPost(allBranchIds).getData();
            //证件类型国际化
            List<SyscodeRespFc> idTypeList = platformBaseInternationServiceApi.queryInternationalTerminology(TerminologyTypeEnum.ID_TYPE.name(), null).getData();
            applyUnderWriteListBos.forEach(applyUnderWriteListBo -> {
                UnderWriteListResponse underWriteListResponse = new UnderWriteListResponse();
                ClazzUtils.copyPropertiesIgnoreNull(applyUnderWriteListBo, underWriteListResponse);
                agentResponses.stream().filter(agentResponse -> applyUnderWriteListBo.getAgentId().equals(agentResponse.getAgentId())).findFirst().ifPresent((agent) -> {
                    underWriteListResponse.setAgentCode(agent.getAgentCode());
                    underWriteListResponse.setAgentName(agent.getAgentName());
                    underWriteListResponse.setAgentPhone(agent.getMobile());
                });
                underWriteListResponse.setAgentNameByCode(underWriteListResponse.getAgentName() + "/" + underWriteListResponse.getAgentCode());
                //销售机构名称
                if (AssertUtils.isNotEmpty(allBranchResponses)) {
                    allBranchResponses.stream().filter(branchRespFc -> branchRespFc.getBranchId().equals(applyUnderWriteListBo.getSalesBranchId())).findFirst().ifPresent((sales) -> {
                        underWriteListResponse.setSalesBranchName(sales.getBranchName());
                    });
                    allBranchResponses.stream().filter(branchRespFc -> branchRespFc.getBranchId().equals(applyUnderWriteListBo.getManagerBranchId())).findFirst().ifPresent((manager) -> {
                        underWriteListResponse.setManagerBranchName(manager.getBranchName());
                    });
                }
                listWorkflowTaskResponse.stream().filter(workflowTaskRespFc -> workflowTaskRespFc.getBusinessId().equals(applyUnderWriteListBo.getApplyId())).findFirst().ifPresent((value2) -> {
                    if (AssertUtils.isNotEmpty(value2.getCreateUserId())) {
                        EmployeResponse employeResponse = platformEmployeeApi.employeGet(value2.getCreateUserId()).getData();
                        if (AssertUtils.isNotNull(employeResponse)) {
                            underWriteListResponse.setReviewer(employeResponse.getEmployeName());
                        }
                    }

                    //-------------设置复核时间---------------
                    long startDate = value2.getStartDate();
                    if (AssertUtils.isNotNull(startDate)) {
                        underWriteListResponse.setReviewDate(startDate);
                    }
                    underWriteListResponse.setUnderWriteStatus(value2.getWorkItemStatus());
                    //设置承保标识
                    //获取当前机构的所有父机构
                    ResultObject<List<BranchResponse>> listResultObject = platformBranchApi.userParentBranchs(applyUnderWriteListBo.getSalesBranchId());
                    if (!AssertUtils.isResultObjectListDataNull(listResultObject)) {
                        List<String> branchIds = listResultObject.getData().stream().map(BranchResponse::getBranchId).distinct().collect(Collectors.toList());
                        ApplyConfigDetailBo applyConfigDetailBo = applyConfigBranchExtDao.loadApplyConfigDetailBo(branchIds, ApplyTermEnum.APPLY_CONFIG_FLAG.UNDERWRITE_FLAG.name());
                        if (AssertUtils.isNotNull(applyConfigDetailBo)) {
                            underWriteListResponse.setUnderwriteFlag(applyConfigDetailBo.getValue());
                        }
                    }
                });

                underWriteListResponses.add(underWriteListResponse);
            });

            //获取总页数
            Integer totalLine = AssertUtils.isNotNull(applyUnderWriteListBos) ? applyUnderWriteListBos.get(0).getTotalLine() : null;

            BasePageResponse<UnderWriteListResponse> basePageResponse = BasePageResponse.getData(applyUnderWriteListRequest.getCurrentPage(), applyUnderWriteListRequest.getPageSize(), totalLine, underWriteListResponses);

            resultObject.setData(basePageResponse);

        } catch (Exception e) {
            e.printStackTrace();
            if (e instanceof RequestException) {
                RequestException error = (RequestException) e;
                //错误设置
                resultObject.setIenum(error.getiEnum());
            } else {
                resultObject.setIenum(ApplyErrorConfigEnum.APPLY_QUERY_APPLY_UNDER_WRITE_INFO_ERROR);
            }
        }
        return resultObject;
    }

    /**
     * 合同信息
     *
     * @param applyId           投保单ID
     * @param currentLoginUsers
     * @return ApplyForUnderWriteResponse
     */
    @Override
    public ResultObject<ApplyForUnderWriteResponse> getApplyForUnderWrite(String applyId, Users currentLoginUsers) {
        ResultObject<ApplyForUnderWriteResponse> resultObject = new ResultObject<>();
        try {
            //参数验证
            AssertUtils.isNotEmpty(log, applyId, ApplyErrorConfigEnum.APPLY_UNDERWRITE_APPLYID_IS_NOT_NULL);
            ApplyForUnderWriteResponse applyForUnderWriteResponse = applyUnderWriteExtDao.getApplyInfo(applyId);
            if (AssertUtils.isNotNull(applyForUnderWriteResponse)) {
                if (AssertUtils.isNotEmpty(applyForUnderWriteResponse.getManagerBranchName())) {
                    BranchResponse branchResponse = platformBranchApi.branchGet(applyForUnderWriteResponse.getManagerBranchName()).getData();
                    //管理机构
                    if (AssertUtils.isNotNull(branchResponse)) {
                        applyForUnderWriteResponse.setManagerBranchName(branchResponse.getBranchName());
                    }
                }
                //销售渠道
                if (AssertUtils.isNotEmpty(applyForUnderWriteResponse.getChannelType())) {
                    SyscodeRespFc syscodeRespFc = platformBaseInternationServiceApi.getTerminology(TerminologyTypeEnum.CHANNEL_TYPE.name(), applyForUnderWriteResponse.getChannelType()).getData();
                    if (AssertUtils.isNotNull(syscodeRespFc)) {
                        applyForUnderWriteResponse.setChannelType(syscodeRespFc.getCodeName());
                    }
                }
                List<UnderWriteProblemResponse> underWriteProblemResponses = applyUnderWriteExtDao.getProblemInfo(applyId);
                if (AssertUtils.isNotEmpty(underWriteProblemResponses)) {
                    underWriteProblemResponses.forEach(underWriteProblemResponse -> {
                        underWriteProblemResponse.setCreatedDateFormat(DateUtils.timeStrToString(underWriteProblemResponse.getCreatedDate(), DateUtils.FORMATE6));
                        if (ApplyTermEnum.AUTO_UNDERWRITE_PROBLEM.REAL_CLIENT.name().equals(underWriteProblemResponse.getProblemCode())) {
                            underWriteProblemResponse.setRealClientFlag(TerminologyConfigEnum.WHETHER.YES.name());
                        }
                    });
                    applyForUnderWriteResponse.setUnderWriteProblemResponse(underWriteProblemResponses);
                }
                List<String> underWritePromptList = new ArrayList<>();
                List<SyscodeRespFc> underWritePromptSyscodeList = platformBaseInternationServiceApi.queryInternational(UNDER_WRITE_PROMPT.name(), currentLoginUsers.getLanguage()).getData();
                // 在人工核保时，自动根据投保人或被保人的证件类型+证件号码匹配到业务员（不含离职）的证件类型+证件号码完全一致的，系统自动提示“此单为自保件
                if (AssertUtils.isNotNull(applyForUnderWriteResponse.getInsuredIdNo()) && AssertUtils.isNotNull(applyForUnderWriteResponse.getInsuredIdType())) {
                    AgentBaseResponse insuredAgentBaseResponse = agentBaseApi.queryAgentByIdNoAndIdType(applyForUnderWriteResponse.getInsuredIdNo(), applyForUnderWriteResponse.getInsuredIdType()).getData();
                    AgentBaseResponse applicantAgentBaseResponse = agentBaseApi.queryAgentByIdNoAndIdType(applyForUnderWriteResponse.getApplicantIdNo(), applyForUnderWriteResponse.getApplicantIdType()).getData();
                    if (AssertUtils.isNotNull(insuredAgentBaseResponse) || AssertUtils.isNotNull(applicantAgentBaseResponse)) {
                        underWritePromptList.add(LanguageUtils.getCodeName(underWritePromptSyscodeList, SELF_INSURANCE.name()));
                        applyForUnderWriteResponse.setSelfInsuranceFlag(YES.name());
                    }
                }

                // 在个险人工核保，根据当前被保人的职业，识别风险级别，超过4（含）以上提示“被保险人从事4类以上风险职业
                List<String> notRiskOccupationType = Arrays.asList("1", "2", "3");
                if (AssertUtils.isNotNull(applyForUnderWriteResponse.getOccupationType()) && !notRiskOccupationType.contains(applyForUnderWriteResponse.getOccupationType())) {
                    underWritePromptList.add(LanguageUtils.getCodeName(underWritePromptSyscodeList, OCCUPATION_TYPE.name()));
                }
                // 如果当前被保人有效的单加上当前投保的单，合起来意外死亡或全残的保险金额>=20万美元，提示“被保险人累计风险保额超过20万美元”，只算1号、1+号、8号和9号的单
                List<PolicyCoverageBo> applyCoverageBoList = applyCoverageBaseDao.queryApplyCoverageBo(applyId);
                if (AssertUtils.isNotNull(applyForUnderWriteResponse.getInsuredIdNo())) {
                    List<PolicyCoverageBo> policyCoverageBoList = policyApi.queryIdNoByAmount(applyForUnderWriteResponse.getInsuredIdNo()).getData();
                    applyCoverageBoList.addAll(policyCoverageBoList);
                }
                BigDecimal amount = new BigDecimal(0);
                if (AssertUtils.isNotNull(applyCoverageBoList)) {
                    for (PolicyCoverageBo policyCoverageBo : applyCoverageBoList) {
                        amount = amount.add(this.calculationAmount(policyCoverageBo));
                    }
                }
                if (amount.compareTo(new BigDecimal(500000)) >= 0) {
                    underWritePromptList.add(LanguageUtils.getCodeName(underWritePromptSyscodeList, INSURED_AMOUNT.name()));
                }
                applyForUnderWriteResponse.setUnderWritePromptList(underWritePromptList);

                //折扣保费信息
                ApplyDiscountPremiumResponse applyDiscountPremiumResponse = applyDataTransform.transApplyDiscountPremium(applyBaseService.queryApply(applyId));
                if (AssertUtils.isNotNull(applyDiscountPremiumResponse)
//                        && (!AssertUtils.isNotEmpty(applyDiscountPremiumResponse.getDiscountModel()) || ProductTermEnum.DISCOUNT_MODEL.PERCENTAGE.name().equals(applyDiscountPremiumResponse.getDiscountModel()))
                ) {
                    applyForUnderWriteResponse.setPromotionType(applyDiscountPremiumResponse.getPromotionType());
                    applyForUnderWriteResponse.setDiscountPremiumFlag(TerminologyConfigEnum.WHETHER.YES.name());
                    applyForUnderWriteResponse.setTotalPremium(applyDiscountPremiumResponse.getTotalPremium());
                    applyForUnderWriteResponse.setSpecialDiscount(applyDiscountPremiumResponse.getSpecialDiscount());
                    applyForUnderWriteResponse.setDiscountType(applyDiscountPremiumResponse.getDiscountType());
                    applyForUnderWriteResponse.setDiscountModel(applyDiscountPremiumResponse.getDiscountModel());
                }
                resultObject.setData(applyForUnderWriteResponse);
            }
        } catch (Exception e) {
            e.printStackTrace();
            if (e instanceof RequestException) {
                RequestException error = (RequestException) e;
                //错误设置
                resultObject.setIenum(error.getiEnum());
            } else {
                resultObject.setIenum(ApplyErrorConfigEnum.APPLY_QUERY_APPLY_UNDER_WRITE_INFO_ERROR);
            }
        }
        return resultObject;
    }


    private BigDecimal calculationAmount(PolicyCoverageBo policyCoverageBo) {
        Long frequency = policyCoverageBo.getFrequency();
        String productId = policyCoverageBo.getProductId();
        String productLevel = policyCoverageBo.getProductLevel();
        String mult = policyCoverageBo.getMult();
        mult = AssertUtils.isNotEmpty(mult) ? mult : "1";
        BigDecimal coverageTotalAmount = AssertUtils.isNotEmpty(policyCoverageBo.getTotalAmount()) ? new BigDecimal(policyCoverageBo.getTotalAmount()) : new BigDecimal(0);
//        if (productId.equals("PRO88000000000003")) {
//            //1号：意外死亡或高残金乘以份数，
//            int amount = "A".equals(productLevel) ? 5000 : "B".equals(productLevel) ? 10000 : "C".equals(productLevel) ? 20000 : 0;
//            BigDecimal multiply = new BigDecimal(amount).multiply(new BigDecimal(mult));
//            if (!AssertUtils.isNotNull(frequency) || frequency.equals(new Long(1))) {
//                multiply = multiply.add(multiply.multiply(new BigDecimal(0.1)));
//            } else if (frequency.equals(new Long(2))) {
//                multiply = multiply.add(multiply.multiply(new BigDecimal(0.2)));
//            } else if (frequency >= new Long(3)) {
//                multiply = multiply.add(multiply.multiply(new BigDecimal(0.3)));
//            }
//            return multiply;
//        }

        // 8号： 1.保健金：一共可以领取的保健金总和，      2.意外死亡或高残金：保额乘以300%，
        if (productId.equals("PRO88000000000008")) {
            return coverageTotalAmount.multiply(new BigDecimal(3));
        }
        // 9号：保额乘以400%，
        if (productId.equals("PRO88000000000009")) {
            return coverageTotalAmount.multiply(new BigDecimal(3));
        }
        if (productId.equals("PRO880000000000013")) {
            return coverageTotalAmount;
        }

//        // 1+：意外死亡或高残金乘以份数，
//        if (productId.equals("PRO8800000000000G3")) {
//            int amount = "A".equals(productLevel) ? 5000 : "B".equals(productLevel) ? 10000 : "C".equals(productLevel) ? 20000 : 0;
//            return new BigDecimal(amount).multiply(new BigDecimal(mult));
//        }
        return new BigDecimal(0);
    }


    /**
     * 投保人信息
     *
     * @param applyId 投保单ID
     * @return 投保人信息
     */
    @Override
    public ResultObject<ApplyUWApplicantRiskAmountResponse> getApplicantForUnderWrite(String applyId) {
        ResultObject<ApplyUWApplicantRiskAmountResponse> resultObject = new ResultObject<>();
        //参数验证
        AssertUtils.isNotEmpty(log, applyId, ApplyErrorConfigEnum.APPLY_UNDERWRITE_APPLYID_IS_NOT_NULL);
        ApplyApplicantResponse applyApplicantResponse = applyUnderWriteExtDao.getApplicantInfo(applyId);
        if (AssertUtils.isNotNull(applyApplicantResponse)) {
            ApplyUWApplicantRiskAmountResponse applyUWApplicantRiskAmountResponse = new ApplyUWApplicantRiskAmountResponse();
            ClazzUtils.copyPropertiesIgnoreNull(applyApplicantResponse, applyUWApplicantRiskAmountResponse);
            //职业
            if (AssertUtils.isNotEmpty(applyUWApplicantRiskAmountResponse.getOccupationCode())) {
                CareerNameResponse careerNameResponse = platformCareerApi.careerNameGet(applyUWApplicantRiskAmountResponse.getOccupationCode()).getData();
                if (AssertUtils.isNotNull(careerNameResponse)) {
                    applyUWApplicantRiskAmountResponse.setOccupationName(careerNameResponse.getCareerName());
                }
            }
            ApplyRiskAmountResponse applyRiskAmountResponse = applyAutoUnderWriteBusinessService.getApplyRiskAmountResponse(applyUWApplicantRiskAmountResponse.getCustomerId(), null, applyId);
            applyUWApplicantRiskAmountResponse.setBasicNBSAR(applyRiskAmountResponse.getBasicNBSAR());
            applyUWApplicantRiskAmountResponse.setCiNBSAR(applyRiskAmountResponse.getCiNBSAR());
            applyUWApplicantRiskAmountResponse.setWopNBSAR(applyRiskAmountResponse.getWopNBSAR());
            applyUWApplicantRiskAmountResponse.setSumTPDAmount(applyRiskAmountResponse.getSumTPDAmount());
            applyUWApplicantRiskAmountResponse.setSumADDAmount(applyRiskAmountResponse.getSumADDAmount());
            applyUWApplicantRiskAmountResponse.setSumCIAmount(applyRiskAmountResponse.getSumCIAmount());
            resultObject.setData(applyUWApplicantRiskAmountResponse);
        }
        return resultObject;
    }

    /**
     * 被保人信息
     *
     * @param applyId 投保单ID
     * @return 被保人信息
     */
    @Override
    public ResultObject<List<ApplyUWInsuredRiskAmountResponse>> getInsuredForUnderWrite(String applyId) {
        ResultObject<List<ApplyUWInsuredRiskAmountResponse>> resultObject = new ResultObject<>();
        //参数验证
        AssertUtils.isNotEmpty(log, applyId, ApplyErrorConfigEnum.APPLY_UNDERWRITE_APPLYID_IS_NOT_NULL);
        List<UnderWriteInsuredResponse> underWriteInsuredResponses = applyUnderWriteExtDao.getInsuredInfo(applyId);
        if (AssertUtils.isNotEmpty(underWriteInsuredResponses)) {
            List<ApplyUWInsuredRiskAmountResponse> applyUWInsuredRiskAmountResponses = (List<ApplyUWInsuredRiskAmountResponse>) this.converterList(underWriteInsuredResponses, new TypeToken<List<ApplyUWInsuredRiskAmountResponse>>() {
            }.getType());
            applyUWInsuredRiskAmountResponses.forEach(applyUWInsuredRiskAmountResponse -> {
                //职业
                if (AssertUtils.isNotEmpty(applyUWInsuredRiskAmountResponse.getOccupationCode())) {
                    CareerNameResponse careerNameResponse = platformCareerApi.careerNameGet(applyUWInsuredRiskAmountResponse.getOccupationCode()).getData();
                    if (AssertUtils.isNotNull(careerNameResponse)) {
                        applyUWInsuredRiskAmountResponse.setOccupationName(careerNameResponse.getCareerName());
                    }
                }
                ApplyRiskAmountResponse applyRiskAmountResponse = applyAutoUnderWriteBusinessService.getApplyRiskAmountResponse(applyUWInsuredRiskAmountResponse.getCustomerId(), null, applyId);
                applyUWInsuredRiskAmountResponse.setBasicNBSAR(applyRiskAmountResponse.getBasicNBSAR());
                applyUWInsuredRiskAmountResponse.setCiNBSAR(applyRiskAmountResponse.getCiNBSAR());
                applyUWInsuredRiskAmountResponse.setWopNBSAR(applyRiskAmountResponse.getWopNBSAR());
                applyUWInsuredRiskAmountResponse.setSumTPDAmount(applyRiskAmountResponse.getSumTPDAmount());
                applyUWInsuredRiskAmountResponse.setSumADDAmount(applyRiskAmountResponse.getSumADDAmount());
                applyUWInsuredRiskAmountResponse.setSumCIAmount(applyRiskAmountResponse.getSumCIAmount());
            });

            resultObject.setData(applyUWInsuredRiskAmountResponses);
        }
        return resultObject;
    }

    /**
     * 险种信息
     *
     * @param applyId   投保单ID
     * @param insuredId 被保人ID
     * @return ResultObject<UnderWriteCoverageExtResponse>
     */
    @Override
    public ResultObject<UnderWriteCoverageExtResponse> getCoverageForUnderWrite(String applyId, String insuredId) {
        ResultObject<UnderWriteCoverageExtResponse> resultObject = new ResultObject<>();
        UnderWriteCoverageExtResponse underWriteCoverageExtResponse = new UnderWriteCoverageExtResponse();
        try {
            //参数验证
            AssertUtils.isNotEmpty(log, applyId, ApplyErrorConfigEnum.APPLY_UNDERWRITE_APPLYID_IS_NOT_NULL);
            AssertUtils.isNotEmpty(log, insuredId, ApplyErrorConfigEnum.APPLY_UNDERWRITE_INSUREDID_IS_NOT_NULL);

            ApplyPo applyPo = applyExtDao.loadApplyPoById(applyId);
            AssertUtils.isNotNull(log, applyPo, ApplyErrorConfigEnum.APPLY_BUSINESS_APPLY_IS_NOT_FOUND_OBJECT);

            String saleBranchId = applyPo.getSalesBranchId();
            List<ApplyCoverageBo> applyCoverageBos = applyUnderWriteExtDao.getUnderWriteCoverageInfo(applyId, insuredId);
            AssertUtils.isNotEmpty(log, applyCoverageBos, ApplyErrorConfigEnum.APPLY_ACCEPT_PRODUCT_ERROR);

            List<UnderWriteCoverageResponse> underWriteCoverageResponses = new ArrayList<>();

            final String[] mainPremiumFrequency = {""};
            applyCoverageBos.forEach(applyCoverageBo -> {
                if (applyCoverageBo.getPrimaryFlag().equals(ApplyTermEnum.PRODUCT_PRIMARY_FLAG.MAIN.name())) {
                    //设置主险
                    underWriteCoverageExtResponse.setMainWriteCoverage(applyUnderwriteCoverageTaskTransData.transUnderWriteCoverageResponse(applyCoverageBo, saleBranchId));
                    mainPremiumFrequency[0] = applyCoverageBo.getPremiumFrequency();
                } else {
                    underWriteCoverageResponses.add(applyUnderwriteCoverageTaskTransData.transUnderWriteCoverageResponse(applyCoverageBo, saleBranchId));
                }
            });
            //设置附加险
            underWriteCoverageExtResponse.setAdditionalWriteCoverage(underWriteCoverageResponses);
            //是否下发下拉选
            underWriteCoverageExtResponse.setSendStatusSelection(platformBaseInternationServiceApi.getTerminologyList(ApplyTermEnum.SEND_STATUS.SEND_STATUS.name()).getData());

            underWriteCoverageExtResponse.setTotalPremium(applyCoverageBos.stream().map(ApplyCoverageBo::getTotalPremium).reduce(BigDecimal.ZERO, BigDecimal::add));
            List<ApplyAddPremiumPo> applyAddPremiumPos = applyPremiumBaseService.getApplyAddPremium(applyId);
            BigDecimal basePremium = underWriteCoverageExtResponse.getTotalPremium();
            if (AssertUtils.isNotEmpty(applyAddPremiumPos)) {
                BigDecimal annualAddPremium = applyAddPremiumPos.stream().map(ApplyAddPremiumPo::getTotalAddPremium).reduce(BigDecimal.ZERO, BigDecimal::add);
                underWriteCoverageExtResponse.setAnnualAddPremium(annualAddPremium);

                BigDecimal conversionFactor = BigDecimal.valueOf(ApplyTermEnum.PREMIUM_FREQUENCY_CONVERSION_FACTOR.valueOf(mainPremiumFrequency[0]).value());
                BigDecimal conversionFactorTotalAddPremium = annualAddPremium.multiply(conversionFactor).setScale(2, BigDecimal.ROUND_HALF_UP);

                BigDecimal totalAddPremium = applyAddPremiumPos.stream().map(applyAddPremiumPo -> applyAddPremiumPo.getTotalAddPremium().multiply(BigDecimal.valueOf(applyAddPremiumPo.getAddPremiumPeriod()))).reduce(BigDecimal.ZERO, BigDecimal::add);
                underWriteCoverageExtResponse.setTotalAddPremium(totalAddPremium);

                underWriteCoverageExtResponse.setOriginalAddPremium(conversionFactorTotalAddPremium);
                basePremium = basePremium.subtract(conversionFactorTotalAddPremium);
            }
            underWriteCoverageExtResponse.setBasePremium(basePremium);

            //折扣保费信息
            underWriteCoverageExtResponse.setReceivablePremium(underWriteCoverageExtResponse.getTotalPremium());
            ApplyDiscountPremiumResponse applyDiscountPremiumResponse = applyDataTransform.transApplyDiscountPremium(applyBaseService.queryApply(applyId));
            if (AssertUtils.isNotNull(applyDiscountPremiumResponse)
//                    && (!AssertUtils.isNotEmpty(applyDiscountPremiumResponse.getDiscountModel()) || ProductTermEnum.DISCOUNT_MODEL.PERCENTAGE.name().equals(applyDiscountPremiumResponse.getDiscountModel()))
            ) {
                underWriteCoverageExtResponse.setPromotionType(applyDiscountPremiumResponse.getPromotionType());
                underWriteCoverageExtResponse.setDiscountPremiumFlag(TerminologyConfigEnum.WHETHER.YES.name());
                underWriteCoverageExtResponse.setSpecialDiscount(applyDiscountPremiumResponse.getSpecialDiscount());
                underWriteCoverageExtResponse.setDiscountType(applyDiscountPremiumResponse.getDiscountType());
                underWriteCoverageExtResponse.setDiscountModel(applyDiscountPremiumResponse.getDiscountModel());
                underWriteCoverageExtResponse.setReceivablePremium(applyDiscountPremiumResponse.getReceivablePremium());
                underWriteCoverageExtResponse.setTotalPremium(applyDiscountPremiumResponse.getTotalPremium());
            }

            //返回数据
            resultObject.setData(underWriteCoverageExtResponse);

        } catch (Exception e) {
            e.printStackTrace();
            if (e instanceof RequestException) {
                RequestException error = (RequestException) e;
                //错误设置
                resultObject.setIenum(error.getiEnum());
            } else {
                resultObject.setIenum(ApplyErrorConfigEnum.APPLY_QUERY_APPLY_UNDER_WRITE_INFO_ERROR);
            }
        }
        return resultObject;
    }

    /**
     * 人工核保任务签收
     *
     * @param userId  用户ID
     * @param applyId 投保单ID
     * @return 工作流处理结果
     */
    @Override
    @Transactional
    public ResultObject underWriteSign(String userId, String applyId) {
        ResultObject resultObject = new ResultObject();
        try {
            //参数验证
            AssertUtils.isNotEmpty(log, applyId, ApplyErrorConfigEnum.APPLY_APPLY_ID_IS_NOT_NULL);
            //先调用工作流任务签收
            resultObject = workFlowApi.claimTask(userId, applyId, ModelConstantEnum.WORKFLOW_STATUS.ARTIFICIAL_UNDERWRITING_TASK.name(), null);
            AssertUtils.isResultObjectError(log, resultObject);

            ApplyPo applyPo = applyExtDao.loadApplyPoById(applyId);
            String applyStatus = ApplyTermEnum.APPLY_STATUS_FLAG.APPLY_STATUS_UNDERWRITE_ARTIFICIAL.name();
            ApplyPaymentTransactionBo applyPaymentTransactionBo = applyPaymentTransactionBaseService.queryApplyPaymentTransactionByType(applyId, ApplyTermEnum.CHARGE_TYPE.PREPAID_PREMIUM.name());
            if (AssertUtils.isNotNull(applyPaymentTransactionBo) && ApplyTermEnum.PAYMENT_STATUS.PAYMENT_SUCCESS.name().equals(applyPaymentTransactionBo.getPaymentStatus())) {
                applyStatus = ApplyTermEnum.APPLY_STATUS_FLAG.APPLY_STATUS_PAID_PENDING_ON_UW.name();
            }
            applyPo.setApplyStatus(applyStatus);
            applyBoService.saveApplyPo(applyPo);
            //判断在人工核保任务表中是否有核保任务和客户任务，没有就新建任务
            ApplyUnderWriteTaskBo applyUnderWriteTaskBo = applyUnderWriteExtDao.loadUnderWriteTaskExist(applyId);
            if (!AssertUtils.isNotNull(applyUnderWriteTaskBo)) {
                //新建核保任务
                applyUnderWriteTaskBo = applyUnderWriteBoService.saveApplyUnderWriteTaskBo(userId, applyId);
                //查出被保人信息
                List<UnderWriteInsuredResponse> insuredResponses = applyUnderWriteExtDao.getInsuredInfo(applyId);
                if (AssertUtils.isNotEmpty(insuredResponses)) {
                    List<String> insuredIds = new ArrayList<>();
                    insuredResponses.forEach(insuredResponse -> {
                        if (AssertUtils.isNotEmpty(insuredResponse.getCustomerId())) {
                            insuredIds.add(insuredResponse.getCustomerId());
                        }
                    });
                    //新建客户任务
                    if (insuredIds.size() > 0) {
                        applyUnderWriteBoService.saveCustomerTaskBo(applyUnderWriteTaskBo.getUnderwriteTaskId(), insuredIds, userId);
                    }
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
            if (e instanceof RequestException) {
                RequestException error = (RequestException) e;
                //错误设置
                resultObject.setIenum(error.getiEnum());
            } else {
                resultObject.setIenum(ApplyErrorConfigEnum.APPLY_SAVE_APPLY_UNDER_WRITE_TASK_ERROR);
            }
        }
        return resultObject;
    }

    /**
     * 获取健康告知内容
     *
     * @param applyId
     * @return
     */
    @Override
    public ResultObject<ApplyQuestionnaireResponse> loadUnderwriteHealthNotice(String applyId, String customerType) {

        ResultObject<ApplyQuestionnaireResponse> resultObject = new ResultObject<>();
        try {
            List<ProductHealthNoticeResponse> productHealthNoticeResponses = new ArrayList<>();
            //查询投保告知书
            ApplyHealthQuestionnaireRemarkPo applyHealthQuestionnaireRemark = applyRemarkBaseService.getApplyHealthQuestionnaireRemark(applyId, customerType);
            List<ApplyHealthQuestionnaireAnswerBo> healthQuestionnaireBos = applyExtDao.loadHealthQuestionnaire(customerType, applyId);
            //当健康告知存在时,用已有的健康告知
            if (AssertUtils.isNotEmpty(healthQuestionnaireBos)) {
                AppApplyBo appApplyBo = applyPlanBusinessDao.getApplyInfo(applyId);
                //校验数据
                AssertUtils.isNotNull(log, appApplyBo, ApplyErrorConfigEnum.APPLY_BUSINESS_APPLY_IS_NOT_FOUND_OBJECT);
                //当健康告知不存在时,使用产品的健康告知
                appApplyBo.setBranchId(appApplyBo.getSalesBranchId());
                productHealthNoticeResponses = applyInputGetTransData.getProductHealthNoticeResponses(appApplyBo, null, healthQuestionnaireBos);
            }
            if (!AssertUtils.isNotNull(productHealthNoticeResponses)) {
                return resultObject;
            }
            resultObject.setData(new ApplyQuestionnaireResponse(
                    customerType, productHealthNoticeResponses, AssertUtils.isNotNull(applyHealthQuestionnaireRemark) ? applyHealthQuestionnaireRemark.getRemark() : null
            ));
        } catch (Exception e) {
            e.printStackTrace();
            if (e instanceof RequestException) {
                RequestException error = (RequestException) e;
                //错误设置
                resultObject.setIenum(error.getiEnum());
            } else {
                resultObject.setIenum(ApplyErrorConfigEnum.APPLY_QUERY_APPLY_UNDER_WRITE_HEALTH_ERROR);
            }
        }
        return resultObject;
    }

    /**
     * 获取强制人工核保原因
     *
     * @param applyId
     * @return
     */
    @Override
    public ResultObject<UnderWriteConfigResponse> loadUnderwriteConfigByApplyId(String applyId) {
        ResultObject<UnderWriteConfigResponse> resultObject = new ResultObject<>();
        UnderWriteConfigResponse underWriteConfigResponse = new UnderWriteConfigResponse();
        try {
            AssertUtils.isNotEmpty(log, applyId, ApplyErrorConfigEnum.APPLY_UNDERWRITE_DECISION_APPLYID_ERROR);
            ApplyUnderWriteConfigBo applyUnderWriteConfigBo = applyReviewExtDao.loadApplyUnderWriteConfigBo(applyId);
            if (AssertUtils.isNotNull(applyUnderWriteConfigBo)) {
                underWriteConfigResponse.setApplyId(applyUnderWriteConfigBo.getApplyId());
                underWriteConfigResponse.setApplyUnderwriteConfigId(applyUnderWriteConfigBo.getApplyUnderwriteConfigId());
                underWriteConfigResponse.setForceManualUnderwriteFlag(applyUnderWriteConfigBo.getForceManualUnderwriteFlag());
                underWriteConfigResponse.setForceUnderwriteDescription(applyUnderWriteConfigBo.getForceUnderwriteDescription());
            }
            resultObject.setData(underWriteConfigResponse);
        } catch (Exception e) {
            e.printStackTrace();
            if (e instanceof RequestException) {
                RequestException error = (RequestException) e;
                //错误设置
                resultObject.setIenum(error.getiEnum());
            } else {
                resultObject.setIenum(ApplyErrorConfigEnum.APPLY_QUERY_APPLY_UNDER_WRITE_CONFIG_ERROR);
            }
        }
        return resultObject;

    }


    /**
     * 人工核保 影像件集合
     *
     * @return
     */
    @Override
    public ResultObject<List<ApplyUnderWriteAttachmentConfigDetailResponse>> loadUnderwriteAttachments(String applyId) {
        ResultObject<List<ApplyUnderWriteAttachmentConfigDetailResponse>> resultObject = new ResultObject<>();
        try {
            //数据验证
            AssertUtils.isNotEmpty(log, applyId, ApplyErrorConfigEnum.APPLY_PARAMETER_APPLY_APPLY_ID_IS_NOT_NULL);
            //查询附件类型
            List<ApplyAttachmentConfigDetailBo> applyAttachmentConfigDetailBos = applyExtDao.loadApplyAttachmentConfigDetailBos(applyId);
            if (AssertUtils.isNotEmpty(applyAttachmentConfigDetailBos)) {
                //转换
                List<ApplyUnderWriteAttachmentConfigDetailResponse> applyAttachmentConfigDetailResponses = (List<ApplyUnderWriteAttachmentConfigDetailResponse>) this.converterList(applyAttachmentConfigDetailBos, new TypeToken<List<ApplyUnderWriteAttachmentConfigDetailResponse>>() {
                }.getType());

                //List<SyscodeRespFc> applyAttachmentList = platformBaseInternationServiceApi.getTerminologyList(TerminologyTypeEnum.APPLY_ATTACHMENT_TYPE.name()).getData();
                //投保单附件国际化
//                if (AssertUtils.isNotEmpty(applyAttachmentList) && AssertUtils.isNotEmpty(applyAttachmentConfigDetailResponses)) {
//                    applyAttachmentConfigDetailResponses.forEach(applyUnderWriteAttachmentConfigDetailResponse -> {
//                        applyAttachmentList.stream().filter(certifyType -> certifyType.getCodeKey().equals(applyUnderWriteAttachmentConfigDetailResponse.getAttachmentTypeCode())).findFirst().ifPresent(certifyType -> {
//                            applyUnderWriteAttachmentConfigDetailResponse.setAttachmentTypeName(certifyType.getCodeName());
//                        });
//                    });
//                }
                resultObject.setData(applyAttachmentConfigDetailResponses);
            }

        } catch (Exception e) {
            e.printStackTrace();
            if (e instanceof RequestException) {
                RequestException error = (RequestException) e;
                //错误设置
                resultObject.setIenum(error.getiEnum());
            } else {
                resultObject.setIenum(ApplyErrorConfigEnum.APPLY_QUERY_ATTACHMENT_ERROR);
            }
        }
        return resultObject;
    }

    /**
     * 插入  核保  决定
     * 修改  核保  决定
     *
     * @param users
     * @param underwriteDecisionRequest
     * @return 成功或者失败
     */
    @Override
    @Transactional
    public ResultObject saveUnderwriteDecision(Users users, UnderwriteDecisionRequest underwriteDecisionRequest) {
        ResultObject resultObject = new ResultObject();

        try {
            //请求参数效验
            applyUnderWriteParameterValidate.validParameterAddAccept(underwriteDecisionRequest);

            //查看 该险种  是否  有核保决定未下发则编辑      有未下发禁止添加
            ApplyUnderwriteDecisionPo underwriteDecisionPoBo = applyUnderWriteExtDao.getCoverageUnderwriteDecision(underwriteDecisionRequest.getCoverageId());
            if (underwriteDecisionPoBo != null) {
                ApplyUnderwriteDecisionPo applyUnderwriteDecisionPo = applyUnderwriteDecisionTransData.transInputUnderwriteDecision(users, underwriteDecisionRequest, underwriteDecisionPoBo);
                applyBoService.updateOrSaveApplyUnderwriteDecision(applyUnderwriteDecisionPo);
                return resultObject;
            }

            // 根据客户ID 获取 核保客户任务
            ApplyUnderwriteCustomerTaskBo applyUnderwriteCustomerTaskBo = applyUnderWriteExtDao.findByCustomerIdAndApplyId(underwriteDecisionRequest.getCustomerId(), underwriteDecisionRequest.getApplyId());

            if ((!AssertUtils.isNotNull(applyUnderwriteCustomerTaskBo)) || (!AssertUtils.isNotNull(applyUnderwriteCustomerTaskBo.getUnderwriteCustomerTaskId()))) {
                log.debug("插入核保决定 未查出给用户 有 核保客户任务 applyUnderwriteCustomerTaskPo");
                resultObject.setIenum(ApplyErrorConfigEnum.APPLY_UNDERWRITE_DECISION_SAVE_ERROR);
                return resultObject;
            }

            BaseUnderwriteTaskTypeBo baseUnderwriteTaskTypeBo = applyUnderWriteExtDao.getByUnderwriteTaskTypeCode(UnderwriteTaskType.UNDERWRITE_DECISION_CODE.getCode());

            if (!AssertUtils.isNotNull(baseUnderwriteTaskTypeBo) || !AssertUtils.isNotNull(baseUnderwriteTaskTypeBo.getUnderwriteTaskTypeId())) {
                log.debug("插入核保决定 为查出有核保  类型 信息");
                resultObject.setIenum(ApplyErrorConfigEnum.APPLY_UNDERWRITE_DECISION_SAVE_ERROR);
                return resultObject;
            }
            long dataTime = System.currentTimeMillis();
            //封装 apply_underwrite_coverage_task 核保险种任务
            ApplyUnderwriteCoverageTaskPo applyUnderwriteCoverageTaskPo = applyUnderwriteCoverageTaskTransData.transInputUnderwriteDecision(applyUnderwriteCustomerTaskBo, users, baseUnderwriteTaskTypeBo, underwriteDecisionRequest, dataTime);

            //增加 核保险种任务
            applyBoService.updateOrSaveUnderwriteCoverageTask(applyUnderwriteCoverageTaskPo);

            //封装 核保决定信息
            ApplyUnderwriteDecisionPo applyUnderwriteDecisionPo = applyUnderwriteDecisionTransData.transInputUnderwriteDecision(users, underwriteDecisionRequest, dataTime, applyUnderwriteCoverageTaskPo, applyUnderwriteCustomerTaskBo);

            //增加核保决定
            applyBoService.updateOrSaveApplyUnderwriteDecision(applyUnderwriteDecisionPo);

        } catch (Exception e) {
            e.printStackTrace();
            //事务回滚
            TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
            if (e instanceof RequestException) {
                RequestException error = (RequestException) e;
                //错误设置
                resultObject.setIenum(error.getiEnum());
            } else {
                resultObject.setIenum(ApplyErrorConfigEnum.APPLY_UNDERWRITE_DECISION_SAVE_ERROR);
            }
        }

        return resultObject;
    }

    /**
     * 查询 该投保单的 多个核保决定信息
     *
     * @param users
     * @param applyId
     * @return
     */
    @Override
    public ResultObject<List<UnderwriteDecisionResponse>> seeUnderwriteDecision(Users users, String applyId) {
        ResultObject<List<UnderwriteDecisionResponse>> resultObject = new ResultObject<>();
        try {
            AssertUtils.isNotNull(log, applyId, ApplyErrorConfigEnum.APPLY_PARAMETER_APPLY_APPLY_ID_IS_NOT_NULL);
            List<UnderwriteDecisionBo> underwriteDecisionList = applyUnderWriteExtDao.seeUnderwriteDecision(applyId);
            ApplyPo applyPo = applyExtDao.loadApplyPoById(applyId);
            AssertUtils.isNotNull(getLogger(), applyPo, ApplyErrorConfigEnum.APPLY_QUERY_PLAN_APPLY_NO_ERROR);

            List<SyscodeRespFc> syscodeRespFcList = platformBaseInternationServiceApi.getTerminologyList(ApplyTermEnum.SEND_STATUS.SEND_STATUS.name()).getData();
            underwriteDecisionList.forEach(underwriteDecisionBo -> {
                ProductDetailedInfoResponse productDetailedInfoResponse = null;
                if (AssertUtils.isNotNull(underwriteDecisionBo.getProductId()) && AssertUtils.isNotNull(underwriteDecisionBo.getProductCode())) {
                    productDetailedInfoResponse = productApi.getProductDetailInfo(underwriteDecisionBo.getProductId(), applyPo.getSalesBranchId()).getData();
                }
                if (AssertUtils.isNotNull(productDetailedInfoResponse)) {
                    underwriteDecisionBo.setProductName(productDetailedInfoResponse.getProductName());
                }
                if (AssertUtils.isNotEmpty(syscodeRespFcList)) {
                    underwriteDecisionBo.setSendStatus(this.getSendStatus(syscodeRespFcList, underwriteDecisionBo.getSendStatus()));
                }
            });

            List<UnderwriteDecisionResponse> underwriteDecisionResponseList = applyUnderwriteDecisionTransData.transInputUnderwriteDecisionList(underwriteDecisionList);

            resultObject.setData(underwriteDecisionResponseList);
        } catch (Exception e) {
            e.printStackTrace();
            if (e instanceof RequestException) {
                RequestException error = (RequestException) e;
                //错误设置
                resultObject.setIenum(error.getiEnum());
            } else {
                resultObject.setIenum(ApplyErrorConfigEnum.APPLY_UNDERWRITE_DECISION_SEE_ERROR);
            }
            e.printStackTrace();
        }

        return resultObject;
    }

    /**
     * 获取是否  国际化
     *
     * @param syscodeRespFcList
     * @param type
     * @return
     */
    public String getSendStatus(List<SyscodeRespFc> syscodeRespFcList, String type) {
        String typeName = "";
        if (AssertUtils.isNotEmpty(syscodeRespFcList)) {
            for (SyscodeRespFc syscodeRespFc : syscodeRespFcList) {
                if (syscodeRespFc.getCodeKey().equals(type)) {
                    typeName = syscodeRespFc.getCodeName();
                    break;
                }
            }
        }
        return typeName;
    }

    @Override
    @Transactional
    public ResultObject sendUnderwriteDecision(Users users, String applyId) {

        ResultObject resultObject = new ResultObject<>();
        try {
            AssertUtils.isNotNull(log, applyId, ApplyErrorConfigEnum.APPLY_PARAMETER_APPLY_APPLY_ID_IS_NOT_NULL);

            //修改  该核保决定  为下发状态      ****注意  只有  修改  之后才能下发工作流   可对失败进行处理
            int updateNum = applyUnderWriteExtDao.updateSendUnderwriteDecision(users, applyId);
            if (updateNum < 1) {
                resultObject.setIenum(ApplyErrorConfigEnum.APPLY_UNDERWRITE_DECISION_SEND_IS_NULL_ERROR);
                return resultObject;
            }

            CompleteTaskParam completeTaskParam = completeTaskReqFcTransData.transInputApplyId(applyId);
            //发送工作流
            resultObject = workFlowApi.completeTask(completeTaskParam);

            //判断发送工作流是否成功
            AssertUtils.isResultObjectError(log, resultObject, ApplyErrorConfigEnum.APPLY_QUERY_WORKFLOW_FAIL);

        } catch (Exception e) {
            //事务回滚
            TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
            if (e instanceof RequestException) {
                RequestException error = (RequestException) e;
                //错误设置
                resultObject.setIenum(error.getiEnum());
            } else {
                resultObject.setIenum(ApplyErrorConfigEnum.APPLY_UNDERWRITE_DECISION_SEND_ERROR);
            }
            e.printStackTrace();
        }
        return resultObject;
    }

    @Override
    public ResultObject<List<UnderWriteDecisionEnumResponse>> getUnderwriteDecision() {
        ResultObject<List<UnderWriteDecisionEnumResponse>> resultObject = new ResultObject<>();
        try {
            List<UnderWriteDecisionEnumResponse> underWriteDecisionEnumResponse = applyUnderWriteExtDao.loadUnderwriteDecision();
            if (null != underWriteDecisionEnumResponse && underWriteDecisionEnumResponse.size() > 0) {
                resultObject.setData(underWriteDecisionEnumResponse);
            }
        } catch (Exception e) {
            e.printStackTrace();
            if (e instanceof RequestException) {
                RequestException error = (RequestException) e;
                //错误设置
                resultObject.setIenum(error.getiEnum());
            } else {
                resultObject.setIenum(ApplyErrorConfigEnum.APPLY_UNDERWRITE_DECISION_SEND_ERROR);
            }
        }
        return resultObject;

    }

    /**
     * 下发核保结论
     *
     * @param applyUnderWriteDecisionRequest 请求参数
     * @param users                          当前用户
     * @param appRequestHeads                请求头
     * @return ResultObject
     */
    @Override
    @Transactional
    public ResultObject postUnderwriteDecisions(ApplyUnderWriteDecisionRequest applyUnderWriteDecisionRequest, Users users, AppRequestHeads appRequestHeads) {
        ResultObject resultObject = new ResultObject<>();
        String tag = "postUnderwriteDecisions-" + DateUtils.getCurrentTime();
        log.info("------" + tag + "---start---");
        log.info(tag + JSONObject.toJSONString(applyUnderWriteDecisionRequest));
        String applyId = applyUnderWriteDecisionRequest.getApplyId();
        String underwriteDecisionCode = applyUnderWriteDecisionRequest.getUnderWriteDecisionCode();
        String reviewComment = applyUnderWriteDecisionRequest.getReviewComment();
        //参数验证
        AssertUtils.isNotEmpty(log, applyId, ApplyErrorConfigEnum.APPLY_UNDERWRITE_DECISION_APPLYID_ERROR);
        AssertUtils.isNotEmpty(log, underwriteDecisionCode, ApplyErrorConfigEnum.APPLY_UNDERWRITE_DECISION_UNDERWRITEDECISIONCODE_IS_NOT_NULL);
        AssertUtils.isNotEmpty(log, reviewComment, ApplyErrorConfigEnum.APPLY_UNDERWRITE_DECISION_REVIEW_COMMENT_IS_NOT_NULL);
        ApplyPaymentTransactionBo applyPaymentTransactionBo = applyPaymentTransactionBaseService.queryApplyPaymentTransactionByType(applyId, ApplyTermEnum.CHARGE_TYPE.PREPAID_PREMIUM.name());
        if (AssertUtils.isNotNull(applyPaymentTransactionBo) && ApplyTermEnum.PAYMENT_STATUS.PAYMENT_FINISHED.name().equals(applyPaymentTransactionBo.getPaymentStatus())) {
            throwsException(ApplyErrorConfigEnum.APPLY_CUSTOMER_HAS_UPLOADED_THE_PAYMENT_VOUCHER);
        }

        ApplyUnderWriteTaskBo applyUnderWriteTaskBo = applyUnderWriteExtDao.loadUnderWriteTaskExist(applyId);
        log.info(tag + JSONObject.toJSONString(applyUnderWriteTaskBo));
        if (!AssertUtils.isNotNull(applyUnderWriteTaskBo)) {
            resultObject.setIenum(ApplyErrorConfigEnum.APPLY_UNDER_WRITE_TASK_IS_NULL);
            return resultObject;
        }
        List<String> underWriteDecisionCodes = applyUnderWriteExtDao.loadUnderwriteDecision().stream().map(UnderWriteDecisionEnumResponse::getUnderwriteDecisionCode).collect(Collectors.toList());
        log.info(tag + JSONObject.toJSONString(underWriteDecisionCodes));
        //校验underWriteDecisionCode是否正确
        if (!underWriteDecisionCodes.contains(underwriteDecisionCode)) {
            resultObject.setIenum(ApplyErrorConfigEnum.APPLY_UNDERWRITE_DECISION_CODE_FORMAT_ERROR);
            return resultObject;
        }
        //获取核保任务中的投保单任务ID
        String underwriteTaskId = applyUnderWriteTaskBo.getUnderwriteTaskId();
        //核保决定Id
        String underwriteDecisionId = applyUnderWriteExtDao.loadUnderwriteDecisionByCode(underwriteDecisionCode).getUnderwriteDecisionId();
        //是否曾下发过核保决定,有改无增
        ApplyUnderwriteDecisionBo applyUnderwriteDecisionBo = applyUnderWriteExtDao.loadUnderwriteDecisionBoExist(underwriteTaskId);
        log.info(tag + JSONObject.toJSONString(applyUnderwriteDecisionBo));
        if (!AssertUtils.isNotNull(applyUnderwriteDecisionBo)) {
            applyUnderwriteDecisionBo = new ApplyUnderwriteDecisionBo();
            applyUnderwriteDecisionBo.setUnderwriteTaskId(underwriteTaskId);
            applyUnderwriteDecisionBo.setUnderwriteDecisionId(underwriteDecisionId);
        }
        applyUnderwriteDecisionBo.setRemarks(reviewComment);
        applyUnderwriteDecisionBo.setAuditDecisionDate(System.currentTimeMillis());
        //核保决定数据保存
        applyBoService.updateOrSaveApplyUnderwriteDecision(applyUnderwriteDecisionBo);

        //工作流任务完成接口参数
        CompleteTaskParam completeTaskParam = new CompleteTaskParam();
        completeTaskParam.setBusinessId(applyId);
        completeTaskParam.setWorkflowItemType(ModelConstantEnum.WORKFLOW_STATUS.ARTIFICIAL_UNDERWRITING_TASK.name());
        completeTaskParam.setRejectFlag("false");
        completeTaskParam.setReviewComment(applyUnderWriteDecisionRequest.getReviewComment());
        log.info(tag + JSONObject.toJSONString(completeTaskParam));
        //支付
        ApplyBo applyBo = applyExtDao.loadApplyBoById(applyId);
        applyBo.setUnderWriteRemark(reviewComment);
        applyBo.setSelfInsuranceFlag(applyUnderWriteDecisionRequest.getSelfInsuranceFlag());
        //代理人签约状态判断
        ResultObject<AgentSignBaseResponse> respFcResultObject = baseAgentApi.queryOneAgentSignedAgentId(applyBo.getApplyAgentBo().getAgentId());
        AssertUtils.isResultObjectError(log, respFcResultObject);
        if (AssertUtils.isResultObjectError(respFcResultObject) ||
                !ApplyTermEnum.SIGN_STATUS.SIGN_COMPLETE.name().equals(respFcResultObject.getData().getSignStatus())) {
            throw new RequestException(ApplyErrorConfigEnum.APPLY_PLAN_AGENT_NO_SIGN);
        }

        AgentResponse applyAgentRespFc = agentApi.agentByIdGet(applyBo.getApplyAgentBo().getAgentId()).getData();
        applyBo.getApplyAgentBo().setAgentName(applyAgentRespFc.getAgentName());

        //当核保决定为标准体，次标准体时，调用工作流完成接口
        boolean passUW = ApplyTermEnum.DECISION_TYPE.STANDARD.name().equalsIgnoreCase(underwriteDecisionCode)
                || ApplyTermEnum.DECISION_TYPE.SUBSTANDARD.name().equalsIgnoreCase(underwriteDecisionCode);
        if (passUW) {
            ApplyPo applyPo1 = applyUnderWriteExtDao.getApply(applyId);
            //获取该投保单ID的所有险种加费总计
            String sumExtra = applyUnderWriteExtDao.sumExtra(applyId);
            BigDecimal receivablePremium = applyPo1.getReceivablePremium();
            BigDecimal totalReceivablePremium = receivablePremium.add(new BigDecimal(sumExtra));
            /*
             * 次标准体时，将加费金额写入apply表中receivable_premium
             * 应收保费总计=原始保费+加费金额
             * 应收追加保费总计=加费金额
             */
            log.info(tag + JSONObject.toJSONString(underwriteDecisionCode));
            if (ApplyTermEnum.DECISION_TYPE.SUBSTANDARD.name().equalsIgnoreCase(underwriteDecisionCode)) {
                //应收追加保费总计
                applyBo.setReceivablePremium(totalReceivablePremium);
                applyBo.setReceivableAddPremium(new BigDecimal(sumExtra));
                //次标准体的险种存到险种表
                List<ApplyCoverageExtBo> listApplyCoverageExtBo = applyUnderWriteExtDao.getApplyCoverageExtBoList(applyId);
                if (AssertUtils.isNotEmpty(listApplyCoverageExtBo)) {
                    listApplyCoverageExtBo.forEach(applyCoverageExtBo ->
                            applyBoService.saveApplyCoveragePo(applyUnderwriteDecisionTransData.transApplyCoverageData(applyCoverageExtBo))
                    );
                }
            }
            //产生保费记录
            applyPremiumBaseService.saveApplyPremium(users.getUserId(), applyId, PayNotifyTermEnum.PAY_NOTIFY_STATUS.PAYMENT_WAITTING.name());
            //调用工作流完成接口，核保通过,转去支付
            completeTaskParam.setUnderwritingDecisionFlag(ApplyTermEnum.UNDERWRITE_TYPE.UNDERWRITE_PAY.name());

            //保存核保通知书
            this.saveApplyUnderwriteNoticePo(users, applyId);
            //核保通过 初始化投保单变更数据
            applyChangeTransData.initApplyChangeData(users, applyId);
            try {
                // 发送消息给财务
                log.info("==================================核保完成发送消息给财务=====================================");
                messageBusinessService.pushApplyMessageBatch(ApplyTermEnum.MSG_BUSINESS_TYPE.APPLY_UNDERWRITING_CP.name(), applyBo);

                log.info("==================================核保完成消息发送结束=====================================");

            } catch (Exception e) {
                e.printStackTrace();
            }
            log.info(tag + "发消息");
            applyBo.setApplyStatus(ApplyTermEnum.APPLY_STATUS_FLAG.APPLY_STATUS_UNDERWRITE_PASS.name());

//                applyPaymentTransData.initiatePayment(applyBo, users, appRequestHeads, false, null);
            //messageBusinessService.pushApplyMessageCustomer("ABA_Payments",applyBo,users,appRequestHeads);

        } else {
            //支付
            log.info(tag + "拒保");
            applyBo.setApplyStatus(ApplyTermEnum.APPLY_STATUS_FLAG.APPLY_STATUS_REFUND.name());
            applyBo.setInvalidDate(DateUtils.getCurrentTime());
            //拒保或者延期,核保不通过，调用工作流完成接口
            completeTaskParam.setUnderwritingDecisionFlag(ApplyTermEnum.UNDERWRITE_TYPE.UNDERWRITE_REJECT.name());
            // 发消息给业务员 通知app拒保
            try {
                messageBusinessService.pushApplyMessageSingle(
                        ApplyTermEnum.MSG_BUSINESS_TYPE.APPLY_UNDERWRITING_RF.name(), applyBo, applyBo.getApplyAgentBo().getAgentId());
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
        log.info(tag + JSONObject.toJSONString(applyBo));
        applyBoService.saveApplyPo(applyBo);
        resultObject = workFlowApi.completeTask(completeTaskParam);
        AssertUtils.isResultObjectError(getLogger(), resultObject);

        ApplyPaymentUWBo applyPaymentUWBo = new ApplyPaymentUWBo();
        applyPaymentUWBo.setInitiatePaymentFlag(passUW);
        applyPaymentUWBo.setPrepaidPremiumFlag(false);

        applyPaymentTransData.transUWPayment(users, appRequestHeads, applyBo.getApplyId(), applyPaymentUWBo);

        log.info("------" + tag + "---end---");
        return resultObject;
    }


    private void saveApplyUnderwriteNoticePo(Users users, String applyId) {
        //保存核保通知书
        List<ApplyCoveragePo> applyCoveragePosOfInsured = applyCoverageBaseService.listApplyCoverageOfInsured(applyId);
        if (!AssertUtils.isNotEmpty(applyCoveragePosOfInsured)) {
            return;
        }
        Optional<ApplyCoveragePo> first = applyCoveragePosOfInsured.stream().filter(applyCoveragePo ->
                ApplyTermEnum.PRODUCT_PRIMARY_FLAG.MAIN.name().equals(applyCoveragePo.getPrimaryFlag()) &&
                        Arrays.asList(ApplyTermEnum.PRODUCT.PRODUCT_5.id(), ApplyTermEnum.PRODUCT.PRODUCT_28.id()).contains(applyCoveragePo.getProductId())
        ).findFirst();
        if (first.isPresent()) {
            ApplyUnderwriteNoticePo applyUnderwriteNoticePo = applyUnderwriteBaseService.queryApplyUnderwriteNoticePo(applyId);
            if (!AssertUtils.isNotNull(applyUnderwriteNoticePo)) {
                applyUnderwriteNoticePo = new ApplyUnderwriteNoticePo();
            }
            applyUnderwriteNoticePo.setApplyId(applyId);
            applyUnderwriteNoticePo.setPrintStatus(TerminologyConfigEnum.WHETHER.NO.name());
            applyUnderwriteBaseService.saveApplyUnderwriteNoticePo(applyUnderwriteNoticePo, users.getUserId());
        }
    }

    /**
     * 获取人工核保状态
     *
     * @return ResultObject
     */
    @Override
    public ResultObject<List<SyscodeRespFc>> getUnderWriteStatus() {
        ResultObject<List<SyscodeRespFc>> resultObject = new ResultObject<>();
        try {
            resultObject = platformBaseInternationServiceApi.getTerminologyList(TerminologyTypeEnum.APPLY_UNDERWRITE_WORK_FLOW_STATUS.name());
        } catch (Exception e) {
            if (e instanceof RequestException) {
                RequestException error = (RequestException) e;
                //错误设置
                resultObject.setIenum(error.getiEnum());
            } else {
                resultObject.setIenum(ApplyErrorConfigEnum.APPLY_QUERY_ACCEPTS_STATUS_ERROR);
            }
        }
        return resultObject;
    }


    /**
     * 获取承保录入,复核页面初始化数据
     *
     * @param applyId 投保单ID
     * @return ResultObject<ApplyUnderwritePolicyResponse>
     */
    @Override
    public ResultObject<ApplyUnderwritePolicyResponse> getUnderWriteInfo(String applyId) {
        ResultObject<ApplyUnderwritePolicyResponse> resultObject = new ResultObject<>();
        ApplyUnderwritePolicyResponse applyUnderwritePolicyResponse;
        try {
            AssertUtils.isNotEmpty(getLogger(), applyId, ApplyErrorConfigEnum.APPLY_UNDERWRITE_DECISION_APPLYID_ERROR);
            applyUnderwritePolicyResponse = applyUnderWriteExtDao.getApplyUnderwritePolicyResponse(applyId);
            AssertUtils.isNotNull(getLogger(), applyUnderwritePolicyResponse, ApplyErrorConfigEnum.APPLY_QUERY_UNDERWRITE_ERROR);
            //查询代理人信息
            ResultObject<AgentResponse> respFcResultObject = agentApi.agentGet(applyUnderwritePolicyResponse.getAgentCode());
            AssertUtils.isResultObjectDataNull(getLogger(), respFcResultObject, ApplyErrorConfigEnum.APPLY_BUSINESS_AGENT_IS_NOT_FOUND_OBJECT);
            applyUnderwritePolicyResponse.setSaleBranchName(respFcResultObject.getData().getBranchName());
            applyUnderwritePolicyResponse.setSaleChannelName(respFcResultObject.getData().getChannelTypeName());
            applyUnderwritePolicyResponse.setApplySource(applyParameterTransData.getCodeNameByKey(TerminologyTypeEnum.APPLY_SOURCE.name(), applyUnderwritePolicyResponse.getApplySource()));
            //关联代理人
            List<ApplyFactAgentPo> applyFactAgentPos = applyFactAgentDao.fetchByApplyId(applyId);
            if (AssertUtils.isNotEmpty(applyFactAgentPos)) {
                ResultObject<AgentBaseResponse> respFcResultObjectFact = baseAgentApi.queryOneAgentById(applyFactAgentPos.get(0).getAgentId());
                applyUnderwritePolicyResponse.setFactAgentId(respFcResultObjectFact.getData().getAgentId());
                applyUnderwritePolicyResponse.setFactAgentCode(respFcResultObjectFact.getData().getAgentCode());
                applyUnderwritePolicyResponse.setFactAgentName(respFcResultObjectFact.getData().getAgentName());
            }
            resultObject.setData(applyUnderwritePolicyResponse);
        } catch (Exception e) {
            e.printStackTrace();
            if (e instanceof RequestException) {
                RequestException error = (RequestException) e;
                //错误设置
                resultObject.setIenum(error.getiEnum());
            } else {
                resultObject.setIenum(ApplyErrorConfigEnum.APPLY_QUERY_UNDERWRITE_ERROR);
            }
        }
        return resultObject;
    }

    /**
     * 承保录入
     *
     * @param userId                       用户ID
     * @param applyUnderwritePolicyRequest 承保请求类
     * @return
     */
    @Override
    @Transactional
    public ResultObject<BaseResponse> saveUnderWritePolicyInfo(String userId, ApplyUnderwritePolicyRequest applyUnderwritePolicyRequest) {
        ResultObject<BaseResponse> resultObject = new ResultObject<BaseResponse>();
        try {
            //数据验证
            applyUnderWriteParameterValidate.validParameterUnderwriteInstrument(applyUnderwritePolicyRequest);

            ApplyPo applyPo = applyExtDao.loadApplyPoById(applyUnderwritePolicyRequest.getApplyId());

            AssertUtils.isNotNull(getLogger(), applyPo, ApplyErrorConfigEnum.APPLY_QUERY_APPLY_ACCEPT_IS_NULL_ERROR);
            //保存承保信息
            applyUnderwriteDecisionTransData.transApplyPo(applyPo, applyUnderwritePolicyRequest);
            applyBoService.saveApplyPo(applyPo);
            //实际业务归属代理人
            List<ApplyFactAgentPo> applyFactAgentPos = applyFactAgentDao.fetchByApplyId(applyPo.getApplyId());
            if (AssertUtils.isNotEmpty(applyFactAgentPos)) {
                applyFactAgentDao.delete(applyFactAgentPos.get(0));
            }
            if (AssertUtils.isNotEmpty(applyUnderwritePolicyRequest.getFactAgentId())) {
                ApplyFactAgentPo applyFactAgentPo = new ApplyFactAgentPo();
                applyFactAgentPo.setApplyFactAgentId(UUIDUtils.getUUIDShort());
                applyFactAgentPo.setAgentId(applyUnderwritePolicyRequest.getFactAgentId());
                applyFactAgentPo.setApplyId(applyPo.getApplyId());
                applyFactAgentPo.setValidFlag(TerminologyConfigEnum.VALID_FLAG.effective.name());
                applyFactAgentPo.setCreatedDate(DateUtils.getCurrentTime());
                applyFactAgentPo.setCreatedUserId(userId);
                applyFactAgentDao.insert(applyFactAgentPo);
            }
        } catch (Exception e) {
            //事务回滚
            TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
            if (e instanceof RequestException) {
                RequestException error = (RequestException) e;
                //错误设置
                resultObject.setIenum(error.getiEnum());
            } else {
                resultObject.setIenum(ApplyErrorConfigEnum.APPLY_SAVE_UNDERWRITE_ERROR);
            }
            e.printStackTrace();
        }
        return resultObject;
    }

    @Override
    @Transactional
    public ResultObject<BaseResponse> commitUnderWritePolicyInfo(String userId, ApplyUnderwritePolicyRequest applyUnderwritePolicyRequest) {
        ResultObject<BaseResponse> resultObject = new ResultObject<BaseResponse>();
        try {
            //数据验证
            applyUnderWriteParameterValidate.validParameterUnderwriteInstrument(applyUnderwritePolicyRequest);

            ApplyPo applyPo = applyExtDao.loadApplyPoById(applyUnderwritePolicyRequest.getApplyId());

            AssertUtils.isNotNull(getLogger(), applyPo, ApplyErrorConfigEnum.APPLY_QUERY_APPLY_ACCEPT_IS_NULL_ERROR);
            //保存承保信息
            applyUnderwriteDecisionTransData.transApplyPo(applyPo, applyUnderwritePolicyRequest);
            applyBoService.saveApplyPo(applyPo);

            //实际业务归属代理人
            List<ApplyFactAgentPo> applyFactAgentPos = applyFactAgentDao.fetchByApplyId(applyPo.getApplyId());
            if (AssertUtils.isNotEmpty(applyFactAgentPos)) {
                applyFactAgentDao.delete(applyFactAgentPos.get(0));
            }
            if (AssertUtils.isNotEmpty(applyUnderwritePolicyRequest.getFactAgentId())) {
                ApplyFactAgentPo applyFactAgentPo = new ApplyFactAgentPo();
                applyFactAgentPo.setApplyFactAgentId(UUIDUtils.getUUIDShort());
                applyFactAgentPo.setAgentId(applyUnderwritePolicyRequest.getFactAgentId());
                applyFactAgentPo.setApplyId(applyPo.getApplyId());
                applyFactAgentPo.setValidFlag(TerminologyConfigEnum.VALID_FLAG.effective.name());
                applyFactAgentPo.setCreatedDate(DateUtils.getCurrentTime());
                applyFactAgentPo.setCreatedUserId(userId);
                applyFactAgentDao.insert(applyFactAgentPo);
            }
            //支付
            //paymentBusinessService.handApplyPayment(applyPo);
            //直接承保
            //applyToPolicyTransData.transferApplyToPolicy(applyUnderwritePolicyRequest.getApplyId());
            //工作流任务完成接口参数
            CompleteTaskParam completeTaskParam = new CompleteTaskParam();
            completeTaskParam.setBusinessId(applyUnderwritePolicyRequest.getApplyId());
            completeTaskParam.setWorkflowItemType(ModelConstantEnum.WORKFLOW_STATUS.ARTIFICIAL_UNDERWRITING_TASK.name());
            resultObject = workFlowApi.completeTask(completeTaskParam);
        } catch (Exception e) {
            //事务回滚
            TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
            if (e instanceof RequestException) {
                RequestException error = (RequestException) e;
                //错误设置
                resultObject.setIenum(error.getiEnum());
            } else {
                resultObject.setIenum(ApplyErrorConfigEnum.APPLY_SAVE_UNDERWRITE_ERROR);
            }
            e.printStackTrace();
        }
        return resultObject;
    }


    /**
     * 保存承保复核信息
     *
     * @param userId                       用户ID
     * @param applyUnderwritePolicyRequest 承保请求类
     * @return
     */
    @Override
    @Transactional
    public ResultObject<BaseResponse> saveUnderWriteReviewPolicyInfo(String userId, ApplyUnderwritePolicyRequest applyUnderwritePolicyRequest) {
        ResultObject resultObject = new ResultObject();
        try {
            //数据验证
            applyUnderWriteParameterValidate.validParameterUnderwriteInstrument(applyUnderwritePolicyRequest);
            ApplyPo applyPo = applyExtDao.loadApplyPoById(applyUnderwritePolicyRequest.getApplyId());
            AssertUtils.isNotNull(getLogger(), applyPo, ApplyErrorConfigEnum.APPLY_QUERY_APPLY_ACCEPT_IS_NULL_ERROR);
            //保存承保信息
            applyUnderwriteDecisionTransData.transApplyPo(applyPo, applyUnderwritePolicyRequest);
            applyBoService.saveApplyPo(applyPo);
        } catch (Exception e) {
            //事务回滚
            TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
            if (e instanceof RequestException) {
                RequestException error = (RequestException) e;
                //错误设置
                resultObject.setIenum(error.getiEnum());
            } else {
                resultObject.setIenum(ApplyErrorConfigEnum.APPLY_SAVE_UNDERWRITE_ERROR);
            }
            e.printStackTrace();
        }
        return resultObject;
    }

    @Override
    @Transactional
    public ResultObject<BaseResponse> commitUnderWriteReviewPolicyInfo(String userId, ApplyUnderwritePolicyRequest applyUnderwritePolicyRequest) {
        ResultObject resultObject = new ResultObject();
        try {
            //数据验证
            applyUnderWriteParameterValidate.validParameterUnderwriteInstrument(applyUnderwritePolicyRequest);
            ApplyPo applyPo = applyExtDao.loadApplyPoById(applyUnderwritePolicyRequest.getApplyId());
            AssertUtils.isNotNull(getLogger(), applyPo, ApplyErrorConfigEnum.APPLY_QUERY_APPLY_ACCEPT_IS_NULL_ERROR);
            //保存承保信息
            applyUnderwriteDecisionTransData.transApplyPo(applyPo, applyUnderwritePolicyRequest);
            applyBoService.saveApplyPo(applyPo);
            //支付
            //paymentBusinessService.handApplyPayment(applyPo);
            applyPremiumBaseService.saveApplyPremium(userId, applyUnderwritePolicyRequest.getApplyId(), ApplyTermEnum.PAYMENT_STATUS.PAYMENT_SUCCESS.name());
            //直接承保
            applyToPolicyTransData.transferApplyToPolicy(applyUnderwritePolicyRequest.getApplyId(), null);
            //工作流任务完成接口参数
            CompleteTaskParam completeTaskParam = new CompleteTaskParam();
            completeTaskParam.setBusinessId(applyUnderwritePolicyRequest.getApplyId());
            completeTaskParam.setWorkflowItemType(ModelConstantEnum.WORKFLOW_STATUS.UNDERWRITING_REVIEW_TASK.name());
            completeTaskParam.setUnderwritingDecisionFlag(ApplyTermEnum.UNDERWRITE_TYPE.UNDERWRITE_PASS.name());
            resultObject = workFlowApi.completeTask(completeTaskParam);
        } catch (Exception e) {
            //事务回滚
            TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
            if (e instanceof RequestException) {
                RequestException error = (RequestException) e;
                //错误设置
                resultObject.setIenum(error.getiEnum());
            } else {
                resultObject.setIenum(ApplyErrorConfigEnum.APPLY_SAVE_UNDERWRITE_ERROR);
            }
            e.printStackTrace();
        }
        return resultObject;
    }


    /**
     * 分页查询承保复核列表
     *
     * @param userId                     投保单ID
     * @param applyUnderWriteListRequest 核保列表请求参数
     * @return UnderWriteListResponse
     */
    @Override
    public ResultObject<BasePageResponse<UnderWriteListResponse>> getUnderWriteReviews(String userId, ApplyUnderWriteListRequest applyUnderWriteListRequest) {
        ResultObject<BasePageResponse<UnderWriteListResponse>> resultObject = new ResultObject<>();
        List<UnderWriteListResponse> underWriteListResponses = new ArrayList<>();
        try {
            WaitingTaskRequest waitingTaskRequest = new WaitingTaskRequest();
            waitingTaskRequest.setWorkflowItemType(ModelConstantEnum.WORKFLOW_STATUS.UNDERWRITING_REVIEW_TASK.name());
            waitingTaskRequest.setWorkflowType(ModelConstantEnum.WORKFLOW_STATUS.NEW_CONTRACT.name());
            waitingTaskRequest.setUserId(userId);
            waitingTaskRequest.setAcceptStatusDec(applyUnderWriteListRequest.getUnderWriteStatus());
            if (AssertUtils.isNotNull(applyUnderWriteListRequest.getInputStartDate())) {
                waitingTaskRequest.setHandStartDate(applyUnderWriteListRequest.getInputStartDate() + "");
            }
            if (AssertUtils.isNotNull(applyUnderWriteListRequest.getInputEndDate())) {
                waitingTaskRequest.setHandEndDate(applyUnderWriteListRequest.getInputEndDate() + "");
            }

            //获取当前用户的流程中的待办任务
            ResultObject<List<WorkItemResponse>> resultObject1 = workFlowApi.queryWaitingTasks(waitingTaskRequest);
            AssertUtils.isResultObjectError(log, resultObject1, ApplyErrorConfigEnum.APPLY_QUERY_WORKFLOW_FAIL);
            List<WorkItemResponse> listWorkflowTaskResponse = resultObject1.getData();
            List<String> listPolicyIds = new ArrayList<>();
            if (AssertUtils.isNotEmpty(listWorkflowTaskResponse)) {
                listPolicyIds = listWorkflowTaskResponse.stream().map(WorkItemResponse::getBusinessId).distinct().collect(Collectors.toList());
            }

            if (!AssertUtils.isNotEmpty(listPolicyIds)) {
                return resultObject;
            }

            List<ApplyUnderWriteListBo> applyUnderWriteListBos = applyUnderWriteExtDao.loadUnderWriteList(applyUnderWriteListRequest, listPolicyIds);

            if (!AssertUtils.isNotEmpty(applyUnderWriteListBos)) {
                return resultObject;
            }
            List<String> allBranchIds = new ArrayList<>();
            List<String> agentIds = new ArrayList<>();
            applyUnderWriteListBos.forEach(applyUnderWriteListBo -> {
                allBranchIds.add(applyUnderWriteListBo.getManagerBranchId());
                allBranchIds.add(applyUnderWriteListBo.getSalesBranchId());
                if (AssertUtils.isNotEmpty(applyUnderWriteListBo.getAgentId())) {
                    agentIds.add(applyUnderWriteListBo.getAgentId());
                }
            });

            //调取代理人接口获取代理人信息
            AgentApplyQueryRequest applyAgentRequest = new AgentApplyQueryRequest();
            applyAgentRequest.setListAgentId(agentIds);
            List<AgentResponse> agentResponses = agentApi.agentsGet(applyAgentRequest).getData();
            AssertUtils.isNotEmpty(log, agentResponses, ApplyErrorConfigEnum.APPLY_BUSINESS_AGENT_IS_NOT_FOUND_OBJECT);

            List<BranchResponse> allBranchResponses = platformBranchApi.branchsPost(allBranchIds).getData();

            applyUnderWriteListBos.forEach(applyUnderWriteListBo -> {
                UnderWriteListResponse underWriteListResponse = new UnderWriteListResponse();
                ClazzUtils.copyPropertiesIgnoreNull(applyUnderWriteListBo, underWriteListResponse);
                agentResponses.stream().filter(agentResponse -> applyUnderWriteListBo.getAgentId().equals(agentResponse.getAgentId())).findFirst().ifPresent((agent) -> {
                    underWriteListResponse.setAgentCode(agent.getAgentCode());
                    underWriteListResponse.setAgentName(agent.getAgentName());
                    underWriteListResponse.setAgentPhone(agent.getMobile());
                });

                if (AssertUtils.isNotEmpty(allBranchResponses)) {
                    allBranchResponses.stream().filter(branchResponse -> branchResponse.getBranchId().equals(applyUnderWriteListBo.getSalesBranchId())).findFirst().ifPresent((sales) -> {
                        underWriteListResponse.setSalesBranchName(sales.getBranchName());
                    });
                    allBranchResponses.stream().filter(branchRespFc -> branchRespFc.getBranchId().equals(applyUnderWriteListBo.getManagerBranchId())).findFirst().ifPresent((manager) -> {
                        underWriteListResponse.setManagerBranchName(manager.getBranchName());
                    });
                }
                listWorkflowTaskResponse.stream().filter(workflowTaskRespFc -> workflowTaskRespFc.getBusinessId().equals(applyUnderWriteListBo.getApplyId())).findFirst().ifPresent((value2) -> {
                    if (AssertUtils.isNotEmpty(value2.getCreateUserId())) {
                        EmployeResponse employeResponse = platformEmployeeApi.employeGet(value2.getCreateUserId()).getData();
                        if (AssertUtils.isNotNull(employeResponse)) {
                            underWriteListResponse.setReviewer(employeResponse.getEmployeName());
                        }
                    }

                    long startDate = value2.getStartDate();
                    if (AssertUtils.isNotNull(startDate)) {
                        underWriteListResponse.setReviewDate(startDate);
                    }
                    underWriteListResponse.setUnderWriteStatus(value2.getWorkItemStatus());
                    //设置承保标识
                    //获取当前机构的所有父机构
                    ResultObject<List<BranchResponse>> listResultObject = platformBranchApi.userParentBranchs(applyUnderWriteListBo.getSalesBranchId());
                    if (!AssertUtils.isResultObjectListDataNull(listResultObject)) {
                        List<String> branchIds = listResultObject.getData().stream().map(BranchResponse::getBranchId).distinct().collect(Collectors.toList());
                        ApplyConfigDetailBo applyConfigDetailBo = applyConfigBranchExtDao.loadApplyConfigDetailBo(branchIds, ApplyTermEnum.APPLY_CONFIG_FLAG.UNDERWRITE_FLAG.name());
                        if (AssertUtils.isNotNull(applyConfigDetailBo)) {
                            underWriteListResponse.setUnderwriteFlag(applyConfigDetailBo.getValue());
                        }
                    }
                });

                underWriteListResponses.add(underWriteListResponse);
            });

            //获取总页数
            Integer totalLine = AssertUtils.isNotNull(applyUnderWriteListBos) ? applyUnderWriteListBos.get(0).getTotalLine() : null;

            BasePageResponse<UnderWriteListResponse> basePageResponse = BasePageResponse.getData(applyUnderWriteListRequest.getCurrentPage(), applyUnderWriteListRequest.getPageSize(), totalLine, underWriteListResponses);

            resultObject.setData(basePageResponse);

        } catch (Exception e) {
            e.printStackTrace();
            if (e instanceof RequestException) {
                RequestException error = (RequestException) e;
                //错误设置
                resultObject.setIenum(error.getiEnum());
            } else {
                resultObject.setIenum(ApplyErrorConfigEnum.APPLY_QUERY_APPLY_UNDER_WRITE_INFO_ERROR);
            }
        }
        return resultObject;
    }


    /**
     * 人工核保任务签收
     *
     * @param userId  用户ID
     * @param applyId 投保单ID
     * @return 工作流处理结果
     */
    @Override
    @Transactional
    public ResultObject underWriteReviewSign(String userId, String applyId) {
        ResultObject resultObject = new ResultObject();
        try {
            //参数验证
            AssertUtils.isNotEmpty(log, applyId, ApplyErrorConfigEnum.APPLY_APPLY_ID_IS_NOT_NULL);
            //先调用工作流任务签收
            resultObject = workFlowApi.claimTask(userId, applyId, ModelConstantEnum.WORKFLOW_STATUS.UNDERWRITING_REVIEW_TASK.name(), null);
            AssertUtils.isResultObjectError(log, resultObject);
        } catch (Exception e) {
            e.printStackTrace();
            if (e instanceof RequestException) {
                RequestException error = (RequestException) e;
                //错误设置
                resultObject.setIenum(error.getiEnum());
            } else {
                resultObject.setIenum(ApplyErrorConfigEnum.APPLY_SAVE_APPLY_UNDER_WRITE_TASK_ERROR);
            }
        }
        return resultObject;
    }

    @Override
    public ResultObject<PolicyVo> applyUnderwrite(String applyNo, String password) {
        ResultObject<PolicyVo> resultObject = new ResultObject<>();
        //获取事物状态
        TransactionStatus transactionStatus = platformTransactionManager.getTransaction(new DefaultTransactionDefinition());
        String applyId = null;
        boolean isRollback = false;
        boolean errorFlag = false;
        try {
            //参数验证
            AssertUtils.isNotEmpty(log, applyNo, ApplyErrorConfigEnum.APPLY_POLICY_NO_IS_NOT_NULL);
            AssertUtils.isNotEmpty(log, password, ApplyErrorConfigEnum.APPLY_POLICY_UNDERWRITE_PASSWORD_IS_NOT_NULL);
            if (!password.equals("gclife520")) {
                throw new RequestException(ApplyErrorConfigEnum.APPLY_POLICY_UNDERWRITE_PASSWORD_IS_ERROR);
            }
            //查询投保单是否承保失败
            ApplyPo applyPo = applyDao.fetchOneByApplyNo(applyNo);
            AssertUtils.isNotNull(getLogger(), applyPo, ApplyErrorConfigEnum.APPLY_BUSINESS_APPLY_IS_NOT_FOUND_OBJECT);
            applyId = applyPo.getApplyId();
            if (!ApplyTermEnum.APPLY_STATUS_FLAG.APPLY_STATUS_APPROVE_FAILED.name().equals(applyPo.getApplyStatus())) {
                throw new RequestException(ApplyErrorConfigEnum.APPLY_IS_NO_NEED_REUNDERWRITE);
            }
            //承保
            isRollback = true;
            PolicyVo policyVo = applyToPolicyTransData.transferApplyToPolicy(applyPo.getApplyId(), null);
            resultObject.setData(policyVo);
            //提交事物
            platformTransactionManager.commit(transactionStatus);
        } catch (Exception e) {
            errorFlag = true;
            //事务回滚
            platformTransactionManager.rollback(transactionStatus);
            if (e instanceof RequestException) {
                RequestException error = (RequestException) e;
                //错误设置
                resultObject.setIenum(error.getiEnum());
            } else {
                resultObject.setIenum(ApplyErrorConfigEnum.APPLY_BUSINESS_SAVE_POLICY_DATA_ERROR);
            }
        } finally {
            if (errorFlag && isRollback) {
                TransactionStatus finallyTransactionStatus = platformTransactionManager.getTransaction(new DefaultTransactionDefinition());
                try {
                    //承保失败
                    if (AssertUtils.isNotEmpty(applyId)) {
                        //查询投保单
                        ApplyBo applyBo = applyExtDao.loadApplyBoById(applyId);
                        //发送承保失败消息
                        messageBusinessService.pushApplyMessageBatch(ApplyTermEnum.MSG_BUSINESS_TYPE.APPLY_TRANFER_POLICY_FAIL.name(), applyBo);

                        ApplyPo applyPo = applyToPolicyTransData.transApplyFailedToPolicy(applyId);
                        if (AssertUtils.isNotNull(applyPo)) {
                            applyBoService.saveApplyPo(applyPo);
                        }
                        // POLICY 回滚
                        policyApi.applyToPolicyRollback(applyId);
                    }
                    //提交事物
                    platformTransactionManager.commit(finallyTransactionStatus);
                } catch (Exception e) {
                    platformTransactionManager.rollback(finallyTransactionStatus);
                }
            }
        }
        return resultObject;
    }

    @Override
    public ResultObject saveReinsuranceRules(String applyId, String reinsuranceRules) {
        List<ApplyInsuredPo> applyInsuredPos = applyInsuredDao.fetchByApplyId(applyId);
        if (!AssertUtils.isNotEmpty(applyInsuredPos)) {
            return ResultObject.success();
        }
        applyInsuredPos.forEach(applyInsuredPo -> applyInsuredPo.setReinsuranceRules(reinsuranceRules));
        applyInsuredDao.update(applyInsuredPos);
        return ResultObject.success();
    }

    @Override
    @Transactional
    public ResultObject<String> saveCoverageAddPremium(Users users, List<ApplyAddPremiumRequest> applyAddPremiumRequests, String applyId) {
        ResultObject<String> resultObject = new ResultObject<>();

        AssertUtils.isNotEmpty(log, applyId, ApplyErrorConfigEnum.APPLY_APPLY_ID_IS_NOT_NULL);
        final String[] insuredId = {null};
        ApplyBo applyBo = applyBaseService.queryApply(applyId);
        applyBo.getListInsured().forEach(applyInsuredBo -> insuredId[0] = applyInsuredBo.getInsuredId());

        //保存加费信息
        List<ApplyAddPremiumPo> applyAddPremiumPos = new ArrayList<>();

        if (AssertUtils.isNotEmpty(applyAddPremiumRequests)) {
            this.transAddPremiumPos(applyAddPremiumRequests, applyId, applyAddPremiumPos, true);
        }

        insuredId[0] = this.resetApplyWithAddPremium(applyBo, applyAddPremiumPos, users);

        resultObject.setData(insuredId[0]);
        return resultObject;
    }

    /**
     * 计算加费
     *
     * @param applyAddPremiumRequests
     * @param applyId
     * @return
     */
    @Override
    public ResultObject<AddPremiumCalculateResponse> calculateCoverageAddPremium(List<ApplyAddPremiumRequest> applyAddPremiumRequests, String applyId) {
        ResultObject<AddPremiumCalculateResponse> resultObject = new ResultObject<>();
        AddPremiumCalculateResponse addPremiumCalculateResponse = new AddPremiumCalculateResponse();

        List<ApplyAddPremiumPo> applyAddPremiumPos = new ArrayList<>();
        if (AssertUtils.isNotEmpty(applyAddPremiumRequests)) {
            this.transAddPremiumPos(applyAddPremiumRequests, applyId, applyAddPremiumPos, false);
        }


        ApplyBo applyBo = applyBaseService.queryApply(applyId);
        applyBo.getListInsured().forEach(applyInsuredBo -> {
            applyInsuredBo.getListCoverage().forEach(applyCoverageBo -> {
                if (AssertUtils.isNotEmpty(applyAddPremiumPos)) {
                    List<ApplyAddPremiumPo> collect = applyAddPremiumPos.stream().filter(applyAddPremiumPo -> applyAddPremiumPo.getCoverageId().equals(applyCoverageBo.getCoverageId())).collect(Collectors.toList());
                    if (AssertUtils.isNotEmpty(collect)) {
                        collect.forEach(applyAddPremiumPo -> applyAddPremiumPo.setProductId(applyCoverageBo.getProductId()));
                        applyCoverageBo.setListAddPremium(collect);
                    }
                } else {
                    applyCoverageBo.setListAddPremium(null);
                }
            });
        });

        ApplyRequest applyRequest = (ApplyRequest) this.converterObject(applyBo, ApplyRequest.class);
        applyRequest.setBranchId(applyBo.getSalesBranchId());
        applyRequest.setBusinessType("APPLY_TWO");
        ApplyPremiumBo applyPremiumBo = applyBo.getApplyPremiumBo();
        String participationDiscountFlag = TerminologyConfigEnum.WHETHER.NO.name();
        if (AssertUtils.isNotNull(applyPremiumBo) && (AssertUtils.isNotNull(applyPremiumBo.getSpecialDiscount()) || AssertUtils.isNotNull(applyPremiumBo.getPromotionType()))) {
            participationDiscountFlag = TerminologyConfigEnum.WHETHER.YES.name();
        }
        applyRequest.setParticipationDiscountFlag(participationDiscountFlag);
        applyRequest.setPromotionType(applyPremiumBo.getPromotionType());
        applyRequest.setDiscountDate(applyBo.getAppSubmitUnderwritingDate());

        log.info(JSONObject.toJSONString(applyRequest));

        ResultObject<ApplyResponse> resultObject1 = productApi.trialCalculation(applyRequest);
        AssertUtils.isResultObjectError(log, resultObject1);

        AppApplyBo appApplyBo1 = (AppApplyBo) converterObject(resultObject1.getData(), AppApplyBo.class);

        List<ApplyCoveragePo> applyCoveragePos = new ArrayList<>();
        appApplyBo1.getListInsured().forEach(applyInsuredBo -> applyCoveragePos.addAll(applyInsuredBo.getListCoverage()));

        final String[] mainPremiumFrequency = {""};
        applyCoveragePos.stream().filter(applyCoveragePo -> ApplyTermEnum.PRODUCT_PRIMARY_FLAG.MAIN.name().equals(applyCoveragePo.getPrimaryFlag())).findFirst()
                .ifPresent(applyCoveragePo -> mainPremiumFrequency[0] = applyCoveragePo.getPremiumFrequency());

        addPremiumCalculateResponse.setTotalPremium(applyCoveragePos.stream().map(ApplyCoveragePo::getTotalPremium).reduce(BigDecimal.ZERO, BigDecimal::add));
        BigDecimal basePremium = addPremiumCalculateResponse.getTotalPremium();
        BigDecimal receivablePremium = addPremiumCalculateResponse.getTotalPremium();

        if (AssertUtils.isNotNull(applyPremiumBo)
                && AssertUtils.isNotNull(applyPremiumBo.getPremiumBeforeDiscount())
                && AssertUtils.isNotNull(applyPremiumBo.getDiscountType())
                && AssertUtils.isNotNull(applyPremiumBo.getSpecialDiscount())
        ) {
            addPremiumCalculateResponse.setDiscountPremiumFlag(TerminologyConfigEnum.WHETHER.YES.name());
            addPremiumCalculateResponse.setPromotionType(applyPremiumBo.getPromotionType());
            addPremiumCalculateResponse.setDiscountModel(applyPremiumBo.getDiscountModel());
            addPremiumCalculateResponse.setDiscountType(applyPremiumBo.getDiscountType());
            if (!AssertUtils.isNotEmpty(applyPremiumBo.getDiscountModel()) || ProductTermEnum.DISCOUNT_MODEL.PERCENTAGE.name().equals(applyPremiumBo.getDiscountModel())) {
                addPremiumCalculateResponse.setSpecialDiscount(new BigDecimal(new BigDecimal("100").multiply(applyPremiumBo.getSpecialDiscount()).stripTrailingZeros().toPlainString()));
                receivablePremium = receivablePremium.multiply(new BigDecimal("1").subtract(applyPremiumBo.getSpecialDiscount())).setScale(2, BigDecimal.ROUND_HALF_UP);
            } else if (ProductTermEnum.DISCOUNT_MODEL.FIXED_AMOUNT.name().equals(applyPremiumBo.getDiscountModel())) {
                addPremiumCalculateResponse.setSpecialDiscount(new BigDecimal(applyPremiumBo.getSpecialDiscount().stripTrailingZeros().toPlainString()));
                receivablePremium = receivablePremium.subtract(applyPremiumBo.getSpecialDiscount()).setScale(2, BigDecimal.ROUND_HALF_UP);
            }
        }

        addPremiumCalculateResponse.setReceivablePremium(receivablePremium);

        if (AssertUtils.isNotEmpty(applyAddPremiumPos)) {
            BigDecimal annualAddPremium = applyAddPremiumPos.stream().map(ApplyAddPremiumPo::getTotalAddPremium).reduce(BigDecimal.ZERO, BigDecimal::add);
            addPremiumCalculateResponse.setAnnualAddPremium(annualAddPremium);

            BigDecimal conversionFactor = BigDecimal.valueOf(ApplyTermEnum.PREMIUM_FREQUENCY_CONVERSION_FACTOR.valueOf(mainPremiumFrequency[0]).value());
            BigDecimal conversionFactorTotalAddPremium = annualAddPremium.multiply(conversionFactor).setScale(2, BigDecimal.ROUND_HALF_UP);

            BigDecimal totalAddPremium = applyAddPremiumPos.stream().map(applyAddPremiumPo -> applyAddPremiumPo.getTotalAddPremium().multiply(BigDecimal.valueOf(applyAddPremiumPo.getAddPremiumPeriod()))).reduce(BigDecimal.ZERO, BigDecimal::add);
            addPremiumCalculateResponse.setTotalAddPremium(totalAddPremium);

            addPremiumCalculateResponse.setOriginalAddPremium(conversionFactorTotalAddPremium);
            basePremium = basePremium.subtract(conversionFactorTotalAddPremium);
        }
        addPremiumCalculateResponse.setBasePremium(basePremium);
        resultObject.setData(addPremiumCalculateResponse);
        return resultObject;
    }

    /**
     * 重新自核
     *
     * @param users           用户
     * @param appRequestHeads
     * @param applyId         applyId
     * @return UnderWriteProblemResponses
     */
    @Override
    @Transactional
    public ResultObject<List<UnderWriteProblemResponse>> reAutoUW(Users users, AppRequestHeads appRequestHeads, String applyId) {
        ResultObject<List<UnderWriteProblemResponse>> resultObject = new ResultObject<>();
        AssertUtils.isNotEmpty(log, applyId, ApplyErrorConfigEnum.APPLY_APPLY_ID_IS_NOT_NULL);
        ApplyPo applyPo = applyBaseService.queryApplyPo(applyId);
        AssertUtils.isNotNull(log, applyPo, ApplyErrorConfigEnum.APPLY_BUSINESS_APPLY_IS_NOT_FOUND_OBJECT);
        if (!Arrays.asList(ApplyTermEnum.APPLY_STATUS_FLAG.APPLY_STATUS_UNDERWRITE_ARTIFICIAL.name(), ApplyTermEnum.APPLY_STATUS_FLAG.APPLY_STATUS_PAID_PENDING_ON_UW.name()).contains(applyPo.getApplyStatus())) {
            throwsException(log, ApplyErrorConfigEnum.APPLY_CANNOT_BE_OPERATED_AUTO_UW_ERROR);
        }
        applyAutoUnderWriteBusinessService.initiateAutoUnderWriting(applyId, users, appRequestHeads, "MANUAL_UW");
        List<UnderWriteProblemResponse> underWriteProblemResponses = applyUnderWriteExtDao.getProblemInfo(applyId);
        if (AssertUtils.isNotEmpty(underWriteProblemResponses)) {
            underWriteProblemResponses.forEach(underWriteProblemResponse -> {
                if (ApplyTermEnum.AUTO_UNDERWRITE_PROBLEM.REAL_CLIENT.name().equals(underWriteProblemResponse.getProblemCode())) {
                    underWriteProblemResponse.setRealClientFlag(TerminologyConfigEnum.WHETHER.YES.name());
                }
            });

            resultObject.setData(underWriteProblemResponses);
        }
        return resultObject;
    }

    /**
     * 人工核保时更改投保单缴费周期
     *
     * @param users            用户
     * @param appRequestHeads  请求头
     * @param applyId          投保单ID
     * @param premiumFrequency 缴费周期
     * @return
     */
    @Override
    @Transactional
    public ResultObject<String> calculateCoveragePremiumFrequencyPremium(Users users, AppRequestHeads appRequestHeads, String applyId, String premiumFrequency, String type) {
        ResultObject<String> resultObject = new ResultObject<>();
        final String[] insuredId = {null};
        //判断该投保单能不能进行缴费周期变更
        AssertUtils.isNotEmpty(log, applyId, ApplyErrorConfigEnum.APPLY_APPLY_ID_IS_NOT_NULL);
        ApplyBo applyBo = applyBaseService.queryApply(applyId);
        AssertUtils.isNotNull(log, applyBo, ApplyErrorConfigEnum.APPLY_BUSINESS_APPLY_IS_NOT_FOUND_OBJECT);
        applyChangeTransData.validatePremiumFrequency(applyBo, premiumFrequency, type);
        //设置各个险种的缴费周期，然后重新算费
        applyBo.getListInsured().forEach(applyInsuredBo -> {
            applyInsuredBo.getListCoverage().forEach(applyCoverageBo -> applyCoverageBo.setPremiumFrequency(premiumFrequency));
        });
        List<ApplyAddPremiumPo> applyAddPremiumPos = applyBo.getListApplyAddPremiumPo();
        ApplyBo applyBo1 = this.reCalApplyPremium(applyBo);
        applyBo1.getListInsured().forEach(applyInsuredBo -> {
            insuredId[0] = applyInsuredBo.getInsuredId();
            applyInsuredBo.getListCoverage().forEach(applyCoverageBo -> {
                if (AssertUtils.isNotEmpty(applyAddPremiumPos)) {
                    applyAddPremiumPos.forEach(applyAddPremiumPo -> {
                        if (applyAddPremiumPo.getProductId().equals(applyCoverageBo.getProductId())) {
                            applyAddPremiumPo.setCoverageId(applyCoverageBo.getCoverageId());
                        }
                    });
                }
            });
        });
        if (AssertUtils.isNotEmpty(applyAddPremiumPos)) {
            applyPremiumBaseService.saveApplyAddPremium(users.getUserId(), applyAddPremiumPos);
        }
        resultObject.setData(insuredId[0]);
        return resultObject;
    }

    private void transAddPremiumPos(List<ApplyAddPremiumRequest> applyAddPremiumRequests, String applyId, List<ApplyAddPremiumPo> applyAddPremiumPos, boolean saveFlag) {
        applyAddPremiumRequests.forEach(applyAddPremiumRequest -> {
            AssertUtils.isNotEmpty(log, applyAddPremiumRequest.getCoverageId(), ApplyErrorConfigEnum.APPLY_UNDERWRITE_DECISION_COVERAGEID_ERROR);
            ApplyCoveragePo applyCoveragePo = applyCoverageDao.findById(applyAddPremiumRequest.getCoverageId());
            AssertUtils.isNotNull(getLogger(), applyCoveragePo, ApplyErrorConfigEnum.APPLY_BASE_BUSINESS_APPLY_COVERAGE_IS_NOT_FOUND_OBJECT);
            //删除原先的加费信息
            if (saveFlag) {
                applyPremiumBaseService.deleteApplyAddPremium(applyId, null, applyAddPremiumRequest.getCoverageId());
            }
            String originProductId = applyCoveragePo.getProductId();

            applyAddPremiumRequest.getListAddPremium().forEach(applyAddPremiumResponse -> {
                if (!AssertUtils.isNotNull(applyAddPremiumResponse.getEm()) && !AssertUtils.isNotNull(applyAddPremiumResponse.getFer())) {
//                    throwsException(ApplyErrorConfigEnum.APPLY_BASE_PARAMETER_ADD_PREMIUM_OBJECT_CODE_IS_NOT_NULL);
                    return;
                }
                AssertUtils.isNotEmpty(log, applyAddPremiumResponse.getRatingsName(), ApplyErrorConfigEnum.APPLY_BASE_PARAMETER_RATINGS_NAME_IS_NOT_NULL);
                if (AssertUtils.isNotNull(applyAddPremiumResponse.getEm())) {
                    //em必须是25的倍数
                    if (applyAddPremiumResponse.getEm().compareTo(new BigDecimal("25")) < 0
                            || applyAddPremiumResponse.getEm().divideAndRemainder(new BigDecimal("25"))[1].compareTo(BigDecimal.ZERO) != 0) {
                        throwsException(ApplyErrorConfigEnum.APPLY_BASE_PARAMETER_EM_FORMAT_ERROR);
                    }
                    AssertUtils.isNotNull(log, applyAddPremiumResponse.getEmp(), ApplyErrorConfigEnum.APPLY_BASE_PARAMETER_TOTAL_ADD_PREMIUM_IS_NOT_NULL);
                    AssertUtils.isNotNull(log, applyAddPremiumResponse.getEpy(), ApplyErrorConfigEnum.APPLY_BASE_PARAMETER_ADD_PREMIUM_PERIOD_IS_NOT_NULL);
                    AssertUtils.isNotPureDigital(log, String.valueOf(applyAddPremiumResponse.getEpy()), ApplyErrorConfigEnum.APPLY_BASE_PARAMETER_ADD_PREMIUM_PERIOD_FORMAT_ERROR);
                    if (applyAddPremiumResponse.getEpy() <= 0) {
                        throwsException(ApplyErrorConfigEnum.APPLY_BASE_PARAMETER_ADD_PREMIUM_PERIOD_FORMAT_ERROR);
                    }
                    if (applyAddPremiumResponse.getEpy() <= 0) {
                        throwsException(ApplyErrorConfigEnum.APPLY_BASE_PARAMETER_ADD_PREMIUM_PERIOD_FORMAT_ERROR);
                    }

                    if (AssertUtils.isNotEmpty(applyCoveragePo.getPremiumPeriod()) && Long.parseLong(applyCoveragePo.getPremiumPeriod()) < applyAddPremiumResponse.getEpy()) {
                        throwsException(ApplyErrorConfigEnum.APPLY_BASE_PARAMETER_ADD_PREMIUM_PERIOD_CANNOT_EXCEED_THE_PREMIUM_PERIOD);
                    }
                    ApplyAddPremiumPo applyAddPremiumPo = new ApplyAddPremiumPo();
                    applyAddPremiumPo.setRatingsName(applyAddPremiumResponse.getRatingsName().replace(" ", "_").toUpperCase());
                    applyAddPremiumPo.setAddPremiumObjectCode("EM(%)");
                    applyAddPremiumPo.setAddPremiumObjectValue(applyAddPremiumResponse.getEm());
                    applyAddPremiumPo.setTotalAddPremium(applyAddPremiumResponse.getEmp());
                    applyAddPremiumPo.setAddPremiumPeriod(applyAddPremiumResponse.getEpy());
                    applyAddPremiumPo.setAddPremiumId(null);
                    applyAddPremiumPo.setApplyId(applyId);
                    applyAddPremiumPo.setCoverageId(applyAddPremiumRequest.getCoverageId());
                    applyAddPremiumPo.setProductId(originProductId);
                    applyAddPremiumPos.add(applyAddPremiumPo);
                }
                if (AssertUtils.isNotNull(applyAddPremiumResponse.getFer())) {
                    AssertUtils.isNotNull(log, applyAddPremiumResponse.getFep(), ApplyErrorConfigEnum.APPLY_BASE_PARAMETER_TOTAL_ADD_PREMIUM_IS_NOT_NULL);
                    AssertUtils.isNotNull(log, applyAddPremiumResponse.getFey(), ApplyErrorConfigEnum.APPLY_BASE_PARAMETER_ADD_PREMIUM_PERIOD_IS_NOT_NULL);
                    AssertUtils.isNotPureDigital(log, String.valueOf(applyAddPremiumResponse.getFey()), ApplyErrorConfigEnum.APPLY_BASE_PARAMETER_ADD_PREMIUM_PERIOD_FORMAT_ERROR);
                    if (applyAddPremiumResponse.getFey() <= 0) {
                        throwsException(ApplyErrorConfigEnum.APPLY_BASE_PARAMETER_ADD_PREMIUM_PERIOD_FORMAT_ERROR);
                    }

                    if (AssertUtils.isNotEmpty(applyCoveragePo.getPremiumPeriod()) && Long.parseLong(applyCoveragePo.getPremiumPeriod()) < applyAddPremiumResponse.getFey()) {
                        throwsException(ApplyErrorConfigEnum.APPLY_BASE_PARAMETER_ADD_PREMIUM_PERIOD_CANNOT_EXCEED_THE_PREMIUM_PERIOD);
                    }

                    ApplyAddPremiumPo applyAddPremiumPo = new ApplyAddPremiumPo();
                    applyAddPremiumPo.setRatingsName(applyAddPremiumResponse.getRatingsName().replace(" ", "_").toUpperCase());
                    applyAddPremiumPo.setAddPremiumObjectCode("FER(‰)");
                    applyAddPremiumPo.setAddPremiumObjectValue(applyAddPremiumResponse.getFer());
                    applyAddPremiumPo.setTotalAddPremium(applyAddPremiumResponse.getFep());
                    applyAddPremiumPo.setAddPremiumPeriod(applyAddPremiumResponse.getFey());
                    applyAddPremiumPo.setAddPremiumId(null);
                    applyAddPremiumPo.setApplyId(applyId);
                    applyAddPremiumPo.setCoverageId(applyAddPremiumRequest.getCoverageId());
                    applyAddPremiumPo.setProductId(originProductId);
                    applyAddPremiumPos.add(applyAddPremiumPo);
                }
            });
        });
    }

    /**
     * 保存加费信息
     *
     * @param applyBo
     * @param applyAddPremiumPos
     * @param users
     * @return
     */
    private String resetApplyWithAddPremium(ApplyBo applyBo, List<ApplyAddPremiumPo> applyAddPremiumPos, Users users) {
        final String[] insuredId = {null};
        String applyId = applyBo.getApplyId();
        if (!AssertUtils.isNotEmpty(applyAddPremiumPos)) {
            List<ApplyAddPremiumPo> applyAddPremium = applyPremiumBaseService.getApplyAddPremium(applyId);
            applyAddPremiumDao.delete(applyAddPremium);
        }
        applyBo.getListInsured().forEach(applyInsuredBo -> {
            insuredId[0] = applyInsuredBo.getInsuredId();
            applyInsuredBo.getListCoverage().forEach(applyCoverageBo -> {
                if (AssertUtils.isNotEmpty(applyAddPremiumPos)) {
                    List<ApplyAddPremiumPo> collect = applyAddPremiumPos.stream().filter(applyAddPremiumPo -> applyAddPremiumPo.getCoverageId().equals(applyCoverageBo.getCoverageId())).collect(Collectors.toList());
                    if (AssertUtils.isNotEmpty(collect)) {
                        collect.forEach(applyAddPremiumPo -> applyAddPremiumPo.setProductId(applyCoverageBo.getProductId()));
                        applyCoverageBo.setListAddPremium(collect);
                    }
                } else {
                    applyCoverageBo.setListAddPremium(null);
                }
            });
        });

        ApplyBo applyBo1 = this.reCalApplyPremium(applyBo);

        applyBo1.getListInsured().forEach(applyInsuredBo -> {
            insuredId[0] = applyInsuredBo.getInsuredId();
            applyInsuredBo.getListCoverage().forEach(applyCoverageBo -> {
                if (AssertUtils.isNotEmpty(applyAddPremiumPos)) {
                    applyAddPremiumPos.forEach(applyAddPremiumPo -> {
                        if (applyAddPremiumPo.getProductId().equals(applyCoverageBo.getProductId())) {
                            applyAddPremiumPo.setCoverageId(applyCoverageBo.getCoverageId());
                        }
                    });
                }
            });
        });
        if (AssertUtils.isNotEmpty(applyAddPremiumPos)) {
            applyPremiumBaseService.saveApplyAddPremium(users.getUserId(), applyAddPremiumPos);
        }
        return insuredId[0];
    }

    private ApplyBo reCalApplyPremium(ApplyBo applyBo) {
        ApplyRequest applyRequest = (ApplyRequest) this.converterObject(applyBo, ApplyRequest.class);
        applyRequest.setBranchId(applyBo.getSalesBranchId());
        applyRequest.setBusinessType("APPLY_TWO");
        ApplyPremiumBo applyPremiumBo = applyBo.getApplyPremiumBo();
        String participationDiscountFlag = TerminologyConfigEnum.WHETHER.NO.name();
        if (AssertUtils.isNotNull(applyPremiumBo) && (AssertUtils.isNotNull(applyPremiumBo.getSpecialDiscount()) || AssertUtils.isNotNull(applyPremiumBo.getPromotionType()))) {
            participationDiscountFlag = TerminologyConfigEnum.WHETHER.YES.name();
        }
        applyRequest.setParticipationDiscountFlag(participationDiscountFlag);
        applyRequest.setDiscountDate(applyBo.getAppSubmitUnderwritingDate());
        applyRequest.setPromotionType(applyPremiumBo.getPromotionType());
        log.info(JSONObject.toJSONString(applyRequest));

        ResultObject<ApplyResponse> resultObject1 = productApi.trialCalculation(applyRequest);
        log.info(JSONObject.toJSONString(resultObject1));
        AssertUtils.isResultObjectError(log, resultObject1);

        AppApplyBo appApplyBo1 = (AppApplyBo) converterObject(resultObject1.getData(), AppApplyBo.class);
        appApplyBo1.setBranchId(applyBo.getSalesBranchId());
        appApplyBo1.setUwAddPremiumFlag(true);

        com.gclife.apply.model.request.ApplyRequest applyRequest1 = (com.gclife.apply.model.request.ApplyRequest) converterObject(applyBo, com.gclife.apply.model.request.ApplyRequest.class);
        // 团险产品和5号产品没有变更缴费周期，不考虑多个被保人和银行第一受益人的情况
        InsuredInfoRequest insuredInfoRequest1 = applyRequest1.getListInsured().get(0);
        insuredInfoRequest1.setListBeneficiary(null);
        ApplyInsuredBo applyInsuredBo = applyBo.getListInsured().get(0);
        // 受益人结构不一致，需要重新赋值.
        List<BeneficiaryInfoRequest> listBeneficiary = new ArrayList<>();
        applyInsuredBo.getListBeneficiary().forEach(applyBeneficiaryInfoBo -> {
            BeneficiaryInfoRequest beneficiaryInfoRequest = new BeneficiaryInfoRequest();
            ClazzUtils.copyPropertiesIgnoreNull(applyBeneficiaryInfoBo, beneficiaryInfoRequest);
            ClazzUtils.copyPropertiesIgnoreNull(applyBeneficiaryInfoBo.getApplyBeneficiaryBo(), beneficiaryInfoRequest);
            if (AssertUtils.isNotNull(applyBeneficiaryInfoBo.getApplyBeneficiaryBo().getBirthday())) {
                beneficiaryInfoRequest.setBirthday(applyBeneficiaryInfoBo.getApplyBeneficiaryBo().getBirthday() + "");
            }
            if (AssertUtils.isNotNull(applyBeneficiaryInfoBo.getBeneficiaryProportion())) {
                beneficiaryInfoRequest.setBeneficiaryProportion(applyBeneficiaryInfoBo.getBeneficiaryProportion() + "");
            }
            listBeneficiary.add(beneficiaryInfoRequest);
        });
        insuredInfoRequest1.setListBeneficiary(listBeneficiary);

        //调用此方法会删除险种重新保存
        return applyDataSaveCommonService.saveInputApplyData(applyBo.getApplyId(), applyRequest1, appApplyBo1);
    }

}