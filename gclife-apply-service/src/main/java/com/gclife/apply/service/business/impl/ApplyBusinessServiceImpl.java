package com.gclife.apply.service.business.impl;

import com.alibaba.fastjson.JSON;
import com.gclife.agent.api.AgentApi;
import com.gclife.agent.model.request.AgentApplyQueryRequest;
import com.gclife.agent.model.response.AgentResponse;
import com.gclife.apply.core.jooq.tables.daos.ApplyPrintInfoDao;
import com.gclife.apply.core.jooq.tables.pojos.ApplyCoveragePo;
import com.gclife.apply.core.jooq.tables.pojos.ApplyInsuredPo;
import com.gclife.apply.core.jooq.tables.pojos.ApplyPo;
import com.gclife.apply.core.jooq.tables.pojos.ApplyUnderwriteProblemPo;
import com.gclife.apply.core.jooq.tables.pojos.ApplyPrintInfoPo;
import com.gclife.apply.dao.ApplyExtDao;
import com.gclife.apply.dao.ApplyPrintDao;
import com.gclife.apply.model.bo.*;
import com.gclife.apply.model.config.ApplyErrorConfigEnum;
import com.gclife.apply.model.config.ApplyTermEnum;
import com.gclife.apply.model.request.ApplyConditionListRequest;
import com.gclife.apply.model.request.ApplyConditionPrintRequest;
import com.gclife.apply.model.request.ApplyListRequest;
import com.gclife.apply.model.request.CalculationBmiAndAgeRequest;
import com.gclife.apply.model.respone.TemporaryCoverResponse;
import com.gclife.apply.model.response.*;
import com.gclife.apply.service.*;
import com.gclife.apply.service.business.ApplyBusinessService;
import com.gclife.apply.transform.ApplyDataTransform;
import com.gclife.apply.validate.parameter.transform.ApplyDetailTransData;
import com.gclife.apply.validate.parameter.transform.ApplyToPolicyTransData;
import com.gclife.attachment.api.AttachmentPDFDocumentApi;
import com.gclife.attachment.model.policy.apply.CoverNotePrintBo;
import com.gclife.attachment.model.request.ElectronicPolicyGeneratorRequest;
import com.gclife.attachment.model.response.AttachmentByteResponse;
import com.gclife.attachment.model.response.AttachmentResponse;
import com.gclife.common.exception.RequestException;
import com.gclife.common.model.BasePageResponse;
import com.gclife.common.model.BaseResponse;
import com.gclife.common.model.ResultObject;
import com.gclife.common.model.base.Users;
import com.gclife.common.model.config.TerminologyConfigEnum;
import com.gclife.common.model.config.TerminologyTypeEnum;
import com.gclife.common.model.feign.SyscodeRespFc;
import com.gclife.common.model.pojo.AppRequestHeads;
import com.gclife.common.service.impl.BaseBusinessServiceImpl;
import com.gclife.common.util.AssertUtils;
import com.gclife.common.util.DateUtils;
import com.gclife.common.util.JackSonUtils;
import com.gclife.platform.api.PlatformBaseInternationServiceApi;
import com.gclife.platform.api.PlatformBranchApi;
import com.gclife.platform.api.PlatformBranchBaseApi;
import com.gclife.platform.model.response.BranchResponse;
import com.gclife.policy.api.PolicyApi;
import com.gclife.policy.model.response.PolicyAttachmentResponse;
import com.gclife.policy.model.vo.PolicyAttachmentVo;
import com.gclife.thirdparty.api.ThirdpartyShortUrlApi;
import com.gclife.thirdparty.model.config.ThirdpartyTermEnum;
import com.gclife.thirdparty.model.request.ShortUrlRequest;
import com.gclife.thirdparty.model.response.ShortUrlResponse;
import org.omg.CORBA.PRIVATE_MEMBER;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.interceptor.TransactionAspectSupport;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.OutputStream;
import java.math.BigDecimal;
import java.net.URLEncoder;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

import static com.gclife.attachment.model.config.AttachmentPdfEnum.COVER_NOTE;
import static com.gclife.common.model.config.TerminologyConfigEnum.LANGUAGE.*;
import static com.gclife.common.model.config.TerminologyConfigEnum.LANGUAGE.ZH_CN;

/**
 * <AUTHOR>
 * create 18-4-23
 * description:
 */
@Service
public class ApplyBusinessServiceImpl extends BaseBusinessServiceImpl implements ApplyBusinessService {

    @Autowired
    private PlatformBaseInternationServiceApi platformBaseInternationServiceApi;
    @Autowired
    private PlatformBranchApi platformBranchApi;
    @Autowired
    private PlatformBranchBaseApi platformBranchBaseApi;
    @Autowired
    private ApplyExtDao applyExtDao;
    @Autowired
    private ApplyDetailTransData applyDetailTransData;
    @Autowired
    private AgentApi agentApi;
    @Autowired
    private ApplyQueryBaseService applyQueryBaseService;
    @Autowired
    private ApplyCoverageBaseService applyCoverageBaseService;
    @Autowired
    private ApplyBaseService applyBaseService;
    @Autowired
    private ApplyApplicantBaseService applyApplicantBaseService;
    @Autowired
    private ApplyInsuredBaseService applyInsuredBaseService;
    @Autowired
    private ApplyPaymentTransactionBaseService applyPaymentTransactionBaseService;
    @Autowired
    private ThirdpartyShortUrlApi thirdpartyShortUrlApi;
    @Autowired
    private AttachmentPDFDocumentApi attachmentPDFDocumentApi;
    @Autowired
    private PolicyApi policyApi;
    @Autowired
    private ApplyDataTransform applyDataTransform;
    @Autowired
    private ApplyUnderwriteBaseService applyUnderwriteBaseService;
    @Autowired
    private ApplyToPolicyTransData applyToPolicyTransData;
    @Autowired
    private ApplyPrintDao applyPrintDao;
    @Autowired
    private ApplyPrintInfoDao applyPrintInfoDao;

    /**
     * 查询投保单列表
     *
     * @param users            用户
     * @param applyListRequest 列表请求参数
     * @return ApplyQueryListResponse
     */
    @Override
    public ResultObject<BasePageResponse<ApplyQueryListResponse>> getApplyQueryList(Users users, ApplyListRequest applyListRequest) {
        ResultObject<BasePageResponse<ApplyQueryListResponse>> resultObject = new ResultObject<>();
        try {
            List<BranchResponse> branchResponses = platformBranchBaseApi.queryUserOptionBranchTreeLeaf(users.getUserId()).getData();
            if (!AssertUtils.isNotEmpty(branchResponses)) {
                return resultObject;
            }
            List<String> branchIds = branchResponses.stream().filter(branchBaseRespFc ->
                    AssertUtils.isNotEmpty(branchBaseRespFc.getBranchId())).map(BranchResponse::getBranchId).collect(Collectors.toList());

            List<ApplyListBo> applyListBos = applyExtDao.loadApplyListById(branchIds, applyListRequest);

            if (!AssertUtils.isNotEmpty(applyListBos)) {
                return resultObject;
            }
            List<ApplyQueryListResponse> applyQueryListResponses = this.transApplyList(applyListBos,null);
            //获取总页数
            Integer totalLine = AssertUtils.isNotNull(applyListBos) ? applyListBos.get(0).getTotalLine() : null;

            BasePageResponse<ApplyQueryListResponse> basePageResponse = BasePageResponse.getData(applyListRequest.getCurrentPage(), applyListRequest.getPageSize(), totalLine, applyQueryListResponses);

            resultObject.setData(basePageResponse);
        } catch (Exception e) {
            e.printStackTrace();
            if (e instanceof RequestException) {
                RequestException error = (RequestException) e;
                //错误设置
                resultObject.setIenum(error.getiEnum());
            } else {
                resultObject.setIenum(ApplyErrorConfigEnum.APPLY_QUERY_APPLY_ERROR);
            }
        }
        return resultObject;
    }

    /**
     * 投保单详情
     *
     * @param applyId 投保单id
     * @return ApplyDetailResponse
     */
    @Override
    public ResultObject<ApplyDetailResponse> loadApplyDetail(String applyId, AppRequestHeads appRequestHeads) {
        ResultObject<ApplyDetailResponse> resultObject = new ResultObject<>();
        try {
            //验证
            AssertUtils.isNotEmpty(this.getLogger(), applyId, ApplyErrorConfigEnum.APPLY_APPLY_ID_IS_NOT_NULL);
            //查询保单信息
            ApplyBo applyBo = applyExtDao.loadApplyBoById(applyId);
            AssertUtils.isNotNull(this.getLogger(), applyBo, ApplyErrorConfigEnum.APPLY_BUSINESS_APPLY_IS_NOT_FOUND_OBJECT);
            //数据转换
            ApplyDetailResponse applyDetailResponse = applyDetailTransData.transferApplyDetail(applyBo,appRequestHeads);
            //设置返回数据
            resultObject.setData(applyDetailResponse);
        } catch (Exception e) {
            e.printStackTrace();
            if (e instanceof RequestException) {
                RequestException error = (RequestException) e;
                //错误设置
                resultObject.setIenum(error.getiEnum());
            } else {
                resultObject.setIenum(ApplyErrorConfigEnum.APPLY_QUERY_APPLY_ERROR);
            }
        }
        return resultObject;
    }

    /**
     * 多条件查询投保单
     *
     * @param users                     用户
     * @param applyConditionListRequest 请求参数
     * @return ApplyQueryListResponse
     */
    @Override
    public ResultObject<BasePageResponse<ApplyQueryListResponse>> postApplyList(Users users, ApplyConditionListRequest applyConditionListRequest) {
        ResultObject<BasePageResponse<ApplyQueryListResponse>> resultObject = new ResultObject<>();
        try {
            List<BranchResponse> branchResponses = platformBranchApi.userManagerLeafBranchs().getData();
            if (!AssertUtils.isNotEmpty(branchResponses)) {
                return resultObject;
            }
            List<String> branchIds = branchResponses.stream().map(BranchResponse::getBranchId).distinct().collect(Collectors.toList());
            List<ApplyListBo> applyListBos = applyExtDao.loadApplyConditionListById(branchIds, applyConditionListRequest);
            if (!AssertUtils.isNotEmpty(applyListBos)) {
                return resultObject;
            }
            List<ApplyQueryListResponse> applyQueryListResponses = this.transApplyList(applyListBos,null);
            //获取总页数
            Integer totalLine = AssertUtils.isNotNull(applyListBos) ? applyListBos.get(0).getTotalLine() : null;

            BasePageResponse<ApplyQueryListResponse> basePageResponse = BasePageResponse.getData(applyConditionListRequest.getCurrentPage(), applyConditionListRequest.getPageSize(), totalLine, applyQueryListResponses);

            resultObject.setData(basePageResponse);
        } catch (Exception e) {
            e.printStackTrace();
            if (e instanceof RequestException) {
                RequestException error = (RequestException) e;
                //错误设置
                resultObject.setIenum(error.getiEnum());
            } else {
                resultObject.setIenum(ApplyErrorConfigEnum.APPLY_QUERY_APPLY_ERROR);
            }
        }
        return resultObject;
    }

    @Override
    public ResultObject<List<SyscodeRespFc>> getApplyStatusList() {
        ResultObject<List<SyscodeRespFc>> resultObject = new ResultObject<>();
        try {
            ResultObject<List<SyscodeRespFc>> syscodeRespFcs = platformBaseInternationServiceApi.getInternationalList(TerminologyTypeEnum.APPLY_STATUS.name());
            if (!AssertUtils.isResultObjectListDataNull(syscodeRespFcs)) {
                resultObject.setData(syscodeRespFcs.getData());
            }
        } catch (Exception e) {
            e.printStackTrace();
            if (e instanceof RequestException) {
                RequestException error = (RequestException) e;
                //错误设置
                resultObject.setIenum(error.getiEnum());
            } else {
                resultObject.setIenum(ApplyErrorConfigEnum.APPLY_FEIGN_CLIENT_PLATFORM_IS_NOT_NULL);
            }
        }
        return resultObject;
    }

    private List<ApplyQueryListResponse> transApplyList(List<ApplyListBo> applyListBos,String applyType) {
        List<String> allBranchIds = new ArrayList<>();
        applyListBos.forEach(applyListBo -> {
            allBranchIds.add(applyListBo.getSalesBranchId());
            allBranchIds.add(applyListBo.getManagerBranchId());
        });
        List<BranchResponse> branchResponses = platformBranchApi.branchsPost(allBranchIds.stream().distinct().collect(Collectors.toList())).getData();

        List<String> agentIds = applyListBos.stream().filter(applyListBo -> AssertUtils.isNotEmpty(applyListBo.getAgentId()))
                .map(ApplyListBo::getAgentId).collect(Collectors.toList());

        AgentApplyQueryRequest applyAgentRequest = new AgentApplyQueryRequest();
        applyAgentRequest.setListAgentId(agentIds);

        List<AgentResponse> agentResponses = agentApi.agentsGet(applyAgentRequest).getData();
        AssertUtils.isNotEmpty(this.getLogger(), agentResponses, ApplyErrorConfigEnum.APPLY_BUSINESS_AGENT_IS_NOT_FOUND_OBJECT);

        List<ApplyQueryListResponse> applyQueryListResponses = new ArrayList<>();
        //转换时间
        applyListBos.forEach(applyImageListBo -> {
            ApplyQueryListResponse applyQueryListResponse = (ApplyQueryListResponse) this.converterObject(applyImageListBo, ApplyQueryListResponse.class);
            //投保日期换成提交核保日期
            if (AssertUtils.isNotEmpty(applyImageListBo.getApplyId())) {
                ApplyPo applyPo = applyBaseService.queryApplyPo(applyImageListBo.getApplyId());
                if (AssertUtils.isNotNull(applyPo)) {
                    applyQueryListResponse.setCommitApplyDate(applyPo.getAppSubmitUnderwritingDate());
                }
            }
            if (AssertUtils.isNotEmpty(applyType)){
                if (ApplyTermEnum.APPLY_TYPE.LIFE_INSURANCE_PERSONAL.name().equals(applyType)){
                    List<ApplyCoveragePo> applyCoveragePos = applyCoverageBaseService.getApplyCoverageList(applyImageListBo.getApplyId());
                    if (AssertUtils.isNotNull(applyCoveragePos)){
                        applyQueryListResponse.setCoveragePeriod(applyCoveragePos.get(0).getCoveragePeriod());
                        applyQueryListResponse.setCoveragePeriodUnit(applyCoveragePos.get(0).getCoveragePeriodUnit());
                    }
                }
            }
            if (AssertUtils.isNotNull(applyImageListBo.getEffectiveDate())) {
                applyQueryListResponse.setEffectiveDate(DateUtils.timeStrToString(applyImageListBo.getEffectiveDate(), DateUtils.FORMATE6));
            }
            if (AssertUtils.isNotNull(applyImageListBo.getApplyDate())) {
                applyQueryListResponse.setApplyDate(DateUtils.timeStrToString(applyImageListBo.getApplyDate(), DateUtils.FORMATE3));
                applyQueryListResponse.setApplyDateFormat(DateUtils.timeStrToString(applyImageListBo.getApplyDate(), DateUtils.FORMATE6));
            }

            agentResponses.stream().filter(agentResponse -> agentResponse.getAgentId().equals(applyImageListBo.getAgentId())).findFirst().ifPresent((agent) -> {
                applyQueryListResponse.setAgentCode(agent.getAgentCode());
                applyQueryListResponse.setAgentName(agent.getAgentName());
                applyQueryListResponse.setAgentIdNo(agent.getIdNo());
                applyQueryListResponse.setAgentMobile(agent.getMobile());
                applyQueryListResponse.setAgentNameByCode(agent.getAgentName() + "/" + agent.getAgentCode());
            });
            //销售机构
            if (AssertUtils.isNotEmpty(branchResponses)) {
                branchResponses.forEach(branchResponse -> {
                    if (branchResponse.getBranchCode().equals(applyQueryListResponse.getSalesBranchId())) {
                        applyQueryListResponse.setSalesBranchName(branchResponse.getBranchName());
                    }
                    if (branchResponse.getBranchCode().equals(applyQueryListResponse.getManagerBranchId())) {
                        applyQueryListResponse.setManagerBranchName(branchResponse.getBranchName());
                    }
                });
                branchResponses.stream().filter(branchResponse -> branchResponse.getBranchCode().equals(applyQueryListResponse.getSalesBranchId())).findFirst().ifPresent(saleBranch -> {
                });
            }
            applyQueryListResponse.setSalesBranchByChanelType(applyQueryListResponse.getSalesBranchName() + "/" + applyQueryListResponse.getChannelTypeName());

            applyQueryListResponses.add(applyQueryListResponse);
        });
        return applyQueryListResponses;
    }


    @Override
    public ResultObject<BasePageResponse<ApplyQueryListResponse>> loadAgentApplyList(Users currentLoginUsers, ApplyListRequest applyListRequest) {
        ResultObject<BasePageResponse<ApplyQueryListResponse>> resultObject = new ResultObject<>();
        try {

            List<ApplyListBo> applyListBos = applyQueryBaseService.loadApplyListByUserId(applyListRequest.getAgentId(), applyListRequest, applyListRequest.getApplyType(), null);
            if (!AssertUtils.isNotEmpty(applyListBos)) {
                return resultObject;
            }
            List<ApplyQueryListResponse> applyQueryListResponses = this.transApplyList(applyListBos,applyListRequest.getApplyType());
            //获取总页数
            Integer totalLine = AssertUtils.isNotNull(applyListBos) ? applyListBos.get(0).getTotalLine() : null;

            BasePageResponse<ApplyQueryListResponse> basePageResponse = BasePageResponse.getData(applyListRequest.getCurrentPage(), applyListRequest.getPageSize(), totalLine, applyQueryListResponses);

            resultObject.setData(basePageResponse);
        } catch (Exception e) {
            e.printStackTrace();
            if (e instanceof RequestException) {
                RequestException error = (RequestException) e;
                //错误设置
                resultObject.setIenum(error.getiEnum());
            } else {
                resultObject.setIenum(ApplyErrorConfigEnum.APPLY_QUERY_APPLY_ERROR);
            }
        }
        return resultObject;
    }

    /**
     * 查询健康告知详情(核心投保单详情调用)
     *
     * @param applyId  投保单id
     * @param language 语言
     * @return
     */
    @Override
    public ResultObject<List<ApplyQuestionnaireResponse>> queryApplyQuestionnaireDetailGet(String applyId, String language) {
        ResultObject<List<ApplyQuestionnaireResponse>> resultObject = new ResultObject<>();
        language = AssertUtils.isNotEmpty(language) ? language : TerminologyConfigEnum.LANGUAGE.ZH_CN.name();
        //验证
        AssertUtils.isNotEmpty(this.getLogger(), applyId, ApplyErrorConfigEnum.APPLY_APPLY_ID_IS_NOT_NULL);
        //查询保单信息
        ApplyBo applyBo = applyExtDao.loadApplyBoById(applyId);
        AssertUtils.isNotNull(this.getLogger(), applyBo, ApplyErrorConfigEnum.APPLY_BUSINESS_APPLY_IS_NOT_FOUND_OBJECT);
        //数据转换
        List<ApplyQuestionnaireResponse> applyQuestionnaireResponses = applyDetailTransData.transApplyQuestionnaireDetail(applyBo, language);
        //设置返回数据
        resultObject.setData(applyQuestionnaireResponses);
        return resultObject;
    }

    /**
     * 查询暂保单详情(暂保条款)
     * @param applyId 投保ID
     * @return
     */
    @Override
    public ResultObject<TemporaryCoverResponse> queryTemporaryCover(String applyId) {
        AssertUtils.isNotEmpty(this.getLogger(), applyId, ApplyErrorConfigEnum.APPLY_APPLY_ID_IS_NOT_NULL);
        // 查询投保单
        ApplyPo applyPo = applyBaseService.queryApplyPo(applyId);
        AssertUtils.isNotNull(this.getLogger(), applyPo, ApplyErrorConfigEnum.APPLY_BUSINESS_APPLY_IS_NOT_FOUND_OBJECT);
        // 查询投保人
        ApplyApplicantBo applyApplicantBo = applyApplicantBaseService.queryApplyApplicant(applyId);
        AssertUtils.isNotNull(this.getLogger(), applyApplicantBo, ApplyErrorConfigEnum.APPLY_BUSINESS_APPLY_APPLICANT_IS_NOT_FOUND_OBJECT);
        // 查询被保人
        List<ApplyInsuredPo> applyInsuredPos = applyInsuredBaseService.listApplyInsuredPo(applyId);
        AssertUtils.isNotEmpty(this.getLogger(), applyInsuredPos, ApplyErrorConfigEnum.APPLY_BUSINESS_APPLY_INSURED_IS_NOT_FOUND_OBJECT);

        TemporaryCoverResponse temporaryCoverResponse = new TemporaryCoverResponse();
        temporaryCoverResponse.setApplyId(applyId);
        temporaryCoverResponse.setApplyNo(applyPo.getApplyNo());
        temporaryCoverResponse.setApplicantName(applyApplicantBo.getName());
        temporaryCoverResponse.setInsuredName(applyInsuredPos.get(0).getName());
        // 查询签发日期
        ApplyPaymentTransactionBo applyPaymentTransactionBo =
                applyPaymentTransactionBaseService.queryApplyPaymentTransactionByType(applyId, ApplyTermEnum.CHARGE_TYPE.PREPAID_PREMIUM.name());
        if (AssertUtils.isNotNull(applyPaymentTransactionBo) &&
                ApplyTermEnum.PAYMENT_STATUS.PAYMENT_SUCCESS.name().equals(applyPaymentTransactionBo.getPaymentStatus())) {
            temporaryCoverResponse.setGainedDate(applyPaymentTransactionBo.getActualPayDate());
        }
        // 获取暂保条款链接
        ShortUrlRequest shortUrlRequest = new ShortUrlRequest();
        shortUrlRequest.setShortType(ThirdpartyTermEnum.SHORT_TYPE.TEMPORARY_COVER.name());
        shortUrlRequest.setBusinessId(applyId);
        getLogger().info("投保单ID：{}，获取暂保条款链接请求参数：{}", applyId, JSON.toJSONString(shortUrlRequest));
        ResultObject<ShortUrlResponse> urlResultObject = thirdpartyShortUrlApi.queryShortUrl(shortUrlRequest);
        getLogger().info("投保单ID：{}，获取暂保条款链接返回结果：{}", applyId, JSON.toJSONString(urlResultObject));
        if (!AssertUtils.isResultObjectDataNull(urlResultObject)) {
            temporaryCoverResponse.setTemporaryCoverUrl(urlResultObject.getData().getFullUrl());
        }
        ResultObject<TemporaryCoverResponse> resultObject = new ResultObject<>();
        resultObject.setData(temporaryCoverResponse);
        return resultObject;
    }

    /**
     * 暂保单打印
     * @param applyId 投保ID
     * @param language 语言
     * @return
     */
    @Override
    public ResultObject printTemporaryCover(String applyId, String language, HttpServletResponse response) throws IOException {
        AssertUtils.isNotEmpty(this.getLogger(), applyId, ApplyErrorConfigEnum.APPLY_APPLY_ID_IS_NOT_NULL);
        if (!AssertUtils.isNotEmpty(language)) {
            language = TerminologyConfigEnum.LANGUAGE.KM_KH.name();
        }
        // 查询投保单
        ApplyPo applyPo = applyBaseService.queryApplyPo(applyId);
        AssertUtils.isNotNull(this.getLogger(), applyPo, ApplyErrorConfigEnum.APPLY_BUSINESS_APPLY_IS_NOT_FOUND_OBJECT);
        String attachmentId;
        // 先查询是否已生成PDF
        ResultObject<PolicyAttachmentResponse> policyAttachmentObject =
                policyApi.getAttachmentByType(applyId, ApplyTermEnum.ATTACHMENT_TYPE_FLAG.TEMPORARY_COVER.name(), language);
        if (!AssertUtils.isResultObjectDataNull(policyAttachmentObject)) {
            attachmentId = policyAttachmentObject.getData().getAttachmentId();
        } else {
            // 查询投保人
            ApplyApplicantBo applyApplicantBo = applyApplicantBaseService.queryApplyApplicant(applyId);
            AssertUtils.isNotNull(this.getLogger(), applyApplicantBo, ApplyErrorConfigEnum.APPLY_BUSINESS_APPLY_APPLICANT_IS_NOT_FOUND_OBJECT);
            // 查询被保人
            List<ApplyInsuredPo> applyInsuredPos = applyInsuredBaseService.listApplyInsuredPo(applyId);
            AssertUtils.isNotEmpty(this.getLogger(), applyInsuredPos, ApplyErrorConfigEnum.APPLY_BUSINESS_APPLY_INSURED_IS_NOT_FOUND_OBJECT);

            CoverNotePrintBo coverNotePrintBo = new CoverNotePrintBo();
            coverNotePrintBo.setApplyNo(applyPo.getApplyNo());
            coverNotePrintBo.setApplicantName(applyApplicantBo.getName());
            coverNotePrintBo.setInsuredName(applyInsuredPos.get(0).getName());
            // 查询签发日期
            ApplyPaymentTransactionBo applyPaymentTransactionBo =
                    applyPaymentTransactionBaseService.queryApplyPaymentTransactionByType(applyId, ApplyTermEnum.CHARGE_TYPE.PREPAID_PREMIUM.name());
            if (AssertUtils.isNotNull(applyPaymentTransactionBo) &&
                    ApplyTermEnum.PAYMENT_STATUS.PAYMENT_SUCCESS.name().equals(applyPaymentTransactionBo.getPaymentStatus())) {
                coverNotePrintBo.setGainedDate(applyPaymentTransactionBo.getActualPayDate());
            }

            ElectronicPolicyGeneratorRequest generatorRequest = new ElectronicPolicyGeneratorRequest();
            generatorRequest.setPdfType(COVER_NOTE.name());
            generatorRequest.setLanguage(language);
            generatorRequest.setContent(JackSonUtils.toJson(coverNotePrintBo, language));
            getLogger().info("暂保单打印|generatorRequest:::::" + JSON.toJSONString(generatorRequest));
            // 生成PDF
            ResultObject<List<AttachmentResponse>> attachmentObject = attachmentPDFDocumentApi.electronicPolicyGenerator(generatorRequest);
            getLogger().info("暂保单打印|attachmentObject:::::" + JSON.toJSONString(attachmentObject));
            AssertUtils.isResultObjectError(this.getLogger(), attachmentObject);
            List<AttachmentResponse> attachmentResponses = attachmentObject.getData();
            // 下载PDF附件
            AttachmentResponse attachmentResponse = attachmentResponses.get(0);
            attachmentId = attachmentResponse.getMediaId();
            try {
                // 保存附件
                PolicyAttachmentVo policyAttachmentVo = new PolicyAttachmentVo();
                policyAttachmentVo.setPolicyId(applyId);
                policyAttachmentVo.setAttachmentId(attachmentId);
                policyAttachmentVo.setAttachmentTypeCode(ApplyTermEnum.ATTACHMENT_TYPE_FLAG.TEMPORARY_COVER.name());
                policyAttachmentVo.setLanguage(language);
                policyApi.saveAttachment(policyAttachmentVo);
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
        ResultObject<AttachmentByteResponse> attachmentByteResponseFcResultObject = attachmentPDFDocumentApi.electronicPolicyDownload(attachmentId);
        AssertUtils.isResultObjectError(this.getLogger(), attachmentByteResponseFcResultObject);

        byte[] bytesFile = attachmentByteResponseFcResultObject.getData().getFileByte();
        response.setHeader("Content-Type", "application/pdf");
        response.addHeader("Content-Disposition", "inline;filename=" + URLEncoder.encode(applyPo.getApplyNo() + ".pdf", "UTF-8"));
        response.setHeader("Access-Control-Allow-Origin", "*");
        response.setHeader("Access-Control-Allow-Methods", "POST,GET");
        response.setHeader("Access-Control-Allow-Credentials", "true");
        OutputStream outputStream = response.getOutputStream();
        outputStream.write(bytesFile);
        outputStream.close();

        return ResultObject.success();
    }

    @Override
    public ResultObject<List<ApplyListBo>> getSystemWarningApplyQueryList() {
        ResultObject<List<ApplyListBo>> resultObject = new ResultObject<>();
        List<ApplyListBo> applyListBos = applyExtDao.loadSystemWarningApplyList();
//        this.getLogger().info("系统预警查询已承保的投保单列表:::" + JSON.toJSONString(applyListBos));
        this.getLogger().info("系统预警查询已承保的投保单列表::: " + applyListBos.size());
        resultObject.setData(applyListBos);
        return resultObject;
    }

    @Override
    public ResultObject<List<ApplyListBo>> getSystemWarningApplyPaymentList() {
        ResultObject<List<ApplyListBo>> resultObject = new ResultObject<>();
        List<ApplyListBo> applyListBos2 = new ArrayList<>();
        List<ApplyListBo> applyListBos3 = new ArrayList<>();
        //个险智能核保筛选条件
        //List<String> applyTypePersonList = new ArrayList<>();
        //applyTypePersonList.add(ApplyTermEnum.APPLY_TYPE.LIFE_INSURANCE_PERSONAL.name());

        //团险智能核保筛选条件
        //List<String> applyTypeGroupList = new ArrayList<>();
        //applyTypeGroupList.add(ApplyTermEnum.APPLY_TYPE.LIFE_INSURANCE_GROUP.name());

        //个团险筛选条件
        List<String> applyStatusList = new ArrayList<>();
        applyStatusList.add(ApplyTermEnum.APPLY_STATUS_FLAG.APPLY_STATUS_APPROVE_SUCCESS.name());
       /* applyGroupStatusList.add(ApplyTermEnum.APPLY_STATUS_FLAG.APPLY_STATUS_UNDERWRITE_PASS.name());
        applyGroupStatusList.add(ApplyTermEnum.APPLY_STATUS_FLAG.APPROVED_HC.name());*/

        /*List<String> applyPersonStatusList = new ArrayList<>();
        applyPersonStatusList.add(ApplyTermEnum.APPLY_STATUS_FLAG.APPLY_STATUS_UNDERWRITE_PASS.name());
        applyPersonStatusList.add(ApplyTermEnum.APPLY_STATUS_FLAG.APPLY_STATUS_TEMPORARY_COVER_EFFECTIVE.name());
        applyPersonStatusList.add(ApplyTermEnum.APPLY_STATUS_FLAG.APPLY_STATUS_INITIAL_COMPLETE.name());
        applyPersonStatusList.add(ApplyTermEnum.APPLY_STATUS_FLAG.APPLY_STATUS_INPUT_REVIEW.name());
        applyPersonStatusList.add(ApplyTermEnum.APPLY_STATUS_FLAG.APPLY_STATUS_INPUT_REVIEW_COMPLETE.name());
        applyPersonStatusList.add(ApplyTermEnum.APPLY_STATUS_FLAG.APPLY_STATUS_UNDERWRITE_ARTIFICIAL.name());
        applyPersonStatusList.add(ApplyTermEnum.APPLY_STATUS_FLAG.APPLY_STATUS_PAID_PENDING_ON_UW.name());*/

        //查询个团险投保单数据
        List<ApplyListBo> applyListBos = applyExtDao.loadSystemWarningApplyPaymentList(applyStatusList,null);
//        this.getLogger().info("系统预警查询个险的投保单列表:::" + JSON.toJSONString(applyListBos));
        this.getLogger().info("系统预警查询个险的投保单列表::: " + applyListBos.size());

        //筛除掉投保来源为ACCEPT_INPUT的并且状态不是人工核保通过的
       /* applyListBos.removeIf(applyListBo -> ApplyTermEnum.APPLY_SOURCE.ACCEPT_INPUT.name().equals(applyListBo.getApplySource())
                    && !(ApplyTermEnum.APPLY_STATUS_FLAG.APPLY_STATUS_UNDERWRITE_PASS.name().equals(applyListBo.getApplyStatus())));*/



        //过滤出发起支付的个险智能核保未通过投保单数据，再去查找查询投保自核问题表，如果没有数据表明为智能核保功能开发前的旧数据单，不做支付查询操作
       /* applyListBos.forEach(applyListBo -> {
            String applyId = applyListBo.getApplyId();
            String applyType = applyListBo.getApplyType();
            if (AssertUtils.isNotNull(applyId) && AssertUtils.isNotNull(applyType)) {
                boolean autoUWFlag = applyDataTransform.doWhetherThroughAutoUW(applyId, applyType);
                if (!autoUWFlag) {
                    applyListBos2.add(applyListBo);
                }
            }
        });*/

        //过滤未智能核保通过中，智能核保功能开发前的旧数据单
       /* applyListBos2.forEach(applyListBo -> {
            String applyId = applyListBo.getApplyId();
            if (AssertUtils.isNotEmpty(applyId)) {
                List<ApplyUnderwriteProblemPo> applyUnderwriteProblemPos = applyUnderwriteBaseService.queryApplyUnderwriteProblemPo(applyId);

                if (!AssertUtils.isNotEmpty(applyUnderwriteProblemPos)) {
                    applyListBos3.add(applyListBo);
                }
            }

        });*/

        //applyListBos.removeAll(applyListBos3);

        //查询团险投保单数据
       /* List<ApplyListBo> applyListBos1 = applyExtDao.loadSystemWarningApplyPaymentList(applyGroupStatusList,applyTypeGroupList);
        this.getLogger().info("系统预警查询团险的投保单列表:::" + JSON.toJSONString(applyListBos1));*/

        /*applyListBos.addAll(applyListBos1);
        this.getLogger().info("系统预警查询最终发起支付的投保单列表:::" + JSON.toJSONString(applyListBos));*/

        /*applyListBos2.removeIf(applyListBo -> ApplyTermEnum.APPLY_STATUS_FLAG.APPLY_STATUS_ABANDONED.name().equals(applyListBo.getApplyStatus()));
        this.getLogger().info("系统预警查询过滤最终发起支付的投保单列表:::" + JSON.toJSONString(applyListBos2));*/
        resultObject.setData(applyListBos);
        return resultObject;
    }

    @Override
    public ResultObject<ApplyListBo> getSystemWarningApplyPayment(String applyId) {
        ResultObject<ApplyListBo> resultObject = new ResultObject<>();
        if (!AssertUtils.isNotEmpty(applyId)) {
            return null;
        }
        ApplyListBo applyListBo = applyExtDao.loadSystemWarningApplyPayment(applyId);
        resultObject.setData(applyListBo);
        return resultObject;
    }

    @Override
    public ResultObject printApply(String printInfoId, String language, HttpServletResponse response) throws IOException {
        AssertUtils.isNotEmpty(this.getLogger(), printInfoId, ApplyErrorConfigEnum.APPLY_PRINT_ID_IS_NOT_NULL);
        if (!AssertUtils.isNotEmpty(language)) {
            language = TerminologyConfigEnum.LANGUAGE.KM_KH.name();
        }
        //根据打印id查询投保单id
        ApplyPrintInfoPo applyPrintInfoPo = applyPrintInfoDao.fetchOneByPrintInfoId(printInfoId);

        // 查询投保单
        ApplyBo applyBo = applyBaseService.queryApply(applyPrintInfoPo.getApplyId());
        AssertUtils.isNotNull(this.getLogger(), applyBo, ApplyErrorConfigEnum.APPLY_BUSINESS_APPLY_IS_NOT_FOUND_OBJECT);
        String attachmentId;
        // 先查询是否已生成PDF
        ResultObject<PolicyAttachmentResponse> policyAttachmentObject =
                policyApi.getAttachmentByType(applyPrintInfoPo.getApplyId(), ApplyTermEnum.ATTACHMENT_TYPE_FLAG.APPLY_BOOK.name(), language);
        if (!AssertUtils.isResultObjectDataNull(policyAttachmentObject)) {
            attachmentId = policyAttachmentObject.getData().getAttachmentId();
        }else {
            //生成投保单PDF附件
            List<AttachmentResponse> attachmentResponses = applyToPolicyTransData.attachmentPdfGenerate(applyBo, language);
            getLogger().info("投保单打印|attachmentResponses:::::" + JSON.toJSONString(attachmentResponses));
            AssertUtils.isNotEmpty(this.getLogger(),attachmentResponses,ApplyErrorConfigEnum.APPLY_GENERATE_APPLY_PDF_ERROR);

            // 下载PDF附件
            AttachmentResponse attachmentResponse = attachmentResponses.get(0);
            attachmentId = attachmentResponse.getMediaId();
        }

        ResultObject<AttachmentByteResponse> attachmentByteResponseFcResultObject = attachmentPDFDocumentApi.electronicPolicyDownload(attachmentId);
        AssertUtils.isResultObjectError(this.getLogger(), attachmentByteResponseFcResultObject);

        byte[] bytesFile = attachmentByteResponseFcResultObject.getData().getFileByte();
        response.setHeader("Content-Type", "application/pdf");
        response.addHeader("Content-Disposition", "inline;filename=" + URLEncoder.encode(applyBo.getApplyNo() + ".pdf", "UTF-8"));
        response.setHeader("Access-Control-Allow-Origin", "*");
        response.setHeader("Access-Control-Allow-Methods", "POST,GET");
        response.setHeader("Access-Control-Allow-Credentials", "true");
        OutputStream outputStream = response.getOutputStream();
        outputStream.write(bytesFile);
        outputStream.close();

        return ResultObject.success();
    }

    @Override
    public ResultObject<BasePageResponse<ApplyPrintResponse>> getPrintApplyList(Users currentLoginUsers, ApplyConditionPrintRequest applyConditionPrintRequest) {
        ResultObject<BasePageResponse<ApplyPrintResponse>> resultObject = new ResultObject<>();
        try {
            applyConditionPrintRequest.setApplyType("LIFE_INSURANCE_PERSONAL");
            BasePageResponse<ApplyPrintResponse> printApply = getPrintApply(null, currentLoginUsers.getUserId(), applyConditionPrintRequest);
            resultObject.setData(printApply);
        } catch (Exception e) {
            if (e instanceof RequestException) {
                RequestException error = (RequestException) e;
                //错误设置
                resultObject.setIenum(error.getiEnum());
            } else {
                resultObject.setIenum(ApplyErrorConfigEnum.APPLY_PRINT_INFO_QUERY_ERROR);
            }
            e.printStackTrace();
        }
        return resultObject;
    }

    @Override
    public ResultObject applyPrintFileComplete(String printInfoId, String language) {
        ResultObject<BaseResponse> resultObject = new ResultObject<>();
        try {
            // 参数校验
            AssertUtils.isNotEmpty(this.getLogger(), printInfoId,  ApplyErrorConfigEnum.APPLY_PRINT_ID_IS_NOT_NULL);
            AssertUtils.isNotEmpty(this.getLogger(), language, ApplyErrorConfigEnum.APPLY_PARAMETER_LANGUAGE_IS_NOT_NULL);
            if (!(KM_KH.name().equals(language)
                    || EN_US.name().equals(language)
                    || ZH_CN.name().equals(language))) {
                // 语言格式错误
                throw new RequestException(ApplyErrorConfigEnum.APPLY_PARAMETER_LANGUAGE_FORMAT_INVALID);
            }
            // 查询数据
            ApplyPrintInfoPo applyPrintInfoPo = applyPrintInfoDao.findById(printInfoId);
            // 数据校验
            AssertUtils.isNotNull(this.getLogger(), applyPrintInfoPo, ApplyErrorConfigEnum.APPLY_BUSINESS_APPLY_PRINT_INFO_IS_NOT_FOUND_OBJECT);
            if (KM_KH.name().equals(language)) {
                applyPrintInfoPo.setCambodianFlag(ApplyTermEnum.YES_NO.YES.name());
            } else if (EN_US.name().equals(language)) {
                applyPrintInfoPo.setEnglishFlag(ApplyTermEnum.YES_NO.YES.name());
            } else if (ZH_CN.name().equals(language)) {
                applyPrintInfoPo.setChineseFlag(ApplyTermEnum.YES_NO.YES.name());
            }
            // 保存数据
            applyBaseService.saveApplyPrintInfo(applyPrintInfoPo);

        } catch (Exception e) {
            e.printStackTrace();
            if (e instanceof RequestException) {
                RequestException error = (RequestException) e;
                //错误设置
                resultObject.setIenum(error.getiEnum());
            } else {
                resultObject.setIenum(ApplyErrorConfigEnum.APPLY_PRINT_INFO_SAVE_ERROR);
            }
        }
        return resultObject;
    }

    @Override
    public ResultObject applyPrintFinish(Users currentLoginUsers,String printInfoId) {
        ResultObject<BaseResponse> resultObject = new ResultObject<>();
        try {
            // 参数校验
            AssertUtils.isNotEmpty(this.getLogger(), printInfoId, ApplyErrorConfigEnum.APPLY_PRINT_ID_IS_NOT_NULL);
            // 查询打印数据
            ApplyPrintInfoPo applyPrintInfoPo = applyPrintInfoDao.findById(printInfoId);
            // 数据校验
            AssertUtils.isNotNull(this.getLogger(), applyPrintInfoPo, ApplyErrorConfigEnum.APPLY_BUSINESS_APPLY_PRINT_INFO_IS_NOT_FOUND_OBJECT);

            this.getLogger().info("投保单打印信息：" + JSON.toJSONString(applyPrintInfoPo));

            // 更新保单打印表
            applyPrintInfoPo.setPrintDate(DateUtils.getCurrentTime());
            applyPrintInfoPo.setPrintstatus(ApplyTermEnum.YES_NO.YES.name());
            applyPrintInfoDao.update(applyPrintInfoPo);

        } catch (Exception e) {
            e.printStackTrace();
            //事务回滚
            TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
            if (e instanceof RequestException) {
                RequestException error = (RequestException) e;
                //错误设置
                resultObject.setIenum(error.getiEnum());
            } else {
                resultObject.setIenum(ApplyErrorConfigEnum.APPLY_PRINT_INFO_SAVE_ERROR);
            }
        }
        return resultObject;
    }

    @Override
    public ResultObject<CalculationBmiAndAgeResponse> calculationBmiAndAge(CalculationBmiAndAgeRequest calculationBmiAndAgeRequest) {
        ResultObject<CalculationBmiAndAgeResponse> resultObject = new ResultObject<>();
        CalculationBmiAndAgeResponse calculationBmiAndAgeResponse = new CalculationBmiAndAgeResponse();

        //投保單複核展示bmi，有值直接展示，沒值根據身高體重算，在重新設置，反之，直接返回空
        if (AssertUtils.isNotEmpty(calculationBmiAndAgeRequest.getStature())
                && AssertUtils.isNotEmpty(calculationBmiAndAgeRequest.getAvoirdupois())) {
            BigDecimal powStature = new BigDecimal(calculationBmiAndAgeRequest.getStature()).divide(new BigDecimal("100")).pow(2);
            BigDecimal bmi = new BigDecimal(calculationBmiAndAgeRequest.getAvoirdupois()).divide(powStature, 2, BigDecimal.ROUND_HALF_UP);
            calculationBmiAndAgeResponse.setBmi(bmi + "");
        }

        //設置投保年齡
        if (AssertUtils.isNotNull(calculationBmiAndAgeRequest.getBirthday()) && AssertUtils.isNotNull(calculationBmiAndAgeRequest.getApplyDate())) {
            Integer applyAge = getAgeYear(calculationBmiAndAgeRequest.getBirthday(), calculationBmiAndAgeRequest.getApplyDate());
            calculationBmiAndAgeResponse.setApplyAge(applyAge + "");
        }
        resultObject.setData(calculationBmiAndAgeResponse);
        return resultObject;
    }

    private Integer getAgeYear(Long insuredBirthday, Long approveDate) {
        try {
            if (AssertUtils.isNotNull(insuredBirthday) && AssertUtils.isNotNull(approveDate)) {
                return DateUtils.getAgeYear(new Date(insuredBirthday), new Date(approveDate));
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }

    public BasePageResponse<ApplyPrintResponse> getPrintApply(List<String> listPolicyIds, String userId, ApplyConditionPrintRequest applyConditionPrintRequest) {
        //获取当前用户子机构  id
        ResultObject<List<BranchResponse>> listResultObject = platformBranchApi.userManagerLeafBranchs();
        List<BranchResponse> branchRespFcList = listResultObject.getData();
        List<ApplyPrintResponse> applyPrintResponse = new ArrayList<>();
        if (!AssertUtils.isNotEmpty(branchRespFcList)) {
            return null;
        }
        List<String> branchIdList = branchRespFcList.stream().map(BranchResponse::getBranchId).distinct().collect(Collectors.toList());
        applyConditionPrintRequest.setApplyType("LIFE_INSURANCE_PERSONAL");
        List<ApplyConditionPrintBo> applyConditionPrintList = applyPrintDao.getApplyConditionPrintList(listPolicyIds, applyConditionPrintRequest, branchIdList);
        if (AssertUtils.isNotEmpty(applyConditionPrintList)) {
            //获取销售机构ID
            List<String> branchIds = applyConditionPrintList.stream()
                    .filter(applyConditionPrintBo -> AssertUtils.isNotEmpty(applyConditionPrintBo.getSalesBranchId()))
                    .map(ApplyConditionPrintBo::getSalesBranchId).distinct().collect(Collectors.toList());
            // 获取管理机构ID
            branchIds.addAll(applyConditionPrintList.stream()
                    .filter(applyConditionPrint -> AssertUtils.isNotEmpty(applyConditionPrint.getSalesBranchId()))
                    .map(ApplyConditionPrintBo::getManagerBranchId).distinct().collect(Collectors.toList()));
            //机构国际化
            List<BranchResponse> branchRespFcs = null;
            if (AssertUtils.isNotEmpty(branchIds)) {
                branchRespFcs = platformBranchApi.branchsPost(branchIds).getData();
            }
            List<String> agentIdList = applyConditionPrintList.stream()
                    .filter(applyConditionPrint -> AssertUtils.isNotEmpty(applyConditionPrint.getAgentId()))
                    .map(ApplyConditionPrintBo::getAgentId).distinct().collect(Collectors.toList());
            List<AgentResponse> applyAgentRespFcs = null;
            if (AssertUtils.isNotEmpty(agentIdList)) {
                AgentApplyQueryRequest agentApplyQueryReqFc = new AgentApplyQueryRequest();
                agentApplyQueryReqFc.setListAgentId(agentIdList);
                applyAgentRespFcs = agentApi.agentsGet(agentApplyQueryReqFc).getData();
            }

            for (ApplyConditionPrintBo applyConditionPrint : applyConditionPrintList) {
                //格式化时间
                if (AssertUtils.isNotNull(applyConditionPrint.getApproveDate())) {
                    applyConditionPrint.setApproveDate(DateUtils.timeStrToString(applyConditionPrint.getApproveDate(), DateUtils.FORMATE6));
                }
                ApplyPrintResponse applyPrintListResponse = (ApplyPrintResponse) this.converterObject(applyConditionPrint, ApplyPrintResponse.class);

                if (AssertUtils.isNotEmpty(branchRespFcs) && (AssertUtils.isNotEmpty(applyPrintListResponse.getSalesBranchId())
                        || AssertUtils.isNotEmpty(applyPrintListResponse.getManagerBranchId()))) {
                    branchRespFcs.forEach(branchRespFc -> {
                        if (AssertUtils.isNotEmpty(applyPrintListResponse.getManagerBranchId()) && applyPrintListResponse.getManagerBranchId().equals(branchRespFc.getBranchId())) {
                            applyPrintListResponse.setManagerBranchName(branchRespFc.getBranchShortname());
                        }
                        if (AssertUtils.isNotEmpty(applyPrintListResponse.getSalesBranchId()) && applyPrintListResponse.getSalesBranchId().equals(branchRespFc.getBranchId())) {
                            applyPrintListResponse.setSalesBranchName(branchRespFc.getBranchShortname());
                        }
                    });
                }
                //  设置代理人信息
                if (AssertUtils.isNotEmpty(applyAgentRespFcs) && AssertUtils.isNotEmpty(applyPrintListResponse.getAgentId())) {
                    applyAgentRespFcs.stream().filter(agentResponse -> agentResponse.getAgentId().equals(applyPrintListResponse.getAgentId())).findFirst().ifPresent(agentResponse -> {
                        applyPrintListResponse.setAgentName(agentResponse.getAgentName());
                        applyPrintListResponse.setAgentCode(agentResponse.getAgentCode());
                        applyPrintListResponse.setMobile(agentResponse.getMobile());
                    });
                }
                applyPrintResponse.add(applyPrintListResponse);
            }
        }
        //获取总页数
        Integer totalLine = AssertUtils.isNotNull(applyConditionPrintList) ? applyConditionPrintList.get(0).getTotalLine() : null;

        BasePageResponse basePageResponse = BasePageResponse.getData(applyConditionPrintRequest.getCurrentPage(), applyConditionPrintRequest.getPageSize(), totalLine, applyPrintResponse);
        return basePageResponse;
    }
}
