package com.gclife.apply.service.data.impl;

import com.gclife.apply.core.jooq.tables.pojos.ApplyOperationPo;
import com.gclife.apply.dao.ApplyExtDao;
import com.gclife.apply.dao.app.ApplyPlanBusinessDao;
import com.gclife.apply.model.bo.*;
import com.gclife.apply.model.config.ApplyErrorConfigEnum;
import com.gclife.apply.model.config.ApplyTermEnum;
import com.gclife.apply.model.config.ModelConstantEnum;
import com.gclife.apply.model.feign.ApplyAttachmentResp;
import com.gclife.apply.model.feign.ProductCertifyRespFc;
import com.gclife.apply.model.request.ImageAttachmentRequest;
import com.gclife.apply.model.request.app.AppAttachmentRequest;
import com.gclife.apply.model.response.AppPageJumpResponse;
import com.gclife.apply.model.response.app.AppAttachmentResponse;
import com.gclife.apply.service.ApplyApplicantBaseService;
import com.gclife.apply.service.ApplyBaseService;
import com.gclife.apply.service.business.app.ApplyPlanBusinessService;
import com.gclife.apply.service.data.ApplyBoService;
import com.gclife.common.model.ResultObject;
import com.gclife.common.model.config.TerminologyConfigEnum;
import com.gclife.common.service.impl.BaseBusinessServiceImpl;
import com.gclife.common.util.AssertUtils;
import com.gclife.product.api.ProductCertifyApi;
import com.gclife.product.api.ProductSalesApi;
import com.gclife.product.model.config.ProductTermEnum;
import com.gclife.product.model.response.insurnce.certify.CertifyAttachmentTypeResponse;
import com.gclife.product.model.response.insurnce.certify.ProductDisplayCertifyResponse;
import com.gclife.product.model.response.sales.ProductSalesDetailedResponse;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

/**
 * <AUTHOR>
 * create 17-11-29
 * description:
 */
@Service
public class ApplyPlanDataSaveService extends BaseBusinessServiceImpl {
    private static final Logger LOGGER = LoggerFactory.getLogger(ApplyDataSaveCommonService.class);

    @Autowired
    private ApplyBoService applyBoService;

    @Autowired
    private ApplyPlanBusinessDao applyPlanBusinessDao;

    @Autowired
    private ApplyExtDao applyExtDao;

    @Autowired
    private ApplyPlanBusinessService applyPlanBusinessService;


    @Autowired
    private ProductSalesApi productSalesApi;
    @Autowired
    private ProductCertifyApi productCertifyApi;
    @Autowired
    private ApplyBaseService applyBaseService;
    @Autowired
    private ApplyApplicantBaseService applyApplicantBaseService;

    /**
     * 人身保险产品风险提示书
     *
     * @param imageAttachmentRequest
     */
    public void saveRiskTipsData(ImageAttachmentRequest imageAttachmentRequest) {
        //删除原有RISK_TIPS
        List<ApplyAttachmentBo> applyAttachmentBos = applyPlanBusinessDao.getIdAttachment(imageAttachmentRequest.getApplyId(), ModelConstantEnum.APPLY_ATTACHMENT.RISK_TIPS.name());
        if (AssertUtils.isNotEmpty(applyAttachmentBos)) {
            applyBoService.deleteApplyAttachmentBoList(applyAttachmentBos);
        }
        //保存
        imageAttachmentRequest.getRiskTips().forEach(idAttachId -> {
            ApplyAttachmentBo applyAttachmentBo = new ApplyAttachmentBo();
            applyAttachmentBo.setAttachmentTypeCode(ModelConstantEnum.APPLY_ATTACHMENT.RISK_TIPS.name());
            applyAttachmentBo.setApplyId(imageAttachmentRequest.getApplyId());
            applyAttachmentBo.setAttachmentId(idAttachId.getAttachmentId());
            applyAttachmentBo.setAttachmentSeq(idAttachId.getAttachmentSeq());
            applyBoService.saveApplyAttachmentPo(applyAttachmentBo);
        });
    }

    /**
     * 人身保险产品投保提示书
     *
     * @param imageAttachmentRequest
     */
    public void saveApplyTipsData(ImageAttachmentRequest imageAttachmentRequest) {
        //删除原有APPLY_TIPS
        List<ApplyAttachmentBo> applyAttachmentBos = applyPlanBusinessDao.getIdAttachment(imageAttachmentRequest.getApplyId(), ModelConstantEnum.APPLY_ATTACHMENT.APPLY_TIPS.name());
        if (AssertUtils.isNotEmpty(applyAttachmentBos)) {
            applyBoService.deleteApplyAttachmentBoList(applyAttachmentBos);
        }
        //保存
        imageAttachmentRequest.getApplyTips().forEach(idAttachId -> {
            ApplyAttachmentBo applyAttachmentBo = new ApplyAttachmentBo();
            applyAttachmentBo.setAttachmentTypeCode(ModelConstantEnum.APPLY_ATTACHMENT.APPLY_TIPS.name());
            applyAttachmentBo.setApplyId(imageAttachmentRequest.getApplyId());
            applyAttachmentBo.setAttachmentId(idAttachId.getAttachmentId());
            applyAttachmentBo.setAttachmentSeq(idAttachId.getAttachmentSeq());
            applyBoService.saveApplyAttachmentPo(applyAttachmentBo);
        });
    }

    /**
     * 电子保单申请确认书
     *
     * @param imageAttachmentRequest
     */
    public void saveApplicationConfirmationData(ImageAttachmentRequest imageAttachmentRequest) {
        //删除原有POLICY_APPLICATION_CONFIRMATION
        List<ApplyAttachmentBo> applyAttachmentBos = applyPlanBusinessDao.getIdAttachment(imageAttachmentRequest.getApplyId(), ModelConstantEnum.APPLY_ATTACHMENT.POLICY_APPLICATION_CONFIRMATION.name());
        if (AssertUtils.isNotEmpty(applyAttachmentBos)) {
            applyBoService.deleteApplyAttachmentBoList(applyAttachmentBos);
        }
        //保存
        imageAttachmentRequest.getApplicationConfirmation().forEach(idAttachId -> {
            ApplyAttachmentBo applyAttachmentBo = new ApplyAttachmentBo();
            applyAttachmentBo.setAttachmentTypeCode(ModelConstantEnum.APPLY_ATTACHMENT.POLICY_APPLICATION_CONFIRMATION.name());
            applyAttachmentBo.setApplyId(imageAttachmentRequest.getApplyId());
            applyAttachmentBo.setAttachmentId(idAttachId.getAttachmentId());
            applyAttachmentBo.setAttachmentSeq(idAttachId.getAttachmentSeq());
            applyBoService.saveApplyAttachmentPo(applyAttachmentBo);
        });
    }

    public void saveImageAttachmentData(String applyId, List<ProductCertifyRespFc> certifyRespFcs) {
        certifyRespFcs.forEach(productCertifyRespFc -> {
            List<ApplyAttachmentBo> applyAttachmentBos = applyPlanBusinessDao.getIdAttachment(applyId, productCertifyRespFc.getAttachmentTypeCode());
            if (AssertUtils.isNotEmpty(applyAttachmentBos)) {
                applyBoService.deleteApplyAttachmentBoList(applyAttachmentBos);
            }
            long initSeq = 1;
            for (ApplyAttachmentResp applyAttachmentResp : productCertifyRespFc.getAttachments()) {
                ApplyAttachmentBo applyAttachmentBo = new ApplyAttachmentBo();
                applyAttachmentBo.setAttachmentTypeCode(productCertifyRespFc.getAttachmentTypeCode());
                applyAttachmentBo.setApplyId(applyId);
                applyAttachmentBo.setAttachmentId(applyAttachmentResp.getAttachmentId());
                applyAttachmentBo.setAttachmentSeq(initSeq);
                applyBoService.saveApplyAttachmentPo(applyAttachmentBo);
                initSeq = initSeq + 1;
            }
        });
    }

    public void saveImageApplicantSignatureAttachmentData(String applyId, List<AppAttachmentRequest> appAttachmentRequests) {
        appAttachmentRequests.forEach(appAttachmentRequest -> {
            List<ApplyAttachmentBo> applyAttachmentBos = applyPlanBusinessDao.getIdAttachment(applyId, appAttachmentRequest.getAttachmentTypeCode());
            if (AssertUtils.isNotEmpty(applyAttachmentBos)) {
                applyBoService.deleteApplyAttachmentBoList(applyAttachmentBos);
            }
            long initSeq = 1;
                ApplyAttachmentBo applyAttachmentBo = new ApplyAttachmentBo();
                applyAttachmentBo.setAttachmentTypeCode(appAttachmentRequest.getAttachmentTypeCode());
                applyAttachmentBo.setApplyId(applyId);
                applyAttachmentBo.setAttachmentId(appAttachmentRequest.getAttachmentId());
                applyAttachmentBo.setAttachmentSeq(initSeq);
                applyAttachmentBo.setDescription(appAttachmentRequest.getPngAttachmentId());
                applyBoService.saveApplyAttachmentPo(applyAttachmentBo);
        });
    }

    public void saveImageOtherAttachmentData(String applyId, List<AppAttachmentRequest> appAttachmentRequests) {
        appAttachmentRequests.forEach(appAttachmentRequest -> {
            List<ApplyAttachmentBo> applyAttachmentBos = applyPlanBusinessDao.getIdAttachment(applyId, ApplyTermEnum.CERTIFY_ATTACHMENT_TYPE.APPLY_PLAN_OTHER.name());
            if (AssertUtils.isNotEmpty(applyAttachmentBos)) {
                applyBoService.deleteApplyAttachmentBoList(applyAttachmentBos);
            }
            long initSeq = 1;
            for (AppAttachmentRequest attachmentRequest : appAttachmentRequests) {
                ApplyAttachmentBo applyAttachmentBo = new ApplyAttachmentBo();
                applyAttachmentBo.setAttachmentTypeCode(ApplyTermEnum.CERTIFY_ATTACHMENT_TYPE.APPLY_PLAN_OTHER.name());
                applyAttachmentBo.setApplyId(applyId);
                applyAttachmentBo.setAttachmentId(attachmentRequest.getAttachmentId());
                applyAttachmentBo.setAttachmentSeq(initSeq);
                applyBoService.saveApplyAttachmentPo(applyAttachmentBo);
                initSeq = initSeq + 1;
            }
        });
    }

    public void saveAgentImageSignatureAttachmentData(String applyId, List<AppAttachmentRequest> appAttachmentRequests) {
        appAttachmentRequests.forEach(appAttachmentRequest -> {
            List<ApplyAttachmentBo> applyAttachmentBos = applyPlanBusinessDao.getIdAttachment(applyId, appAttachmentRequest.getAttachmentTypeCode());
            if (AssertUtils.isNotEmpty(applyAttachmentBos)) {
                applyBoService.deleteApplyAttachmentBoList(applyAttachmentBos);
            }
            long initSeq = 1;
                ApplyAttachmentBo applyAttachmentBo = new ApplyAttachmentBo();
                applyAttachmentBo.setAttachmentTypeCode(appAttachmentRequest.getAttachmentTypeCode());
                applyAttachmentBo.setApplyId(applyId);
                applyAttachmentBo.setAttachmentId(appAttachmentRequest.getAttachmentId());
                applyAttachmentBo.setAttachmentSeq(initSeq);
                applyAttachmentBo.setDescription(appAttachmentRequest.getPngAttachmentId());
                applyBoService.saveApplyAttachmentPo(applyAttachmentBo);
        });
    }

    public void saveImageInsuredSignatureAttachmentData(String applyId, List<AppAttachmentRequest> appAttachmentRequests) {
        appAttachmentRequests.forEach(appAttachmentRequest -> {
            List<ApplyAttachmentBo> applyAttachmentBos = applyPlanBusinessDao.getIdAttachment(applyId, appAttachmentRequest.getAttachmentTypeCode());
            if (AssertUtils.isNotEmpty(applyAttachmentBos)) {
                applyBoService.deleteApplyAttachmentBoList(applyAttachmentBos);
            }
            long initSeq = 1;
                ApplyAttachmentBo applyAttachmentBo = new ApplyAttachmentBo();
                applyAttachmentBo.setAttachmentTypeCode(appAttachmentRequest.getAttachmentTypeCode());
                applyAttachmentBo.setApplyId(applyId);
                applyAttachmentBo.setAttachmentId(appAttachmentRequest.getAttachmentId());
                applyAttachmentBo.setAttachmentSeq(initSeq);
                applyAttachmentBo.setDescription(appAttachmentRequest.getPngAttachmentId());
                applyBoService.saveApplyAttachmentPo(applyAttachmentBo);
        });
    }

    public void saveImageApplicantSignatureVideoAttachmentData(String applyId, List<AppAttachmentRequest> appAttachmentRequests) {
        appAttachmentRequests.forEach(appAttachmentRequest -> {
            List<ApplyAttachmentBo> applyAttachmentBos = applyPlanBusinessDao.getIdAttachment(applyId, appAttachmentRequest.getAttachmentTypeCode());
            if (AssertUtils.isNotEmpty(applyAttachmentBos)) {
                applyBoService.deleteApplyAttachmentBoList(applyAttachmentBos);
            }
            long initSeq = 1;
            for (AppAttachmentRequest attachmentRequest : appAttachmentRequests) {
                ApplyAttachmentBo applyAttachmentBo = new ApplyAttachmentBo();
                applyAttachmentBo.setAttachmentTypeCode(ApplyTermEnum.CERTIFY_ATTACHMENT_TYPE.APPLY_PLAN_SIGNATURE_APPLICANT_VIDEO.name());
                applyAttachmentBo.setApplyId(applyId);
                applyAttachmentBo.setAttachmentId(attachmentRequest.getAttachmentId());
                applyAttachmentBo.setAttachmentSeq(initSeq);
                applyBoService.saveApplyAttachmentPo(applyAttachmentBo);
                initSeq = initSeq + 1;
            }
        });
    }

    public void saveImageInsuredSignatureVideoAttachmentData(String applyId, List<AppAttachmentRequest> appAttachmentRequests) {
        appAttachmentRequests.forEach(appAttachmentRequest -> {
            List<ApplyAttachmentBo> applyAttachmentBos = applyPlanBusinessDao.getIdAttachment(applyId, appAttachmentRequest.getAttachmentTypeCode());
            if (AssertUtils.isNotEmpty(applyAttachmentBos)) {
                applyBoService.deleteApplyAttachmentBoList(applyAttachmentBos);
            }
            long initSeq = 1;
            for (AppAttachmentRequest attachmentRequest : appAttachmentRequests) {
                ApplyAttachmentBo applyAttachmentBo = new ApplyAttachmentBo();
                applyAttachmentBo.setAttachmentTypeCode(ApplyTermEnum.CERTIFY_ATTACHMENT_TYPE.APPLY_PLAN_SIGNATURE_INSURED_VIDEO.name());
                applyAttachmentBo.setApplyId(applyId);
                applyAttachmentBo.setAttachmentId(attachmentRequest.getAttachmentId());
                applyAttachmentBo.setAttachmentSeq(initSeq);
                applyBoService.saveApplyAttachmentPo(applyAttachmentBo);
                initSeq = initSeq + 1;
            }
        });
    }

    public void saveImageTogetherVideoAttachmentData(String applyId, List<AppAttachmentRequest> appAttachmentRequests) {
        appAttachmentRequests.forEach(appAttachmentRequest -> {
            List<ApplyAttachmentBo> applyAttachmentBos = applyPlanBusinessDao.getIdAttachment(applyId, appAttachmentRequest.getAttachmentTypeCode());
            if (AssertUtils.isNotEmpty(applyAttachmentBos)) {
                applyBoService.deleteApplyAttachmentBoList(applyAttachmentBos);
            }
            long initSeq = 1;
            for (AppAttachmentRequest attachmentRequest : appAttachmentRequests) {
                ApplyAttachmentBo applyAttachmentBo = new ApplyAttachmentBo();
                applyAttachmentBo.setAttachmentTypeCode(ApplyTermEnum.CERTIFY_ATTACHMENT_TYPE.APPLY_PLAN_SIGNATURE_TOGETHER_VIDEO.name());
                applyAttachmentBo.setApplyId(applyId);
                applyAttachmentBo.setAttachmentId(attachmentRequest.getAttachmentId());
                applyAttachmentBo.setAttachmentSeq(initSeq);
                applyBoService.saveApplyAttachmentPo(applyAttachmentBo);
                initSeq = initSeq + 1;
            }
        });
    }

    public void saveImageTogetherPhotoAttachmentData(String applyId, List<AppAttachmentRequest> appAttachmentRequests) {
        appAttachmentRequests.forEach(appAttachmentRequest -> {
            List<ApplyAttachmentBo> applyAttachmentBos = applyPlanBusinessDao.getIdAttachment(applyId, appAttachmentRequest.getAttachmentTypeCode());
            if (AssertUtils.isNotEmpty(applyAttachmentBos)) {
                applyBoService.deleteApplyAttachmentBoList(applyAttachmentBos);
            }
            long initSeq = 1;
            for (AppAttachmentRequest attachmentRequest : appAttachmentRequests) {
                ApplyAttachmentBo applyAttachmentBo = new ApplyAttachmentBo();
                applyAttachmentBo.setAttachmentTypeCode(ApplyTermEnum.CERTIFY_ATTACHMENT_TYPE.APPLY_PLAN_SIGNATURE_TOGETHER_PHOTO.name());
                applyAttachmentBo.setApplyId(applyId);
                applyAttachmentBo.setAttachmentId(attachmentRequest.getAttachmentId());
                applyAttachmentBo.setAttachmentSeq(initSeq);
                applyBoService.saveApplyAttachmentPo(applyAttachmentBo);
                initSeq = initSeq + 1;
            }
        });
    }

    public void saveApplyPdfAttachmentData(String applyId, List<AppAttachmentRequest> appAttachmentRequests, String language) {
        appAttachmentRequests.forEach(appAttachmentRequest -> {
            long initSeq = 1;
            for (AppAttachmentRequest attachmentRequest : appAttachmentRequests) {
                ApplyAttachmentBo applyAttachmentBo = new ApplyAttachmentBo();
                applyAttachmentBo.setAttachmentTypeCode(ApplyTermEnum.CERTIFY_ATTACHMENT_TYPE.APPLY_BOOK.name());
                applyAttachmentBo.setApplyId(applyId);
                applyAttachmentBo.setAttachmentId(attachmentRequest.getAttachmentId());
                applyAttachmentBo.setAttachmentSeq(initSeq);
                applyBoService.saveApplyAttachmentPo(applyAttachmentBo);
                initSeq = initSeq + 1;
            }
        });
    }

    public void savePlanPdfAttachmentData(String applyId, List<AppAttachmentRequest> appAttachmentRequests, String language) {
        appAttachmentRequests.forEach(appAttachmentRequest -> {
            long initSeq = 1;
            for (AppAttachmentRequest attachmentRequest : appAttachmentRequests) {
                ApplyAttachmentBo applyAttachmentBo = new ApplyAttachmentBo();
                applyAttachmentBo.setAttachmentTypeCode(ApplyTermEnum.CERTIFY_ATTACHMENT_TYPE.PLAN_BOOK.name());
                applyAttachmentBo.setApplyId(applyId);
                applyAttachmentBo.setAttachmentId(attachmentRequest.getAttachmentId());
                applyAttachmentBo.setAttachmentSeq(initSeq);
                applyBoService.saveApplyAttachmentPo(applyAttachmentBo);
                initSeq = initSeq + 1;
            }
        });
    }

    public AppPageJumpResponse loadAppPageJump(String applyId, String deviceChannel) {
        AppPageJumpResponse appPageJumpResponse = new AppPageJumpResponse();
        //获取被保人
        ApplyInsuredBo applyInsuredBo = applyExtDao.loadApplyInsuredBoByApplyId(applyId);
        AssertUtils.isNotNull(this.getLogger(), applyInsuredBo, ApplyErrorConfigEnum.APPLY_BUSINESS_APPLY_INSURED_IS_NOT_FOUND_OBJECT);
        //被保人证件上传标识
        boolean insuredSameWithApplicant = ModelConstantEnum.RELATION_TYPE.ONESELF.name().equals(applyInsuredBo.getRelationship());
        String YES = TerminologyConfigEnum.WHETHER.YES.name();
        if (!insuredSameWithApplicant) {
            appPageJumpResponse.setInsuredIdImageFlag(YES);
        }
        //受益人证件上传标识
        List<ApplyBeneficiaryInfoBo> applyBeneficialBoList = applyPlanBusinessDao.getApplyBeneficialBoList(applyId, YES);
        if (AssertUtils.isNotEmpty(applyBeneficialBoList)) {
            //查询投保人
            ApplyApplicantBo applyApplicantBo = applyApplicantBaseService.queryApplyApplicant(applyId);

            for (ApplyBeneficiaryInfoBo applyBeneficiaryInfoBo : applyBeneficialBoList) {
                //当受益人客户ID和投被保人客户ID都不一致时，需要上传每位收益人的证件照
                String customerId = applyBeneficiaryInfoBo.getApplyBeneficiaryBo().getCustomerId();
                if (AssertUtils.isNotEmpty(customerId) && !applyApplicantBo.getCustomerId().equals(customerId) &&
                        !applyInsuredBo.getCustomerId().equals(customerId)) {
                    System.out.println(applyBeneficiaryInfoBo.getApplyBeneficiaryBo().getName());
                    appPageJumpResponse.setBeneficiaryIdImageFlag(YES);
                    break;
                }
            }

        }

        List<ApplyCoverageBo> applyCoverageBos = applyPlanBusinessDao.loadApplyCoverage(applyId);
        List<String> healthFlag = new ArrayList<>();
        List<String> contractLoanFlag = new ArrayList<>();
        List<String> reinsuranceFlag = new ArrayList<>();
        List<String> productIds = new ArrayList<>();
        applyCoverageBos.forEach(applyCoverageBo -> {
            productIds.add(applyCoverageBo.getProductId());
            ProductSalesDetailedResponse productSalesDetailedResponse = productSalesApi.getProductSimpleInfo(applyCoverageBo.getProductId(), deviceChannel, ApplyTermEnum.YES_NO.NO.name()).getData();
            if (AssertUtils.isNotNull(productSalesDetailedResponse)) {
                if (AssertUtils.isNotEmpty(productSalesDetailedResponse.getHealthNoticeFlag())) {
                    healthFlag.add(productSalesDetailedResponse.getHealthNoticeFlag());
                }
                if (AssertUtils.isNotEmpty(productSalesDetailedResponse.getContractLoanFlag())) {
                    contractLoanFlag.add(productSalesDetailedResponse.getContractLoanFlag());
                }
                if (AssertUtils.isNotEmpty(productSalesDetailedResponse.getReinsuranceFlag())) {
                    reinsuranceFlag.add(productSalesDetailedResponse.getReinsuranceFlag());
                }
            } else {
                if (Arrays.asList(ApplyTermEnum.PRODUCT.PRODUCT_1.id(), ApplyTermEnum.PRODUCT.PRODUCT_3.id(), ApplyTermEnum.PRODUCT.PRODUCT_5.id(), ApplyTermEnum.PRODUCT.PRODUCT_28.id(), ApplyTermEnum.PRODUCT.PRODUCT_8.id(), ApplyTermEnum.PRODUCT.PRODUCT_9.id(), ApplyTermEnum.PRODUCT.PRODUCT_13.id()).contains(applyCoverageBo.getProductId())) {
                    healthFlag.add(YES);
                }
                if (ProductTermEnum.PRODUCT.PRODUCT_5.id().equals(applyCoverageBo.getProductId()) || ApplyTermEnum.PRODUCT.PRODUCT_28.id().equals(applyCoverageBo.getProductId())) {
                    contractLoanFlag.add(YES);
                }
                if (Arrays.asList(ProductTermEnum.PRODUCT.PRODUCT_13.id(), ProductTermEnum.PRODUCT.PRODUCT_14.id(), ProductTermEnum.PRODUCT.PRODUCT_15.id()).contains(applyCoverageBo.getProductId())) {
                    reinsuranceFlag.add(YES);
                }
            }
        });

        //影像附件上传标识
        ResultObject<List<CertifyAttachmentTypeResponse>> productCertify = productCertifyApi.queryProductCertify(productIds);
        if (!AssertUtils.isResultObjectListDataNull(productCertify)) {
            appPageJumpResponse.setImageAttachmentFlag(YES);
        }

        //健康告知标识
        if (AssertUtils.isNotEmpty(healthFlag) && healthFlag.contains(YES)) {
            appPageJumpResponse.setHealthNoticeFlag(YES);
        }
        //贷款合同上传标识
//        if (AssertUtils.isNotEmpty(contractLoanFlag) && contractLoanFlag.contains(YES)) {
//            appPageJumpResponse.setContractLoanFlag(YES);
//        }
//        if (AssertUtils.isNotEmpty(reinsuranceFlag) && reinsuranceFlag.contains(YES)) {
            appPageJumpResponse.setStatementFlag(YES);
//        }
        List<ProductDisplayCertifyResponse> productDisplayCertifyResponses = productCertifyApi.queryDisplayProductCertify(productIds).getData();
        if (AssertUtils.isNotEmpty(productDisplayCertifyResponses)) {
            productDisplayCertifyResponses.forEach(productDisplayCertifyRespFc -> {
                if (ApplyTermEnum.APPLY_ATTACHMENT_TYPE.APPLICANT_INSURED_DECLARATION.name().equals(productDisplayCertifyRespFc.getAttachmentTypeCode())) {
                    if (AssertUtils.isNotEmpty(productDisplayCertifyRespFc.getListAttachmentRelation())) {
                        appPageJumpResponse.setApplicantDeclarationFlag(YES);
                    }
                }
                if (ApplyTermEnum.APPLY_ATTACHMENT_TYPE.APPLICANT_TRANSFER_AUTHORIZATION_DECLARATION.name().equals(productDisplayCertifyRespFc.getAttachmentTypeCode())) {
                    if (AssertUtils.isNotEmpty(productDisplayCertifyRespFc.getListAttachmentRelation())) {
                        appPageJumpResponse.setAuthorizedTransferFlag(YES);
                    }
                }
            });
        }
        //上传修改申请书标识
        ApplyOperationPo applyOperationPo = applyBaseService.queryApplyOperationPo(applyId);
        if (AssertUtils.isNotNull(applyOperationPo) && ApplyTermEnum.APPLY_OPERATION_CODE.REAPPLYING.name().equals(applyOperationPo.getOperationCode())) {
            appPageJumpResponse.setModifyApplicationFlag(YES);
        }
//        ApplyAccountBo applyAccountBo = applyPlanBusinessDao.loadAccountById(applyId);
//        if (AssertUtils.isNotNull(applyAccountBo)) {
//            appPageJumpResponse.setBankCardImageFlag(YES);
//        }
        return appPageJumpResponse;
    }
}