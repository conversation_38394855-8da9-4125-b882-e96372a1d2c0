package com.gclife.apply.service.business.impl;

import com.alibaba.fastjson.JSONObject;
import com.gclife.agent.api.AgentApi;
import com.gclife.agent.model.response.AgentResponse;
import com.gclife.apply.core.jooq.tables.pojos.*;
import com.gclife.apply.dao.ApplyCoverageBaseDao;
import com.gclife.apply.dao.ApplyExtDao;
import com.gclife.apply.dao.ApplyUnderWriteExtDao;
import com.gclife.apply.model.bo.*;
import com.gclife.apply.model.bo.app.ApplyPlanBo;
import com.gclife.apply.model.config.ApplyErrorConfigEnum;
import com.gclife.apply.model.config.ApplyTermEnum;
import com.gclife.apply.model.config.PayNotifyTermEnum;
import com.gclife.apply.model.respone.ApplyAutoUWResponse;
import com.gclife.apply.model.response.ApplyRiskAmountResponse;
import com.gclife.apply.model.response.UnderWriteInsuredResponse;
import com.gclife.apply.service.*;
import com.gclife.apply.service.business.ApplyAutoUnderWriteBusinessService;
import com.gclife.apply.service.business.MessageBusinessService;
import com.gclife.apply.service.data.ApplyBoService;
import com.gclife.apply.service.data.ApplyUnderWriteBoService;
import com.gclife.apply.validate.parameter.transform.ApplyChangeTransData;
import com.gclife.apply.validate.parameter.transform.ApplyParameterTransData;
import com.gclife.apply.validate.parameter.transform.ApplyPaymentTransData;
import com.gclife.attachment.model.policy.policy.PolicyCoverageBo;
import com.gclife.claim.api.ClaimApi;
import com.gclife.claim.model.respone.CaseRevokeListResponse;
import com.gclife.common.model.ResultObject;
import com.gclife.common.model.base.Users;
import com.gclife.common.model.config.TerminologyConfigEnum;
import com.gclife.common.model.pojo.AppRequestHeads;
import com.gclife.common.service.impl.BaseBusinessServiceImpl;
import com.gclife.common.util.AssertUtils;
import com.gclife.common.util.DateUtils;
import com.gclife.common.util.JackSonUtils;
import com.gclife.party.api.CustomerBaseApi;
import com.gclife.party.api.CustomerManageApi;
import com.gclife.party.model.response.CustomerAgentResponse;
import com.gclife.party.model.response.CustomerMessageResponse;
import com.gclife.policy.api.PolicyApi;
import com.gclife.policy.model.response.PolicyRealClientListResponse;
import com.gclife.product.model.config.ProductTermEnum;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * create 2021/6/4 14:33
 * description:智能核保
 */
@Slf4j
@Service
public class ApplyAutoUnderWriteBusinessServiceImpl extends BaseBusinessServiceImpl implements ApplyAutoUnderWriteBusinessService {

    @Autowired
    private ApplyBaseService applyBaseService;
    @Autowired
    private ApplyCoverageBaseDao applyCoverageBaseDao;
    @Autowired
    private PolicyApi policyApi;
    @Autowired
    private CustomerBaseApi customerBaseApi;
    @Autowired
    private ApplyExtDao applyExtDao;
    @Autowired
    private ApplyRemarkBaseService applyRemarkBaseService;
    @Autowired
    private ApplyUnderwriteBaseService applyUnderwriteBaseService;
    @Autowired
    private MessageBusinessService messageBusinessService;
    @Autowired
    private ApplyUnderWriteExtDao applyUnderWriteExtDao;
    @Autowired
    private ApplyUnderWriteBoService applyUnderWriteBoService;
    @Autowired
    private ApplyBoService applyBoService;
    @Autowired
    private ApplyCoverageBaseService applyCoverageBaseService;
    @Autowired
    private ApplyPremiumBaseService applyPremiumBaseService;
    @Autowired
    private CustomerManageApi customerManageApi;
    @Autowired
    private ClaimApi claimApi;
    @Autowired
    private AgentApi agentApi;
    @Autowired
    private ApplyPaymentTransData applyPaymentTransData;
    @Autowired
    private ApplyPlanBaseService applyPlanBaseService;
    @Autowired
    private ApplyChangeTransData applyChangeTransData;
    @Autowired
    private ApplyParameterTransData applyParameterTransData;

    /**
     * 发起智能核保
     *
     * @param applyId         投保单ID
     * @param users           用户
     * @param appRequestHeads 请求头
     * @param uwType
     * @return 是否通过智能核保
     */
    @Override
    public ApplyAutoUWResponse initiateAutoUnderWriting(String applyId, Users users, AppRequestHeads appRequestHeads, String uwType) {
        ApplyAutoUWResponse applyAutoUWResponse = new ApplyAutoUWResponse();
        /*
        智能核保规则
        1. 健康告知
            回答全为否通过，有任何一项为是，则进入人工核保；
        2.年龄（产品层面已限定）
            根据每款产品的投保年龄而定；
        3.单笔保单保额上限
            单笔保单≥100,000，则流转到人工核保；
            1. 具体数值待总部同事提供；
        4.累计风险保额
            同一客户累计风险保额≥1,000,000，则流转到人工核保；
            1. 具体数值待总部同事提供；
        5.BMI（体重指数）
            1. 16≥BMI≤30，数值满足该期间则通过，否则流转到人工核保；
        6.是否标准职业
            1. 系统现职业分类表，1-3级通过，4-5级需进入人工核保；
        7.受益人关系为"其他"

        8.职业为"其他"
        当在智能核保时，无需校验累计风险保额和有无理赔历史，因为只要存在疑似客户都不通过，有疑似客户才会有累计风险保额和有无理赔历史；
         */
        ApplyBo applyBo = applyBaseService.queryApply(applyId);
        AssertUtils.isNotNull(log, applyBo, ApplyErrorConfigEnum.APPLY_BUSINESS_APPLY_IS_NOT_FOUND_OBJECT);
        AssertUtils.isNotNull(log, applyBo.getApplicant(), ApplyErrorConfigEnum.APPLY_BUSINESS_APPLY_APPLICANT_IS_NOT_FOUND_OBJECT);
        AssertUtils.isNotEmpty(log, applyBo.getListInsured(), ApplyErrorConfigEnum.APPLY_BUSINESS_APPLY_INSURED_IS_NOT_FOUND_OBJECT);
        ApplyInsuredBo applyInsuredBo = applyBo.getListInsured().get(0);
        List<ApplyBeneficiaryInfoBo> listBeneficiary = applyInsuredBo.getListBeneficiary();

        boolean isOnline = ApplyTermEnum.CHANNEL_TYPE.ONLINE.name().equals(applyBo.getChannelTypeCode());

        List<BaseUnderwriteProblemPo> baseUnderwriteProblemPos = applyUnderwriteBaseService.queryAllBaseUnderwriteProblem();
        List<ApplyUnderwriteProblemPo> applyUnderwriteProblemPos = new ArrayList<>();
        //TODO 网销渠道的单不需要执行智能核保规则，如有需要，后续再加
        if (!isOnline) {
            this.transApplyUnderwriteProblem(applyId, uwType, applyBo, applyInsuredBo, listBeneficiary, baseUnderwriteProblemPos, applyUnderwriteProblemPos);
        }

        boolean passAutoUnderWritingFlag = true;
        applyUnderwriteBaseService.deleteApplyUnderwriteBatch(applyId);
        if (AssertUtils.isNotEmpty(applyUnderwriteProblemPos)) {
            ApplyUnderwriteBatchPo applyUnderwriteBatchPo = new ApplyUnderwriteBatchPo();
            applyUnderwriteBatchPo.setApplyId(applyId);
            applyUnderwriteBatchPo.setCheckStatus("UNCHECK");
            applyUnderwriteBaseService.saveApplyUnderwriteBatch(applyUnderwriteBatchPo, users.getUserId());

            applyUnderwriteProblemPos.forEach(applyUnderwriteProblemPo -> {
                applyUnderwriteProblemPo.setApplyUnderwriteBatchId(applyUnderwriteBatchPo.getApplyUnderwriteBatchId());
                applyUnderwriteProblemPo.setApplyId(applyId);
            });
            applyUnderwriteBaseService.saveApplyUnderwriteProblemPo(applyUnderwriteProblemPos, users.getUserId());
            passAutoUnderWritingFlag = false;
        }
        if ("AUTO_UW".equals(uwType)) {
            if (passAutoUnderWritingFlag) {
                //智能核保通过,模拟人工核保通过处理,新增核保信息,保费计算处理
                applyParameterTransData.simulateManualUW(users.getUserId(), applyId);
                //智能核保通过 初始化投保单变更数据
                applyChangeTransData.initApplyChangeData(users, applyId);
                try {
                    AgentResponse applyAgentRespFc = agentApi.agentByIdGet(applyBo.getApplyAgentBo().getAgentId()).getData();
                    applyBo.getApplyAgentBo().setAgentName(applyAgentRespFc.getAgentName());
                    // 发送消息给微信/钉钉
                    /*log.info("==================================核保完成发送消息给微信/钉钉=====================================");
                    messageBusinessService.pushApplyMessageBatch(ApplyTermEnum.MSG_BUSINESS_TYPE.PASSED_THE_UNDERWRITING_OF_THE_NEW_SINGLE_INSURANCE_POLICY.name(), applyBo);

                    // 发消息给业务员
                    log.info("==================================核保完成发消息给业务员=====================================");
                    messageBusinessService.pushApplyMessageSingle(
                            ApplyTermEnum.MSG_BUSINESS_TYPE.PASSED_THE_UNDERWRITING_OF_THE_NEW_SINGLE_INSURANCE_POLICY.name(), applyBo, applyBo.getApplyAgentBo().getAgentId());

                    // 发消息给客户
                    log.info("==================================核保完成发消息给客户=====================================");
                    messageBusinessService.pushApplyMessageCustomer(ApplyTermEnum.MSG_BUSINESS_TYPE.PASSED_THE_UNDERWRITING_OF_THE_NEW_SINGLE_INSURANCE_POLICY.name(), applyBo, users, appRequestHeads);*/
                } catch (Exception e) {
                    e.printStackTrace();
                }
            }
            //都需要默认发起支付
            ApplyPaymentUWBo applyPaymentUWBo = new ApplyPaymentUWBo();
            applyPaymentUWBo.setInitiatePaymentFlag(true);
            applyPaymentUWBo.setPrepaidPremiumFlag(!passAutoUnderWritingFlag);
            boolean paymentFlag = applyPaymentTransData.transUWPayment(users, appRequestHeads, applyId, applyPaymentUWBo);
            applyAutoUWResponse.setPaymentFlag(paymentFlag);
        }
        log.info("智能核保是否通过：{}", passAutoUnderWritingFlag);
        applyAutoUWResponse.setPassAutoUnderWritingFlag(passAutoUnderWritingFlag);
        return applyAutoUWResponse;
    }

    private void transApplyUnderwriteProblem(String applyId, String uwType, ApplyBo applyBo, ApplyInsuredBo applyInsuredBo, List<ApplyBeneficiaryInfoBo> listBeneficiary, List<BaseUnderwriteProblemPo> baseUnderwriteProblemPos, List<ApplyUnderwriteProblemPo> applyUnderwriteProblemPos) {
        //保单回溯
        this.transUWBackdating(applyBo, baseUnderwriteProblemPos, applyUnderwriteProblemPos);

        // 健康告知
        this.transUWHealth(applyId, baseUnderwriteProblemPos, applyUnderwriteProblemPos);

        //投保人BMI
        this.transUWApplicantBMI(applyBo.getApplicant(), baseUnderwriteProblemPos, applyUnderwriteProblemPos);

        //被保人BMI
        this.transUWInsuredBMI(applyInsuredBo, baseUnderwriteProblemPos, applyUnderwriteProblemPos);

        //被保人职业类别
        this.transUWInsuredOccType(applyInsuredBo, baseUnderwriteProblemPos, applyUnderwriteProblemPos);

        //受益人关系为"其他"
        this.transUWBeneficiaryRelationship(listBeneficiary, baseUnderwriteProblemPos, applyUnderwriteProblemPos);

        //投保人职业为"其他"
        this.transUWApplicantOccupation(applyBo.getApplicant(), baseUnderwriteProblemPos, applyUnderwriteProblemPos);

        //被保人职业为"其他"
        this.transUWInsuredOccupation(applyInsuredBo, baseUnderwriteProblemPos, applyUnderwriteProblemPos);

        List<PolicyCoverageBo> applyCoverageBoList = applyCoverageBaseDao.queryApplyCoverageBo(applyId);

        //疑似客户

        this.transUWExistingClient(applyInsuredBo, applyBo.getApplicant(), baseUnderwriteProblemPos, applyUnderwriteProblemPos);

//        if ("MANUAL_UW".equals(uwType)) {
        //累计风险保额
        ApplyRiskAmountResponse applyRiskAmountResponse = this.transUWRiskAmount(applyId, applyInsuredBo, baseUnderwriteProblemPos, applyUnderwriteProblemPos, applyCoverageBoList);

        //累计体检保额上限
        this.transUWSingleAmount(applyBo.getApplyDate(), applyInsuredBo.getBirthday(), baseUnderwriteProblemPos, applyUnderwriteProblemPos, applyCoverageBoList, applyRiskAmountResponse);

        //有理赔历史
        this.transUWClaimHistory(applyInsuredBo, baseUnderwriteProblemPos, applyUnderwriteProblemPos);
//        }

        //保单折扣
        this.transUWDiscount(applyBo, baseUnderwriteProblemPos, applyUnderwriteProblemPos);
    }

    private void transUWDiscount(ApplyBo applyBo, List<BaseUnderwriteProblemPo> baseUnderwriteProblemPos, List<ApplyUnderwriteProblemPo> applyUnderwriteProblemPos) {
        ApplyPlanBo applyPlanBo = applyPlanBaseService.queryApplyPlan(applyBo.getApplyId());
        if (AssertUtils.isNotNull(applyPlanBo) && AssertUtils.isNotNull(applyPlanBo.getDiscountModel()) && AssertUtils.isNotNull(applyPlanBo.getDiscountType())
        ) {
            this.transApplyUnderwriteProblemPo(applyUnderwriteProblemPos,
                    this.filterBaseUnderwriteProblem(baseUnderwriteProblemPos, ApplyTermEnum.AUTO_UNDERWRITE_PROBLEM.THIS_APPLICATION_HAS_REQUESTED_FOR_DISCOUNT.name()));
        }
    }

    private void transUWBackdating(ApplyBo applyBo, List<BaseUnderwriteProblemPo> baseUnderwriteProblemPos, List<ApplyUnderwriteProblemPo> applyUnderwriteProblemPos) {
        if (AssertUtils.isNotNull(applyBo.getBackTrackDate()) && applyBo.getBackTrackDate() > 0) {
            this.transApplyUnderwriteProblemPo(applyUnderwriteProblemPos,
                    this.filterBaseUnderwriteProblem(baseUnderwriteProblemPos, ApplyTermEnum.AUTO_UNDERWRITE_PROBLEM.THIS_APPLICATION_APPLIED_BACKDATING_FUNCTION.name()));
        }
    }

    private void transUWInsuredOccupation(ApplyInsuredBo applyInsuredBo, List<BaseUnderwriteProblemPo> baseUnderwriteProblemPos, List<ApplyUnderwriteProblemPo> applyUnderwriteProblemPos) {
        List<String> notApproveOccupation = Arrays.asList("GC-1303027", "GC-1001004", "GC-16");
        if (AssertUtils.isNotNull(applyInsuredBo.getOccupationCode()) && notApproveOccupation.contains(applyInsuredBo.getOccupationCode())) {
            this.transApplyUnderwriteProblemPo(applyUnderwriteProblemPos,
                    this.filterBaseUnderwriteProblem(baseUnderwriteProblemPos, ApplyTermEnum.AUTO_UNDERWRITE_PROBLEM.INSURED_OCCUPATION_IS_OTHER.name()));
        }
    }

    private void transUWApplicantOccupation(ApplyApplicantBo applicant, List<BaseUnderwriteProblemPo> baseUnderwriteProblemPos, List<ApplyUnderwriteProblemPo> applyUnderwriteProblemPos) {
        if (AssertUtils.isNotNull(applicant.getOccupationCode()) && "GC-16".equals(applicant.getOccupationCode())) {
            this.transApplyUnderwriteProblemPo(applyUnderwriteProblemPos,
                    this.filterBaseUnderwriteProblem(baseUnderwriteProblemPos, ApplyTermEnum.AUTO_UNDERWRITE_PROBLEM.APPLICANT_OCCUPATION_IS_OTHER.name()));
        }
    }

    private void transUWClaimHistory(ApplyInsuredBo applyInsuredBo, List<BaseUnderwriteProblemPo> baseUnderwriteProblemPos, List<ApplyUnderwriteProblemPo> applyUnderwriteProblemPos) {
        ResultObject<List<CaseRevokeListResponse>> listResultObject = claimApi.queryCustomerClaimHistory(applyInsuredBo.getCustomerId());
        if (!AssertUtils.isResultObjectListDataNull(listResultObject)) {
            this.transApplyUnderwriteProblemPo(applyUnderwriteProblemPos,
                    this.filterBaseUnderwriteProblem(baseUnderwriteProblemPos, ApplyTermEnum.AUTO_UNDERWRITE_PROBLEM.EXISTING_CLAIM.name()));
        }
    }

    private void transUWExistingClient(ApplyInsuredBo applyInsuredBo, ApplyApplicantBo applicant, List<BaseUnderwriteProblemPo> baseUnderwriteProblemPos, List<ApplyUnderwriteProblemPo> applyUnderwriteProblemPos) {
        ResultObject<List<CustomerMessageResponse>> listResultObject = customerManageApi.querySuspectedCustomer(applyInsuredBo.getCustomerId());
        ResultObject<List<CustomerMessageResponse>> listResultObject2 = customerManageApi.querySuspectedCustomer(applicant.getCustomerId());
        String applyId = applicant.getApplyId();
        // 疑似客户
        if (!AssertUtils.isResultObjectListDataNull(listResultObject) || !AssertUtils.isResultObjectListDataNull(listResultObject2)) {
            this.transApplyUnderwriteProblemPo(applyUnderwriteProblemPos,
                    this.filterBaseUnderwriteProblem(baseUnderwriteProblemPos, ApplyTermEnum.AUTO_UNDERWRITE_PROBLEM.EXISTING_CLIENT.name()));
        }
        // 存在客户
        final boolean[] realClient = {false};
        List<String> allCustomerIds = new ArrayList<>();

        //被保人
        ResultObject<List<CustomerAgentResponse>> listCustomerAgentObject = customerBaseApi.getCustomerRelationByCustomerAgentId(applyInsuredBo.getCustomerId());
        AssertUtils.isResultObjectError(getLogger(), listCustomerAgentObject);
        if (!AssertUtils.isResultObjectListDataNull(listCustomerAgentObject)) {
            allCustomerIds.addAll(listCustomerAgentObject.getData().stream().map(CustomerAgentResponse::getCustomerAgentId).distinct().collect(Collectors.toList()));
        }
        //投保人
        ResultObject<List<CustomerAgentResponse>> listCustomerAgentObject1 = customerBaseApi.getCustomerRelationByCustomerAgentId(applicant.getCustomerId());
        AssertUtils.isResultObjectError(getLogger(), listCustomerAgentObject1);
        if (!AssertUtils.isResultObjectListDataNull(listCustomerAgentObject1)) {
            allCustomerIds.addAll(listCustomerAgentObject1.getData().stream().map(CustomerAgentResponse::getCustomerAgentId).distinct().collect(Collectors.toList()));
        }

        // 保单
        if (AssertUtils.isNotEmpty(allCustomerIds)) {
            List<ApplyRealClientListBo> applyRealClientListBos = applyBaseService.getApplyRealClient(applyId, allCustomerIds, false);
            ResultObject<List<PolicyRealClientListResponse>> policyRealClient = policyApi.getPolicyRealClient(allCustomerIds.stream().distinct().collect(Collectors.toList()));
            if (AssertUtils.isNotEmpty(applyRealClientListBos) || !AssertUtils.isResultObjectListDataNull(policyRealClient)) {
                realClient[0] = true;
            }
        }

//        // 查询受益人
//        List<ApplyBeneficiaryInfoBo> applyBeneficiaryInfoBos = applyBaseService.queryApplyBeneficiaryList(applyId);
//        if (AssertUtils.isNotEmpty(applyBeneficiaryInfoBos)) {
//            applyBeneficiaryInfoBos.stream().filter(applyBeneficiaryInfoBo -> AssertUtils.isNotNull(applyBeneficiaryInfoBo.getApplyBeneficiaryBo())
//                    && AssertUtils.isNotEmpty(applyBeneficiaryInfoBo.getApplyBeneficiaryBo().getCustomerId()))
//                    .map(applyBeneficiaryInfoBo -> applyBeneficiaryInfoBo.getApplyBeneficiaryBo().getCustomerId()).forEach(beneficiaryCustomerId -> {
//                ResultObject<List<CustomerAgentResponse>> listCustomerBeneficiary = customerBaseApi.getCustomerRelationByCustomerAgentId(beneficiaryCustomerId);
//                if (!AssertUtils.isResultObjectListDataNull(listCustomerBeneficiary)) {
//                    List<CustomerAgentResponse> collect = listCustomerBeneficiary.getData().stream().filter(customerAgentResponse -> !customerAgentResponse.getCustomerAgentId().equals(beneficiaryCustomerId)).collect(Collectors.toList());
//                    //投保单
//                    List<ApplyRealClientListBo> applyRealClientListBos = applyBaseService.getApplyRealClient(applyId, listCustomerBeneficiary.getData().stream().map(CustomerAgentResponse::getCustomerAgentId).distinct().collect(Collectors.toList()), false);
//                    if (AssertUtils.isNotEmpty(collect) || AssertUtils.isNotEmpty(applyRealClientListBos)) {
//                        realClient[0] = true;
//                    }
//                }
//            });
//        }

        if (realClient[0]) {
            this.transApplyUnderwriteProblemPo(applyUnderwriteProblemPos, this.filterBaseUnderwriteProblem(baseUnderwriteProblemPos, ApplyTermEnum.AUTO_UNDERWRITE_PROBLEM.REAL_CLIENT.name()));
        }
    }

    private void transUWBeneficiaryRelationship(List<ApplyBeneficiaryInfoBo> listBeneficiary, List<BaseUnderwriteProblemPo> baseUnderwriteProblemPos, List<ApplyUnderwriteProblemPo> applyUnderwriteProblemPos) {
        if (!AssertUtils.isNotEmpty(listBeneficiary)) {
            return;
        }
        Optional<ApplyBeneficiaryInfoBo> any = listBeneficiary.stream().filter(applyBeneficiaryInfoBo ->
                ApplyTermEnum.RELATIONSHIP_WITH_THE_INSURED.OTHER.name().equals(applyBeneficiaryInfoBo.getRelationship())
                        || ApplyTermEnum.RELATIONSHIP_WITH_THE_INSURED.ONESELF.name().equals(applyBeneficiaryInfoBo.getRelationship())
        ).findAny();
        if (any.isPresent()) {
            // 受益人关系为“其他”
            this.transApplyUnderwriteProblemPo(applyUnderwriteProblemPos,
                    this.filterBaseUnderwriteProblem(baseUnderwriteProblemPos, ApplyTermEnum.AUTO_UNDERWRITE_PROBLEM.BENEFICIARY_RELATIONSHIP_IS_OTHER.name()));
        }
    }

    private void transUWInsuredOccType(ApplyInsuredBo applyInsuredBo, List<BaseUnderwriteProblemPo> baseUnderwriteProblemPos, List<ApplyUnderwriteProblemPo> applyUnderwriteProblemPos) {
        List<String> notRiskOccupationType = Arrays.asList("1", "2", "3");
        if (AssertUtils.isNotNull(applyInsuredBo.getOccupationType()) && !notRiskOccupationType.contains(applyInsuredBo.getOccupationType())) {
            // 是否标准职业
            this.transApplyUnderwriteProblemPo(applyUnderwriteProblemPos,
                    this.filterBaseUnderwriteProblem(baseUnderwriteProblemPos, ApplyTermEnum.AUTO_UNDERWRITE_PROBLEM.STANDARD_OCCUPATION.name()));
        }
    }

    private void transUWInsuredBMI(ApplyInsuredBo applyInsuredBo, List<BaseUnderwriteProblemPo> baseUnderwriteProblemPos, List<ApplyUnderwriteProblemPo> applyUnderwriteProblemPos) {
        if (AssertUtils.isNotEmpty(applyInsuredBo.getBmi())) {
            BigDecimal bmi = new BigDecimal(applyInsuredBo.getBmi());
            if (new BigDecimal("16").compareTo(bmi) > 0 || bmi.compareTo(new BigDecimal("30")) > 0) {
                // BMI（体重指数）
                this.transApplyUnderwriteProblemPo(applyUnderwriteProblemPos,
                        this.filterBaseUnderwriteProblem(baseUnderwriteProblemPos, ApplyTermEnum.AUTO_UNDERWRITE_PROBLEM.INSURED_BMI_NOT_STANDARD.name()));
            }
        }
    }

    private void transUWApplicantBMI(ApplyApplicantBo applicant, List<BaseUnderwriteProblemPo> baseUnderwriteProblemPos, List<ApplyUnderwriteProblemPo> applyUnderwriteProblemPos) {
        if (AssertUtils.isNotEmpty(applicant.getBmi())) {
            BigDecimal bmi = new BigDecimal(applicant.getBmi());
            if (new BigDecimal("16").compareTo(bmi) > 0 || bmi.compareTo(new BigDecimal("30")) > 0) {
                // BMI（体重指数）
                this.transApplyUnderwriteProblemPo(applyUnderwriteProblemPos,
                        this.filterBaseUnderwriteProblem(baseUnderwriteProblemPos, ApplyTermEnum.AUTO_UNDERWRITE_PROBLEM.APPLICANT_BMI_NOT_STANDARD.name()));
            }
        }
    }

    private void transUWSingleAmount(Long applyDate, Long birthday, List<BaseUnderwriteProblemPo> baseUnderwriteProblemPos, List<ApplyUnderwriteProblemPo> applyUnderwriteProblemPos, List<PolicyCoverageBo> applyCoverageBoList,
                                     ApplyRiskAmountResponse applyRiskAmountResponse) {
        BigDecimal allNBSARAmount = applyRiskAmountResponse.getAllNBSAR();
        BigDecimal fiveNBSARAmount = applyRiskAmountResponse.getFiveNBSAR();
        boolean[] isFiveProductFlag = new boolean[1];
        if (AssertUtils.isNotNull(applyCoverageBoList)) {
            for (PolicyCoverageBo policyCoverageBo : applyCoverageBoList) {
                if (Arrays.asList(ApplyTermEnum.PRODUCT.PRODUCT_5.id(), ApplyTermEnum.PRODUCT.PRODUCT_28.id()).contains(policyCoverageBo.getProductId())) {
                    isFiveProductFlag[0] = true;
                }
            }
        }

        //根据被保人年龄，计算对应的保额上限
        int age = 0;
        try {
            age = DateUtils.getAgeYear(DateUtils.stringToDate(DateUtils.timeStrToString(birthday)), DateUtils.stringToDate(DateUtils.timeStrToString(applyDate)));
        } catch (Exception e) {
            e.printStackTrace();
        }

        log.info("该被保人年龄：{}. 累计保额：{}. 累计保额：{}", age, allNBSARAmount, fiveNBSARAmount);

        BigDecimal maximumAmount = BigDecimal.ZERO;
        if (isFiveProductFlag[0]) {
            //五号产品单独判断

            //年龄在18到45岁之间，保额超过80000就体检
            if (18 <= age && age <= 45) {
                maximumAmount = new BigDecimal("80000");
            }

            //年龄在46到55岁之间，保额超过25000就体检
            if (46 <= age && age <= 55) {
                maximumAmount = new BigDecimal("25000");
            }

            //年龄大于55岁的都要体检
            if (fiveNBSARAmount.compareTo(maximumAmount) > 0 || 55 < age) {
                // 累积五号产品保额体检表上限
                this.transApplyUnderwriteProblemPo(applyUnderwriteProblemPos,
                        this.filterBaseUnderwriteProblem(baseUnderwriteProblemPos, ApplyTermEnum.AUTO_UNDERWRITE_PROBLEM.MAXIMUM_AMOUNT_OF_A_SINGLE_POLICY.name()));
            }
        } else {
            //年龄小于等于17岁，保额超过15000就体检
            if (age <= 17) {
                maximumAmount = new BigDecimal("15000");
            }

            //年龄在18到45岁之间，保额超过50000就体检
            if (18 <= age && age <= 45) {
                maximumAmount = new BigDecimal("50000");
            }

            //年龄在46到50岁之间，保额超过15000就体检
            if (46 <= age && age <= 50) {
                maximumAmount = new BigDecimal("15000");
            }

            //年龄大于50岁的都要体检
            if (allNBSARAmount.compareTo(maximumAmount) > 0 || 50 < age) {
                // 累积基础保单保额上限
                this.transApplyUnderwriteProblemPo(applyUnderwriteProblemPos,
                        this.filterBaseUnderwriteProblem(baseUnderwriteProblemPos, ApplyTermEnum.AUTO_UNDERWRITE_PROBLEM.MAXIMUM_AMOUNT_OF_A_SINGLE_POLICY.name()));
            }
        }
    }


    private ApplyRiskAmountResponse transUWRiskAmount(String applyId, ApplyInsuredBo applyInsuredBo, List<BaseUnderwriteProblemPo> baseUnderwriteProblemPos, List<ApplyUnderwriteProblemPo> applyUnderwriteProblemPos, List<PolicyCoverageBo> applyCoverageBoList) {
        ApplyRiskAmountResponse applyRiskAmountResponse = this.getApplyRiskAmountResponse(applyInsuredBo.getCustomerId(), applyCoverageBoList, applyId);
        log.info("累计风险保额 - applyRiskAmountResponse:{}", JackSonUtils.toJson(applyRiskAmountResponse));

        // 累计高度残疾风险保额超过上限
        if (applyRiskAmountResponse.getSumTPDAmount().compareTo(new BigDecimal("500000")) > 0) {
            this.transApplyUnderwriteProblemPo(applyUnderwriteProblemPos,
                    this.filterBaseUnderwriteProblem(baseUnderwriteProblemPos, ApplyTermEnum.AUTO_UNDERWRITE_PROBLEM.ACCUMULATED_SUM_AT_RISK_OF_TPD_LIMIT.name()));
        }
        // 累计意外死亡风险保额超过上限
        if (applyRiskAmountResponse.getSumADDAmount().compareTo(new BigDecimal("300000")) > 0) {
            this.transApplyUnderwriteProblemPo(applyUnderwriteProblemPos,
                    this.filterBaseUnderwriteProblem(baseUnderwriteProblemPos, ApplyTermEnum.AUTO_UNDERWRITE_PROBLEM.ACCUMULATED_SUM_AT_RISK_OF_ADD_LIMIT.name()));
        }
        // 累计重大疾病风险保额超过上限
        if (applyRiskAmountResponse.getSumCIAmount().compareTo(new BigDecimal("100000")) > 0) {
            this.transApplyUnderwriteProblemPo(applyUnderwriteProblemPos,
                    this.filterBaseUnderwriteProblem(baseUnderwriteProblemPos, ApplyTermEnum.AUTO_UNDERWRITE_PROBLEM.ACCUMULATED_SUM_AT_RISK_OF_CI_LIMIT.name()));
        }
        return applyRiskAmountResponse;
    }

    private void transUWHealth(String applyId, List<BaseUnderwriteProblemPo> baseUnderwriteProblemPos, List<ApplyUnderwriteProblemPo> applyUnderwriteProblemPos) {
        List<ApplyHealthQuestionnaireAnswerBo> healthQuestionnaireBos = applyExtDao.loadHealthQuestionnaire(applyId);
        List<ApplyHealthQuestionnaireRemarkPo> applyHealthQuestionnaireRemarkPos = applyRemarkBaseService.listApplyHealthQuestionnaireRemark(applyId);
        boolean notPassUWHealthFlag = false;
        if (AssertUtils.isNotEmpty(healthQuestionnaireBos)) {
            Optional<ApplyHealthQuestionnaireAnswerBo> any = healthQuestionnaireBos.stream()
                    .filter(applyHealthQuestionnaireAnswerBo -> "Y".equals(applyHealthQuestionnaireAnswerBo.getAnswer())).findAny();
            if (any.isPresent()) {
                notPassUWHealthFlag = true;
            }
        }
        if (AssertUtils.isNotEmpty(applyHealthQuestionnaireRemarkPos)) {
            Optional<ApplyHealthQuestionnaireRemarkPo> any = applyHealthQuestionnaireRemarkPos.stream()
                    .filter(applyHealthQuestionnaireRemarkPo -> AssertUtils.isNotEmpty(applyHealthQuestionnaireRemarkPo.getRemark())).findAny();
            if (any.isPresent()) {
                notPassUWHealthFlag = true;
            }
        }
        if (notPassUWHealthFlag) {
            this.transApplyUnderwriteProblemPo(applyUnderwriteProblemPos,
                    this.filterBaseUnderwriteProblem(baseUnderwriteProblemPos, ApplyTermEnum.AUTO_UNDERWRITE_PROBLEM.HEALTH_NOTIFICATION_NOT_STANDARD.name()));
        }
    }

    @Override
    public ApplyRiskAmountResponse getApplyRiskAmountResponse(String customerAgentId, List<PolicyCoverageBo> applyCoverageBoList, String applyId) {
        //客户本次投保的险种
        if (!AssertUtils.isNotEmpty(applyCoverageBoList)) {
            applyCoverageBoList = applyCoverageBaseDao.queryApplyCoverageByInsured(applyId, customerAgentId);
        }
        if (AssertUtils.isNotNull(customerAgentId)) {
            //客户对应的有效保单
            List<PolicyCoverageBo> policyCoverageBoList = policyApi.queryAmountByCustomerId(customerAgentId).getData();
            if (AssertUtils.isNotEmpty(policyCoverageBoList)) {
                policyCoverageBoList.removeIf(policyCoverageBo -> policyCoverageBo.getApplyId().equals(applyId));
                if (AssertUtils.isNotEmpty(policyCoverageBoList)) {
                    applyCoverageBoList.addAll(policyCoverageBoList);
                }
            }
            //状态在待复核/待核保/复核中/核保中/待支付的被保人客户的其他投保单    通过接口去查相同客户号不同客户ID的数据（来自客户合并）
            ResultObject<List<CustomerAgentResponse>> listCustomerAgentObject = customerBaseApi.getCustomerRelationByCustomerAgentId(customerAgentId);
            AssertUtils.isResultObjectError(getLogger(), listCustomerAgentObject);
            List<CustomerAgentResponse> customerAgentResponses = new ArrayList<>(listCustomerAgentObject.getData());
            if (AssertUtils.isNotEmpty(customerAgentResponses)) {
                List<String> customerAgentIds = customerAgentResponses.stream().map(CustomerAgentResponse::getCustomerAgentId).distinct().collect(Collectors.toList());
                List<PolicyCoverageBo> policyCoverageBos = applyCoverageBaseDao.queryOtherApplyCoverageByInsured(applyId, customerAgentIds);
                if (AssertUtils.isNotEmpty(policyCoverageBos)) {
                    applyCoverageBoList.addAll(policyCoverageBos);
                }
            }
        }
        ApplyRiskAmountResponse applyRiskAmountResponse = new ApplyRiskAmountResponse();
        if (AssertUtils.isNotNull(applyCoverageBoList)) {
            for (PolicyCoverageBo policyCoverageBo : applyCoverageBoList) {
                this.calculationRiskAmount(policyCoverageBo, applyRiskAmountResponse);
            }
        }
        this.calculationWOPRiskAmount(applyRiskAmountResponse, applyCoverageBoList);
        return applyRiskAmountResponse;
    }

    /**
     * 智能核保通过,模拟人工核保通过处理,新增核保信息,保费计算处理
     *
     * @param userId  用户
     * @param applyId 投保单ID
     */
    private void simulateManualUW(String userId, String applyId) {
        //判断在人工核保任务表中是否有核保任务和客户任务，没有就新建任务
        ApplyUnderWriteTaskBo applyUnderWriteTaskBo = applyUnderWriteExtDao.loadUnderWriteTaskExist(applyId);
        if (!AssertUtils.isNotNull(applyUnderWriteTaskBo)) {
            //新建核保任务
            applyUnderWriteTaskBo = applyUnderWriteBoService.saveApplyUnderWriteTaskBo(userId, applyId);
            //查出被保人信息
            List<UnderWriteInsuredResponse> insuredResponses = applyUnderWriteExtDao.getInsuredInfo(applyId);
            if (AssertUtils.isNotEmpty(insuredResponses)) {
                List<String> insuredIds = new ArrayList<>();
                insuredResponses.forEach(insuredResponse -> {
                    if (AssertUtils.isNotEmpty(insuredResponse.getCustomerId())) {
                        insuredIds.add(insuredResponse.getCustomerId());
                    }
                });
                //新建客户任务
                if (insuredIds.size() > 0) {
                    applyUnderWriteBoService.saveCustomerTaskBo(applyUnderWriteTaskBo.getUnderwriteTaskId(), insuredIds, userId);
                }
            }
        }
        //获取核保任务中的投保单任务ID
        String underwriteTaskId = applyUnderWriteTaskBo.getUnderwriteTaskId();
        //核保决定Id
        String underwriteDecisionId = applyUnderWriteExtDao.loadUnderwriteDecisionByCode(ApplyTermEnum.DECISION_TYPE.STANDARD.name()).getUnderwriteDecisionId();
        //是否曾下发过核保决定,有改无增
        ApplyUnderwriteDecisionBo applyUnderwriteDecisionBo = applyUnderWriteExtDao.loadUnderwriteDecisionBoExist(underwriteTaskId);
        this.getLogger().info(JSONObject.toJSONString(applyUnderwriteDecisionBo));
        if (!AssertUtils.isNotNull(applyUnderwriteDecisionBo)) {
            applyUnderwriteDecisionBo = new ApplyUnderwriteDecisionBo();
            applyUnderwriteDecisionBo.setUnderwriteTaskId(underwriteTaskId);
            applyUnderwriteDecisionBo.setUnderwriteDecisionId(underwriteDecisionId);
        }
        applyUnderwriteDecisionBo.setRemarks("Auto Underwriting Passed");
        applyUnderwriteDecisionBo.setAuditDecisionDate(System.currentTimeMillis());
        //核保决定数据保存
        applyBoService.updateOrSaveApplyUnderwriteDecision(applyUnderwriteDecisionBo);

        //产生保费记录
        applyPremiumBaseService.saveApplyPremium(userId, applyId, PayNotifyTermEnum.PAY_NOTIFY_STATUS.PAYMENT_WAITTING.name());
        //保存核保通知书
        this.saveApplyUnderwriteNoticePo(userId, applyId);
    }

    private void saveApplyUnderwriteNoticePo(String userId, String applyId) {
        //保存核保通知书
        List<ApplyCoveragePo> applyCoveragePosOfInsured = applyCoverageBaseService.listApplyCoverageOfInsured(applyId);
        if (!AssertUtils.isNotEmpty(applyCoveragePosOfInsured)) {
            return;
        }
        Optional<ApplyCoveragePo> first = applyCoveragePosOfInsured.stream().filter(applyCoveragePo ->
                ApplyTermEnum.PRODUCT_PRIMARY_FLAG.MAIN.name().equals(applyCoveragePo.getPrimaryFlag()) &&
                        Arrays.asList(ApplyTermEnum.PRODUCT.PRODUCT_5.id(), ApplyTermEnum.PRODUCT.PRODUCT_28.id()).contains(applyCoveragePo.getProductId())
        ).findFirst();
        if (first.isPresent()) {
            ApplyUnderwriteNoticePo applyUnderwriteNoticePo = applyUnderwriteBaseService.queryApplyUnderwriteNoticePo(applyId);
            if (!AssertUtils.isNotNull(applyUnderwriteNoticePo)) {
                applyUnderwriteNoticePo = new ApplyUnderwriteNoticePo();
            }
            applyUnderwriteNoticePo.setApplyId(applyId);
            applyUnderwriteNoticePo.setPrintStatus(TerminologyConfigEnum.WHETHER.NO.name());
            applyUnderwriteBaseService.saveApplyUnderwriteNoticePo(applyUnderwriteNoticePo, userId);
        }
    }

    private BaseUnderwriteProblemPo filterBaseUnderwriteProblem(List<BaseUnderwriteProblemPo> baseUnderwriteProblemPos, String name) {
        return baseUnderwriteProblemPos.stream().filter(baseUnderwriteProblemPo -> baseUnderwriteProblemPo.getProblemCode().equals(name)).findFirst().get();
    }

    private void transApplyUnderwriteProblemPo(List<ApplyUnderwriteProblemPo> applyUnderwriteProblemPos, BaseUnderwriteProblemPo baseUnderwriteProblemPo) {
        ApplyUnderwriteProblemPo applyUnderwriteProblemPo = new ApplyUnderwriteProblemPo();
        applyUnderwriteProblemPo.setProblemId(baseUnderwriteProblemPo.getProblemId());
        applyUnderwriteProblemPo.setProblemCode(baseUnderwriteProblemPo.getProblemCode());
        applyUnderwriteProblemPo.setProblemDesc(baseUnderwriteProblemPo.getProblemDesc());
        applyUnderwriteProblemPo.setUnderwriteTaskType(ApplyTermEnum.UNDERWRITE_TASK_TYPE.AUTO_UNDERWRITING.name());
        applyUnderwriteProblemPos.add(applyUnderwriteProblemPo);
    }

    private BigDecimal calculationSingleAmount(PolicyCoverageBo policyCoverageBo) {
        String productId = policyCoverageBo.getProductId();
        String productLevel = policyCoverageBo.getProductLevel();
        String mult = policyCoverageBo.getMult();
        mult = AssertUtils.isNotEmpty(mult) ? mult : "1";
        BigDecimal coverageTotalAmount = AssertUtils.isNotEmpty(policyCoverageBo.getTotalAmount()) ? new BigDecimal(policyCoverageBo.getTotalAmount()) : new BigDecimal(0);

//        List<String> coverageTotalAmountProductIds = Arrays.asList(ProductTermEnum.PRODUCT.PRODUCT_4.id(), ProductTermEnum.PRODUCT.PRODUCT_8.id(),
//                ProductTermEnum.PRODUCT.PRODUCT_9.id(), ProductTermEnum.PRODUCT.PRODUCT_13.id(), ProductTermEnum.PRODUCT.PRODUCT_14.id(), ProductTermEnum.PRODUCT.PRODUCT_20.id(), ProductTermEnum.PRODUCT.PRODUCT_21.id());
        // #15产品不统计保额上限
        if (ProductTermEnum.PRODUCT.PRODUCT_15.id().equals(productId)) {
            return new BigDecimal(0);
        }
        return coverageTotalAmount;
    }


    private void calculationWOPRiskAmount(ApplyRiskAmountResponse applyRiskAmountResponse, List<PolicyCoverageBo> applyCoverageBoList) {
        if (!AssertUtils.isNotEmpty(applyCoverageBoList)) {
            return;
        }
        //豁免附加在什么险种就计算对应险种
        Map<String, List<PolicyCoverageBo>> map =
                applyCoverageBoList.parallelStream().collect(Collectors.groupingBy(PolicyCoverageBo::getInsuredId));
        for (String s : map.keySet()) {
            List<PolicyCoverageBo> policyCoverageBos = map.get(s);
            //每个投保单/保单的新业务核保的累计风险保额（豁免保费）
            BigDecimal applyWopNBSAR = BigDecimal.ZERO;
            BigDecimal applyWopAllNBSAR = BigDecimal.ZERO;
            //找到16号产品
            Optional<PolicyCoverageBo> first = policyCoverageBos.stream().filter(policyCoverageBo -> Arrays.asList(ProductTermEnum.PRODUCT.PRODUCT_16A.id(), ProductTermEnum.PRODUCT.PRODUCT_16B.id()).contains(policyCoverageBo.getProductId())).findFirst();
            if (first.isPresent()) {
                PolicyCoverageBo pro16 = first.get();
                String productLevel = pro16.getProductLevel();
                for (PolicyCoverageBo policyCoverageBo : policyCoverageBos) {
                    //WOP风  险保额 = 0.5 * 保险期限 * 年缴保费
                    if (Arrays.asList("OPTION_ONE", "OPTION_TWO").contains(productLevel)
                            && Arrays.asList(ProductTermEnum.PRODUCT.PRODUCT_13.id(), ProductTermEnum.PRODUCT.PRODUCT_15.id(), ProductTermEnum.PRODUCT.PRODUCT_8.id(), ProductTermEnum.PRODUCT.PRODUCT_21.id(), ProductTermEnum.PRODUCT.PRODUCT_24.id()).contains(policyCoverageBo.getProductId())) {
                        applyWopNBSAR = applyWopNBSAR.add(this.calculationWOPAmount(policyCoverageBo));
                        applyWopAllNBSAR = applyWopAllNBSAR.add(this.calculationWOPAmount(policyCoverageBo));
                    }
                    if (Arrays.asList("OPTION_THREE").contains(productLevel)
                            && Arrays.asList(ProductTermEnum.PRODUCT.PRODUCT_13.id(), ProductTermEnum.PRODUCT.PRODUCT_14.id(), ProductTermEnum.PRODUCT.PRODUCT_15.id(),
                            ProductTermEnum.PRODUCT.PRODUCT_9.id(), ProductTermEnum.PRODUCT.PRODUCT_8.id(), ProductTermEnum.PRODUCT.PRODUCT_4.id(),
                            ProductTermEnum.PRODUCT.PRODUCT_21.id(), ProductTermEnum.PRODUCT.PRODUCT_19.id(), ProductTermEnum.PRODUCT.PRODUCT_24.id()).contains(policyCoverageBo.getProductId())) {
                        applyWopNBSAR = applyWopNBSAR.add(this.calculationWOPAmount(policyCoverageBo));
                    }
                }
            }
            applyRiskAmountResponse.setWopNBSAR(applyRiskAmountResponse.getWopNBSAR().add(applyWopNBSAR));
            applyRiskAmountResponse.setAllNBSAR(applyRiskAmountResponse.getAllNBSAR().add(applyWopAllNBSAR));
        }
    }

    private BigDecimal calculationWOPAmount(PolicyCoverageBo policyCoverageBo) {
        //WOP风险保额 = 0.5 * 保险期限 * 年缴保费
        BigDecimal coveragePeriod = BigDecimal.ZERO;

        if ("YEAR".equals(policyCoverageBo.getCoveragePeriodUnit())) {
            coveragePeriod = new BigDecimal(policyCoverageBo.getCoveragePeriod());
        }

        BigDecimal conversionFactor = BigDecimal.valueOf(ApplyTermEnum.PREMIUM_FREQUENCY_YEAR_FACTOR.valueOf(policyCoverageBo.getPremiumFrequency()).value());
        BigDecimal yearTotalPPremium = policyCoverageBo.getTotalPremium().multiply(conversionFactor);
        return new BigDecimal("0.5").multiply(coveragePeriod).multiply(yearTotalPPremium).setScale(2, BigDecimal.ROUND_HALF_UP);
    }

    private void calculationRiskAmount(PolicyCoverageBo policyCoverageBo, ApplyRiskAmountResponse applyRiskAmountResponse) {
        Long frequency = policyCoverageBo.getFrequency();
        String productId = policyCoverageBo.getProductId();
        String productLevel = policyCoverageBo.getProductLevel();
        String mult = policyCoverageBo.getMult();
        mult = AssertUtils.isNotEmpty(mult) ? mult : "1";
        BigDecimal coverageTotalAmount = AssertUtils.isNotEmpty(policyCoverageBo.getTotalAmount()) ? new BigDecimal(policyCoverageBo.getTotalAmount()) : new BigDecimal(0);
        if (Arrays.asList(ApplyTermEnum.PRODUCT.PRODUCT_5.id(), ApplyTermEnum.PRODUCT.PRODUCT_28.id()).contains(productId)) {
            applyRiskAmountResponse.setBasicNBSAR(applyRiskAmountResponse.getBasicNBSAR().add(coverageTotalAmount));
            applyRiskAmountResponse.setFiveNBSAR(applyRiskAmountResponse.getFiveNBSAR().add(coverageTotalAmount));
        }
        // 注释16A/16B，其保额在豁免保额方法里会计算
        if (productId.equals(ProductTermEnum.PRODUCT.PRODUCT_3.id())/* || productId.equals(ProductTermEnum.PRODUCT.PRODUCT_16A.id()) || productId.equals(ProductTermEnum.PRODUCT.PRODUCT_16B.id())*/) {
            applyRiskAmountResponse.setAllNBSAR(applyRiskAmountResponse.getAllNBSAR().add(coverageTotalAmount));
        }
        if (productId.equals(ProductTermEnum.PRODUCT.PRODUCT_8.id())) {
            applyRiskAmountResponse.setSumTPDAmount(applyRiskAmountResponse.getSumTPDAmount().add(coverageTotalAmount));
            applyRiskAmountResponse.setSumADDAmount(applyRiskAmountResponse.getSumADDAmount().add(coverageTotalAmount.multiply(new BigDecimal(2))));
            applyRiskAmountResponse.setBasicNBSAR(applyRiskAmountResponse.getBasicNBSAR().add(coverageTotalAmount));
            applyRiskAmountResponse.setAllNBSAR(applyRiskAmountResponse.getAllNBSAR().add(coverageTotalAmount));
        }
        if (productId.equals(ProductTermEnum.PRODUCT.PRODUCT_9.id())) {
            applyRiskAmountResponse.setSumTPDAmount(applyRiskAmountResponse.getSumTPDAmount().add(coverageTotalAmount));
            applyRiskAmountResponse.setSumADDAmount(applyRiskAmountResponse.getSumADDAmount().add(coverageTotalAmount.multiply(new BigDecimal(2))));
            applyRiskAmountResponse.setBasicNBSAR(applyRiskAmountResponse.getBasicNBSAR().add(coverageTotalAmount));
            applyRiskAmountResponse.setAllNBSAR(applyRiskAmountResponse.getAllNBSAR().add(coverageTotalAmount));
        }
        if (productId.equals(ProductTermEnum.PRODUCT.PRODUCT_13.id())) {
            applyRiskAmountResponse.setSumTPDAmount(applyRiskAmountResponse.getSumTPDAmount().add(coverageTotalAmount));
            applyRiskAmountResponse.setBasicNBSAR(applyRiskAmountResponse.getBasicNBSAR().add(coverageTotalAmount));
            applyRiskAmountResponse.setAllNBSAR(applyRiskAmountResponse.getAllNBSAR().add(coverageTotalAmount));
        }
        if (productId.equals(ProductTermEnum.PRODUCT.PRODUCT_15.id())) {
            applyRiskAmountResponse.setSumADDAmount(applyRiskAmountResponse.getSumADDAmount().add(coverageTotalAmount));
        }
        if (productId.equals(ProductTermEnum.PRODUCT.PRODUCT_4.id()) || productId.equals(ProductTermEnum.PRODUCT.PRODUCT_14.id())) {
            applyRiskAmountResponse.setSumCIAmount(applyRiskAmountResponse.getSumCIAmount().add(coverageTotalAmount));
            applyRiskAmountResponse.setCiNBSAR(applyRiskAmountResponse.getCiNBSAR().add(coverageTotalAmount));
            applyRiskAmountResponse.setAllNBSAR(applyRiskAmountResponse.getAllNBSAR().add(coverageTotalAmount));
        }
        if (productId.equals(ProductTermEnum.PRODUCT.PRODUCT_20.id())) {
            applyRiskAmountResponse.setSumTPDAmount(applyRiskAmountResponse.getSumTPDAmount().add(coverageTotalAmount));
            applyRiskAmountResponse.setSumADDAmount(applyRiskAmountResponse.getSumADDAmount().add(coverageTotalAmount));
            applyRiskAmountResponse.setBasicNBSAR(applyRiskAmountResponse.getBasicNBSAR().add(coverageTotalAmount));
            applyRiskAmountResponse.setAllNBSAR(applyRiskAmountResponse.getAllNBSAR().add(coverageTotalAmount));
        }
        if (productId.equals(ProductTermEnum.PRODUCT.PRODUCT_21.id())) {
            applyRiskAmountResponse.setSumTPDAmount(applyRiskAmountResponse.getSumTPDAmount().add(coverageTotalAmount));
            applyRiskAmountResponse.setSumCIAmount(applyRiskAmountResponse.getSumCIAmount().add(coverageTotalAmount));
            applyRiskAmountResponse.setBasicNBSAR(applyRiskAmountResponse.getBasicNBSAR().add(coverageTotalAmount));
            applyRiskAmountResponse.setAllNBSAR(applyRiskAmountResponse.getAllNBSAR().add(coverageTotalAmount));
//            applyRiskAmountResponse.setCiNBSAR(applyRiskAmountResponse.getCiNBSAR().add(coverageTotalAmount));
        }
        if (productId.equals(ProductTermEnum.PRODUCT.PRODUCT_19.id())) {
            applyRiskAmountResponse.setSumTPDAmount(applyRiskAmountResponse.getSumTPDAmount().add(coverageTotalAmount));
            applyRiskAmountResponse.setBasicNBSAR(applyRiskAmountResponse.getBasicNBSAR().add(coverageTotalAmount));
            applyRiskAmountResponse.setAllNBSAR(applyRiskAmountResponse.getAllNBSAR().add(coverageTotalAmount));
        }
        if (productId.equals(ProductTermEnum.PRODUCT.PRODUCT_22.id())) {
            applyRiskAmountResponse.setSumCIAmount(applyRiskAmountResponse.getSumCIAmount().add(coverageTotalAmount));
            applyRiskAmountResponse.setCiNBSAR(applyRiskAmountResponse.getCiNBSAR().add(coverageTotalAmount));
            applyRiskAmountResponse.setAllNBSAR(applyRiskAmountResponse.getAllNBSAR().add(coverageTotalAmount));
        }
        if (Arrays.asList(ProductTermEnum.PRODUCT.PRODUCT_24.id()).contains(productId)) {
            if ("OPTION_ONE".equals(productLevel)) {
                applyRiskAmountResponse.setSumTPDAmount(applyRiskAmountResponse.getSumTPDAmount().add(coverageTotalAmount));
            }
            if ("OPTION_TWO".equals(productLevel)) {
//                applyRiskAmountResponse.setCiNBSAR(applyRiskAmountResponse.getCiNBSAR().add(coverageTotalAmount));
                applyRiskAmountResponse.setSumCIAmount(applyRiskAmountResponse.getSumCIAmount().add(coverageTotalAmount));
            }
            applyRiskAmountResponse.setBasicNBSAR(applyRiskAmountResponse.getBasicNBSAR().add(coverageTotalAmount));
            applyRiskAmountResponse.setAllNBSAR(applyRiskAmountResponse.getAllNBSAR().add(coverageTotalAmount));
        }
        if (Arrays.asList(ApplyTermEnum.PRODUCT.PRODUCT_23A.id(), ApplyTermEnum.PRODUCT.PRODUCT_23B.id()).contains(productId)) {
            if ("OPTION_ONE".equals(productLevel)) {
                applyRiskAmountResponse.setSumTPDAmount(applyRiskAmountResponse.getSumTPDAmount().add(coverageTotalAmount));
            }
            if ("OPTION_TWO".equals(productLevel)) {
                applyRiskAmountResponse.setCiNBSAR(applyRiskAmountResponse.getCiNBSAR().add(coverageTotalAmount));
                applyRiskAmountResponse.setSumCIAmount(applyRiskAmountResponse.getSumCIAmount().add(coverageTotalAmount));
            }
            applyRiskAmountResponse.setAllNBSAR(applyRiskAmountResponse.getAllNBSAR().add(coverageTotalAmount));
        }
    }
}
