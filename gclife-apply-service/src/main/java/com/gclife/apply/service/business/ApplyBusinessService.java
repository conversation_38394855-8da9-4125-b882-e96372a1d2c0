package com.gclife.apply.service.business;

import com.gclife.apply.model.bo.ApplyListBo;
import com.gclife.apply.model.request.ApplyConditionListRequest;
import com.gclife.apply.model.request.ApplyConditionPrintRequest;
import com.gclife.apply.model.request.ApplyListRequest;
import com.gclife.apply.model.request.CalculationBmiAndAgeRequest;
import com.gclife.apply.model.respone.TemporaryCoverResponse;
import com.gclife.apply.model.response.*;
import com.gclife.common.model.BasePageResponse;
import com.gclife.common.model.ResultObject;
import com.gclife.common.model.base.Users;
import com.gclife.common.model.feign.SyscodeRespFc;
import com.gclife.common.model.pojo.AppRequestHeads;
import com.gclife.common.service.BaseBusinessService;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.List;

/**
 * <AUTHOR>
 * create 18-4-23
 * description:
 */
public interface ApplyBusinessService extends BaseBusinessService {
    /**
     * 查询投保单列表
     *
     * @param users            用户
     * @param applyListRequest 列表请求参数
     * @return 投保单列表
     */
    ResultObject<BasePageResponse<ApplyQueryListResponse>> getApplyQueryList(Users users, ApplyListRequest applyListRequest);

    /**
     * 投保单详情
     *
     * @param applyId 投保单id
     * @return 投保单详情
     */
    ResultObject<ApplyDetailResponse> loadApplyDetail(String applyId, AppRequestHeads appRequestHeads);

    /**
     * 多条件查询投保单
     *
     * @param users                     用户
     * @param applyConditionListRequest 请求参数
     * @return 列表
     */
    ResultObject<BasePageResponse<ApplyQueryListResponse>> postApplyList(Users users, ApplyConditionListRequest applyConditionListRequest);

    /**
     * 投保单状态枚举
     *
     * @return List<SyscodeRespFc>
     */
    ResultObject<List<SyscodeRespFc>> getApplyStatusList();

    /**
     * 查询投保单列表
     *
     * @param currentLoginUsers
     * @param applyListRequest
     * @return
     */
    ResultObject<BasePageResponse<ApplyQueryListResponse>> loadAgentApplyList(Users currentLoginUsers, ApplyListRequest applyListRequest);

    /**
     * 查询健康告知详情(核心投保单详情调用)
     *
     * @param applyId  投保单id
     * @param language 语言
     * @return
     */
    ResultObject<List<ApplyQuestionnaireResponse>> queryApplyQuestionnaireDetailGet(String applyId, String language);

    /**
     * 查询暂保单详情(暂保条款)
     * @param applyId 投保ID
     * @return
     */
    ResultObject<TemporaryCoverResponse> queryTemporaryCover(String applyId);

    /**
     * 暂保单打印
     * @param applyId 投保ID
     * @param language 语言
     * @return
     */
    ResultObject printTemporaryCover(String applyId, String language, HttpServletResponse response) throws IOException;

    /**
     * 系统预警查询已承保的投保单列表
     * @return 投保单列表
     */
    ResultObject<List<ApplyListBo>> getSystemWarningApplyQueryList();


    /**
     * 系统预警查询支付的投保单列表
     * @return 投保单列表
     */
    ResultObject<List<ApplyListBo>> getSystemWarningApplyPaymentList();

    /**
     * 系统预警查询支付的投保单
     * @return 投保单
     */
    ResultObject<ApplyListBo> getSystemWarningApplyPayment(String applyId);

    /**
     * 投保单打印(仅针对#13号及其附加险)
     * @param printInfoId 投保ID
     * @param language 语言
     * @return
     */
    ResultObject printApply(String printInfoId, String language, HttpServletResponse response) throws IOException;

    /**
     * 查询投保单打印列表(仅针对#13号及其附加险)
     * @param currentLoginUsers
     * @param applyConditionPrintRequest
     * @return
     */
    ResultObject<BasePageResponse<ApplyPrintResponse>> getPrintApplyList(Users currentLoginUsers, ApplyConditionPrintRequest applyConditionPrintRequest);

    /**
     * 投保单打印完成(仅针对#13号及其附加险)
     * @param printInfoId
     * @param language
     * @return
     */
    ResultObject applyPrintFileComplete(String printInfoId, String language);

    /**
     * 投保单打印结束(仅针对#13号及其附加险)
     * @param printInfoId
     * @param currentLoginUsers
     * @return
     */
    ResultObject applyPrintFinish(Users currentLoginUsers,String printInfoId);

    /**
     * 计算bmi和年龄接口
     * @param calculationBmiAndAgeRequest
     * @return
     */
    ResultObject<CalculationBmiAndAgeResponse> calculationBmiAndAge(CalculationBmiAndAgeRequest calculationBmiAndAgeRequest);
}
