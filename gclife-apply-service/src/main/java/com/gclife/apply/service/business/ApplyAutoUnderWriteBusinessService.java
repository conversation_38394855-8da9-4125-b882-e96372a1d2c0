package com.gclife.apply.service.business;

import com.gclife.apply.model.respone.ApplyAutoUWResponse;
import com.gclife.apply.model.response.ApplyRiskAmountResponse;
import com.gclife.attachment.model.policy.policy.PolicyCoverageBo;
import com.gclife.common.model.base.Users;
import com.gclife.common.model.pojo.AppRequestHeads;
import com.gclife.common.service.BaseBusinessService;

import java.util.List;

/**
 * <AUTHOR>
 * create 2021/6/4 14:31
 * description:智能核保
 */
public interface ApplyAutoUnderWriteBusinessService extends BaseBusinessService {
    /**
     * 发起智能核保
     *
     * @param applyId         投保单ID
     * @param users           用户
     * @param appRequestHeads 请求头
     * @param uwType
     * @return 是否通过智能核保
     */
    ApplyAutoUWResponse initiateAutoUnderWriting(String applyId, Users users, AppRequestHeads appRequestHeads, String uwType);

    ApplyRiskAmountResponse getApplyRiskAmountResponse(String customerAgentId, List<PolicyCoverageBo> applyCoverageBoList, String applyId);
}
