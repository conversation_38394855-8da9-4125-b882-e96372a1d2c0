package com.gclife.apply.service.business.impl;

import com.alibaba.fastjson.JSON;
import com.gclife.apply.core.jooq.tables.pojos.ApplyAddPremiumPo;
import com.gclife.apply.core.jooq.tables.pojos.ApplyElectronicSignaturePo;
import com.gclife.apply.core.jooq.tables.pojos.ApplyUnderwriteDecisionPo;
import com.gclife.apply.core.jooq.tables.pojos.ScratchCardPo;
import com.gclife.apply.dao.ApplyPlanBaseDao;
import com.gclife.apply.model.bo.ApplyBo;
import com.gclife.apply.model.bo.ApplyInsuredBo;
import com.gclife.apply.model.bo.ApplyPremiumBo;
import com.gclife.apply.model.bo.app.ApplyPlanBo;
import com.gclife.apply.model.bo.card.ScratchCardAssignBo;
import com.gclife.apply.model.config.ApplyTermEnum;
import com.gclife.apply.service.ApplyUnderwriteBaseService;
import com.gclife.apply.service.ScratchCardBaseService;
import com.gclife.apply.service.business.MessageBusinessService;
import com.gclife.common.model.ResultObject;
import com.gclife.common.model.base.Users;
import com.gclife.common.model.config.TerminologyConfigEnum;
import com.gclife.common.model.pojo.AppRequestHeads;
import com.gclife.common.service.impl.BaseBusinessServiceImpl;
import com.gclife.common.util.AssertUtils;
import com.gclife.common.util.DateUtils;
import com.gclife.message.api.BusinessMessageApi;
import com.gclife.message.model.request.BusinessMessagePushRequest;
import com.gclife.message.model.request.ReceiverUserRequest;
import com.gclife.platform.api.PlatformInternationalBaseApi;
import com.gclife.platform.api.PlatformUsersApi;
import com.gclife.platform.model.response.SyscodeResponse;
import com.gclife.product.api.ProductApi;
import com.gclife.product.model.config.ProductTermEnum;
import com.gclife.product.model.response.insurnce.ProductSimpleInfoResponse;
import com.gclife.thirdparty.api.ThirdpartyShortUrlApi;
import com.gclife.thirdparty.model.config.ThirdpartyTermEnum;
import com.gclife.thirdparty.model.request.ShortUrlRequest;
import com.gclife.thirdparty.model.response.ShortUrlResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.*;

/**
 * <AUTHOR>
 * @version v1.0
 * Description: 消息处理
 * @date 18-4-13
 */
@Service
public class MessageBusinessServiceImpl extends BaseBusinessServiceImpl implements MessageBusinessService {
    @Autowired
    private BusinessMessageApi businessMessageApi;
    @Autowired
    private PlatformUsersApi platformUsersApi;
    @Autowired
    private PlatformInternationalBaseApi platformInternationalBaseApi;

    @Autowired
    private ProductApi productApi;

    @Autowired
    private ThirdpartyShortUrlApi thirdpartyShortUrlApi;

    @Autowired
    private ApplyUnderwriteBaseService applyUnderwriteBaseService;

    @Autowired
    private ApplyPlanBaseDao applyPlanBaseDao;
    @Autowired
    private ScratchCardBaseService scratchCardBaseService;

    /**
     * 投保消息发送(单个用户)
     *
     * @param businessCode 消息类型
     * @param applyBo      投保单对象
     * @param userId       用户ID
     */
    @Override
    public void pushApplyMessageSingle(String businessCode, ApplyBo applyBo, String userId) {
        try {
            this.getLogger().error("消息发送入口 投保单对象applyBo:" + JSON.toJSONString(applyBo));
            List<String> userIds = new ArrayList<>();
            userIds.add(userId);
            // 消息发送
            pushBusinessMessage(businessCode, userIds, transferMessageParam(applyBo));
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    /**
     * 发送预交加费总保费消息发送(单个用户)
     *
     * @param businessCode 消息类型
     * @param applyBo      投保单对象
     * @param userId       用户ID
     */
    @Override
    public void pushApplyMessagePreAllSingle(String businessCode, ApplyBo applyBo, String userId) {
        try {
            this.getLogger().error("消息发送入口 投保单对象applyBo:" + JSON.toJSONString(applyBo));
            List<String> userIds = new ArrayList<>();
            userIds.add(userId);
            // 消息发送
            pushBusinessMessage(businessCode, userIds, transferMessageParamNew(applyBo));
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    /**
     * 投保消息发送(单个用户)
     *
     * @param businessCode 消息类型
     * @param applyBo      投保单对象
     * @param userId       用户ID
     */
    @Override
    @Async
    public void pushApplyMessageSingleNew(String businessCode, ApplyBo applyBo, String userId, BigDecimal paymentAmount) {
        try {
            this.getLogger().error("消息发送入口 投保单对象applyBo:" + JSON.toJSONString(applyBo));
            List<String> userIds = new ArrayList<>();
            userIds.add(userId);
            // 消息发送
            pushBusinessMessage(businessCode, userIds, transferMessageParamNew(applyBo, paymentAmount));
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    @Override
    @Async
    public void pushApplyMessageSingle(String businessCode, ApplyPlanBo applyPlanBo, String userId, Map<String, String> map) {
        try {
            this.getLogger().error("消息发送入口 计划书对象applyPlanBo:" + JSON.toJSONString(applyPlanBo));
            List<String> userIds = new ArrayList<>();
            userIds.add(userId);
            // 消息发送
            pushBusinessMessage(businessCode, userIds, transferApplyPlanMessageParam(applyPlanBo, map));
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    @Override
    public void pushApplyMessageSingle(String businessCode, ApplyBo applyBo, String userId, Map<String, String> map) {
        try {
            this.getLogger().error("消息发送入口 投保单对象applyBo:" + JSON.toJSONString(applyBo));
            List<String> userIds = new ArrayList<>();
            userIds.add(userId);
            // 消息发送
            pushBusinessMessage(businessCode, userIds, transferMessageMapParam(applyBo, map));
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    /**
     * 投保消息发送(多个用户)
     *
     * @param businessCode 消息类型
     * @param applyBo      投保单对象
     */
    @Override
    public void pushApplyMessageBatch(String businessCode, ApplyBo applyBo) {
        try {
            this.getLogger().error("applyBo2:" + JSON.toJSONString(applyBo));
            List<String> userIds = platformUsersApi.getBusinessUsers(businessCode, applyBo.getSalesBranchId()).getData();
            // 消息发送
            pushBusinessMessage(businessCode, userIds, transferMessageParam(applyBo));
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    /**
     * 投保消息发送(多个用户)
     *
     * @param businessCode 消息类型
     * @param applyBo      投保单对象
     */
    @Override
    public void pushApplyMessageBatchNew(String businessCode, ApplyBo applyBo) {
        try {
            this.getLogger().error("applyBo2:" + JSON.toJSONString(applyBo));
            List<String> userIds = platformUsersApi.getBusinessUsers(businessCode, applyBo.getSalesBranchId()).getData();
            // 消息发送
            pushBusinessMessage(businessCode, userIds, transferMessageParamNew(applyBo));
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    /**
     * 投保消息发送(多个用户)
     *
     * @param businessCode 消息类型
     * @param applyBo      投保单对象
     */
    @Override
    @Async
    public void pushApplyMessageBatch(String businessCode, ApplyBo applyBo, BigDecimal paymentAmount) {
        try {
            this.getLogger().error("applyBo2:" + JSON.toJSONString(applyBo));
            List<String> userIds = platformUsersApi.getBusinessUsers(businessCode, applyBo.getSalesBranchId()).getData();
            // 消息发送
            pushBusinessMessage(businessCode, userIds, transferMessageParam(applyBo, paymentAmount));
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    /**
     * 问题件回退消息发送
     *
     * @param businessCode    消息类型
     * @param applyBo         投保单对象
     * @param userId          用户ID
     * @param currentCodeName 当前结点名
     */
    @Override
    @Async
    public void pushApplyMessageSingle(String businessCode, ApplyBo applyBo, String userId, String currentCodeName) {
        try {
            this.getLogger().error("applyBo3:" + JSON.toJSONString(applyBo));
            List<String> userIds = new ArrayList<>();
            userIds.add(userId);
            // 消息发送
            pushBusinessMessage(businessCode, userIds, transferMessageParam(applyBo, currentCodeName));
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    @Override
    @Async
    public void pushApplyMessageSingleToApplicant(String businessCode, ApplyBo applyBo) {
        System.out.println("===================================发送8==============================================");
        try {
            System.out.println("pushApplyMessageSingleToApplicant ==================================");
            System.out.println(JSON.toJSONString(applyBo));
            System.out.println("pushApplyMessageSingleToApplicant ==================================");
            if (AssertUtils.isNotNull(applyBo.getApplicant()) && AssertUtils.isNotEmpty(applyBo.getApplicant().getMobile())) {
                BusinessMessagePushRequest businessMessagePushReqFc = new BusinessMessagePushRequest();
                businessMessagePushReqFc.setBusinessCode(businessCode);
                businessMessagePushReqFc.setMessageParam(JSON.toJSONString(transferMessageParam(applyBo)));
                String language = TerminologyConfigEnum.LANGUAGE.EN_US.name();
                if (businessCode.equals(ApplyTermEnum.MSG_BUSINESS_TYPE.POLICY_UNDERWRITTEN_TO_CUSTOMER_ONLINE.name()) && AssertUtils.isNotEmpty(applyBo.getOnlineLanguage())) {
                    language = applyBo.getOnlineLanguage();
                }
                businessMessagePushReqFc.setLanguage(language);
                businessMessagePushReqFc.setDeviceChannel("gclife_agent_app");
                List<ReceiverUserRequest> receiverUserRequests = new ArrayList<>();
                ReceiverUserRequest receiverUserRequest = new ReceiverUserRequest();
                receiverUserRequest.setMsgChannel("SMS");
                receiverUserRequest.setReceiverUser(applyBo.getApplicant().getMobile());
                receiverUserRequests.add(receiverUserRequest);
                businessMessagePushReqFc.setReceiverUsers(receiverUserRequests);
                this.getLogger().error("pushApplyMessageSingleToApplicant businessMessagePushReqFc:" + JSON.toJSONString(businessMessagePushReqFc));
                businessMessageApi.pushBusinessMessage(businessMessagePushReqFc);
            }

        } catch (Exception e) {
            System.out.println("pushApplyMessageSingleToApplicant ====error==============================");
            e.printStackTrace();
        }
    }

    /**
     * 发送消息给客户
     *
     * @param businessCode    消息类型
     * @param applyBo         投保单数据
     * @param users           当前用户
     * @param appRequestHeads 请求头
     */
    @Override
    public void pushApplyMessageCustomer(String businessCode, ApplyBo applyBo, Users users, AppRequestHeads appRequestHeads) {
        try {
            Map<String, String> map = new HashMap<>();
            map.put("applyNo", applyBo.getApplyNo());
            map.put("premium", applyBo.getReceivablePremium().toString());
            if (AssertUtils.isNotNull(applyBo.getApplicant())) {
                map.put("applicantName", applyBo.getApplicant().getName());
            }
            if (AssertUtils.isNotNull(applyBo.getApplyPremiumBo()) && AssertUtils.isNotNull(applyBo.getApplyPremiumBo().getReceivableDate())) {
                map.put("receivableDate", DateUtils.timeStrToString(applyBo.getApplyPremiumBo().getReceivableDate(), "yyyy-MM-dd"));
            } else {
                map.put("receivableDate", DateUtils.timeStrToString(DateUtils.getCurrentTime(), DateUtils.FORMATE3));
            }

            if (AssertUtils.isNotEmpty(applyBo.getListInsured()) && AssertUtils.isNotEmpty(applyBo.getListInsured().get(0).getListCoverage())) {
                applyBo.getListInsured().get(0).getListCoverage().forEach(applyCoverageBo -> {
                    if (applyCoverageBo.getPrimaryFlag().equals(ApplyTermEnum.PRODUCT_PRIMARY_FLAG.MAIN.name())) {
                        map.put("productName", applyCoverageBo.getProductName());
                    }
                });
            }
            transPaymentShortUrl(applyBo, map);

            BusinessMessagePushRequest businessMessagePushReqFc = new BusinessMessagePushRequest();
            businessMessagePushReqFc.setBusinessCode(businessCode);
            businessMessagePushReqFc.setMessageParam(JSON.toJSONString(map));
            businessMessagePushReqFc.setDeviceChannel(appRequestHeads.getDeviceChannel());
            String language = TerminologyConfigEnum.LANGUAGE.EN_US.name();
            if (businessCode.equals(ApplyTermEnum.MSG_BUSINESS_TYPE.PASSED_THE_UNDERWRITING_OF_THE_NEW_SINGLE_INSURANCE_POLICY_ONLINE_CUSTOMER.name())
                    && AssertUtils.isNotEmpty(applyBo.getOnlineLanguage())) {
                language = applyBo.getOnlineLanguage();
            }
            businessMessagePushReqFc.setLanguage(language);

            List<ReceiverUserRequest> receiverUsers = new ArrayList<>();
            ReceiverUserRequest receiverUserRequest = new ReceiverUserRequest();
            receiverUserRequest.setMsgChannel("SMS");
            if (AssertUtils.isNotNull(applyBo.getApplicant())) {
                receiverUserRequest.setReceiverUser(applyBo.getApplicant().getMobile());
            }
            receiverUsers.add(receiverUserRequest);
            businessMessagePushReqFc.setReceiverUsers(receiverUsers);

            this.getLogger().error("businessMessagePushReqFc:" + JSON.toJSONString(businessMessagePushReqFc));

            businessMessageApi.pushBusinessMessage(businessMessagePushReqFc);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    @Override
    public void pushApplySignatureMessageApplicant(String businessCode, ApplyBo applyBo, Users users) {
        try {
            Map<String, String> map = new HashMap<>();
            if (AssertUtils.isNotNull(applyBo.getApplicant())) {
                map.put("applicantName", applyBo.getApplicant().getName());
            }

            if (AssertUtils.isNotEmpty(applyBo.getListInsured()) && AssertUtils.isNotEmpty(applyBo.getListInsured().get(0).getListCoverage())) {
                applyBo.getListInsured().get(0).getListCoverage().forEach(applyCoverageBo -> {
                    if (applyCoverageBo.getPrimaryFlag().equals(ApplyTermEnum.PRODUCT_PRIMARY_FLAG.MAIN.name())) {
                        map.put("productName", applyCoverageBo.getProductName());
                    }
                });
            }

            //查询applyBo
            //ApplyBo applyBo = applyBaseService.queryApply(applyId);

            ApplyElectronicSignaturePo applyElectronicSignaturePo = applyPlanBaseDao.querySignature(applyBo.getApplyId());

            if (AssertUtils.isNotNull(applyElectronicSignaturePo)) {
                String applicantSignatureUrl = applyElectronicSignaturePo.getApplicantSignatureUrl();
                if (AssertUtils.isNotNull(applicantSignatureUrl)) {
                    map.put("signatureUrl", applicantSignatureUrl);
                }
            }

            BusinessMessagePushRequest businessMessagePushReqFc = new BusinessMessagePushRequest();
            businessMessagePushReqFc.setBusinessCode(businessCode);
            businessMessagePushReqFc.setMessageParam(JSON.toJSONString(map));
            //businessMessagePushReqFc.setDeviceChannel(appRequestHeads.getDeviceChannel());
            businessMessagePushReqFc.setLanguage(TerminologyConfigEnum.LANGUAGE.EN_US.name());

            List<ReceiverUserRequest> receiverUsers = new ArrayList<>();
            ReceiverUserRequest receiverUserRequest = new ReceiverUserRequest();
            receiverUserRequest.setMsgChannel("SMS");
            if (AssertUtils.isNotNull(applyBo.getApplicant())) {
                receiverUserRequest.setReceiverUser(applyBo.getApplicant().getMobile());
            }
            if (AssertUtils.isNotNull(applyBo.getApplicant().getDelegateMobile())) {
                receiverUserRequest.setReceiverUser(applyBo.getApplicant().getDelegateMobile());
            }
            receiverUsers.add(receiverUserRequest);
            businessMessagePushReqFc.setReceiverUsers(receiverUsers);

            this.getLogger().error("businessMessagePushReqFc:" + JSON.toJSONString(businessMessagePushReqFc));

            businessMessageApi.pushBusinessMessage(businessMessagePushReqFc);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    @Override
    public void pushApplySignatureMessageInsured(String businessCode, ApplyBo applyBo, Users users) {
        try {
            Map<String, String> map = new HashMap<>();
            if (AssertUtils.isNotNull(applyBo.getApplicant())) {
                map.put("applicantName", applyBo.getApplicant().getName());
            }

            if (AssertUtils.isNotEmpty(applyBo.getListInsured()) && AssertUtils.isNotEmpty(applyBo.getListInsured().get(0).getListCoverage())) {
                applyBo.getListInsured().get(0).getListCoverage().forEach(applyCoverageBo -> {
                    if (applyCoverageBo.getPrimaryFlag().equals(ApplyTermEnum.PRODUCT_PRIMARY_FLAG.MAIN.name())) {
                        map.put("productName", applyCoverageBo.getProductName());
                    }
                });
            }

            //查询applyBo
            //ApplyBo applyBo = applyBaseService.queryApply(applyId);

            if (AssertUtils.isNotNull(applyBo.getListInsured())) {
                List<ApplyInsuredBo> listInsured = applyBo.getListInsured();
                listInsured.forEach(applyInsuredBo -> {
                    String name = applyInsuredBo.getName();
                    if (AssertUtils.isNotNull(name)) {
                        map.put("insuredName", name);
                    }
                });
            }

            ApplyElectronicSignaturePo applyElectronicSignaturePo = applyPlanBaseDao.querySignature(applyBo.getApplyId());

            if (AssertUtils.isNotNull(applyElectronicSignaturePo)) {
                String insuredSignatureUrl = applyElectronicSignaturePo.getInsuredSignatureUrl();
                if (AssertUtils.isNotNull(insuredSignatureUrl)) {
                    map.put("signatureUrl", insuredSignatureUrl);
                }
            }

            BusinessMessagePushRequest businessMessagePushReqFc = new BusinessMessagePushRequest();
            businessMessagePushReqFc.setBusinessCode(businessCode);
            businessMessagePushReqFc.setMessageParam(JSON.toJSONString(map));
            //businessMessagePushReqFc.setDeviceChannel(appRequestHeads.getDeviceChannel());
            businessMessagePushReqFc.setLanguage(TerminologyConfigEnum.LANGUAGE.EN_US.name());

            List<ReceiverUserRequest> receiverUsers = new ArrayList<>();
            ReceiverUserRequest receiverUserRequest = new ReceiverUserRequest();
            receiverUserRequest.setMsgChannel("SMS");
            if (AssertUtils.isNotNull(applyBo.getListInsured())) {
                List<ApplyInsuredBo> listInsured = applyBo.getListInsured();
                listInsured.forEach(applyInsuredBo -> {
                    String mobile = applyInsuredBo.getMobile();
                    if (AssertUtils.isNotNull(mobile)) {
                        receiverUserRequest.setReceiverUser(mobile);
                    }
                });


            }
            receiverUsers.add(receiverUserRequest);
            businessMessagePushReqFc.setReceiverUsers(receiverUsers);

            this.getLogger().error("businessMessagePushReqFc:" + JSON.toJSONString(businessMessagePushReqFc));

            businessMessageApi.pushBusinessMessage(businessMessagePushReqFc);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    @Override
    public void pushApplyApplicantSignatureMessageAgent(String businessCode, ApplyBo applyBo, Users users) {
        try {
            Map<String, String> map = new HashMap<>();
            if (AssertUtils.isNotNull(applyBo.getApplyAgentBo())) {
                map.put("agentName", applyBo.getApplyAgentBo().getAgentName());
            }
            map.put("applyNo", applyBo.getApplyNo());
            if (AssertUtils.isNotNull(applyBo.getApplicant())) {
                map.put("applicantName", applyBo.getApplicant().getName());
            }

            BusinessMessagePushRequest businessMessagePushReqFc = new BusinessMessagePushRequest();
            businessMessagePushReqFc.setBusinessCode(businessCode);
            businessMessagePushReqFc.setMessageParam(JSON.toJSONString(map));
            //businessMessagePushReqFc.setDeviceChannel(appRequestHeads.getDeviceChannel());
            businessMessagePushReqFc.setLanguage(users.getLanguage());
            List<String> userIdList = new ArrayList<>();
            if (AssertUtils.isNotNull(applyBo.getApplyAgentBo())) {
                if (AssertUtils.isNotNull(applyBo.getApplyAgentBo().getAgentId())) {
                    userIdList.add(applyBo.getApplyAgentBo().getAgentId());
                }
            }
            businessMessagePushReqFc.setUserIdList(userIdList);
            this.getLogger().error("businessMessagePushReqFc:" + JSON.toJSONString(businessMessagePushReqFc));

            businessMessageApi.pushBusinessMessage(businessMessagePushReqFc);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    @Override
    public void pushApplyInsuredSignatureMessageAgent(String businessCode, ApplyBo applyBo, Users users) {
        try {
            Map<String, String> map = new HashMap<>();
            if (AssertUtils.isNotNull(applyBo.getApplyAgentBo())) {
                map.put("agentName", applyBo.getApplyAgentBo().getAgentName());
            }
            map.put("applyNo", applyBo.getApplyNo());
            if (AssertUtils.isNotNull(applyBo.getListInsured())) {
                List<ApplyInsuredBo> listInsured = applyBo.getListInsured();
                if (AssertUtils.isNotNull(listInsured)) {
                    listInsured.forEach(applyInsuredBo -> {
                        if (AssertUtils.isNotNull(applyInsuredBo)) {
                            map.put("insuredName", applyInsuredBo.getName());
                        }
                    });
                }
            }

            BusinessMessagePushRequest businessMessagePushReqFc = new BusinessMessagePushRequest();
            businessMessagePushReqFc.setBusinessCode(businessCode);
            businessMessagePushReqFc.setMessageParam(JSON.toJSONString(map));
            //businessMessagePushReqFc.setDeviceChannel(appRequestHeads.getDeviceChannel());
            businessMessagePushReqFc.setLanguage(users.getLanguage());
            List<String> userIdList = new ArrayList<>();
            if (AssertUtils.isNotNull(applyBo.getApplyAgentBo())) {
                if (AssertUtils.isNotNull(applyBo.getApplyAgentBo().getAgentId())) {
                    userIdList.add(applyBo.getApplyAgentBo().getAgentId());
                }
            }
            businessMessagePushReqFc.setUserIdList(userIdList);
            this.getLogger().error("businessMessagePushReqFc:" + JSON.toJSONString(businessMessagePushReqFc));

            businessMessageApi.pushBusinessMessage(businessMessagePushReqFc);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    @Override
    public void pushApplyAllSignatureMessageAgent(String businessCode, ApplyBo applyBo, Users users) {
        try {
            Map<String, String> map = new HashMap<>();
            if (AssertUtils.isNotNull(applyBo.getApplyAgentBo())) {
                map.put("agentName", applyBo.getApplyAgentBo().getAgentName());
            }
            map.put("applyNo", applyBo.getApplyNo());

            BusinessMessagePushRequest businessMessagePushReqFc = new BusinessMessagePushRequest();
            businessMessagePushReqFc.setBusinessCode(businessCode);
            businessMessagePushReqFc.setMessageParam(JSON.toJSONString(map));
            //businessMessagePushReqFc.setDeviceChannel(appRequestHeads.getDeviceChannel());
            businessMessagePushReqFc.setLanguage(users.getLanguage());
            List<String> userIdList = new ArrayList<>();
            if (AssertUtils.isNotNull(applyBo.getApplyAgentBo())) {
                if (AssertUtils.isNotNull(applyBo.getApplyAgentBo().getAgentId())) {
                    userIdList.add(applyBo.getApplyAgentBo().getAgentId());
                }
            }
            businessMessagePushReqFc.setUserIdList(userIdList);
            this.getLogger().error("businessMessagePushReqFc:" + JSON.toJSONString(businessMessagePushReqFc));

            businessMessageApi.pushBusinessMessage(businessMessagePushReqFc);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    /**
     * 发送预交加费消息给客户
     *
     * @param businessCode    消息类型
     * @param applyBo         投保单数据
     * @param users           当前用户
     * @param appRequestHeads 请求头
     */
    @Override
    public void pushApplyPreMessageCustomer(String businessCode, ApplyBo applyBo, Users users, AppRequestHeads appRequestHeads) {
        try {
            Map<String, String> map = new HashMap<>();
            map.put("applyNo", applyBo.getApplyNo());
            this.transMessagePremium(map, applyBo);
            if (AssertUtils.isNotNull(applyBo.getApplicant())) {
                map.put("applicantName", applyBo.getApplicant().getName());
            }
            if (AssertUtils.isNotNull(applyBo.getApplyPremiumBo()) && AssertUtils.isNotNull(applyBo.getApplyPremiumBo().getReceivableDate())) {
                map.put("receivableDate", DateUtils.timeStrToString(applyBo.getApplyPremiumBo().getReceivableDate(), "yyyy-MM-dd"));
            } else {
                map.put("receivableDate", DateUtils.timeStrToString(DateUtils.getCurrentTime(), DateUtils.FORMATE3));
            }

            if (AssertUtils.isNotEmpty(applyBo.getListInsured()) && AssertUtils.isNotEmpty(applyBo.getListInsured().get(0).getListCoverage())) {
                applyBo.getListInsured().get(0).getListCoverage().forEach(applyCoverageBo -> {
                    if (applyCoverageBo.getPrimaryFlag().equals(ApplyTermEnum.PRODUCT_PRIMARY_FLAG.MAIN.name())) {
                        map.put("productName", applyCoverageBo.getProductName());
                    }
                });
            }
            transPaymentShortUrl(applyBo, map);

            BusinessMessagePushRequest businessMessagePushReqFc = new BusinessMessagePushRequest();
            businessMessagePushReqFc.setBusinessCode(businessCode);
            businessMessagePushReqFc.setMessageParam(JSON.toJSONString(map));
            businessMessagePushReqFc.setDeviceChannel(appRequestHeads.getDeviceChannel());
            businessMessagePushReqFc.setLanguage(TerminologyConfigEnum.LANGUAGE.EN_US.name());

            List<ReceiverUserRequest> receiverUsers = new ArrayList<>();
            ReceiverUserRequest receiverUserRequest = new ReceiverUserRequest();
            receiverUserRequest.setMsgChannel("SMS");
            if (AssertUtils.isNotNull(applyBo.getApplicant())) {
                receiverUserRequest.setReceiverUser(applyBo.getApplicant().getMobile());
            }
            receiverUsers.add(receiverUserRequest);
            businessMessagePushReqFc.setReceiverUsers(receiverUsers);

            this.getLogger().error("businessMessagePushReqFc:" + JSON.toJSONString(businessMessagePushReqFc));

            businessMessageApi.pushBusinessMessage(businessMessagePushReqFc);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    /**
     * 设置map里的各个保费
     *
     * @param map
     * @param applyBo
     */
    private void transMessagePremium(Map<String, String> map, ApplyBo applyBo) {
        List<ApplyAddPremiumPo> applyAddPremiumPos = applyBo.getListApplyAddPremiumPo();
        final String[] premiumFrequency = {""};
        ApplyPremiumBo applyPremiumBo = applyBo.getApplyPremiumBo();
        if (!AssertUtils.isNotEmpty(applyBo.getListInsuredCoverage()) || !AssertUtils.isNotNull(applyPremiumBo)) {
            return;
        }
        applyBo.getListInsuredCoverage().stream().filter(coverageBo -> ApplyTermEnum.PRODUCT_PRIMARY_FLAG.MAIN.name().equals(coverageBo.getPrimaryFlag())).findFirst()
                .ifPresent(applyCoverageBo -> {
                    premiumFrequency[0] = applyCoverageBo.getPremiumFrequency();
                });
        BigDecimal originalStandardPremium = applyBo.getReceivablePremium();
        BigDecimal originalAddPremium = BigDecimal.ZERO;
        BigDecimal totalPremium = applyBo.getReceivablePremium();
        if (AssertUtils.isNotEmpty(applyAddPremiumPos)) {
            BigDecimal annualAddPremium = applyAddPremiumPos.stream().map(ApplyAddPremiumPo::getTotalAddPremium).reduce(BigDecimal.ZERO, BigDecimal::add);
            BigDecimal conversionFactor = BigDecimal.valueOf(ApplyTermEnum.PREMIUM_FREQUENCY_CONVERSION_FACTOR.valueOf(premiumFrequency[0]).value());
            originalAddPremium = annualAddPremium.multiply(conversionFactor).setScale(2, BigDecimal.ROUND_HALF_UP);
            //若有百分比折扣的投保单，加费金额就需要取优惠后的折扣加费
            if (AssertUtils.isNotNull(applyPremiumBo.getSpecialDiscount()) && ProductTermEnum.DISCOUNT_MODEL.PERCENTAGE.name().equals(applyPremiumBo.getDiscountModel())) {
                originalAddPremium = originalAddPremium.multiply(new BigDecimal("1").subtract(applyPremiumBo.getSpecialDiscount())).setScale(2, BigDecimal.ROUND_HALF_UP);
            }
            originalStandardPremium = totalPremium.subtract(originalAddPremium);
        }
        map.put("standardPremium", originalStandardPremium.toString());
        map.put("addPremium", originalAddPremium.toString());
        map.put("totalPremium", totalPremium.toString());
    }

    /**
     * 发送预交加费消息给客户
     *
     * @param businessCode    消息类型
     * @param applyBo         投保单数据
     * @param users           当前用户
     * @param appRequestHeads 请求头
     */
    @Override
    public void pushApplyPreAllMessageCustomer(String businessCode, ApplyBo applyBo, Users users, AppRequestHeads appRequestHeads) {
        Map<String, String> map = new HashMap<>();
        map.put("applyNo", applyBo.getApplyNo());
        this.transMessagePremium(map, applyBo);
        if (AssertUtils.isNotNull(applyBo.getApplicant())) {
            map.put("applicantName", applyBo.getApplicant().getName());
        }
        if (AssertUtils.isNotNull(applyBo.getApplyPremiumBo()) && AssertUtils.isNotNull(applyBo.getApplyPremiumBo().getReceivableDate())) {
            map.put("receivableDate", DateUtils.timeStrToString(applyBo.getApplyPremiumBo().getReceivableDate(), "yyyy-MM-dd"));
        } else {
            map.put("receivableDate", DateUtils.timeStrToString(DateUtils.getCurrentTime(), DateUtils.FORMATE3));
        }

        if (AssertUtils.isNotEmpty(applyBo.getListInsured()) && AssertUtils.isNotEmpty(applyBo.getListInsured().get(0).getListCoverage())) {
            applyBo.getListInsured().get(0).getListCoverage().forEach(applyCoverageBo -> {
                if (applyCoverageBo.getPrimaryFlag().equals(ApplyTermEnum.PRODUCT_PRIMARY_FLAG.MAIN.name())) {
                    map.put("productName", applyCoverageBo.getProductName());
                }
            });
        }
        transPaymentShortUrl(applyBo, map);

        BusinessMessagePushRequest businessMessagePushReqFc = new BusinessMessagePushRequest();
        businessMessagePushReqFc.setBusinessCode(businessCode);
        businessMessagePushReqFc.setMessageParam(JSON.toJSONString(map));
        businessMessagePushReqFc.setDeviceChannel(appRequestHeads.getDeviceChannel());
        businessMessagePushReqFc.setLanguage(TerminologyConfigEnum.LANGUAGE.EN_US.name());

        List<ReceiverUserRequest> receiverUsers = new ArrayList<>();
        ReceiverUserRequest receiverUserRequest = new ReceiverUserRequest();
        receiverUserRequest.setMsgChannel("SMS");
        if (AssertUtils.isNotNull(applyBo.getApplicant())) {
            receiverUserRequest.setReceiverUser(applyBo.getApplicant().getMobile());
        }
        receiverUsers.add(receiverUserRequest);
        businessMessagePushReqFc.setReceiverUsers(receiverUsers);

        this.getLogger().error("businessMessagePushReqFc:" + JSON.toJSONString(businessMessagePushReqFc));

        businessMessageApi.pushBusinessMessage(businessMessagePushReqFc);

    }

    private void transPaymentShortUrl(ApplyBo applyBo, Map<String, String> map) {
        ShortUrlRequest shortUrlRequest = new ShortUrlRequest();
        shortUrlRequest.setShortType(ThirdpartyTermEnum.SHORT_TYPE.PAYMENT_GUIDE.name());
        if (AssertUtils.isNotEmpty(applyBo.getPaymentId())) {
            shortUrlRequest.setBusinessId(applyBo.getPaymentId());
        } else if (AssertUtils.isNotNull(applyBo.getApplyPaymentTransactionBo())) {
            if (AssertUtils.isNotNull(applyBo.getApplyPaymentTransactionBo().getPaymentId())) {
                shortUrlRequest.setBusinessId(applyBo.getApplyPaymentTransactionBo().getPaymentId());
            }
        }

        getLogger().info("投保单ID：{}，获取短链接请求参数：{}", applyBo.getApplyId(), JSON.toJSONString(shortUrlRequest));
        ResultObject<ShortUrlResponse> resultObject = thirdpartyShortUrlApi.queryShortUrl(shortUrlRequest);
        getLogger().info("投保单ID：{}，获取短链接返回结果：{}", applyBo.getApplyId(), JSON.toJSONString(resultObject));
        if (!AssertUtils.isResultObjectDataNull(resultObject)) {
            map.put("paymentUrl", resultObject.getData().getFullUrl());
        }
    }

    /**
     * 发送预交加费暂保消息给客户
     *
     * @param businessCode    消息类型
     * @param applyBo         投保单数据
     * @param appRequestHeads 请求头
     */
    @Override
    @Async
    public void pushApplyPreStoreMessageCustomer(String businessCode, ApplyBo applyBo, AppRequestHeads appRequestHeads) {
        Map<String, String> map = new HashMap<>();
        if (AssertUtils.isNotNull(applyBo.getApplicant())) {
            map.put("applicantName", applyBo.getApplicant().getName());
        }
        if (AssertUtils.isNotEmpty(applyBo.getListInsuredCoverage())) {
            applyBo.getListInsuredCoverage().stream()
                    .filter(coverageBo -> ApplyTermEnum.PRODUCT_PRIMARY_FLAG.MAIN.name().equals(coverageBo.getPrimaryFlag()))
                    .findFirst().ifPresent(coverageBo -> {
                map.put("productName", coverageBo.getProductName());
            });
        }
        map.put("applyNo", applyBo.getApplyNo());
        // 获取暂保条款链接
        ShortUrlRequest shortUrlRequest = new ShortUrlRequest();
        shortUrlRequest.setShortType(ThirdpartyTermEnum.SHORT_TYPE.TEMPORARY_COVER.name());
        shortUrlRequest.setBusinessId(applyBo.getApplyId());
        shortUrlRequest.setSuffixUrl("?applyId=" + applyBo.getApplyId());
        getLogger().info("投保单ID：{}，获取暂保条款链接请求参数：{}", applyBo.getApplyId(), JSON.toJSONString(shortUrlRequest));
        ResultObject<ShortUrlResponse> resultObject = thirdpartyShortUrlApi.queryShortUrl(shortUrlRequest);
        getLogger().info("投保单ID：{}，获取暂保条款链接返回结果：{}", applyBo.getApplyId(), JSON.toJSONString(resultObject));
        if (!AssertUtils.isResultObjectDataNull(resultObject)) {
            map.put("temporaryCoverUrl", resultObject.getData().getFullUrl());
        }

        BusinessMessagePushRequest businessMessagePushReqFc = new BusinessMessagePushRequest();
        businessMessagePushReqFc.setBusinessCode(businessCode);
        businessMessagePushReqFc.setMessageParam(JSON.toJSONString(map));
        businessMessagePushReqFc.setDeviceChannel(appRequestHeads.getDeviceChannel());
        businessMessagePushReqFc.setLanguage(TerminologyConfigEnum.LANGUAGE.EN_US.name());

        List<ReceiverUserRequest> receiverUsers = new ArrayList<>();
        ReceiverUserRequest receiverUserRequest = new ReceiverUserRequest();
        receiverUserRequest.setMsgChannel("SMS");
        if (AssertUtils.isNotNull(applyBo.getApplicant())) {
            receiverUserRequest.setReceiverUser(applyBo.getApplicant().getMobile());
        }
        receiverUsers.add(receiverUserRequest);
        businessMessagePushReqFc.setReceiverUsers(receiverUsers);

        this.getLogger().error("businessMessagePushReqFc:" + JSON.toJSONString(businessMessagePushReqFc));

        businessMessageApi.pushBusinessMessage(businessMessagePushReqFc);

    }


    /**
     * 发送消息给客户
     *
     * @param businessCode    消息类型
     * @param applyBo         投保单数据
     * @param users           当前用户
     * @param appRequestHeads 请求头
     * @param paymentAmount   金额
     */
    @Override
    @Async
    public void pushApplyMessageCustomer(String businessCode, ApplyBo applyBo, Users users, AppRequestHeads appRequestHeads, BigDecimal paymentAmount) {
        Map<String, String> map = new HashMap<>();
        map.put("applyNo", applyBo.getApplyNo());
        map.put("premium", paymentAmount.toString());
        if (AssertUtils.isNotNull(applyBo.getApplicant())) {
            map.put("applicantName", applyBo.getApplicant().getName());
        }
        if (AssertUtils.isNotNull(applyBo.getApplyPremiumBo()) && AssertUtils.isNotNull(applyBo.getApplyPremiumBo().getReceivableDate())) {
            map.put("receivableDate", DateUtils.timeStrToString(applyBo.getApplyPremiumBo().getReceivableDate(), "yyyy-MM-dd"));
        } else {
            map.put("receivableDate", DateUtils.timeStrToString(DateUtils.getCurrentTime(), DateUtils.FORMATE3));
        }

        if (AssertUtils.isNotEmpty(applyBo.getListInsured()) && AssertUtils.isNotEmpty(applyBo.getListInsured().get(0).getListCoverage())) {
            applyBo.getListInsured().get(0).getListCoverage().forEach(applyCoverageBo -> {
                if (applyCoverageBo.getPrimaryFlag().equals(ApplyTermEnum.PRODUCT_PRIMARY_FLAG.MAIN.name())) {
                    map.put("productName", applyCoverageBo.getProductName());
                }
            });
        }
        transPaymentShortUrl(applyBo, map);

        BusinessMessagePushRequest businessMessagePushReqFc = new BusinessMessagePushRequest();
        businessMessagePushReqFc.setBusinessCode(businessCode);
        businessMessagePushReqFc.setMessageParam(JSON.toJSONString(map));
        businessMessagePushReqFc.setDeviceChannel(appRequestHeads.getDeviceChannel());
        businessMessagePushReqFc.setLanguage(TerminologyConfigEnum.LANGUAGE.EN_US.name());

        List<ReceiverUserRequest> receiverUsers = new ArrayList<>();
        ReceiverUserRequest receiverUserRequest = new ReceiverUserRequest();
        receiverUserRequest.setMsgChannel("SMS");
        if (AssertUtils.isNotNull(applyBo.getApplicant())) {
            receiverUserRequest.setReceiverUser(applyBo.getApplicant().getMobile());
        }
        receiverUsers.add(receiverUserRequest);
        businessMessagePushReqFc.setReceiverUsers(receiverUsers);

        this.getLogger().error("businessMessagePushReqFc:" + JSON.toJSONString(businessMessagePushReqFc));

        businessMessageApi.pushBusinessMessage(businessMessagePushReqFc);

    }

    /**
     * 发送ABA消息给客户(sprint-v4.0.1.20210425)
     *
     * @param businessCode    消息类型
     * @param applyBo         投保单数据
     * @param users           当前用户
     * @param appRequestHeads 请求头
     */
    @Override
    @Async
    public void pushApplyABAMessageCustomer(String businessCode, ApplyBo applyBo, Users users, AppRequestHeads appRequestHeads) {
        Map<String, String> map = new HashMap<>();
        map.put("applyNo", applyBo.getApplyNo());
        map.put("receivablePremium", applyBo.getReceivablePremium().toString());

        BusinessMessagePushRequest businessMessagePushReqFc = new BusinessMessagePushRequest();
        businessMessagePushReqFc.setBusinessCode(businessCode);
        businessMessagePushReqFc.setMessageParam(JSON.toJSONString(map));
        businessMessagePushReqFc.setDeviceChannel(appRequestHeads.getDeviceChannel());
        businessMessagePushReqFc.setLanguage(TerminologyConfigEnum.LANGUAGE.EN_US.name());

        List<ReceiverUserRequest> receiverUsers = new ArrayList<>();
        ReceiverUserRequest receiverUserRequest = new ReceiverUserRequest();
        receiverUserRequest.setMsgChannel("SMS");
        if (AssertUtils.isNotNull(applyBo.getApplicant().getMobile())) {
            receiverUserRequest.setReceiverUser(applyBo.getApplicant().getMobile());
        }
        if (AssertUtils.isNotNull(applyBo.getApplicant().getDelegateMobile())) {
            receiverUserRequest.setReceiverUser(applyBo.getApplicant().getDelegateMobile());
        }
        receiverUsers.add(receiverUserRequest);
        businessMessagePushReqFc.setReceiverUsers(receiverUsers);

        this.getLogger().error("businessMessagePushReqFc:" + JSON.toJSONString(businessMessagePushReqFc));

        businessMessageApi.pushBusinessMessage(businessMessagePushReqFc);

    }


    /**
     * 发送暂予承保消息给客户 手机号码
     *
     * @param businessCode 业务类型
     * @param applyBo      投保单
     */
    @Override
    public void pushGroupPreMessageCustomerSMS(String businessCode, ApplyBo applyBo) {
        try {
            Map<String, String> map = new HashMap<>();
            Map<String, String> map1 = transferGroupPreMessageParam(applyBo, map);

            transPaymentShortUrl(applyBo, map1);

            BusinessMessagePushRequest businessMessagePushReqFc = new BusinessMessagePushRequest();
            businessMessagePushReqFc.setBusinessCode(businessCode);
            businessMessagePushReqFc.setMessageParam(JSON.toJSONString(map1));
            businessMessagePushReqFc.setDeviceChannel("gclife_agent_app");
            businessMessagePushReqFc.setLanguage(TerminologyConfigEnum.LANGUAGE.EN_US.name());

            List<ReceiverUserRequest> receiverUsers = new ArrayList<>();
            ReceiverUserRequest receiverUserRequest = new ReceiverUserRequest();
            receiverUserRequest.setMsgChannel("SMS");
            if (AssertUtils.isNotNull(applyBo.getApplicant())) {
                receiverUserRequest.setReceiverUser(applyBo.getApplicant().getDelegateMobile());
            }
            receiverUsers.add(receiverUserRequest);
            businessMessagePushReqFc.setReceiverUsers(receiverUsers);

            this.getLogger().error("businessMessagePushReqFc:" + JSON.toJSONString(businessMessagePushReqFc));

            businessMessageApi.pushBusinessMessage(businessMessagePushReqFc);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    @Override
    public void pushGroupPreMessageAgent(String businessCode, ApplyBo applyBo, String userId) {
        try {
            this.getLogger().error("消息发送入口 投保单对象applyBo:" + JSON.toJSONString(applyBo));
            List<String> userIds = new ArrayList<>();
            userIds.add(userId);
            // 消息发送
            Map<String, String> map = new HashMap<>();
            Map<String, String> map1 = transferGroupPreMessageParam(applyBo, map);
            pushBusinessMessage(businessCode, userIds, transferGroupPreMessageParam(applyBo, map1));
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    @Override
    public void pushGroupPreMessageBatch(String businessCode, ApplyBo applyBo) {
        try {
            this.getLogger().info("applyBo2:" + JSON.toJSONString(applyBo));
            Map<String, String> map = new HashMap<>();
            List<String> userIds = new ArrayList<>();
            userIds.add(applyBo.getApplyAgentBo().getAgentId());
            List<String> data = platformUsersApi.getBusinessUsers(businessCode, applyBo.getSalesBranchId()).getData();
            if (AssertUtils.isNotEmpty(data)) {
                userIds.addAll(data);
            }
            this.getLogger().info("userIds:" + JSON.toJSONString(userIds));
            if (AssertUtils.isNotEmpty(applyBo.getPolicyNo())) {
                map.put("policyNo", applyBo.getPolicyNo());
            }

            ApplyUnderwriteDecisionPo applyUnderwriteDecisionPo = applyUnderwriteBaseService.queryApplyUnderwriteDecisionPo(applyBo.getApplyId());
            if (AssertUtils.isNotEmpty(applyBo.getListInsured()) && AssertUtils.isNotEmpty(applyBo.getListInsured().get(0).getListCoverage())) {
                applyBo.getListInsured().get(0).getListCoverage().stream().filter(applyCoverageBo -> applyCoverageBo.getPrimaryFlag().equals(ApplyTermEnum.PRODUCT_PRIMARY_FLAG.MAIN.name()))
                        .findFirst().ifPresent(applyCoverageBo -> {
                    map.put("productName", applyCoverageBo.getProductName());
                    if (AssertUtils.isNotNull(applyUnderwriteDecisionPo)) {
                        //5.8.3 作废时间默认14天
                        String renewalDate = DateUtils.timeStrToString(DateUtils.addStringDayRT(applyUnderwriteDecisionPo.getCreatedDate(), 14), DateUtils.FORMATE3);
                        if (Arrays.asList(ApplyTermEnum.PRODUCT.PRODUCT_5.id(), ApplyTermEnum.PRODUCT.PRODUCT_28.id()).contains(applyCoverageBo.getProductId())) {
                            renewalDate = DateUtils.timeStrToString(DateUtils.addStringDayRT(applyUnderwriteDecisionPo.getCreatedDate(), 30), DateUtils.FORMATE3);
                        }
                        map.put("renewalDate", renewalDate);
                    }
                });
            }
            map.put("time", DateUtils.timeStrToString(DateUtils.getCurrentTime(), "yyyy-MM-dd HH:mm:ss"));
            // 消息发送
            BusinessMessagePushRequest businessMessagePushReqFc = new BusinessMessagePushRequest();
            businessMessagePushReqFc.setBusinessCode(businessCode);
            businessMessagePushReqFc.setUserIdList(userIds);
            businessMessagePushReqFc.setMessageParam(JSON.toJSONString(map));

            this.getLogger().error("businessMessagePushReqFc:" + JSON.toJSONString(businessMessagePushReqFc));

            businessMessageApi.pushBusinessMessage(businessMessagePushReqFc);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    /**
     * 发送消息给客户
     *
     * @param businessCode    消息类型
     * @param applyBo         投保单数据
     * @param users           当前用户
     * @param appRequestHeads 请求头
     */
    @Override
    public void pushApplyGroupMessageCustomer(String businessCode, ApplyBo applyBo, Users users, AppRequestHeads appRequestHeads) {
        try {
            Map<String, String> map = new HashMap<>();
            map.put("applyNo", applyBo.getApplyNo());
            map.put("premium", applyBo.getReceivablePremium().toString());

            if (AssertUtils.isNotNull(applyBo.getApplicant())) {
                String name = applyBo.getApplicant().getName();
                if (ApplyTermEnum.APPLY_TYPE.LIFE_INSURANCE_GROUP.name().equals(applyBo.getApplyType())) {
                    name = applyBo.getApplicant().getDelegateName();
                }
                map.put("applicantName", name);
            }

            ApplyUnderwriteDecisionPo applyUnderwriteDecisionPo = applyUnderwriteBaseService.queryApplyUnderwriteDecisionPo(applyBo.getApplyId());
            if (AssertUtils.isNotEmpty(applyBo.getListInsured()) && AssertUtils.isNotEmpty(applyBo.getListInsured().get(0).getListCoverage())) {
                applyBo.getListInsured().get(0).getListCoverage().stream().filter(applyCoverageBo -> applyCoverageBo.getPrimaryFlag().equals(ApplyTermEnum.PRODUCT_PRIMARY_FLAG.MAIN.name()))
                        .findFirst().ifPresent(applyCoverageBo -> {
                    map.put("productName", applyCoverageBo.getProductName());
                    this.getLogger().info("applyUnderwriteDecisionPo:" + applyUnderwriteDecisionPo);
                    if (AssertUtils.isNotNull(applyUnderwriteDecisionPo)) {
                        this.getLogger().info("========暂保提交时间======" + applyUnderwriteDecisionPo.getCreatedDate());
                        //5.8.3 作废时间默认14天
                        String renewalDate = DateUtils.timeStrToString(DateUtils.addStringDayRT(applyUnderwriteDecisionPo.getCreatedDate(), 14), DateUtils.FORMATE3);
                        if (Arrays.asList(ApplyTermEnum.PRODUCT.PRODUCT_5.id(), ApplyTermEnum.PRODUCT.PRODUCT_28.id()).contains(applyCoverageBo.getProductId())) {
                            renewalDate = DateUtils.timeStrToString(DateUtils.addStringDayRT(applyUnderwriteDecisionPo.getCreatedDate(), 30), DateUtils.FORMATE3);
                        }
                        map.put("renewalDate", renewalDate);
                    }
                });
            }

            transPaymentShortUrl(applyBo, map);

            BusinessMessagePushRequest businessMessagePushReqFc = new BusinessMessagePushRequest();
            businessMessagePushReqFc.setBusinessCode(businessCode);
            businessMessagePushReqFc.setMessageParam(JSON.toJSONString(map));
            businessMessagePushReqFc.setDeviceChannel(appRequestHeads.getDeviceChannel());
            businessMessagePushReqFc.setLanguage(TerminologyConfigEnum.LANGUAGE.EN_US.name());

            List<ReceiverUserRequest> receiverUsers = new ArrayList<>();
            ReceiverUserRequest receiverUserRequest = new ReceiverUserRequest();
            receiverUserRequest.setMsgChannel("SMS");
            if (ApplyTermEnum.APPLY_TYPE.LIFE_INSURANCE_GROUP.name().equals(applyBo.getApplyType())) {
                if (AssertUtils.isNotNull(applyBo.getApplicant().getDelegateMobile())) {
                    receiverUserRequest.setReceiverUser(applyBo.getApplicant().getDelegateMobile());
                }
            } else {
                if (AssertUtils.isNotNull(applyBo.getApplicant().getMobile())) {
                    receiverUserRequest.setReceiverUser(applyBo.getApplicant().getMobile());
                }
            }

            receiverUsers.add(receiverUserRequest);
            businessMessagePushReqFc.setReceiverUsers(receiverUsers);

            this.getLogger().error("businessMessagePushReqFc:" + JSON.toJSONString(businessMessagePushReqFc));

            businessMessageApi.pushBusinessMessage(businessMessagePushReqFc);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    /**
     * 发送ABA消息给代理人(sprint-v4.0.1.20210425)
     *
     * @param businessCode    消息类型
     * @param applyBo         投保单数据
     * @param appRequestHeads 请求头
     */
    @Override
    @Async
    public void pushApplyABAMessageAgent(String businessCode, ApplyBo applyBo, AppRequestHeads appRequestHeads) {
        Map<String, String> map = new HashMap<>();
        map.put("agentName", applyBo.getApplyAgentBo().getAgentName());
        map.put("applyNo", applyBo.getApplyNo());
        map.put("receivablePremium", applyBo.getReceivablePremium().toString());

        BusinessMessagePushRequest businessMessagePushReqFc = new BusinessMessagePushRequest();
        businessMessagePushReqFc.setBusinessCode(businessCode);
        businessMessagePushReqFc.setMessageParam(JSON.toJSONString(map));
        businessMessagePushReqFc.setDeviceChannel(appRequestHeads.getDeviceChannel());
        businessMessagePushReqFc.setLanguage(TerminologyConfigEnum.LANGUAGE.EN_US.name());
        List<String> userIdList = new ArrayList<>();
        if (AssertUtils.isNotNull(applyBo.getApplyAgentBo().getAgentId())) {
            userIdList.add(applyBo.getApplyAgentBo().getAgentId());
        }
        businessMessagePushReqFc.setUserIdList(userIdList);
        this.getLogger().error("businessMessagePushReqFc:" + JSON.toJSONString(businessMessagePushReqFc));
        businessMessageApi.pushBusinessMessage(businessMessagePushReqFc);

    }

    /**
     * 消息发送
     *
     * @param businessCode 消息类型
     * @param userIds      用户IDS
     * @param messageParam 消息参数
     */
    private void pushBusinessMessage(String businessCode, List<String> userIds, Map<String, String> messageParam) {
        BusinessMessagePushRequest businessMessagePushReqFc = new BusinessMessagePushRequest();
        businessMessagePushReqFc.setBusinessCode(businessCode);
        businessMessagePushReqFc.setUserIdList(userIds);
        businessMessagePushReqFc.setMessageParam(JSON.toJSONString(messageParam));

        this.getLogger().error("businessMessagePushReqFc:" + JSON.toJSONString(businessMessagePushReqFc));

        businessMessageApi.pushBusinessMessage(businessMessagePushReqFc);
    }


    /**
     * 投保消息发送参数转换
     *
     * @param applyPlanBo 计划书对象
     */
    private Map<String, String> transferApplyPlanMessageParam(ApplyPlanBo applyPlanBo, Map<String, String> bodyMap) {
        Map<String, String> map = new HashMap<>();
        if (AssertUtils.isNotNull(bodyMap)) {
            map.putAll(bodyMap);
        }
        if (AssertUtils.isNotNull(applyPlanBo)) {
            map.put("applyId", applyPlanBo.getApplyId());
            map.put("applyNo", applyPlanBo.getApplyNo());
            map.put("applyPlanId", applyPlanBo.getApplyPlanId());
            map.put("businessNo", applyPlanBo.getApplyNo());
            if (AssertUtils.isNotNull(applyPlanBo.getReceivablePremium())) {
                map.put("premium", applyPlanBo.getReceivablePremium().toString());
            }
            if (AssertUtils.isNotNull(applyPlanBo.getApplicant())) {
                map.put("applicantName", AssertUtils.isNotEmpty(applyPlanBo.getApplicant().getName()) ? applyPlanBo.getApplicant().getName() : "无");
            } else {
                map.put("applicantName", "无");
            }

            if (AssertUtils.isNotEmpty(applyPlanBo.getCoverages())) {
                applyPlanBo.getCoverages().forEach(applyCoverageBo -> {
                    if (applyCoverageBo.getPrimaryFlag().equals(ApplyTermEnum.PRODUCT_PRIMARY_FLAG.MAIN.name())) {
                        map.put("productName", applyCoverageBo.getProductName());
                    }
                });
            }
            map.put("time", DateUtils.timeStrToString(DateUtils.getCurrentTime(), "yyyy-MM-dd HH:mm:ss"));
        }
        return map;
    }

    /**
     * 投保消息发送参数转换
     *
     * @param applyBo 投保单对象
     */
    private Map<String, String> transferMessageParam(ApplyBo applyBo) {
        Map<String, String> map = new HashMap<>();
        if (AssertUtils.isNotNull(applyBo)) {
            map.put("applyId", applyBo.getApplyId());
            map.put("applyNo", applyBo.getApplyNo());
            map.put("businessNo", applyBo.getApplyNo());
            map.put("reason", applyBo.getUnderWriteRemark());
            this.transMessagePremium(map, applyBo);
            if (AssertUtils.isNotEmpty(applyBo.getPolicyNo())) {
                map.put("policyNo", applyBo.getPolicyNo());
            }
            if (AssertUtils.isNotEmpty(applyBo.getPolicyId())) {
                map.put("policyId", applyBo.getPolicyId());
            }
            if (AssertUtils.isNotNull(applyBo.getApplyDate())) {
                map.put("applyDate", DateUtils.timeStrToString(applyBo.getApplyDate(), "yyyy-MM-dd HH:mm:ss"));
            }
            if (AssertUtils.isNotNull(applyBo.getReceivablePremium())) {
                map.put("premium", applyBo.getReceivablePremium().toString());
            }
            if (AssertUtils.isNotNull(applyBo.getApplicant())) {
                map.put("applicantName", AssertUtils.isNotEmpty(applyBo.getApplicant().getName()) ? applyBo.getApplicant().getName() : "无");
                map.put("customerName", AssertUtils.isNotEmpty(applyBo.getApplicant().getName()) ? applyBo.getApplicant().getName() : "无");
            } else {
                map.put("applicantName", "无");
                map.put("customerName", "无");
            }

            if (AssertUtils.isNotNull(applyBo.getEffectiveDate())) {
                //判断，如果生效日期是第二天，则取当天，如果生效日期大于第二天(指定某天生效)则取生效日期
                try {
                    //第二天
                    Date nextCurrentDate = DateUtils.addDays(DateUtils.getTimeStrToDate(System.currentTimeMillis() + "", DateUtils.FORMATE3), 1);
                    //生效日期
                    Date effectiveDate = DateUtils.getTimeStrToDate(applyBo.getEffectiveDate() + "", DateUtils.FORMATE3);
                    if (nextCurrentDate.getTime() == effectiveDate.getTime()) {
                        map.put("effectiveDate", DateUtils.timeStrToString(System.currentTimeMillis(), "dd-MM-yyyy"));
                    } else {
                        map.put("effectiveDate", DateUtils.timeStrToString(applyBo.getEffectiveDate(), "dd-MM-yyyy"));
                    }
                } catch (Exception e) {

                }
            }

            if (AssertUtils.isNotEmpty(applyBo.getListInsured()) && AssertUtils.isNotEmpty(applyBo.getListInsured().get(0).getListCoverage())) {
                applyBo.getListInsured().get(0).getListCoverage().forEach(applyCoverageBo -> {
                    if (applyCoverageBo.getPrimaryFlag().equals(ApplyTermEnum.PRODUCT_PRIMARY_FLAG.MAIN.name())) {
                        map.put("productName", applyCoverageBo.getProductName());
                        map.put("secondPremium", applyCoverageBo.getTotalPremium().toString());
                        if (AssertUtils.isNotNull(applyBo.getEffectiveDate()) && ProductTermEnum.PRODUCT.PRODUCT_20A.id().equals(applyCoverageBo.getProductId())) {
                            map.put("secondPaymentDate", DateUtils.timeStrToString(DateUtils.addStringYearsRT(applyBo.getEffectiveDate(), 1), "dd-MM-yyyy"));
                        }
                    }
                });
            }

            if (!map.containsKey("productName")) {
                if (AssertUtils.isNotEmpty(applyBo.getListApplyCoverageAcceptBo())) {
                    applyBo.getListApplyCoverageAcceptBo().forEach(applyCoverageAcceptBo -> {
                        if (applyCoverageAcceptBo.getPrimaryFlag().equals(ApplyTermEnum.PRODUCT_PRIMARY_FLAG.MAIN.name())) {
                            ResultObject<ProductSimpleInfoResponse> productSimple = productApi.getProductSimpleInfo(applyCoverageAcceptBo.getProductId(), applyBo.getSalesBranchId());
                            map.put("productName", productSimple.getData().getProductName());
                        }
                    });
                }
            }
            if (AssertUtils.isNotNull(applyBo.getApplyAgentBo())) {
                map.put("agentName", applyBo.getApplyAgentBo().getAgentName());
                map.put("agentCode", applyBo.getApplyAgentBo().getAgentCode());
                map.put("recommendAgentName", applyBo.getApplyAgentBo().getRecommendAgentName());
            }
            map.put("time", DateUtils.timeStrToString(DateUtils.getCurrentTime(), "yyyy-MM-dd HH:mm:ss"));
        }
        return map;
    }

    /**
     * 投保消息发送参数转换
     *
     * @param applyBo 投保单对象
     */
    private Map<String, String> transferMessageParamNew(ApplyBo applyBo) {
        Map<String, String> map = new HashMap<>();
        if (AssertUtils.isNotNull(applyBo)) {
            map.put("applyId", applyBo.getApplyId());
            map.put("applyNo", applyBo.getApplyNo());
            map.put("businessNo", applyBo.getApplyNo());
            this.transMessagePremium(map, applyBo);
            map.put("reason", applyBo.getUnderWriteRemark());
            if (AssertUtils.isNotEmpty(applyBo.getPolicyNo())) {
                map.put("policyNo", applyBo.getPolicyNo());
            }
            if (AssertUtils.isNotEmpty(applyBo.getPolicyId())) {
                map.put("policyId", applyBo.getPolicyId());
            }
            if (AssertUtils.isNotNull(applyBo.getApplyDate())) {
                map.put("applyDate", DateUtils.timeStrToString(applyBo.getApplyDate(), "yyyy-MM-dd HH:mm:ss"));
            }
            if (AssertUtils.isNotNull(applyBo.getReceivablePremium())) {
                map.put("premium", applyBo.getReceivablePremium().toString());
            }
            if (AssertUtils.isNotNull(applyBo.getApplicant())) {
                map.put("applicantName", AssertUtils.isNotEmpty(applyBo.getApplicant().getName()) ? applyBo.getApplicant().getName() : "无");
                map.put("customerName", AssertUtils.isNotEmpty(applyBo.getApplicant().getName()) ? applyBo.getApplicant().getName() : "无");
            } else {
                map.put("applicantName", "无");
                map.put("customerName", "无");
            }

            if (AssertUtils.isNotNull(applyBo.getEffectiveDate())) {
                //判断，如果生效日期是第二天，则取当天，如果生效日期大于第二天(指定某天生效)则取生效日期
                try {
                    //第二天
                    Date nextCurrentDate = DateUtils.addDays(DateUtils.getTimeStrToDate(System.currentTimeMillis() + "", DateUtils.FORMATE3), 1);
                    //生效日期
                    Date effectiveDate = DateUtils.getTimeStrToDate(applyBo.getEffectiveDate() + "", DateUtils.FORMATE3);
                    if (nextCurrentDate.getTime() == effectiveDate.getTime()) {
                        map.put("effectiveDate", DateUtils.timeStrToString(System.currentTimeMillis(), "dd-MM-yyyy"));
                    } else {
                        map.put("effectiveDate", DateUtils.timeStrToString(applyBo.getEffectiveDate(), "dd-MM-yyyy"));
                    }
                } catch (Exception e) {

                }
            }

            if (AssertUtils.isNotEmpty(applyBo.getListInsured()) && AssertUtils.isNotEmpty(applyBo.getListInsured().get(0).getListCoverage())) {
                applyBo.getListInsured().get(0).getListCoverage().forEach(applyCoverageBo -> {
                    if (applyCoverageBo.getPrimaryFlag().equals(ApplyTermEnum.PRODUCT_PRIMARY_FLAG.MAIN.name())) {
                        map.put("productName", applyCoverageBo.getProductName());
                    }
                });
            }

            if (!map.containsKey("productName")) {
                if (AssertUtils.isNotEmpty(applyBo.getListApplyCoverageAcceptBo())) {
                    applyBo.getListApplyCoverageAcceptBo().forEach(applyCoverageAcceptBo -> {
                        if (applyCoverageAcceptBo.getPrimaryFlag().equals(ApplyTermEnum.PRODUCT_PRIMARY_FLAG.MAIN.name())) {
                            ResultObject<ProductSimpleInfoResponse> productSimple = productApi.getProductSimpleInfo(applyCoverageAcceptBo.getProductId(), applyBo.getSalesBranchId());
                            map.put("productName", productSimple.getData().getProductName());
                        }
                    });
                }
            }
            if (AssertUtils.isNotNull(applyBo.getApplyAgentBo())) {
                map.put("agentName", applyBo.getApplyAgentBo().getAgentName());
                map.put("agentCode", applyBo.getApplyAgentBo().getAgentCode());
                map.put("recommendAgentName", applyBo.getApplyAgentBo().getRecommendAgentName());
            }
            map.put("time", DateUtils.timeStrToString(DateUtils.getCurrentTime(), "yyyy-MM-dd HH:mm:ss"));
        }
        return map;
    }

    private Map<String, String> transferGroupPreMessageParam(ApplyBo applyBo, Map<String, String> map) {
        if (AssertUtils.isNotNull(applyBo.getApplicant())) {
            map.put("applicantName", applyBo.getApplicant().getName());
            map.put("delegateName", applyBo.getApplicant().getDelegateName());
            map.put("companyName", applyBo.getApplicant().getCompanyName());
        }
        if (AssertUtils.isNotEmpty(applyBo.getPolicyNo())) {
            map.put("policyNo", applyBo.getPolicyNo());
        }

        ApplyUnderwriteDecisionPo applyUnderwriteDecisionPo = applyUnderwriteBaseService.queryApplyUnderwriteDecisionPo(applyBo.getApplyId());
        this.getLogger().info("applyUnderwriteDecisionPo1:" + applyUnderwriteDecisionPo);
        if (AssertUtils.isNotEmpty(applyBo.getListInsured()) && AssertUtils.isNotEmpty(applyBo.getListInsured().get(0).getListCoverage())) {
            applyBo.getListInsured().get(0).getListCoverage().stream().filter(applyCoverageBo -> applyCoverageBo.getPrimaryFlag().equals(ApplyTermEnum.PRODUCT_PRIMARY_FLAG.MAIN.name()))
                    .findFirst().ifPresent(applyCoverageBo -> {
                map.put("productName", applyCoverageBo.getProductName());
                if (AssertUtils.isNotNull(applyUnderwriteDecisionPo)) {
                    this.getLogger().info("========暂保提交时间1======" + applyUnderwriteDecisionPo.getCreatedDate());
                    //5.8.3 作废时间默认14天
                    String renewalDate = DateUtils.timeStrToString(DateUtils.addStringDayRT(applyUnderwriteDecisionPo.getCreatedDate(), 14), DateUtils.FORMATE3);
                    if (Arrays.asList(ApplyTermEnum.PRODUCT.PRODUCT_5.id(), ApplyTermEnum.PRODUCT.PRODUCT_28.id()).contains(applyCoverageBo.getProductId())) {
                        renewalDate = DateUtils.timeStrToString(DateUtils.addStringDayRT(applyUnderwriteDecisionPo.getCreatedDate(), 30), DateUtils.FORMATE3);
                    }
                    map.put("renewalDate", renewalDate);
                }
            });
        }
        return map;
    }

    /**
     * 投保消息发送参数转换(新版)
     *
     * @param applyBo 投保单对象
     */
    private Map<String, String> transferMessageParamNew(ApplyBo applyBo, BigDecimal paymentAmount) {
        Map<String, String> map = new HashMap<>();
        if (AssertUtils.isNotNull(applyBo)) {
            map.put("applyId", applyBo.getApplyId());
            map.put("applyNo", applyBo.getApplyNo());
            map.put("businessNo", applyBo.getApplyNo());
            map.put("reason", applyBo.getUnderWriteRemark());
            if (AssertUtils.isNotEmpty(applyBo.getPolicyNo())) {
                map.put("policyNo", applyBo.getPolicyNo());
            }
            if (AssertUtils.isNotEmpty(applyBo.getPolicyId())) {
                map.put("policyId", applyBo.getPolicyId());
            }
            if (AssertUtils.isNotNull(applyBo.getApplyDate())) {
                map.put("applyDate", DateUtils.timeStrToString(applyBo.getApplyDate(), "yyyy-MM-dd HH:mm:ss"));
            }
            map.put("premium", paymentAmount.toString());
            if (AssertUtils.isNotNull(applyBo.getApplicant())) {
                map.put("applicantName", AssertUtils.isNotEmpty(applyBo.getApplicant().getName()) ? applyBo.getApplicant().getName() : "无");
                map.put("customerName", AssertUtils.isNotEmpty(applyBo.getApplicant().getName()) ? applyBo.getApplicant().getName() : "无");
            } else {
                map.put("applicantName", "无");
                map.put("customerName", "无");
            }

            if (AssertUtils.isNotNull(applyBo.getEffectiveDate())) {
                //判断，如果生效日期是第二天，则取当天，如果生效日期大于第二天(指定某天生效)则取生效日期
                try {
                    //第二天
                    Date nextCurrentDate = DateUtils.addDays(DateUtils.getTimeStrToDate(System.currentTimeMillis() + "", DateUtils.FORMATE3), 1);
                    //生效日期
                    Date effectiveDate = DateUtils.getTimeStrToDate(applyBo.getEffectiveDate() + "", DateUtils.FORMATE3);
                    if (nextCurrentDate.getTime() == effectiveDate.getTime()) {
                        map.put("effectiveDate", DateUtils.timeStrToString(System.currentTimeMillis(), "dd-MM-yyyy"));
                    } else {
                        map.put("effectiveDate", DateUtils.timeStrToString(applyBo.getEffectiveDate(), "dd-MM-yyyy"));
                    }
                } catch (Exception e) {

                }
            }

            if (AssertUtils.isNotEmpty(applyBo.getListInsured()) && AssertUtils.isNotEmpty(applyBo.getListInsured().get(0).getListCoverage())) {
                applyBo.getListInsured().get(0).getListCoverage().forEach(applyCoverageBo -> {
                    if (applyCoverageBo.getPrimaryFlag().equals(ApplyTermEnum.PRODUCT_PRIMARY_FLAG.MAIN.name())) {
                        map.put("productName", applyCoverageBo.getProductName());
                    }
                });
            }

            if (!map.containsKey("productName")) {
                if (AssertUtils.isNotEmpty(applyBo.getListApplyCoverageAcceptBo())) {
                    applyBo.getListApplyCoverageAcceptBo().forEach(applyCoverageAcceptBo -> {
                        if (applyCoverageAcceptBo.getPrimaryFlag().equals(ApplyTermEnum.PRODUCT_PRIMARY_FLAG.MAIN.name())) {
                            ResultObject<ProductSimpleInfoResponse> productSimple = productApi.getProductSimpleInfo(applyCoverageAcceptBo.getProductId(), applyBo.getSalesBranchId());
                            map.put("productName", productSimple.getData().getProductName());
                        }
                    });
                }
            }
            if (AssertUtils.isNotNull(applyBo.getApplyAgentBo())) {
                map.put("agentName", applyBo.getApplyAgentBo().getAgentName());
                map.put("agentCode", applyBo.getApplyAgentBo().getAgentCode());
                map.put("recommendAgentName", applyBo.getApplyAgentBo().getRecommendAgentName());
            }
            map.put("time", DateUtils.timeStrToString(DateUtils.getCurrentTime(), "yyyy-MM-dd HH:mm:ss"));
        }
        return map;
    }

    /**
     * 投保消息发送参数转换
     *
     * @param applyBo 投保单对象
     */
    private Map<String, String> transferMessageParam(ApplyBo applyBo, BigDecimal paymentAmount) {
        Map<String, String> map = new HashMap<>();
        if (AssertUtils.isNotNull(applyBo)) {
            map.put("applyId", applyBo.getApplyId());
            map.put("applyNo", applyBo.getApplyNo());
            map.put("businessNo", applyBo.getApplyNo());
            map.put("reason", applyBo.getUnderWriteRemark());
            if (AssertUtils.isNotEmpty(applyBo.getPolicyNo())) {
                map.put("policyNo", applyBo.getPolicyNo());
            }
            if (AssertUtils.isNotEmpty(applyBo.getPolicyId())) {
                map.put("policyId", applyBo.getPolicyId());
            }
            if (AssertUtils.isNotNull(applyBo.getApplyDate())) {
                map.put("applyDate", DateUtils.timeStrToString(applyBo.getApplyDate(), "yyyy-MM-dd HH:mm:ss"));
            }
            if (AssertUtils.isNotNull(paymentAmount)) {
                map.put("premium", paymentAmount.toString());
            }
            if (AssertUtils.isNotNull(applyBo.getApplicant())) {
                map.put("applicantName", AssertUtils.isNotEmpty(applyBo.getApplicant().getName()) ? applyBo.getApplicant().getName() : "无");
                map.put("customerName", AssertUtils.isNotEmpty(applyBo.getApplicant().getName()) ? applyBo.getApplicant().getName() : "无");
            } else {
                map.put("applicantName", "无");
                map.put("customerName", "无");
            }

            if (AssertUtils.isNotNull(applyBo.getEffectiveDate())) {
                //判断，如果生效日期是第二天，则取当天，如果生效日期大于第二天(指定某天生效)则取生效日期
                try {
                    //第二天
                    Date nextCurrentDate = DateUtils.addDays(DateUtils.getTimeStrToDate(System.currentTimeMillis() + "", DateUtils.FORMATE3), 1);
                    //生效日期
                    Date effectiveDate = DateUtils.getTimeStrToDate(applyBo.getEffectiveDate() + "", DateUtils.FORMATE3);
                    if (nextCurrentDate.getTime() == effectiveDate.getTime()) {
                        map.put("effectiveDate", DateUtils.timeStrToString(System.currentTimeMillis(), "dd-MM-yyyy"));
                    } else {
                        map.put("effectiveDate", DateUtils.timeStrToString(applyBo.getEffectiveDate(), "dd-MM-yyyy"));
                    }
                } catch (Exception e) {

                }
            }

            if (AssertUtils.isNotEmpty(applyBo.getListInsured()) && AssertUtils.isNotEmpty(applyBo.getListInsured().get(0).getListCoverage())) {
                applyBo.getListInsured().get(0).getListCoverage().forEach(applyCoverageBo -> {
                    if (applyCoverageBo.getPrimaryFlag().equals(ApplyTermEnum.PRODUCT_PRIMARY_FLAG.MAIN.name())) {
                        map.put("productName", applyCoverageBo.getProductName());
                    }
                });
            }

            if (!map.containsKey("productName")) {
                if (AssertUtils.isNotEmpty(applyBo.getListApplyCoverageAcceptBo())) {
                    applyBo.getListApplyCoverageAcceptBo().forEach(applyCoverageAcceptBo -> {
                        if (applyCoverageAcceptBo.getPrimaryFlag().equals(ApplyTermEnum.PRODUCT_PRIMARY_FLAG.MAIN.name())) {
                            ResultObject<ProductSimpleInfoResponse> productSimple = productApi.getProductSimpleInfo(applyCoverageAcceptBo.getProductId(), applyBo.getSalesBranchId());
                            map.put("productName", productSimple.getData().getProductName());
                        }
                    });
                }
            }
            if (AssertUtils.isNotNull(applyBo.getApplyAgentBo())) {
                map.put("agentName", applyBo.getApplyAgentBo().getAgentName());
                map.put("agentCode", applyBo.getApplyAgentBo().getAgentCode());
                map.put("recommendAgentName", applyBo.getApplyAgentBo().getRecommendAgentName());
            }
            map.put("time", DateUtils.timeStrToString(DateUtils.getCurrentTime(), "yyyy-MM-dd HH:mm:ss"));
        }
        return map;
    }

    /**
     * 投保消息发送参数转换
     *
     * @param applyBo 投保单对象
     */
    private Map<String, String> transferMessageMapParam(ApplyBo applyBo, Map<String, String> paramMap) {
        Map<String, String> map = new HashMap<>();
        if (AssertUtils.isNotNull(applyBo)) {

            if (AssertUtils.isNotNull(paramMap)) {
                map.putAll(paramMap);
                ;
            }
            map.put("applyId", applyBo.getApplyId());
            map.put("applyNo", applyBo.getApplyNo());
            map.put("businessNo", applyBo.getApplyNo());
            if (AssertUtils.isNotEmpty(applyBo.getPolicyNo())) {
                map.put("policyNo", applyBo.getPolicyNo());
            }
            if (AssertUtils.isNotEmpty(applyBo.getPolicyId())) {
                map.put("policyId", applyBo.getPolicyId());
            }
            if (AssertUtils.isNotNull(applyBo.getApplyDate())) {
                map.put("applyDate", DateUtils.timeStrToString(applyBo.getApplyDate(), "yyyy-MM-dd HH:mm:ss"));
            }
            if (AssertUtils.isNotNull(applyBo.getReceivablePremium())) {
                map.put("premium", applyBo.getReceivablePremium().toString());
            }
            if (AssertUtils.isNotNull(applyBo.getApplicant())) {
                map.put("applicantName", AssertUtils.isNotEmpty(applyBo.getApplicant().getName()) ? applyBo.getApplicant().getName() : "无");
            } else {
                map.put("applicantName", "无");
            }

            if (AssertUtils.isNotNull(applyBo.getEffectiveDate())) {
                //判断，如果生效日期是第二天，则取当天，如果生效日期大于第二天(指定某天生效)则取生效日期
                try {
                    //第二天
                    Date nextCurrentDate = DateUtils.addDays(DateUtils.getTimeStrToDate(System.currentTimeMillis() + "", DateUtils.FORMATE3), 1);
                    //生效日期
                    Date effectiveDate = DateUtils.getTimeStrToDate(applyBo.getEffectiveDate() + "", DateUtils.FORMATE3);
                    if (nextCurrentDate.getTime() == effectiveDate.getTime()) {
                        map.put("effectiveDate", DateUtils.timeStrToString(System.currentTimeMillis(), "dd-MM-yyyy"));
                    } else {
                        map.put("effectiveDate", DateUtils.timeStrToString(applyBo.getEffectiveDate(), "dd-MM-yyyy"));
                    }
                } catch (Exception e) {

                }
            }

            if (AssertUtils.isNotEmpty(applyBo.getListInsured()) && AssertUtils.isNotEmpty(applyBo.getListInsured().get(0).getListCoverage())) {
                applyBo.getListInsured().get(0).getListCoverage().forEach(applyCoverageBo -> {
                    if (applyCoverageBo.getPrimaryFlag().equals(ApplyTermEnum.PRODUCT_PRIMARY_FLAG.MAIN.name())) {
                        map.put("productName", applyCoverageBo.getProductName());
                    }
                });
            }

            if (!map.containsKey("productName")) {
                if (AssertUtils.isNotEmpty(applyBo.getListApplyCoverageAcceptBo())) {
                    applyBo.getListApplyCoverageAcceptBo().forEach(applyCoverageAcceptBo -> {
                        if (applyCoverageAcceptBo.getPrimaryFlag().equals(ApplyTermEnum.PRODUCT_PRIMARY_FLAG.MAIN.name())) {
                            ResultObject<ProductSimpleInfoResponse> productSimple = productApi.getProductSimpleInfo(applyCoverageAcceptBo.getProductId(), applyBo.getSalesBranchId());
                            map.put("productName", productSimple.getData().getProductName());
                        }
                    });
                }
            }
            if (AssertUtils.isNotNull(applyBo.getApplyAgentBo())) {
                map.put("agentName", applyBo.getApplyAgentBo().getAgentName());
                map.put("agentCode", applyBo.getApplyAgentBo().getAgentCode());
                map.put("recommendAgentName", applyBo.getApplyAgentBo().getRecommendAgentName());
            }
            map.put("time", DateUtils.timeStrToString(DateUtils.getCurrentTime(), "yyyy-MM-dd HH:mm:ss"));
        }
        return map;
    }

    /**
     * 问题件回退消息发送参数转换
     *
     * @param applyBo         投保单对象
     * @param currentCodeName 当前结点名
     */
    private Map transferMessageParam(ApplyBo applyBo, String currentCodeName) {
        Map map = new HashMap<>();
        Map<String, String> map1 = new HashMap<>();
        if (AssertUtils.isNotNull(applyBo)) {
            map.put("applyId", applyBo.getApplyId());
            map.put("applyNo", applyBo.getApplyNo());
            if (AssertUtils.isNotEmpty(applyBo.getPolicyNo())) {
                map.put("policyNo", applyBo.getPolicyNo());
            }
            if (AssertUtils.isNotNull(applyBo.getApplyDate())) {
                map.put("applyDate", DateUtils.timeStrToString(applyBo.getApplyDate(), "yyyy-MM-dd HH:mm:ss"));
            }
            if (AssertUtils.isNotNull(applyBo.getReceivablePremium())) {
                map.put("premium", applyBo.getReceivablePremium().toString());
            }
            if (AssertUtils.isNotNull(applyBo.getApplicant())) {
                map.put("applicantName", applyBo.getApplicant().getName());
            }
            if (AssertUtils.isNotNull(applyBo.getApplyAgentBo())) {
                map.put("agentName", applyBo.getApplyAgentBo().getAgentName());
                map.put("agentCode", applyBo.getApplyAgentBo().getAgentCode());
                map.put("recommendAgentName", applyBo.getApplyAgentBo().getRecommendAgentName());
            }
            map.put("time", DateUtils.timeStrToString(DateUtils.getCurrentTime(), "yyyy-MM-dd HH:mm:ss"));
        }
        map1.put("key", currentCodeName);
        map1.put("type", "TASK_NODE");
        map.put("flowName", map1);
        //map.put("flowName",currentCodeName);
        return map;
    }

    @Override
    public void generateApplyPlanMessage(String businessCode, String userId, Map<String, String> map) {

        BusinessMessagePushRequest businessMessagePushReqFc = new BusinessMessagePushRequest();
        businessMessagePushReqFc.setBusinessCode(businessCode);
        businessMessagePushReqFc.setUserIdList(Arrays.asList(userId));
        businessMessagePushReqFc.setMessageParam(JSON.toJSONString(map));

        this.getLogger().info("消息推送发送详情 = = " + businessCode + " = 用户ID" + userId + " = 数据" + JSON.toJSONString(map));
        businessMessageApi.pushBusinessMessage(businessMessagePushReqFc);

    }

    @Override
    public void applyInvalidRemindMessage(String businessCode, String userId, Map<String, String> map) {
        BusinessMessagePushRequest businessMessagePushReqFc = new BusinessMessagePushRequest();
        businessMessagePushReqFc.setBusinessCode(businessCode);
        businessMessagePushReqFc.setUserIdList(Arrays.asList(userId));
        businessMessagePushReqFc.setMessageParam(JSON.toJSONString(map));

        this.getLogger().info("消息推送发送详情 = = " + businessCode + " = 用户ID" + userId + " = 数据" + JSON.toJSONString(map));
        businessMessageApi.pushBusinessMessage(businessMessagePushReqFc);

    }

    @Override
    public void applyInvalidRemindMessageCustomer(String businessCode, String applicantMobile, Map<String, String> messageParamMap) {
        BusinessMessagePushRequest businessMessagePushReqFc = new BusinessMessagePushRequest();
        businessMessagePushReqFc.setBusinessCode(businessCode);
        businessMessagePushReqFc.setMessageParam(JSON.toJSONString(messageParamMap));
        businessMessagePushReqFc.setLanguage(TerminologyConfigEnum.LANGUAGE.EN_US.name());

        List<ReceiverUserRequest> receiverUsers = new ArrayList<>();
        ReceiverUserRequest receiverUserRequest = new ReceiverUserRequest();
        receiverUserRequest.setMsgChannel("SMS");
        receiverUserRequest.setReceiverUser(applicantMobile);
        receiverUsers.add(receiverUserRequest);
        businessMessagePushReqFc.setReceiverUsers(receiverUsers);

        this.getLogger().info("消息推送发送详情 = = " + businessCode + " = 手机号" + applicantMobile + " = 数据" + JSON.toJSONString(messageParamMap));
        businessMessageApi.pushBusinessMessage(businessMessagePushReqFc);

    }

    /**
     * 上传银行转账支付凭证消息发送
     *
     * @param businessCode 消息类型
     * @param applyBo      接收用户
     */
    public void pushBusinessCertificateMessage(String businessCode, ApplyBo applyBo) {

        BusinessMessagePushRequest businessMessagePushReqFc = new BusinessMessagePushRequest();
        businessMessagePushReqFc.setBusinessCode(businessCode);
        Map<String, String> messageParam = new HashMap<>();
        messageParam.put("applyNo", applyBo.getApplyNo());
        List<String> list = new ArrayList<>();
        if (AssertUtils.isNotNull(applyBo.getApplyAgentBo())) {
            list.add(applyBo.getApplyAgentBo().getAgentId());
        }
        businessMessagePushReqFc.setUserIdList(list);
        businessMessagePushReqFc.setMessageParam(JSON.toJSONString(messageParam));
        this.getLogger().error("businessMessagePushReqFc:" + JSON.toJSONString(businessMessagePushReqFc));
        businessMessageApi.pushBusinessMessage(businessMessagePushReqFc);

    }

    /**
     * 上传银行转账支付凭证微信钉钉消息发送
     *
     * @param businessCode 消息类型
     * @param applyBo      接收用户
     */
    public void pushBusinessCertificateMessageNew(String businessCode, ApplyBo applyBo) {
        BusinessMessagePushRequest businessMessagePushReqFc = new BusinessMessagePushRequest();
        businessMessagePushReqFc.setBusinessCode(businessCode);
        Map<String, String> messageParam = new HashMap<>();
        messageParam.put("applyNo", applyBo.getApplyNo());
        List<String> userIds = platformUsersApi.getBusinessUsers(businessCode, applyBo.getSalesBranchId()).getData();
        messageParam.put("time", DateUtils.timeStrToString(DateUtils.getCurrentTime(), "yyyy-MM-dd HH:mm:ss"));
        businessMessagePushReqFc.setMessageParam(JSON.toJSONString(messageParam));
        businessMessagePushReqFc.setUserIdList(userIds);
        this.getLogger().error("businessMessagePushReqFc:" + JSON.toJSONString(businessMessagePushReqFc));
        businessMessageApi.pushBusinessMessage(businessMessagePushReqFc);
    }

    /**
     * 发送消息给客户
     *
     * @param businessCode    消息类型
     * @param applyBo         投保单数据
     * @param users           当前用户
     * @param appRequestHeads 请求头
     * @param receivableDate
     * @param changeType
     */
    @Override
    public void pushApplyChangeMessageCustomer(String businessCode, ApplyBo applyBo, Users users, AppRequestHeads appRequestHeads, Long receivableDate, String changeType) {
        try {
            Map<String, String> map = new HashMap<>();
            map.put("applyNo", applyBo.getApplyNo());
            SyscodeResponse syscodeRespFc = platformInternationalBaseApi.queryOneInternational("CHANGE_TYPE", changeType, TerminologyConfigEnum.LANGUAGE.EN_US.name()).getData();
            map.put("changeType", AssertUtils.isNotNull(syscodeRespFc) ? syscodeRespFc.getCodeName() : changeType);
            map.put("receivablePremium", applyBo.getReceivablePremium().toString());
            if (AssertUtils.isNotNull(applyBo.getApplicant())) {
                map.put("applicantName", ApplyTermEnum.APPLY_TYPE.LIFE_INSURANCE_PERSONAL.name().equals(applyBo.getApplyType()) ?
                        applyBo.getApplicant().getName() : applyBo.getApplicant().getDelegateName());
            }
            map.put("receivableDate", DateUtils.timeStrToString(receivableDate, DateUtils.FORMATE6));

            if (AssertUtils.isNotEmpty(applyBo.getListInsured()) && AssertUtils.isNotEmpty(applyBo.getListInsured().get(0).getListCoverage())) {
                applyBo.getListInsured().get(0).getListCoverage().forEach(applyCoverageBo -> {
                    if (applyCoverageBo.getPrimaryFlag().equals(ApplyTermEnum.PRODUCT_PRIMARY_FLAG.MAIN.name())) {
                        map.put("productName", applyCoverageBo.getProductName());
                    }
                });
            }
            transPaymentShortUrl(applyBo, map);

            BusinessMessagePushRequest businessMessagePushReqFc = new BusinessMessagePushRequest();
            businessMessagePushReqFc.setBusinessCode(businessCode);
            businessMessagePushReqFc.setMessageParam(JSON.toJSONString(map));
            businessMessagePushReqFc.setDeviceChannel(appRequestHeads.getDeviceChannel());
            businessMessagePushReqFc.setLanguage(TerminologyConfigEnum.LANGUAGE.EN_US.name());

            List<ReceiverUserRequest> receiverUsers = new ArrayList<>();
            ReceiverUserRequest receiverUserRequest = new ReceiverUserRequest();
            receiverUserRequest.setMsgChannel("SMS");
            if (AssertUtils.isNotNull(applyBo.getApplicant())) {
                receiverUserRequest.setReceiverUser(ApplyTermEnum.APPLY_TYPE.LIFE_INSURANCE_PERSONAL.name().equals(applyBo.getApplyType()) ?
                        applyBo.getApplicant().getMobile() : applyBo.getApplicant().getDelegateMobile());
            }
            receiverUsers.add(receiverUserRequest);
            businessMessagePushReqFc.setReceiverUsers(receiverUsers);

            this.getLogger().error("businessMessagePushReqFc:" + JSON.toJSONString(businessMessagePushReqFc));

            businessMessageApi.pushBusinessMessage(businessMessagePushReqFc);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    /**
     * 发送消息给客户
     *
     * @param businessCode    消息类型
     * @param applyBo         投保单数据
     * @param users           当前用户
     * @param appRequestHeads 请求头
     * @param receivableDate
     * @param changeType
     */
    @Override
    public void pushApplyChangeMessageAgent(String businessCode, ApplyBo applyBo, Users users, AppRequestHeads appRequestHeads, Long receivableDate, String changeType) {
        try {
            Map map = new HashMap<>();
            map.put("applyNo", applyBo.getApplyNo());
            //国际化
            Map<String, String> changeTypeMap = new HashMap<>();
            changeTypeMap.put("key", changeType);
            changeTypeMap.put("type", "CHANGE_TYPE");
            map.put("changeType", changeTypeMap);
            map.put("receivablePremium", applyBo.getReceivablePremium().toString());
            if (AssertUtils.isNotNull(applyBo.getApplicant())) {
                map.put("applicantName", ApplyTermEnum.APPLY_TYPE.LIFE_INSURANCE_PERSONAL.name().equals(applyBo.getApplyType()) ?
                        applyBo.getApplicant().getName() : applyBo.getApplicant().getDelegateName());
            }
            BusinessMessagePushRequest businessMessagePushReqFc = new BusinessMessagePushRequest();
            businessMessagePushReqFc.setBusinessCode(businessCode);
            businessMessagePushReqFc.setMessageParam(JSON.toJSONString(map));
            businessMessagePushReqFc.setDeviceChannel(appRequestHeads.getDeviceChannel());
            List<String> userIdList = new ArrayList<>();
            if (AssertUtils.isNotNull(applyBo.getApplyAgentBo().getAgentId())) {
                userIdList.add(applyBo.getApplyAgentBo().getAgentId());
            }
            businessMessagePushReqFc.setUserIdList(userIdList);
            this.getLogger().error("businessMessagePushReqFc:" + JSON.toJSONString(businessMessagePushReqFc));

            businessMessageApi.pushBusinessMessage(businessMessagePushReqFc);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    /**
     * 发送刮刮卡承保消息给合作伙伴
     *
     * @param businessCode  消息类型
     * @param scratchCardPo 刮刮卡
     */
    @Override
    @Async
    public void pushSMSToPartner(String businessCode, ScratchCardPo scratchCardPo) {
        ScratchCardAssignBo scratchCardAssignBo = scratchCardBaseService.getScratchCardAssign(scratchCardPo.getScratchCardAssignId());
        if (!AssertUtils.isNotNull(scratchCardAssignBo) || !AssertUtils.isNotEmpty(scratchCardAssignBo.getContactMobile())) {
            return;
        }
        try {
            System.out.println("pushSMSToPartner ==================================");
            System.out.println(JSON.toJSONString(scratchCardPo));
            System.out.println("pushSMSToPartner ==================================");
            BusinessMessagePushRequest businessMessagePushReqFc = new BusinessMessagePushRequest();
            businessMessagePushReqFc.setBusinessCode(businessCode);
            Map<String, String> map = new HashMap<>();
            map.put("partnerName", scratchCardAssignBo.getPartnerName());
            map.put("referenceNo", scratchCardPo.getReferenceNo());
            businessMessagePushReqFc.setMessageParam(JSON.toJSONString(map));
            businessMessagePushReqFc.setLanguage(TerminologyConfigEnum.LANGUAGE.EN_US.name());
            businessMessagePushReqFc.setDeviceChannel("gclife_agent_app");
            List<ReceiverUserRequest> receiverUserRequests = new ArrayList<>();
            ReceiverUserRequest receiverUserRequest = new ReceiverUserRequest();
            receiverUserRequest.setMsgChannel("SMS");
            receiverUserRequest.setReceiverUser(scratchCardAssignBo.getContactMobile());
            receiverUserRequests.add(receiverUserRequest);
            businessMessagePushReqFc.setReceiverUsers(receiverUserRequests);
            this.getLogger().info("pushSMSToPartner businessMessagePushReqFc:{}", JSON.toJSONString(businessMessagePushReqFc));
            businessMessageApi.pushBusinessMessage(businessMessagePushReqFc);

        } catch (Exception e) {
            System.out.println("pushSMSToPartner ====error==============================");
            e.printStackTrace();
        }
    }
}
