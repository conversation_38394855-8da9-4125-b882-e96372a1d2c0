package com.gclife.apply.controller;

import com.gclife.apply.model.request.ApplyRequest;
import com.gclife.apply.model.request.ApplyWaitInputListRequest;
import com.gclife.apply.model.request.ProductHealthNoticeRequest;
import com.gclife.apply.model.response.*;
import com.gclife.apply.service.business.ApplyInputBusinessService;
import com.gclife.apply.service.business.ApplyWaitInputBusinessService;
import com.gclife.common.controller.base.BaseController;
import com.gclife.common.model.BasePageResponse;
import com.gclife.common.model.ResultObject;
import com.gclife.common.model.feign.SyscodeRespFc;
import io.swagger.annotations.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * <AUTHOR>
 * create 17-9-12
 */
@Api(tags = "apply_input", description = "投保单录入")
@RestController
@RequestMapping(value = "/v1/")
public class InputController extends BaseController {

    @Autowired
    private ApplyWaitInputBusinessService applyWaitInputBusinessService;

    @Autowired
    private ApplyInputBusinessService applyInputBusinessService;

    @ApiOperation(value = "inputs", notes = "分页查询待录入列表")
    @ApiImplicitParams(
            {
                    @ApiImplicitParam(name = "applyNo", value = "投保单号", example = "180308003A1234567890000398765432101", paramType = "query"),
                    @ApiImplicitParam(name = "salesBranchId", value = "销售机构", example = "8642", paramType = "query"),
                    @ApiImplicitParam(name = "inputStatus", value = "录入状态", example = "waitInput", paramType = "query"),
                    @ApiImplicitParam(name = "acceptStartDate", value = "受理时间-开始时间", example = "1509266964000", paramType = "query"),
                    @ApiImplicitParam(name = "acceptEndDate", value = "受理时间-结束时间", example = "1509366964000", paramType = "query"),
                    @ApiImplicitParam(name = "applyId", value = "投保单ID", example = "001", paramType = "query"),
                    @ApiImplicitParam(name = "channelTypeCode", value = "渠道类型", example = "01", paramType = "query")
            }
    )
    @ApiResponses({
            @ApiResponse(code = 200, message = "请求成功")
    })
    @GetMapping(value = "inputs")
    public ResultObject<BasePageResponse<InputListResponse>> getWaitInputList(
            ApplyWaitInputListRequest applyWaitInputListRequest
    ) {
  /*
           1.调用工作流接口，（参数：当前用户ID,节点编码，受理完成时间，录入状态），工作流返回筛选后的投保单ID
           2.从返回的投保单id再根据渠道类型和销售机构（销售机构）筛选返回给前台页面
  */


        return applyWaitInputBusinessService.getWaitInputs(getCurrentLoginUsers().getUserId(), applyWaitInputListRequest);
    }


    @ApiOperation(value = "input/sign", notes = "待录入签收")
    @ApiImplicitParams(
            {
                    @ApiImplicitParam(name = "applyId", value = "投保单ID", example = "180308003A1234567890000398765432101", paramType = "query")
            }
    )
    @ApiResponses({
            @ApiResponse(code = 200, message = "请求成功")
    })
    @PutMapping(value = "input/sign")
    public ResultObject waitInputSign(
            String applyId
    ) {
        return applyWaitInputBusinessService.waitInputInfoSign(getCurrentLoginUsers().getUserId(), applyId);
    }


    @ApiOperation(value = "投保单录入提交", notes = "投保单录入提交")
    @ApiResponses({
            @ApiResponse(code = 200, message = "请求成功")
    })
    @PutMapping(value = "input/submit")
    public ResultObject applyInputSubmit(@RequestBody ApplyRequest applyRequest) {

        return applyWaitInputBusinessService.submitInputApply(applyRequest);
    }


    @ApiOperation(value = "投保单录入保存", notes = "投保单录入保存")
    @ApiResponses({
            @ApiResponse(code = 200, message = "请求成功")
    })
    @PutMapping(value = "input/save")
    public ResultObject applyInputSave(@RequestBody ApplyRequest applyRequest) {

        return applyWaitInputBusinessService.saveInputApply(getCurrentLoginUsers().getUserId(), applyRequest, false);
    }


    @ApiOperation(value = "004/input", notes = "投保单录入详细信息查询")
    @ApiImplicitParams(
            {
                    @ApiImplicitParam(name = "applyId", value = "投保单ID", example = "APPLY_6fca5ada-991c-4df4-b365-7b410958b74a", paramType = "query"),

            }
    )
    @ApiResponses({
            @ApiResponse(code = 200, message = "请求成功")
    })
    @GetMapping(value = "{applyId}/input")
    public ResultObject<ApplyInputResponse> inputApplyGet(@ApiParam(value = "投保单ID", defaultValue = "004", example = "1") @PathVariable(value = "applyId") String applyId) {
        return applyInputBusinessService.loadInputApplyById(this.getCurrentLoginUsers().getUserId(),this.getAppRequestHandler(), applyId);
    }

    @ApiOperation(value = "input/salebranchs", notes = "获取当前用户下的销售机构")
    @ApiResponses({
            @ApiResponse(code = 200, message = "请求成功")
    })
    @GetMapping(value = "input/salebranchs")
    public ResultObject getSaleBranchs() {
        return applyWaitInputBusinessService.getSaleBranchByUserId(getCurrentLoginUsers().getUserId());
    }


    @ApiOperation(value = "input/dictionaries", notes = "查询字典数据列表,录入页所有字典数据查询,并返回字典数据集")
    @ApiResponses({
            @ApiResponse(code = 200, message = "请求成功")
    })
    @GetMapping(value = "input/dictionaries")
    public ResultObject<InputDictionariesResponse> getInputDictionaries(String applyId) {
        return applyWaitInputBusinessService.getDictionaries(this.getCurrentLoginUsers(), applyId);
    }

    @ApiOperation(value = "input/status", notes = "获取录入状态</br>\n" +
            "1.查询录入状态列表\n")
    @ApiResponses({
            @ApiResponse(code = 200, message = "请求成功")
    })
    @GetMapping(value = "input/status")
    public ResultObject<List<SyscodeRespFc>> inputStatusGet(
    ) {
        return applyWaitInputBusinessService.getInputStatus();
    }
}