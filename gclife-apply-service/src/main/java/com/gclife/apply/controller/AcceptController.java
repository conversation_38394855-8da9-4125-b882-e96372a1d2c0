package com.gclife.apply.controller;

import com.gclife.agent.model.response.AgentKeyWordResponse;
import com.gclife.agent.model.response.AgentResponse;
import com.gclife.apply.model.feign.ApplyAgentRespFc;
import com.gclife.apply.model.request.AcceptInfoSaveRequest;
import com.gclife.apply.model.request.AcceptsRequest;
import com.gclife.apply.model.response.AcceptEntryResponse;
import com.gclife.apply.model.response.AcceptListResponse;
import com.gclife.apply.model.response.AcceptUserInfoResponse;
import com.gclife.apply.service.business.AcceptsBusinessService;
import com.gclife.certify.model.response.CertifyResponse;
import com.gclife.common.configuration.system.permissions.AutoCheckPermissions;
import com.gclife.common.controller.base.BaseController;
import com.gclife.common.model.BasePageResponse;
import com.gclife.common.model.ResultObject;
import com.gclife.common.model.feign.SyscodeRespFc;
import com.gclife.platform.model.response.BranchTreeResponse;
import com.gclife.platform.model.response.ChannelsResponse;
import com.gclife.product.model.response.insurnce.certify.CertifyClassResponse;
import io.swagger.annotations.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * <AUTHOR> by cfw on 17-9-5.
 */
@Api(tags = "apply_accept", description = "投保单受理")
@RestController
@RequestMapping(value = "/v1/")
public class AcceptController extends BaseController {

    private static final Logger LOGGER = LoggerFactory.getLogger(AcceptController.class);

    @Autowired
    private AcceptsBusinessService acceptsBusinessService;

    @ApiOperation(value = "accepts", notes = "分页查询受理信息</br>\n" +
            "1.根据当前用户ID、businessType查询待办任务数据，在非查询条件時，返回前100条数据，默认把已签收数据放在最前面</br>\n" +
            "2.根據查询到的业务数据id，查询当ddddsfsadfsdf前投保单受理信息</br>\n" +
            "sql:select a.apply_id,a.apply_no,a.channel_type_code,a.sales_branch_id,b.agent_id\n" +
            "  from apply a\n" +
            "left join apply_agent b on(a.apply_id=b.apply_id)\n" +
            "where a.sales_branch_id=? and a.channel_type_code=?</br>\n" +
            "3.代理人信息需要调用\"agent\"模块传入agent_id查询代理人</br>\n" +
            "4.机构信息需要调用\"platform\"模块传入sales_branch_id查主机构信息</br>\n" +
            "5.交单时间采用工作流开始时间</br>\n" +
            "6.受理状态采用工作流节点状态</br>\n" +
            "7.字典数据查询均需要国际化支持\n")
    @ApiResponses({
            @ApiResponse(code = 200, message = "请求成功")
    })
    @GetMapping(value = "accepts")
    @AutoCheckPermissions
    public ResultObject<BasePageResponse<AcceptListResponse>> getAcceptList(
            AcceptsRequest acceptsRequest
    ) {
  /*
           1.调用工作流接口，（参数：当前用户ID,节点编码，handDate，受理状态），工作流返回筛选后的投保单ID
           2.从返回的投保单id再根据渠道类型和销售机构（销售机构）筛选返回给前台页面
  */
        return acceptsBusinessService.loadAcceptList(getCurrentLoginUsers().getUserId(), acceptsRequest);
    }


    @ApiOperation(value = "accept/channeltypes", notes = "获取渠道类型</br>\n" +
            "1.查询当前机构管理的所有的渠道类型\n")
    @ApiResponses({
            @ApiResponse(code = 200, message = "请求成功")
    })
    @GetMapping(value = "accept/channeltypes")
    public ResultObject<List<ChannelsResponse>> getChanneltypes(
    ) {
  /*
           1.获取当前用户管理的所有机构,并得到对应的渠道类型集合
           2.通过渠道类型查询字段数据，并调用国际化接口，查询国际化的名称
  */
        return acceptsBusinessService.getChannelsByUserId(getCurrentLoginUsers().getUserId());
    }

    @ApiOperation(value = "accept/salebranchs", notes = "查询当前用户管理的销售机构"
    )
    @ApiResponses({
            @ApiResponse(code = 200, message = "请求成功")
    })
    @GetMapping(value = "accept/salebranchs")
    public ResultObject<List<BranchTreeResponse>> saleBranchsGet(
    ) {
        return acceptsBusinessService.getSaleBranchs(getCurrentLoginUsers().getUserId());
    }

    @ApiOperation(value = "受理获取附件", notes = "根据产品ID获取附件"
    )
    @ApiResponses({
            @ApiResponse(code = 200, message = "请求成功")
    })
    @ApiImplicitParams(
            {
                    @ApiImplicitParam(name = "productId", value = "产品ID", example = "产品ID", paramType = "query")
            }
    )
    @GetMapping(value = "accept/certify/attachments")
    public ResultObject<CertifyClassResponse> attachmentListGet(String productId) {
        return acceptsBusinessService.attachmentListGet(productId);
    }


    @ApiOperation(value = "accept/status", notes = "获取受理状态</br>\n" +
            "1.查询受理状态列表\n")
    @ApiResponses({
            @ApiResponse(code = 200, message = "请求成功")
    })
    @GetMapping(value = "accept/status")
    public ResultObject<List<SyscodeRespFc>> getAcceptStatus(
    ) {
        return acceptsBusinessService.getAcceptStatus();
    }


    @ApiOperation(value = "accept/sign", notes = "待受理单签收<br/>\n" +
            "1.调用工作流签收动作执行签收")
    @ApiImplicitParams(
            {
                    @ApiImplicitParam(name = "applyId", value = "投保单ID", example = "180308003A1234567890000398765432101", paramType = "query")

            }
    )
    @ApiResponses({
            @ApiResponse(code = 200, message = "请求成功")
    })
    @PutMapping(value = "accept/sign")
    public ResultObject waitAcceptSign(
            String applyId
    ) {
        return acceptsBusinessService.waitAcceptInfoSign(getCurrentLoginUsers().getUserId(), applyId);
    }


    @ApiOperation(value = "accept/{applyId}", notes = "获取受理信息</br>\n" +
            "1.根据投保单id查询受理信息</br>\n")
    @ApiResponses({
            @ApiResponse(code = 200, message = "请求成功")
    })
    @AutoCheckPermissions
    @ApiParam(value = "投保单ID", name = "applyId", example = "1")
    @GetMapping(value = "accept/{applyId}")
    public ResultObject<AcceptEntryResponse> getAcceptByApplyId(
            @PathVariable(value = "applyId") String applyId
    ) {
        return acceptsBusinessService.loadAcceptInfoByApplyId(applyId);
    }


    @ApiOperation(value = "accept/certify/{certifyNo}", notes = "获取单证列表</br>\n" +
            "1.根据单证编码搜索查询单信息信息</br>\n")
    @ApiResponses({
            @ApiResponse(code = 200, message = "请求成功")
    })
    @ApiParam(value = "单证号码", example = "1")
    @GetMapping(value = "accept/certify/{certifyNo}")
    public ResultObject<CertifyResponse> getCertifys(@PathVariable(value = "certifyNo") String certifyNo
    ) {
        return acceptsBusinessService.loadCertifyByCertifyCode(certifyNo);
    }


    @ApiOperation(value = "accept/agent/{agentCode}", notes = "获取代理人信息</br>\n" +
            "1.根据代理人编码查询代理人信息</br>\n")
    @ApiResponses({
            @ApiResponse(code = 200, message = "请求成功")
    })
    @GetMapping(value = "accept/agent/{agentCode}")
    public ResultObject<ApplyAgentRespFc> getAgent(
            @ApiParam(value = "代理人编码", example = "1") @PathVariable(value = "agentCode") String agentCode
    ) {
        return acceptsBusinessService.getAgentByAgentCode(agentCode);
    }

    @ApiOperation(value = "keyword/agents", notes = "模糊查询获取代理人列表</br>\n" +
            "1.根据代理人编码关键字查询代理人信息</br>\n")
    @ApiImplicitParams(
            {
                    @ApiImplicitParam(name = "keyword", value = "代理人关键字", example = "邓", paramType = "query")

            }
    )
    @ApiResponses({
            @ApiResponse(code = 200, message = "请求成功")
    })
    @GetMapping(value = "keyword/agents")
    public ResultObject<List<AgentKeyWordResponse>> getAgentByKeyword(String keyword) {
        return acceptsBusinessService.getAgentListByKeyword(keyword);
    }


    @ApiOperation(value = "accept", notes = "受理信息保存(新建)")
    @ApiResponses({
            @ApiResponse(code = 200, message = "请求成功")
    })
    @PostMapping(value = "accept/save")
    public ResultObject acceptInfoSaveNew(@RequestBody AcceptInfoSaveRequest acceptInfoSaveRequest) {

        /*O:
        1.先判断apply_accept表是否存在该投保单记录，如果存在则更新，不存在则插入数据：创建受理ID,单证ID,投保单ID，销售机构,首期保费,总页数,代理人ID,代理人姓名,有效标识
        2.先判断apply_image_type表是否存在该投保单记录，如果存在则更新，不存在则插入数据，
        3.先判断apply_coverage_accept表是否存在记录，存在则更新，不存在则插入

        然后根据id进行分类，分两种情况：
        1.业务员已经扫描投保单（只做信息的存储），不推动工作流
        2.业务员没有扫描投保单直接进行保存，此时需要调用工作流两次，第一次启动工作流，第二次完成签收动作
        */

        return acceptsBusinessService.saveAccept(getCurrentLoginUsers().getUserId(), acceptInfoSaveRequest);
    }


    @ApiOperation(value = "accept/{applyId}", notes = "受理信息保存(修改)")
    @ApiResponses({
            @ApiResponse(code = 200, message = "请求成功")
    })
    @ApiParam(value = "投保单ID", example = "1")
    @PutMapping(value = "accept/{applyId}")
    public ResultObject acceptInfoSavesUpdate(@PathVariable(value = "applyId") String applyId, @RequestBody AcceptInfoSaveRequest acceptInfoSaveRequest
    ) {
        /*
        1.先判断apply_accept表是否存在该投保单记录，如果存在则更新，不存在则插入数据：创建受理ID,单证ID,投保单ID，销售机构,首期保费,总页数,代理人ID,代理人姓名,有效标识
        2.先判断apply_image_type表是否存在该投保单记录，如果存在则更新，不存在则插入数据，
        3.先判断apply_coverage_accept表是否存在记录，存在则更新，不存在则插入

        然后根据id进行分类，分两种情况：
        1.业务员已经扫描投保单（只做信息的存储），不推动工作流
        2.业务员没有扫描投保单直接进行保存，此时需要调用工作流两次，第一次启动工作流，第二次完成签收动作
        */

        return acceptsBusinessService.putAccept(getCurrentLoginUsers().getUserId(), acceptInfoSaveRequest, applyId);
    }


    @ApiOperation(value = "accept/pass", notes = "受理信息通过")
    @ApiResponses({
            @ApiResponse(code = 200, message = "请求成功")
    })
    @PostMapping(value = "accept/pass")
    public ResultObject acceptInfoPass(@RequestBody AcceptInfoSaveRequest acceptInfoSaveRequest) {
        /*
        1.先判断apply_accept表是否存在该投保单记录，如果存在则更新，不存在则插入数据：创建受理ID,单证ID,投保单ID，销售机构,首期保费,总页数,代理人ID,代理人姓名,有效标识
        2.先判断apply_image_type表是否存在该投保单记录，如果存在则更新，不存在则插入数据，
        3.先判断apply_coverage_accept表是否存在记录，存在则更新，不存在则插入
        4.判断apply有没有记录，有就更新，没有就插入，如果有记录的话，只需要更新受理机构，投保单状态。

        然后根据id进行分类，分两种情况：
        1.业务员已经扫描投保单,或者保存过一次，则推动工作流到待扫描上传
        2.业务员没有扫描投保单直接进行受理通过，此时需要调用工作流三次，第一次启动工作流，第二次完成签收动作，第三次受理通过。
        */

        return acceptsBusinessService.commitAccept(getCurrentLoginUsers().getUserId(), acceptInfoSaveRequest);
    }

    @ApiOperation(value = "获取受理人信息集", notes = "根据投保单IDS查询受理人信息集")
    @ApiResponses({
            @ApiResponse(code = 200, message = "请求成功")
    })
    @GetMapping(value = "accept/acceptUsers")
    public ResultObject<List<AcceptUserInfoResponse>> getAcceptUserInfos(@RequestParam("applyId") List<String> applyIds) {
        return acceptsBusinessService.getAcceptUserInfos(applyIds);
    }

}
