package com.gclife.apply.controller;

import com.gclife.apply.model.request.ApplyListRequest;
import com.gclife.apply.model.response.ImageManageDetailResponse;
import com.gclife.apply.model.response.ImageManageListResponse;
import com.gclife.apply.model.response.ImageManageSaveRequest;
import com.gclife.apply.service.business.ApplyImageBusinessService;
import com.gclife.common.configuration.system.permissions.AutoCheckPermissions;
import com.gclife.common.controller.base.BaseController;
import com.gclife.common.model.BasePageResponse;
import com.gclife.common.model.ResultObject;
import com.gclife.common.model.feign.SyscodeRespFc;
import io.swagger.annotations.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * <AUTHOR>
 * create 18-4-2
 * description:影像管理
 */
@Api(tags = "image_manage", description = "影像管理")
@RestController
@RequestMapping(value = "/v1/")
public class ImageManageController extends BaseController {

    @Autowired
    private ApplyImageBusinessService applyImageBusinessService;

    @ApiOperation(value = "影像管理列表", notes = "影像管理列表")
    @ApiResponses({
            @ApiResponse(code = 200, message = "请求成功")
    })
    @GetMapping(value = "manage/images")
    public ResultObject<BasePageResponse<ImageManageListResponse>> getImageList(ApplyListRequest applyListRequest) {
        return applyImageBusinessService.getManageImages(this.getCurrentLoginUsers(), applyListRequest);
    }

    @ApiOperation(value = "影像管理-投保单来源下拉选", notes = "影像管理-投保单来源下拉选")
    @ApiResponses({
            @ApiResponse(code = 200, message = "请求成功")
    })
    @GetMapping(value = "manage/image/apply/sources")
    public ResultObject<List<SyscodeRespFc>> applySourcesGet() {
        return applyImageBusinessService.getApplySources();
    }

    @ApiOperation(value = "影像管理详情信息", notes = "影像管理详情信息")
    @ApiResponses({
            @ApiResponse(code = 200, message = "请求成功")
    })
    @GetMapping(value = "manage/image/{applyId}")
    public ResultObject<ImageManageDetailResponse> imageManageDetailGet(@PathVariable(value = "applyId") String applyId) {
        return applyImageBusinessService.getImageManageDetail(this.getCurrentLoginUsers(), applyId);
    }

    @ApiOperation(value = "保存影像管理附件信息", notes = "保存影像管理附件信息")
    @ApiResponses({
            @ApiResponse(code = 200, message = "请求成功")
    })
    @AutoCheckPermissions
    @PostMapping(value = "manage/image/save")
    public ResultObject saveImages(@RequestBody ImageManageSaveRequest imageManageSaveRequest) {
        return applyImageBusinessService.saveImageManage(getCurrentLoginUsers(), imageManageSaveRequest);
    }
}
