package com.gclife.apply.controller;

import com.gclife.apply.model.bo.ApplyListBo;
import com.gclife.apply.model.request.ApplyConditionListRequest;
import com.gclife.apply.model.request.ApplyConditionPrintRequest;
import com.gclife.apply.model.request.ApplyListRequest;
import com.gclife.apply.model.request.CalculationBmiAndAgeRequest;
import com.gclife.apply.model.respone.TemporaryCoverResponse;
import com.gclife.apply.model.response.*;
import com.gclife.apply.service.business.ApplyBusinessService;
import com.gclife.common.configuration.system.permissions.AutoCheckPermissions;
import com.gclife.common.controller.base.BaseController;
import com.gclife.common.model.BasePageResponse;
import com.gclife.common.model.ResultObject;
import com.gclife.common.model.feign.SyscodeRespFc;
import io.swagger.annotations.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.List;

/**
 * <AUTHOR>
 * create 18-4-23
 * description:
 */
@Api(tags = "apply", description = "投保单")
@RestController
@RequestMapping(value = "/v1/")
public class ApplyController extends BaseController {

    @Autowired
    private ApplyBusinessService applyBusinessService;


    @ApiOperation(value = "投保单列表", notes = "投保单列表")
    @ApiResponses({
            @ApiResponse(code = 200, message = "请求成功")
    })
    @AutoCheckPermissions
    @GetMapping(value = "applies")
    public ResultObject<BasePageResponse<ApplyQueryListResponse>> getApplyQueryList(ApplyListRequest applyListRequest) {
        return applyBusinessService.getApplyQueryList(getCurrentLoginUsers(), applyListRequest);
    }

    @ApiOperation(value = "代理人投保单列表", notes = "代理人投保单列表")
    @ApiResponses({
            @ApiResponse(code = 200, message = "请求成功")
    })
    @AutoCheckPermissions
    @GetMapping(value = "agent/applies")
    public ResultObject<BasePageResponse<ApplyQueryListResponse>> loadAgentApplyList(ApplyListRequest applyListRequest) {
        return applyBusinessService.loadAgentApplyList(getCurrentLoginUsers(), applyListRequest);
    }

    @ApiOperation(value = "查询投保单详情", notes = "查询投保单详情")
    @ApiResponses({
            @ApiResponse(code = 200, message = "请求成功")
    })
    @ApiImplicitParams({
            @ApiImplicitParam(name = "applyId", value = "投保单ID", example = "1", paramType = "query", required = true)
    })
    @AutoCheckPermissions
    @GetMapping(value = "apply/detail")
    public ResultObject<ApplyDetailResponse> queryApplyDetailGet(@RequestParam("applyId") String applyId) {
        return applyBusinessService.loadApplyDetail(applyId,this.getAppRequestHandler());
    }

    @ApiOperation(value = "查询健康告知详情(核心投保单详情调用)", notes = "查询健康告知详情(核心投保单详情调用)")
    @ApiResponses({
            @ApiResponse(code = 200, message = "请求成功")
    })
    @ApiImplicitParams({
            @ApiImplicitParam(name = "applyId", value = "投保单ID", example = "1", paramType = "query", required = true),
            @ApiImplicitParam(name = "language", value = "语言", example = "1", paramType = "query", required = false),
    })
    @AutoCheckPermissions
    @GetMapping(value = "apply/questionnaire/detail")
    public ResultObject<List<ApplyQuestionnaireResponse>> queryApplyQuestionnaireDetailGet(@RequestParam("applyId") String applyId, @RequestParam("language") String language) {
        return applyBusinessService.queryApplyQuestionnaireDetailGet(applyId, language);
    }

    @ApiOperation(value = "多条件查询投保单", notes = "多条件查询投保单")
    @ApiResponses({
            @ApiResponse(code = 200, message = "请求成功")
    })
    @AutoCheckPermissions
    @PostMapping(value = "apply/conditions")
    public ResultObject<BasePageResponse<ApplyQueryListResponse>> postApplyList(@RequestBody(required = false) ApplyConditionListRequest applyConditionListRequest) {
        return applyBusinessService.postApplyList(this.getCurrentLoginUsers(), applyConditionListRequest);
    }

    @ApiOperation(value = "投保单状态下拉选", notes = "投保单状态下拉选")
    @ApiResponses({
            @ApiResponse(code = 200, message = "请求成功")
    })
    @AutoCheckPermissions
    @GetMapping(value = "apply/statuses")
    public ResultObject<List<SyscodeRespFc>> getApplyStatusList() {
        return applyBusinessService.getApplyStatusList();
    }

    @ApiOperation(value = "暂保单详情(暂保条款)", notes = "暂保单详情(暂保条款)")
    @ApiResponses({
            @ApiResponse(code = 200, message = "请求成功")
    })
    @AutoCheckPermissions
    @GetMapping(value = "apply/temporary/cover")
    public ResultObject<TemporaryCoverResponse> queryTemporaryCover(@RequestParam("applyId") String applyId) {
        return applyBusinessService.queryTemporaryCover(applyId);
    }

    @ApiOperation(value = "暂保单打印", notes = "暂保单打印")
    @ApiResponses({
            @ApiResponse(code = 200, message = "请求成功")
    })
    @AutoCheckPermissions
    @GetMapping(value = "apply/temporary/cover/print")
    public ResultObject printTemporaryCover(HttpServletResponse httpServletResponse,
                                            @RequestParam(name = "applyId") String applyId,
                                            @RequestParam(name = "language") String language) throws IOException {
        return applyBusinessService.printTemporaryCover(applyId, language, httpServletResponse);
    }

    @ApiOperation(value = "系统预警查询已承保的投保单列表", notes = "系统预警查询已承保的投保单列表")
    @ApiResponses({
            @ApiResponse(code = 200, message = "请求成功")
    })
    @AutoCheckPermissions
    @GetMapping(value = "system/warning/query/applies")
    public ResultObject<List<ApplyListBo>> getSystemWarningApplyQueryList() {
        return applyBusinessService.getSystemWarningApplyQueryList();
    }

    @ApiOperation(value = "系统预警查询支付的投保单列表", notes = "系统预警查询已承保的投保单列表")
    @ApiResponses({
            @ApiResponse(code = 200, message = "请求成功")
    })
    @AutoCheckPermissions
    @GetMapping(value = "system/warning/query/pay/applies")
    public ResultObject<List<ApplyListBo>> getSystemWarningApplyPaymentList() {
        return applyBusinessService.getSystemWarningApplyPaymentList();
    }

    @ApiOperation(value = "系统预警查询支付的投保单", notes = "系统预警查询支付的投保单")
    @ApiResponses({
            @ApiResponse(code = 200, message = "请求成功")
    })
    @AutoCheckPermissions
    @GetMapping(value = "system/warning/query/pay/apply")
    public ResultObject<ApplyListBo> getSystemWarningApplyPayment(@RequestParam(name = "applyId") String applyId) {
        return applyBusinessService.getSystemWarningApplyPayment(applyId);
    }

    @ApiOperation(value = "投保单打印(仅针对#13号及其附加险)", notes = "投保单打印(仅针对#13号及其附加险)")
    @ApiResponses({
            @ApiResponse(code = 200, message = "请求成功")
    })
    @AutoCheckPermissions
    @GetMapping(value = "apply/print/file")
    public ResultObject printApply(HttpServletResponse httpServletResponse,
                                            @RequestParam(name = "printInfoId") String printInfoId,
                                            @RequestParam(name = "language") String language) throws IOException {
        return applyBusinessService.printApply(printInfoId, language, httpServletResponse);
    }

    @ApiOperation(value = "print/applys", notes = "投保单打印列表")
    @ApiResponses({
            @ApiResponse(code = 200, message = "请求成功")
    })
    @GetMapping(value = "print/applys")
    public ResultObject<BasePageResponse<ApplyPrintResponse>> getPrintApplyList(ApplyConditionPrintRequest applyConditionPrintRequest) {
        return applyBusinessService.getPrintApplyList(getCurrentLoginUsers(), applyConditionPrintRequest);
    }

    @ApiOperation(value = "投保单打印完成", notes = "某语言保单打印完成")
    @ApiResponses({
            @ApiResponse(code = 200, message = "请求成功")
    })
    @ApiImplicitParams({
            @ApiImplicitParam(name = "printInfoId", example = "保单打印ID", paramType = "query", required = true),
            @ApiImplicitParam(name = "language", value = "KM_KH/EN_US/ZH_CN", example = "语言", paramType = "query", required = true)
    })
    @GetMapping(value = "apply/print/file/complete")
    public ResultObject applyPrintFileComplete(@RequestParam(name = "printInfoId") String printInfoId,
                                                @RequestParam(name = "language") String language) {
        return applyBusinessService.applyPrintFileComplete(printInfoId, language);
    }

    @ApiOperation(value = "投保单打印结束", notes = "投保单打印结束")
    @ApiResponses({
            @ApiResponse(code = 200, message = "请求成功")
    })
    @ApiImplicitParams({
            @ApiImplicitParam(name = "printInfoId", value = "保单打印ID", example = "保单打印ID", paramType = "query", required = true)
    })
    @GetMapping(value = "apply/print/finish")
    public ResultObject applyPrintFinish(@RequestParam(name = "printInfoId") String printInfoId) {
        return applyBusinessService.applyPrintFinish(getCurrentLoginUsers(), printInfoId);
    }

    @ApiOperation(value = "计算bmi和年龄接口", notes = "计算bmi和年龄接口")
    @PostMapping(value = "apply/calculation/bmi/age")
    public ResultObject<CalculationBmiAndAgeResponse> calculationBmiAndAge(@RequestBody CalculationBmiAndAgeRequest calculationBmiAndAgeRequest) {
        return applyBusinessService.calculationBmiAndAge(calculationBmiAndAgeRequest);
    }

}
