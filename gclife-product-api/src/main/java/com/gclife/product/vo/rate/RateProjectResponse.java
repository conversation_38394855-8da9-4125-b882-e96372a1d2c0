package com.gclife.product.vo.rate;

import io.swagger.annotations.ApiModelProperty;

/**
 * <AUTHOR>
 * create 18-8-31
 * description:
 */
public class RateProjectResponse {
    @ApiModelProperty(example = "费率项目id")
    private String rateProjectId;
    @ApiModelProperty(example = "费率项目编码")
    private String rateCode;
    @ApiModelProperty(example = "费率项目名称")
    private String rateName;

    public String getRateProjectId() {
        return rateProjectId;
    }

    public void setRateProjectId(String rateProjectId) {
        this.rateProjectId = rateProjectId;
    }

    public String getRateCode() {
        return rateCode;
    }

    public void setRateCode(String rateCode) {
        this.rateCode = rateCode;
    }

    public String getRateName() {
        return rateName;
    }

    public void setRateName(String rateName) {
        this.rateName = rateName;
    }
}
