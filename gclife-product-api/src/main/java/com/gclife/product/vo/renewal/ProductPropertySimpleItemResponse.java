package com.gclife.product.vo.renewal;

import com.gclife.common.model.BaseResponse;
import io.swagger.annotations.ApiModelProperty;

/**
 * Created by cqh on 17-11-6.
 * <AUTHOR>
 * @desction 产品属性数据对象
 */
public class ProductPropertySimpleItemResponse extends BaseResponse {


    @ApiModelProperty(value = "propertyDataId", name = "属性数据Id",example = "属性数据Id")
    private String propertyDataId;
    @ApiModelProperty(value = "propertyDataContent", name = "属性数据内容",example = "属性数据内容")
    private String propertyDataContent;
    @ApiModelProperty(value = "propertyDataContent", name = "属性数据内容",example = "属性数据内容")
    private String propertyDataValue;

    public String getPropertyDataValue() {
        return propertyDataValue;
    }

    public void setPropertyDataValue(String propertyDataValue) {
        this.propertyDataValue = propertyDataValue;
        this.propertyDataContent=this.propertyDataValue;
    }
    public String getPropertyDataId() {
        return propertyDataId;
    }

    public void setPropertyDataId(String propertyDataId) {
        this.propertyDataId = propertyDataId;
    }

    public String getPropertyDataContent() {
        return propertyDataContent;
    }

    public void setPropertyDataContent(String propertyDataContent) {
        this.propertyDataContent = propertyDataContent;
    }
}