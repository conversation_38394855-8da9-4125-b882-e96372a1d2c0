package com.gclife.product.vo.rate;

import com.gclife.common.annotation.Internation;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * create 18-8-31
 * description:
 */
@Data
public class ProcessRateMainResponse {
    @ApiModelProperty(example = "流程费率主表ID")
    private String processRateMainId;
    @ApiModelProperty(example = "缴费类别")
    private String premiumFrequency;
    @ApiModelProperty(example = "缴费类别名称")
    @Internation(filed = "premiumFrequency",codeType = "PRODUCT_PREMIUM_FREQUENCY")
    private String premiumFrequencyName;
    @ApiModelProperty(example = "起始缴费年期")
    private Long premiumStartPeriod;
    @ApiModelProperty(example = "截止缴费年期")
    private Long premiumEndPeriod;
    @ApiModelProperty(example = "开始年度")
    private Long policyStartYear;
    @ApiModelProperty(example = "截止年度")
    private Long policyEndYear;
    @ApiModelProperty(example = "方便前端排序专用字段")
    private int time;

    private List<ProcessRateItemResponse> rateItem;

}
