package com.gclife.product.vo.manager;

import io.swagger.annotations.ApiModelProperty;

/**
 * <AUTHOR>
 * create 18-1-10
 * description:产品工厂-费率详情
 */
public class ProductRateDetailResponse {
    private String     rateItemId;
    @ApiModelProperty(value = "费率项", example = "费率明细No.1")
    private String rate;
    @ApiModelProperty(value = "缴费类型", example = "年缴")
    private String payType;
    @ApiModelProperty(value = "开始年期", example = "1")
    private long startAnnual;
    @ApiModelProperty(value = "结束年期", example = "1")
    private long endAnnual;
    @ApiModelProperty(value = "开始年度", example = "1")
    private long startYear;
    @ApiModelProperty(value = "截至年度", example = "1")
    private long endYear;
    @ApiModelProperty(value = "费率参数", example = "52%")
    private String rateParameter;

    private String rateMainRelationId;

    public String getRateItemId() {
        return rateItemId;
    }

    public void setRateItemId(String rateItemId) {
        this.rateItemId = rateItemId;
    }

    public String getRate() {
        return rate;
    }

    public void setRate(String rate) {
        this.rate = rate;
    }

    public String getPayType() {
        return payType;
    }

    public void setPayType(String payType) {
        this.payType = payType;
    }

    public long getStartAnnual() {
        return startAnnual;
    }

    public void setStartAnnual(long startAnnual) {
        this.startAnnual = startAnnual;
    }

    public long getEndAnnual() {
        return endAnnual;
    }

    public void setEndAnnual(long endAnnual) {
        this.endAnnual = endAnnual;
    }

    public long getStartYear() {
        return startYear;
    }

    public void setStartYear(long startYear) {
        this.startYear = startYear;
    }

    public long getEndYear() {
        return endYear;
    }

    public void setEndYear(long endYear) {
        this.endYear = endYear;
    }

    public String getRateParameter() {
        return rateParameter;
    }

    public void setRateParameter(String rateParameter) {
        this.rateParameter = rateParameter;
    }

    public String getRateMainRelationId() {
        return rateMainRelationId;
    }

    public void setRateMainRelationId(String rateMainRelationId) {
        this.rateMainRelationId = rateMainRelationId;
    }


    @Override
    public boolean equals(Object obj) {
        if (this == obj)
            return true;
        if (obj == null)
            return false;
        if (getClass() != obj.getClass())
            return false;
        final ProductRateDetailResponse other = (ProductRateDetailResponse) obj;
        if(this.getRateItemId()!=other.getRateItemId())
            return false;
        return true;
    }
}
