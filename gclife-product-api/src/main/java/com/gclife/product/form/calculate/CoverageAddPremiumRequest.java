package com.gclife.product.form.calculate;

import com.gclife.common.util.AssertUtils;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * create 2021/3/9 14:50
 * description:
 */
@Data
public class CoverageAddPremiumRequest {
    @ApiModelProperty(example = "加费类型")
    private String addPremiumObjectCode;
    @ApiModelProperty(example = "总加费金额")
    private BigDecimal totalAddPremium;
    @ApiModelProperty(example = "加费期限")
    private Long addPremiumPeriod;
    @ApiModelProperty(example = "加费状态")
    private String addPremiumStatus;

    @Override
    public String toString() {
        return "("  + (AssertUtils.isNotEmpty(addPremiumObjectCode) ? "\\\"" + addPremiumObjectCode + "\\\"" : "") +
                "," + (AssertUtils.isNotNull(totalAddPremium) ? "\\\"" + totalAddPremium + "\\\"" : "") +
                "," + (AssertUtils.isNotNull(addPremiumPeriod) ? "\\\"" + addPremiumPeriod + "\\\"" : "") +
                "," + (AssertUtils.isNotEmpty(addPremiumStatus) ? "\\\"" + addPremiumStatus + "\\\"" : "") +
                ')';
    }
}
