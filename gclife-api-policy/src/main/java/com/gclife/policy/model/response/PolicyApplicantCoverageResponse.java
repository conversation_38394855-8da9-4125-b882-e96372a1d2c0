package com.gclife.policy.model.response;

import com.gclife.common.annotation.Internation;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * create 18-8-31
 * description:
 */
@Data
public class PolicyApplicantCoverageResponse {
    private String policyId;
    private String policyNo;
    private String versionNo;
    private String applyId;
    private String applyNo;
    private String acceptBranchId;
    private String salesBranchId;
    private String managerBranchId;
    private String delegateBranchId;
    private String signBranchId;
    private String policyStatus;
    private String applicantId;
    private String certifyId;
    private String policyType;
    private Long   bizDate;
    private Long   applyDate;
    private Long   effectiveDate;
    private Long   approveDate;
    private Long   hesitation;
    private Long   hesitationEndDate;
    private String applySource;
    private String validFlag;
    private String createdUserId;
    private Long   createdDate;
    private String updatedUserId;
    private Long   updatedDate;
    private String channelTypeCode;
    private String currencyCode;
    private String verifyNo;
    private String providerId;
    private Long   maturityDate;
    private Long   invalidDate;
    private Long   thoroughInvalidDate;
    private Long   contractStartDate;
    private Long   contractEndDate;

    /**
     * 代理人姓名
     */
    private String name;
    // 性别国际化文本 2018-02-09
    private String sexName;
    // 证件类型名称
    private String idTypeName;
    //产品ID
    private String productId;
    //产品编码
    private String productCode;
    //产品名称
    @Internation(codeType = "PRODUCT_ID", filed = "productId")
    private String productName;

    @ApiModelProperty(example = "生效日期")
    private String effectiveDateFormat;

    @ApiModelProperty(example = "承保日期")
    private String approveDateFormat;

    @ApiModelProperty(example = "保单状态")
    private String policyStatusName;

}