package com.gclife.policy.model.response;


import com.gclife.common.annotation.Internation;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * create 17-9-23
 * description
 */
@Data
public class PolicyCoverageExtResponse implements Serializable {
    @ApiModelProperty(example = "产品主键")
    private String productId;

    @ApiModelProperty(example = "产品名称")
    private String productCode;

    @ApiModelProperty(example = "产品名称")
    @Internation(codeType = "PRODUCT_ID", filed = "productId")
    private String productName;

    @ApiModelProperty(example = "每期保费")
    private String totalPremium;

    @ApiModelProperty(example = "缴费周期")
    private String premiumFrequency;

    @ApiModelProperty(example = "缴费周期名称")
    private String premiumFrequencyName;

    @ApiModelProperty(example = "缴费期限")
    private String premiumPeriod;

    @ApiModelProperty(example = "主附险标记")
    private String primaryFlag;

    @ApiModelProperty(example = "主附险标记名称")
    private String primaryFlagName;

}