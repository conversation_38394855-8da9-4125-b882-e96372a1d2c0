package com.gclife.policy.model.request;

import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @version v1.0
 * Description:
 * @date 19-10-23
 */
@Data
public class EndorseCoverageDutyRequest {
    private String     coverageDutyId;
    private String     validFlag;
    private String     createdUserId;
    private Long       createdDate;
    private String     updatedUserId;
    private Long       updatedDate;
    private String     policyId;
    private String     coverageId;
    private String     dutyId;
    private BigDecimal standardPremium;
    private BigDecimal premium;
    private BigDecimal sumPremium;
    private BigDecimal amount;
    private BigDecimal riskAmount;
    private String     floatRate;
    private String     receivePeriodUnit;
    private Long       receivePeriod;
    private String     coveragePeriodUnit;
    private Long       coveragePeriod;
    private Long       coveragePeriodEndDate;
    private String     avoidPayFlag;
    private BigDecimal avoidPayRate;
    private Long       avoidPayStartDate;
    private Long       avoidPayEndDate;
    private Long       startReceiveDate;
    private String     startReceiveType;
    private BigDecimal receiveRate;
    private String     calrule;
    private String     premiumToAmount;
    private Long       dutyEffectiveDate;
    private String     receiveFrequency;
    private String     receiveFrequencyUnit;
    private String     dutyName;
}
