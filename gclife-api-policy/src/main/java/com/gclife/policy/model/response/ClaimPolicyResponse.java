package com.gclife.policy.model.response;

import com.gclife.common.annotation.DateFormat;
import com.gclife.common.annotation.Internation;
import com.gclife.common.model.config.DateFormatPatternEnum;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @description
 * @date 2019/9/11 11:59 上午
 */
@Data
public class ClaimPolicyResponse {


    @ApiModelProperty(example = "保单ID")
    private String policyId;
    @ApiModelProperty(example = "保单号")
    private String policyNo;
    @ApiModelProperty(example = "版本号")
    private String versionNo;
    @ApiModelProperty(example = "产品名称")
    private String productName;
    @ApiModelProperty(example = "投保人姓名")
    private String applicantName;
    @ApiModelProperty(example = "被保人姓名")
    private String insuredName;
    @ApiModelProperty(example = "生效日期")
    private Long   effectiveDate;
    @DateFormat(filed = "effectiveDate",pattern = DateFormatPatternEnum.FORMATE3)
    @ApiModelProperty(example = "生效日期格式化")
    private String   effectiveDateFormat;
    @ApiModelProperty(example = "保单状态")
    private String policyStatus;
    @ApiModelProperty(example = "保单状态名称")
    @Internation(filed = "policyStatus",codeType = "POLICY_STATUS")
    private String policyStatusName;
    @ApiModelProperty(example = "历史保单状态")
    private String historyPolicyStatus;
    @ApiModelProperty(example = "历史保单状态名称")
    @Internation(filed = "historyPolicyStatus",codeType = "POLICY_STATUS")
    private String historyPolicyStatusName;
    @ApiModelProperty(example = "保单类型")
    private String policyType;

    @ApiModelProperty(example = "被保人保障状态")
    private String insuredStatus;
    @ApiModelProperty(example = "被保人保障状态名称")
    @Internation(filed = "insuredStatus", codeType = "INSURED_STATUS")
    private String insuredStatusName;
    @ApiModelProperty(example = "历史被保人保障状态")
    private String historyInsuredStatus;
    @ApiModelProperty(example = "历史被保人保障状态名称")
    @Internation(filed = "historyInsuredStatus", codeType = "INSURED_STATUS")
    private String historyInsuredStatusName;
}
