<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">

    <parent>
        <groupId>com.gclife</groupId>
        <artifactId>gclife-parent</artifactId>
        <version>1.0</version>
        <relativePath></relativePath>
    </parent>

    <modelVersion>4.0.0</modelVersion>
    <groupId>com.gclife</groupId>
    <artifactId>gclife-business-core</artifactId>
    <version>0.0.1-SNAPSHOT</version>
    <packaging>pom</packaging>
    <name>${project.artifactId}</name>
    <description>附件模块 - 大中华人寿保险股份有限公司</description>

    <properties>
        <!-- BASE -->
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <project.reporting.outputEncoding>UTF-8</project.reporting.outputEncoding>
        <java.version>1.8</java.version>
        <!-- DOCKER -->
        <docker.plugin.version>0.4.14</docker.plugin.version>

        <!--swagger-->
        <springfox.swagger.version>1.3.0.RELEASE</springfox.swagger.version>
    </properties>

    <modules>
        <module>gclife-attachment-core</module>
        <module>gclife-attachment-service</module>
    </modules>


    <dependencies>
        <dependency>
            <groupId>com.gclife</groupId>
            <artifactId>gclife-common</artifactId>
        </dependency>

        <dependency>
            <groupId>com.github.xiaoymin</groupId>
            <artifactId>knife4j-spring-boot-starter</artifactId>
        </dependency>

        <!--阿里云服务-->
        <dependency>
            <groupId>com.aliyun.oss</groupId>
            <artifactId>aliyun-sdk-oss</artifactId>
            <version>2.2.1</version>
        </dependency>

        <dependency>
            <groupId>jdom</groupId>
            <artifactId>jdom</artifactId>
            <version>1.1</version>
        </dependency>

        <dependency>
            <groupId>com.aliyun</groupId>
            <artifactId>aliyun-java-sdk-sts</artifactId>
            <version>2.1.6</version>
        </dependency>

        <dependency>
            <groupId>com.aliyun</groupId>
            <artifactId>aliyun-java-sdk-core</artifactId>
            <version>2.1.7</version>
        </dependency>

    </dependencies>

    <build>
        <pluginManagement>
            <plugins>
                <plugin>
                    <!--  The plugin rewrites your manifest  -->
                    <groupId>org.springframework.boot</groupId>
                    <artifactId>spring-boot-maven-plugin</artifactId>
                    <executions>
                        <execution>
                            <goals>
                                <goal>repackage</goal>
                            </goals>
                        </execution>
                    </executions>
                </plugin>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-dependency-plugin</artifactId>
                </plugin>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-resources-plugin</artifactId>
                    <version>2.7</version>
                    <executions>
                        <execution>
                            <id>prepare-dockerfile</id>
                            <phase>validate</phase>
                            <goals>
                                <goal>copy-resources</goal>
                            </goals>
                            <configuration>
                                <outputDirectory>${project.build.directory}/docker</outputDirectory>
                                <resources>
                                    <resource>
                                        <directory>docker</directory>
                                        <filtering>true</filtering>
                                    </resource>
                                </resources>
                            </configuration>
                        </execution>
                    </executions>
                    <configuration>
                        <nonFilteredFileExtensions>
                            <nonFilteredFileExtension>p12</nonFilteredFileExtension>
                            <nonFilteredFileExtension>jks</nonFilteredFileExtension>
                            <nonFilteredFileExtension>html</nonFilteredFileExtension>
                            <nonFilteredFileExtension>css</nonFilteredFileExtension>
                            <nonFilteredFileExtension>js</nonFilteredFileExtension>
                            <nonFilteredFileExtension>lic</nonFilteredFileExtension>
                            <nonFilteredFileExtension>sh</nonFilteredFileExtension>
                            <nonFilteredFileExtension>gz</nonFilteredFileExtension>
                            <nonFilteredFileExtension>tar</nonFilteredFileExtension>
                            <nonFilteredFileExtension>zip</nonFilteredFileExtension>
                            <nonFilteredFileExtension>sql</nonFilteredFileExtension>
                            <nonFilteredFileExtension>pdf</nonFilteredFileExtension>
                        </nonFilteredFileExtensions>
                    </configuration>
                </plugin>
                <plugin>
                    <groupId>it.ozimov</groupId>
                    <artifactId>yaml-properties-maven-plugin</artifactId>
                    <version>1.1.3</version>
                    <executions>
                        <execution>
                            <phase>initialize</phase>
                            <goals>
                                <goal>read-project-properties</goal>
                            </goals>
                            <configuration>
                                <files>
                                    <file>src/main/resources/application.yml</file>
                                </files>
                            </configuration>
                        </execution>
                    </executions>
                </plugin>
                <plugin>
                    <groupId>com.spotify</groupId>
                    <artifactId>docker-maven-plugin</artifactId>
                    <version>${docker.plugin.version}</version>
                    <executions>
                        <execution>
                            <phase>package</phase>
                            <goals>
                                <goal>build</goal>
                            </goals>
                        </execution>
                    </executions>
                    <configuration>
                        <imageName>${project.artifactId}</imageName>
                        <dockerDirectory>${project.build.directory}/docker</dockerDirectory>
                        <!-- <baseImage>java:8</baseImage>
                        <entryPoint>["java", "-jar", "/${project.build.finalName}.jar"]</entryPoint> -->
                        <resources>
                            <resource>
                                <targetPath>/</targetPath>
                                <directory>${project.build.directory}</directory>
                                <include>${project.build.finalName}.jar</include>
                            </resource>
                        </resources>
                    </configuration>
                </plugin>

            </plugins>
        </pluginManagement>
    </build>


    <distributionManagement>


        <repository>
            <id>nexus</id>
            <name>Releases</name>
            <url>http://mvn-repo:8081/repository/gclife-mvn-releases/</url>
        </repository>

        <snapshotRepository>
            <id>nexus</id>
            <name>Snapshot</name>
            <url>http://mvn-repo:8081/repository/gclife-mvn-snapshots/</url>
        </snapshotRepository>



    </distributionManagement>

</project>