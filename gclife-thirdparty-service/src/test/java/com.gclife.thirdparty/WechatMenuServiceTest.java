package com.gclife.thirdparty;


import me.chanjar.weixin.mp.api.WxMpService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.HashMap;
import java.util.Map;

/**
 * \* Created with IntelliJ IDEA.
 * \* User: deep
 * \* Date: 17-9-14
 * \* Time: 下午7:37
 * \* To change this template use File | Settings | File Templates.
 * \* Description: 用户service 测试类
 * \
 */
/*
@RunWith(SpringRunner.class)
@SpringBootTest
public class WechatMenuServiceTest extends UsersTest{

    @Autowired
    private WxMpService wxService;


    @Test
    public void testCreateMenu_by_json() {
        try {
            wxService.getMenuService().menuDelete();
            //System.out.println("current menu :"+JSON.toJSONString();

            String a ="{\n" +
                    "  \"menu\": {\n" +
                    "    \"button\": [\n" +
                    "      {\n" +
                    "        \"name\": \"産品中心\",\n" +
                    "        \"type\": \"view\",\n" +
                    "        \"url\": \"https://www.gc-life.com/mobile/index.html?time=new Date().getTime()#/zHProductCenter\"\n" +
                    "      },\n" +
                    "      {\n" +
                    "        \"name\": \"客戶服務\",\n" +
                    "        \"type\": \"view\",\n" +
                    "        \"url\": \"https://www.gc-life.com/mobile/index.html?time=new Date().getTime()#/zHCustomerService\"\n" +
                    "      },\n" +
                    "      {\n" +
                    "        \"name\": \"關於公司\",\n" +
                    "        \"sub_button\": [\n" +
                    "          {\n" +
                    "            \"name\": \"公司介紹\",\n" +
                    "            \"sub_button\": [],\n" +
                    "            \"type\": \"view\",\n" +
                    "            \"url\": \"https://www.gc-life.com/mobile/index.html?time=new Date().getTime()#/zHAboutUs\"\n" +
                    "          },\n" +
                    "          {\n" +
                    "            \"name\": \"下載APP\",\n" +
                    "            \"sub_button\": [],\n" +
                    "            \"type\": \"view\",\n" +
                    "            \"url\": \"https://www.gc-life.com/mobile/index.html?time=new Date().getTime()#/download\"\n" +
                    "          }\n" +
                    "        ]\n" +
                    "      }\n" +
                    "    ]\n" +
                    "  }\n" +
                    "}";

            // WxMenu menu = WxMenu.fromJson(a);
            // System.out.println(menu.toJson());
            //String s= this.wxService.getMenuService().menuCreate(menu);
            // System.out.println(s);
        }catch (Exception e){

        }
    }


}*/
