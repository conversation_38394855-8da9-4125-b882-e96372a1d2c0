package com.gclife.thirdparty.model.response;

import com.gclife.common.model.BaseResponse;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

/**
 * <AUTHOR>
 *         create 17-12-11
 *         description:
 */
@ApiModel(description = "友盟发送APP消息数据返回")
public class UmengMessageResponse extends BaseResponse{
    @ApiModelProperty(example = "消息ID")
    private String msgId;

    public String getMsgId() {
        return msgId;
    }

    public void setMsgId(String msgId) {
        this.msgId = msgId;
    }
}