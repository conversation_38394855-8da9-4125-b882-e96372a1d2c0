package com.gclife.thirdparty.model.request.qrcode;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class QrcodeStatusRequest {
    @ApiModelProperty(example = "二唯码对应业务类型,解析后使用,调用不同的方法",required = false,value = "二唯码对应业务类型,解析后使用,调用不同的方法")
    private String qrcodeType;
    @ApiModelProperty(example = "业务字符串，如以为解析后的字符、链接，将通过二唯码类型和检索类型处理对应的数据",required = false,value = "业务字符串，如以为解析后的字符、链接，将通过二唯码类型和检索类型处理对应的数据")
    private String bizStr;
    @ApiModelProperty(example = "设备渠道",required = false,value = "设备渠道")
    private String deviceChannel;
    @ApiModelProperty(value = "二维码动作", example = "二维码动作")
    private String actionName;
    @ApiModelProperty(value = "用户id", example = "用户id")
    private String userId;
}
