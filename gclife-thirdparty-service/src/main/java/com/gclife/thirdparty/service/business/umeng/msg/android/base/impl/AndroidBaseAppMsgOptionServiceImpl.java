package com.gclife.thirdparty.service.business.umeng.msg.android.base.impl;

import com.gclife.common.exception.RequestException;
import com.gclife.common.service.impl.BaseBusinessServiceImpl;
import com.gclife.common.util.AssertUtils;
import com.gclife.thirdparty.base.model.config.ThirdpartyErrorConfigEnum;
import com.gclife.thirdparty.core.jooq.tables.pojos.DeviceChannelConfigPo;
import com.gclife.thirdparty.dao.DeviceChannelConfigExtDao;
import com.gclife.thirdparty.model.bo.MessageBo;
import com.gclife.thirdparty.model.config.ThirdpartyTermEnum;
import com.gclife.thirdparty.service.business.umeng.msg.android.AndroidUnicastAppMsgOptionService;
import com.gclife.thirdparty.service.business.umeng.msg.android.base.AndroidBaseAppMsgOptionService;
import com.gclife.thirdparty.service.sdk.umeng.upush.AndroidNotification;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 *         create 17-12-12
 *         description:安卓消息基础处理类
 */
@Component
public class AndroidBaseAppMsgOptionServiceImpl extends BaseBusinessServiceImpl implements AndroidBaseAppMsgOptionService {

    @Autowired
    private AndroidUnicastAppMsgOptionService androidUnicastAppMsgOptionService;

    @Autowired
    private DeviceChannelConfigExtDao deviceChannelConfigExtDao;
    /**
     * 业务验证：验证消息对象
     * @param messageBo　消息对象
     */
    @Override
    public void validBusinessSendMsg(MessageBo messageBo) {
        /*********************发送消息类型数据校验**********************/
        if(ThirdpartyTermEnum.MESSAGE_UMENG_SEND_TYPE.unicast.name().equals(messageBo.getSendType())){
            androidUnicastAppMsgOptionService.validBusinessSendMsg(messageBo);
        }else if(ThirdpartyTermEnum.MESSAGE_UMENG_SEND_TYPE.listcast.name().equals(messageBo.getSendType())){

        }else if(ThirdpartyTermEnum.MESSAGE_UMENG_SEND_TYPE.broadcast.name().equals(messageBo.getSendType())){

        }
    }
    /**
     * 转换友盟发送消息对象
     */
    @Override
    public void transferDeviceNotificationSendMsg(MessageBo messageBo) {
        //查询服务供应商配置信息
        DeviceChannelConfigPo deviceChannelConfigPo=  deviceChannelConfigExtDao.loadDeviceChannelConfigPo(messageBo.getDeviceChannel(),messageBo.getDeviceTypeCode(),ThirdpartyTermEnum.SERVICE_PROVIDER_CODE.UMENG.name());
        AssertUtils.isNotNull(this.getLogger(),deviceChannelConfigPo, ThirdpartyErrorConfigEnum.THIRDPARTY_BUSINESS_APP_MESSAGE_DEVICE_CHANNEL_CONFIG_IS_NOT_FOUND);
        //设置不同发送类型的基础数据
        if(ThirdpartyTermEnum.MESSAGE_UMENG_SEND_TYPE.unicast.name().equals(messageBo.getSendType())){
            androidUnicastAppMsgOptionService.transferDeviceNotificationSendMsg(messageBo,deviceChannelConfigPo);
        }else if(ThirdpartyTermEnum.MESSAGE_UMENG_SEND_TYPE.listcast.name().equals(messageBo.getSendType())){

        }else if(ThirdpartyTermEnum.MESSAGE_UMENG_SEND_TYPE.broadcast.name().equals(messageBo.getSendType())){

        }
        //设置app安卓公共消息数据
        if(AssertUtils.isNotNull(messageBo.getMsgNotification())){
            try {
                AndroidNotification androidNotification = (AndroidNotification) messageBo.getMsgNotification();
                androidNotification.setIcon(messageBo.getIcon());
                androidNotification.setLargeIcon(messageBo.getLargeIcon());
                androidNotification.setImg(messageBo.getImg());
                if(!AssertUtils.isNotNull(messageBo.getTicker())){
                    messageBo.setTicker(messageBo.getTitle());
                }
                androidNotification.setTicker(messageBo.getTicker());
                androidNotification.setTitle(messageBo.getTitle());
                androidNotification.setActivity(messageBo.getActivity());
                androidNotification.setText(messageBo.getText());
                //消息类型
                if(AssertUtils.isNotEmpty(messageBo.getDisplayType())){
                    for (AndroidNotification.DisplayType displayType:AndroidNotification.DisplayType.values()) {
                        if(displayType.getValue().equals(messageBo.getDisplayType())){
                            androidNotification.setDisplayType(displayType);
                        }
                    }
                }else{
                    androidNotification.setDisplayType(AndroidNotification.DisplayType.NOTIFICATION);
                }

                //设置打开行为
                if(ThirdpartyTermEnum.MESSAGE_AFTER_OPEN.go_custom.name().equals(messageBo.getAfterOpen())){
                    androidNotification.goCustomAfterOpen(messageBo.getCustom());
                }else if(ThirdpartyTermEnum.MESSAGE_AFTER_OPEN.go_activity.name().equals(messageBo.getAfterOpen())){
                    androidNotification.goActivityAfterOpen(messageBo.getActivity());
                }else if(ThirdpartyTermEnum.MESSAGE_AFTER_OPEN.go_url.name().equals(messageBo.getAfterOpen())){
                    androidNotification.goUrlAfterOpen(messageBo.getUrl());
                }else {
                    androidNotification.goAppAfterOpen();
                }
                //设置额外参数
                if(messageBo.getExtraMap()!=null&&messageBo.getExtraMap().size()>0){
                    for(String key:messageBo.getExtraMap().keySet().toArray(new String[]{})){
                        androidNotification.setExtraField(key,messageBo.getExtraMap().get(key));
                    }
                }
            }catch (Exception e){
                throw new RequestException(ThirdpartyErrorConfigEnum.THIRDPARTY_BUSINESS_APP_MESSAGE_TRANSFER_ANDROIDNOTIFICATION_ERROR);
            }
        }
    }
}
