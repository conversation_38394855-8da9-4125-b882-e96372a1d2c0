package com.gclife.thirdparty.service.business;

import com.alibaba.fastjson.JSONObject;
import com.gclife.common.model.ResultObject;
import com.gclife.common.service.BaseBusinessService;
import com.gclife.thirdparty.model.request.ThirdPartyRequest;
import com.gclife.thirdparty.model.response.FacebookRollbackResponse;
import com.gclife.thirdparty.model.response.ThirdPartyResponse;

/**
 * <AUTHOR>
 * @version v1.0
 * Description:
 * @date 2022-07-15
 */
public interface AuthBusinessService extends BaseBusinessService{

    /**
     * 谷歌授权
     * @param request
     * @return
     */
    ResultObject<ThirdPartyResponse> googleAuth(ThirdPartyRequest request);

    /**
     * Facebook授权
     * @param request
     * @return
     */
    ResultObject<ThirdPartyResponse> facebookAuth(ThirdPartyRequest request);

    /**
     * 删除回调
     * @param signed_request
     * @return
     */
    JSONObject deleteRollback(Object signed_request);

    /**
     * Apple授权
     * @param request
     * @return
     */
    ResultObject<ThirdPartyResponse> appleAuth(ThirdPartyRequest request);
}