package com.gclife.thirdparty.service.business.msg;

import com.gclife.common.model.ResultObject;
import com.gclife.common.model.base.Users;
import com.gclife.common.service.BaseBusinessService;
import com.gclife.thirdparty.model.request.msg.MsgRequest;

/**
 * \* Created with IntelliJ IDEA.
 * \* User: deep
 * \* Date: 17-9-20
 * \* Time: 下午4:38
 * \* To change this template use File | Settings | File Templates.
 * \* Description: 消息入口
 * <AUTHOR>
 */
public interface MsgPortalService extends BaseBusinessService {

    /**
     * 消息处理公共接口
     * @param users 当前用户
     * @param msgRequest   消息请求对象
     */
    public ResultObject<MsgRequest> msgHandle(Users users, MsgRequest msgRequest);

    /**
     * 消息处理公共接口
     * @param msgRequest   消息请求对象
     */
    public void msgHandle(MsgRequest msgRequest);


}