package com.gclife.thirdparty.service.business.xiangyun.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.gclife.common.model.ResultObject;
import com.gclife.common.service.impl.BaseBusinessServiceImpl;
import com.gclife.common.util.AssertUtils;
import com.gclife.thirdparty.base.model.config.ThirdpartyErrorConfigEnum;
import com.gclife.thirdparty.base.util.HttpUtils;
import com.gclife.thirdparty.core.jooq.tables.pojos.DeviceChannelConfigPo;
import com.gclife.thirdparty.dao.DeviceChannelConfigExtDao;
import com.gclife.thirdparty.form.AttachmentRequest;
import com.gclife.thirdparty.model.bo.xiangyun.XyItem;
import com.gclife.thirdparty.model.response.ocr.OcrGeneralResponse;
import com.gclife.thirdparty.service.business.xiangyun.OrcBusinessService;
import com.gclife.thirdparty.vo.OrcResponse;
import okhttp3.*;
import org.apache.http.HttpResponse;
import org.apache.http.util.EntityUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import sun.misc.BASE64Decoder;

import java.io.IOException;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * \* Created with IntelliJ IDEA.
 * \* User: deep
 * \* Date: 17-9-20
 * \* Time: 下午4:38
 * \* To change this template use File | Settings | File Templates.
 * \* Description:
 *
 * <AUTHOR>
 */
@Service
public class OrcBusinessServiceImpl extends BaseBusinessServiceImpl implements OrcBusinessService {
    @Autowired
    private DeviceChannelConfigExtDao deviceChannelConfigExtDao;


    @Override
    public ResultObject<OrcResponse> optionOrc(AttachmentRequest attachmentRequest) {
        ResultObject<OrcResponse> responseResultObject = new ResultObject<>();
        try {
            AssertUtils.isNotNull(this.getLogger(),attachmentRequest.getFileName(),ThirdpartyErrorConfigEnum.THIRDPARTY_PARAMETER_TENCENT_OCR_FILENAME_ERROR);
            AssertUtils.isNotNull(this.getLogger(),attachmentRequest.getFileContent(),ThirdpartyErrorConfigEnum.THIRDPARTY_PARAMETER_TENCENT_OCR_FILECONTENT_ERROR);
            OrcResponse response =   requestApi(attachmentRequest);
            responseResultObject.setData(response);
        }catch (Exception e){
            e.printStackTrace();
            this.setResultObjectException(this.getLogger(), responseResultObject, e, ThirdpartyErrorConfigEnum.THIRDPARTY_BUSINESS_GENERAL_OCR_IS_ERROR);
        }
        return responseResultObject;
    }

    /**
     * OCR通用文字识别
     * @param url 图片路径
     * @return
     */
    @Override
    public ResultObject<OcrGeneralResponse> ocrGeneral(String url) {
        AssertUtils.isNotEmpty(this.getLogger(), url, ThirdpartyErrorConfigEnum.THIRDPARTY_PARAMETER_IMAG_URL_IS_NOT_NULL);
        // 查询ocr识别配置
        DeviceChannelConfigPo deviceChannelConfigPo = deviceChannelConfigExtDao.loadDeviceChannelConfigPo("OCR");
        AssertUtils.isNotNull(getLogger(), deviceChannelConfigPo, ThirdpartyErrorConfigEnum.THIRDPARTY_BUSINESS_OCR_CONFIG_IS_NOT_FOUND_OBJECT);

        ResultObject<OcrGeneralResponse> resultObject = new ResultObject<>();
        // ocr识别
        String ocrResult = requestOcr(deviceChannelConfigPo, url, null);
        OcrGeneralResponse ocrGeneralResponse = new OcrGeneralResponse();
        ocrGeneralResponse.setOcrResult(ocrResult);
        resultObject.setData(ocrGeneralResponse);
        return resultObject;
    }

    /**
     * ocr请求
     * @param deviceChannelConfigPo ocr配置
     * @param url 图片路径
     * @param img 图片base64
     * @return 识别结果
     */
    private String requestOcr(DeviceChannelConfigPo deviceChannelConfigPo, String url, String img) {
        String host = deviceChannelConfigPo.getHost();
        String path = deviceChannelConfigPo.getPath();
        String method = deviceChannelConfigPo.getHttpMethod();
        String appcode = deviceChannelConfigPo.getMessageSecret();
        Map<String, String> headers = new HashMap<String, String>();
        //最后在header中的格式(中间是英文空格)为Authorization:APPCODE 83359fd73fe94948385f570e3c139105
        headers.put("Authorization", "APPCODE " + appcode);
        //根据API的要求，定义相对应的Content-Type
        headers.put("Content-Type", "application/json; charset=UTF-8");
        Map<String, String> querys = new HashMap<String, String>();
        Map<String, Object> requestBody = new HashMap<>();
        if (AssertUtils.isNotEmpty(url)) {
            requestBody.put("url", url);
            requestBody.put("image", url);
            this.getLogger().info("OCR请求参数[url:{}]", url);
        } else if (AssertUtils.isNotEmpty(img)) {
            requestBody.put("img", img);
            requestBody.put("image", img);
        } else {
            throwsException(ThirdpartyErrorConfigEnum.THIRDPARTY_PARAMETER_OCR_IMG_URL_IS_NOT_NULL);
        }
        requestBody.put("prob", false);
        requestBody.put("charInfo", false);
        requestBody.put("rotate", true);
        requestBody.put("table", false);
        try {
            HttpResponse response = HttpUtils.doPost(host, path, method, headers, querys, JSON.toJSONString(requestBody));
            getLogger().info("OCR响应:{}", response.toString());
            return EntityUtils.toString(response.getEntity());
        } catch (Exception e) {
            e.printStackTrace();
            throwsException(getLogger(), e, ThirdpartyErrorConfigEnum.THIRDPARTY_BUSINESS_GENERAL_OCR_IS_ERROR);
        }
        return null;
    }

    private OrcResponse requestApi(AttachmentRequest attachmentRequest) throws IOException {
        OrcResponse orcResponse=new OrcResponse();
        BASE64Decoder decoder = new BASE64Decoder();
        byte[] img = decoder.decodeBuffer(attachmentRequest.getFileContent());
        OkHttpClient client = new OkHttpClient();
        RequestBody body = new MultipartBody.Builder()
                .setType(MultipartBody.FORM)
                .addFormDataPart("file", attachmentRequest.getFileName(),RequestBody.create(MediaType.parse("image/jpeg"), img))
                .addFormDataPart("key", "6Ys9CvvDdSobCauEWQiyHu")
                .addFormDataPart("secret", "8c2effc5c51e4cf9a67a3079f4c5f3ca")
                .addFormDataPart("typeId", "13")//需修改为对应产品ID
                .addFormDataPart("format", "json")
                .build();
        Request request = new Request.Builder()
                .url("http://netocr.com/api/recog.do")//需修改为对应产品接口url
                .post(body)
                .build();
        Response response = client.newCall(request).execute();
        String result = response.body().string();
        response.close();
        this.getLogger().info("orc识别护照结果:"+result);
        JSONObject parseObject = JSON.parseObject(result);
        JSONObject message = parseObject.getJSONObject("message");
        if("13".equals(message.getString("status"))){
            //识别成功
            JSONArray array = parseObject.getJSONArray("cardsinfo").getJSONObject(0).getJSONArray("items");
            if(AssertUtils.isNotNull(array)){
                List<XyItem> xyItemList = array.toJavaList(XyItem.class);
                xyItemList.forEach(xyItem -> {
                    if("护照号码".equals(xyItem.getDesc())){
                        orcResponse.setIdNo(xyItem.getContent());
                    }
                    if("本国姓名".equals(xyItem.getDesc())){
                        orcResponse.setName(xyItem.getContent());
                    }
                    if("英文姓".equals(xyItem.getDesc())){
                        orcResponse.setSurName(xyItem.getContent());
                    }
                    if("英文姓".equals(xyItem.getDesc())){
                        orcResponse.setGivenName(xyItem.getContent());
                    }
                    if("英文名".equals(xyItem.getDesc())){
                        orcResponse.setSurName(xyItem.getContent());
                    }
                    if("英文姓名".equals(xyItem.getDesc())){
                        orcResponse.setEnName(xyItem.getContent());
                    }
                });
            }
        }
        return orcResponse;
    }


}