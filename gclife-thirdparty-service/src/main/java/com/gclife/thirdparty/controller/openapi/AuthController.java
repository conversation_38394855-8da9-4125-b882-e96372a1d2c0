package com.gclife.thirdparty.controller.openapi;

import com.alibaba.fastjson.JSONObject;
import com.gclife.common.controller.base.BaseController;
import com.gclife.common.model.ResultObject;
import com.gclife.thirdparty.model.request.ThirdPartyRequest;
import com.gclife.thirdparty.model.response.FacebookRollbackResponse;
import com.gclife.thirdparty.model.response.ThirdPartyResponse;
import com.gclife.thirdparty.service.business.AuthBusinessService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiResponse;
import io.swagger.annotations.ApiResponses;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;

/**
 * <AUTHOR>
 * @version v1.0
 * Description:
 * @date 2022-07-15
 */
@Api(tags = "第三方平台授权", value = "第三方平台授权")
@Controller
@RequestMapping("v1/auth")
public class AuthController extends BaseController {
    @Autowired
    private AuthBusinessService authBusinessService;

    @ApiOperation(value = "谷歌授权", notes = "谷歌授权")
    @ApiResponses({
            @ApiResponse(code = 200, message = "请求成功")
    })
    @PostMapping(value = "/google")
    public ResultObject<ThirdPartyResponse> googleAuth(@RequestBody ThirdPartyRequest request) {
        return authBusinessService.googleAuth(request);
    }

    @ApiOperation(value = "Facebook授权", notes = "Facebook授权")
    @ApiResponses({
            @ApiResponse(code = 200, message = "请求成功")
    })
    @PostMapping(value = "/facebook")
    public ResultObject<ThirdPartyResponse> facebookAuth(@RequestBody ThirdPartyRequest request) {
        return authBusinessService.facebookAuth(request);
    }

    @ApiOperation(value = "第三方数据删除回调", notes = "第三方数据删除回调")
    @ApiResponses({
            @ApiResponse(code = 200, message = "请求成功")
    })
    @PostMapping(value = "/facebook/rollback")
    public JSONObject deleteRollback(@RequestBody Object signed_request) {
        System.out.println("facebook数据删除回调请求数据："+ JSONObject.toJSONString(signed_request));
        return authBusinessService.deleteRollback(signed_request);
    }

    @ApiOperation(value = "Apple授权", notes = "Apple授权")
    @ApiResponses({
            @ApiResponse(code = 200, message = "请求成功")
    })
    @PostMapping(value = "/apple")
    public ResultObject<ThirdPartyResponse> appleAuth(@RequestBody ThirdPartyRequest request) {
        return authBusinessService.appleAuth(request);
    }

}