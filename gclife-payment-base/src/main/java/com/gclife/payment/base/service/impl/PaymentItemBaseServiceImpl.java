package com.gclife.payment.base.service.impl;

import com.gclife.common.service.impl.BaseBusinessServiceImpl;
import com.gclife.payment.base.dao.PaymentItemBaseDao;
import com.gclife.payment.base.model.bo.PaymentReportListBo;
import com.gclife.payment.base.service.PaymentItemBaseService;
import com.gclife.payment.model.response.PolicyReportResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 * create 18-05-24
 * description: 支付类型配置base service
 */
@Service
public class PaymentItemBaseServiceImpl extends BaseBusinessServiceImpl implements PaymentItemBaseService {
    @Autowired
    private PaymentItemBaseDao paymentItemBaseDao;

    @Override
    public List<PaymentReportListBo> queryPaymentItemByBusinessId(List<String> applyIds) {
        return paymentItemBaseDao.queryPaymentItemByBusinessId(applyIds);
    }

    @Override
    public List<PolicyReportResponse> queryPaymentMethodByPaymentId(List<String> paymentIdList) {
        return paymentItemBaseDao.queryPaymentMethodByPaymentId(paymentIdList);
    }
}
