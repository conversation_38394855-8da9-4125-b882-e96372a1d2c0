package com.gclife.payment.base.model.bo;

import com.gclife.payment.core.jooq.tables.pojos.PaymentAttachmentPo;
import com.gclife.payment.core.jooq.tables.pojos.PaymentCodePo;
import com.gclife.payment.core.jooq.tables.pojos.PaymentItemPo;
import com.gclife.payment.core.jooq.tables.pojos.PaymentPo;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * create 18-05-17
 * description:　对应于payment表中的记录，映射类
 */

public class PaymentDo extends PaymentPo {
    /**支付码数据**/
    private PaymentCodePo paymentCodePo;
    /**支付项集合*/
    private List<PaymentItemPo> items=new ArrayList<>();
    /**附件列表*/
    private List<PaymentAttachmentPo>   listAttachment;

    public List<PaymentItemPo> getItems() {
        return items;
    }

    public void setItems(List<PaymentItemPo> items) {
        this.items = items;
    }

    public List<PaymentAttachmentPo> getListAttachment() {
        return listAttachment;
    }

    public void setListAttachment(List<PaymentAttachmentPo> listAttachment) {
        this.listAttachment = listAttachment;
    }

    public PaymentCodePo getPaymentCodePo() {
        return paymentCodePo;
    }

    public void setPaymentCodePo(PaymentCodePo paymentCodePo) {
        this.paymentCodePo = paymentCodePo;
    }
}
