package com.gclife.payment.base.model.vo;

import com.gclife.common.model.BasePageRequest;

import java.util.List;

/**
 * <AUTHOR>
 * @version v2.0
 * Description:
 * @date 18-7-12
 */
public class AgencyPaymentVo extends BasePageRequest {
    // 所属机构
    private String branchId;
    // 搜索关键字
    private String keyword;
    // 银行编码
    private String bankCode;
    // 业务类型编码
    private String businessType;
    // 机构ID集
    private List<String> branchIds;

    public String getBranchId() {
        return branchId;
    }

    public void setBranchId(String branchId) {
        this.branchId = branchId;
    }

    public String getKeyword() {
        return keyword;
    }

    public void setKeyword(String keyword) {
        this.keyword = keyword;
    }

    public String getBankCode() {
        return bankCode;
    }

    public void setBankCode(String bankCode) {
        this.bankCode = bankCode;
    }

    public String getBusinessType() {
        return businessType;
    }

    public void setBusinessType(String businessType) {
        this.businessType = businessType;
    }

    public List<String> getBranchIds() {
        return branchIds;
    }

    public void setBranchIds(List<String> branchIds) {
        this.branchIds = branchIds;
    }
}
