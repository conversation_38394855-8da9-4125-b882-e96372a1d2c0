package com.gclife.endorse.base.dao.impl;

import com.gclife.common.dao.base.impl.BaseDaoImpl;
import com.gclife.common.model.config.TerminologyConfigEnum;
import com.gclife.endorse.base.dao.EndorseFeeBaseDao;
import com.gclife.endorse.base.model.bo.EndorseItemFeeDetailBo;
import com.gclife.endorse.core.jooq.tables.pojos.EndorseFeePo;
import com.gclife.endorse.core.jooq.tables.pojos.EndorseItemFeeDetailPo;
import com.gclife.endorse.core.jooq.tables.pojos.EndorseItemFeePo;
import org.springframework.stereotype.Repository;

import java.util.List;

import static com.gclife.endorse.core.jooq.Tables.*;

/**
 * <AUTHOR>
 * @version v2.0
 * Description:保全费用
 * @date 18-9-10
 */
@Repository
public class EndorseFeeBaseDaoImpl extends BaseDaoImpl implements EndorseFeeBaseDao {

    /**
     * 根据保全明细ID查询保全费用明细
     * @param endorseItemId 保全明细ID
     * @return
     */
    @Override
    public List<EndorseItemFeeDetailPo> listEndorseItemFeeDetail(String... endorseItemId) {
        return this.getDslContext()
                .select(ENDORSE_ITEM_FEE_DETAIL.fields())
                .from(ENDORSE_ITEM_FEE_DETAIL)
                .innerJoin(ENDORSE_ITEM_FEE).on(ENDORSE_ITEM_FEE_DETAIL.ENDORSE_ITEM_FEE_ID.eq(ENDORSE_ITEM_FEE.ENDORSE_ITEM_FEE_ID))
                .where(ENDORSE_ITEM_FEE.ENDORSE_ITEM_ID.in(endorseItemId))
                .and(ENDORSE_ITEM_FEE_DETAIL.VALID_FLAG.eq(TerminologyConfigEnum.VALID_FLAG.effective.name()))
                .fetchInto(EndorseItemFeeDetailPo.class);
    }

    /**
     * 根据保全明细ID查询保全费用明细
     * @param endorseItemId 保全明细ID
     * @return
     */
    @Override
    public List<EndorseItemFeeDetailBo> listEndorseItemFeeDetailBo(String endorseItemId) {
        return this.getDslContext()
                .select(ENDORSE_ITEM_FEE_DETAIL.fields())
                .select(BASE_FEE.PROJECT_FEE_CODE)
                .select(BASE_FEE.PROJECT_FEE_NAME)
                .select(BASE_FEE.PERMIT_ALTER_FLAG)
                .from(ENDORSE_ITEM_FEE_DETAIL)
                .innerJoin(ENDORSE_ITEM_FEE).on(ENDORSE_ITEM_FEE_DETAIL.ENDORSE_ITEM_FEE_ID.eq(ENDORSE_ITEM_FEE.ENDORSE_ITEM_FEE_ID))
                .leftJoin(BASE_FEE).on(ENDORSE_ITEM_FEE_DETAIL.PROJECT_FEE_ID.eq(BASE_FEE.PROJECT_FEE_ID))
                .where(ENDORSE_ITEM_FEE.ENDORSE_ITEM_ID.eq(endorseItemId))
                .and(ENDORSE_ITEM_FEE_DETAIL.VALID_FLAG.eq(TerminologyConfigEnum.VALID_FLAG.effective.name()))
                .fetchInto(EndorseItemFeeDetailBo.class);
    }

    /**
     * 查询保全费用数据
     * @param endorseId 保全ID
     * @return
     */
    @Override
    public EndorseFeePo queryEndorseFee(String endorseId) {
        return this.getDslContext()
                .selectFrom(ENDORSE_FEE)
                .where(ENDORSE_FEE.ENDORSE_ID.eq(endorseId))
                .and(ENDORSE_FEE.VALID_FLAG.eq(TerminologyConfigEnum.VALID_FLAG.effective.name()))
                .fetchOneInto(EndorseFeePo.class);
    }

    /**
     * 查询保全费用数据
     * @param endorseIds 保全ID
     * @return
     */
    @Override
    public List<EndorseFeePo> queryEndorseFeeList(List<String> endorseIds) {
        return this.getDslContext()
                .selectFrom(ENDORSE_FEE)
                .where(ENDORSE_FEE.ENDORSE_ID.in(endorseIds))
                .and(ENDORSE_FEE.VALID_FLAG.eq(TerminologyConfigEnum.VALID_FLAG.effective.name()))
                .fetchInto(EndorseFeePo.class);
    }

    /**
     * 查询保全项目费用
     * @param endorseItemId 保全明细ID
     * @return
     */
    @Override
    public EndorseItemFeePo queryEndorseItemFee(String endorseItemId) {
        return this.getDslContext()
                .selectFrom(ENDORSE_ITEM_FEE)
                .where(ENDORSE_ITEM_FEE.ENDORSE_ITEM_ID.eq(endorseItemId))
                .and(ENDORSE_ITEM_FEE.VALID_FLAG.eq(TerminologyConfigEnum.VALID_FLAG.effective.name()))
                .fetchOneInto(EndorseItemFeePo.class);
    }

    /**
     * 查询保全项目费用
     * @param endorseId 保全ID
     * @return
     */
    @Override
    public EndorseItemFeePo queryEndorseItemFeeByEndorseId(String endorseId) {
        return this.getDslContext()
                .select(ENDORSE_ITEM_FEE.fields())
                .from(ENDORSE_ITEM_FEE)
                .innerJoin(ENDORSE_FEE).on(ENDORSE_ITEM_FEE.ENDORSE_FEE_ID.eq(ENDORSE_FEE.ENDORSE_FEE_ID))
                .where(ENDORSE_FEE.ENDORSE_ID.eq(endorseId))
                .and(ENDORSE_ITEM_FEE.VALID_FLAG.eq(TerminologyConfigEnum.VALID_FLAG.effective.name()))
                .and(ENDORSE_FEE.VALID_FLAG.eq(TerminologyConfigEnum.VALID_FLAG.effective.name()))
                .fetchOneInto(EndorseItemFeePo.class);
    }
}
