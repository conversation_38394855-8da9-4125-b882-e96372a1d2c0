package com.gclife.endorse.base.model.bo.group;

import lombok.Data;

@Data
public class GroupEndorseListBo{
    //保全id
    private String endorseId;
    //申请时间
    private Long applyDate;
    //保全状态
    private String endorseStatus;
    //保单id
    private String applyId;
    //保单号
    private String applyNo;
    //单位名称
    private String companyName;
    //投保人姓名
    private String delegateName;
    //投保人手机号码
    private String delegateMobile;
    //保全明细id
    private String endorseItemId;
    //增减被保人人数
    private Long addSubtractNumber;
    //批单项目id
    private String projectId;
    //批单类型编码code
    private String projectCode;
    /**
     * 总行数
     */
    private Integer totalLine;
}
