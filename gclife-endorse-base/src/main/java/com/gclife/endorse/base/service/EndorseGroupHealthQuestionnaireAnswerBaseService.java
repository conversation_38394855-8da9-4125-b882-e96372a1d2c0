package com.gclife.endorse.base.service;

import com.gclife.endorse.core.jooq.tables.pojos.EndorseGroupHealthQuestionnaireAnswerPo;

import java.util.List;

/**
 * <AUTHOR>
 *         create 19-9-10
 *         description:
 */
public interface EndorseGroupHealthQuestionnaireAnswerBaseService {

    /**
     * 被保人下的健康告知
     *
     * @param endorseId
     * @param insuredId
     * @return
     */
    List<EndorseGroupHealthQuestionnaireAnswerPo> queryEndorseGroupHealthQuestionnaireAnswer(String endorseId, String insuredId);

    /**
     * 保存团险健康告知
     *
     * @param endorseGroupHealthQuestionnaireAnswerPos
     * @param userId
     */
    void saveEndorseGroupHealthQuestionnaireAnswer(List<EndorseGroupHealthQuestionnaireAnswerPo> endorseGroupHealthQuestionnaireAnswerPos, String userId);
    /**
     * 被保人下的健康告知
     *
     * @param endorseItemId
     * @param insuredId
     * @return
     */
    List<EndorseGroupHealthQuestionnaireAnswerPo> queryEndorseGroupHealthQuestionnaireAnswerByEndorseItemId(String endorseItemId, String insuredId);

    /**
     * 删除健康告知
     * @param healthAnswerPos 健康告知
     */
    void deleteEndorseGroupHealthQuestionnaireAnswer(List<EndorseGroupHealthQuestionnaireAnswerPo> healthAnswerPos);

    /**
     * 根据被保人和保全明细ID查询健康告知
     * @param insuredIds 被保人ID集
     * @param endorseItemId 保全明细ID
     * @return
     */
    List<EndorseGroupHealthQuestionnaireAnswerPo> listHealthAnswer(List<String> insuredIds, String endorseItemId);
}