package com.gclife.endorse.base.dao.impl;

import com.gclife.common.dao.base.impl.BaseDaoImpl;
import com.gclife.common.model.config.TerminologyConfigEnum;
import com.gclife.common.util.AssertUtils;
import com.gclife.endorse.base.dao.EndorseItemBaseDao;
import com.gclife.endorse.base.model.bo.EndorseItemBo;
import com.gclife.endorse.base.model.config.EndorseTermEnum;
import com.gclife.endorse.core.jooq.tables.pojos.EndorseItemPo;
import org.jooq.Condition;
import org.springframework.stereotype.Repository;

import java.util.ArrayList;
import java.util.List;

import static com.gclife.endorse.core.jooq.Tables.ENDORSE;
import static com.gclife.endorse.core.jooq.Tables.ENDORSE_ACCEPT;
import static com.gclife.endorse.core.jooq.tables.BaseProject.BASE_PROJECT;
import static com.gclife.endorse.core.jooq.tables.EndorseApplicant.ENDORSE_APPLICANT;
import static com.gclife.endorse.core.jooq.tables.EndorseItem.ENDORSE_ITEM;

/**
 * <AUTHOR>
 * @version v2.0
 * Description: 保全明细基础DAO
 * @date 18-9-3
 */
@Repository
public class EndorseItemBaseDaoImpl extends BaseDaoImpl implements EndorseItemBaseDao {

    /**
     * 根据保全ID查询保全明细
     *
     * @param endorseId 保全ID
     * @return 保全明细
     */
    @Override
    public EndorseItemBo queryEndorseItem(String endorseId) {
        return this.getDslContext()
                .select(ENDORSE_ITEM.fields())
                .select(BASE_PROJECT.fields())
                .select(ENDORSE_ACCEPT.APPLY_NO)
                .select(ENDORSE_ACCEPT.APPLY_ID)
                .select(ENDORSE_APPLICANT.COMPANY_NAME)
                .from(ENDORSE_ITEM)
                .innerJoin(BASE_PROJECT).on(ENDORSE_ITEM.PROJECT_ID.eq(BASE_PROJECT.PROJECT_ID))
                .leftJoin(ENDORSE_APPLICANT).on(ENDORSE_APPLICANT.ENDORSE_ITEM_ID.eq(ENDORSE_ITEM.ENDORSE_ITEM_ID)
                        .and(ENDORSE_APPLICANT.CHANGE_FLAG.isNull().or(ENDORSE_APPLICANT.CHANGE_FLAG.eq(EndorseTermEnum.CHANGE_FLAG.UNCHANGE.name()))))
                .leftJoin(ENDORSE_ACCEPT).on(ENDORSE_ACCEPT.ENDORSE_ID.eq(ENDORSE_ITEM.ENDORSE_ID), ENDORSE_ACCEPT.VALID_FLAG.eq(EndorseTermEnum.VALID_FLAG.effective.name()))
                .where(ENDORSE_ITEM.ENDORSE_ID.eq(endorseId))
                .and(BASE_PROJECT.PROJECT_STATUS.eq(EndorseTermEnum.VALID_FLAG.effective.name()))
                .limit(1)
                .fetchOneInto(EndorseItemBo.class);
    }

    @Override
    public EndorseItemBo queryOneEndorseItemPo(String endorseId) {
        return this.getDslContext()
                .select(ENDORSE_ITEM.fields())
                .select(ENDORSE_ACCEPT.APPLY_NO)
                .select(ENDORSE_ACCEPT.APPLY_ID)
                .from(ENDORSE_ITEM)
                .leftJoin(ENDORSE_ACCEPT).on(ENDORSE_ACCEPT.ENDORSE_ID.eq(ENDORSE_ITEM.ENDORSE_ID), ENDORSE_ACCEPT.VALID_FLAG.eq(EndorseTermEnum.VALID_FLAG.effective.name()))
                .where(ENDORSE_ITEM.ENDORSE_ID.eq(endorseId),
                        ENDORSE_ITEM.VALID_FLAG.eq(EndorseTermEnum.VALID_FLAG.effective.name()))
                .limit(1)
                .fetchOneInto(EndorseItemBo.class);
    }

    /**
     * 查询保全明细
     *
     * @param endorseIds 保全ID集
     * @return
     */
    @Override
    public List<EndorseItemBo> listEndorseItem(List<String> endorseIds) {
        return this.getDslContext()
                .select(ENDORSE_ITEM.fields())
                .select(BASE_PROJECT.fields())
                .from(ENDORSE_ITEM)
                .innerJoin(BASE_PROJECT).on(ENDORSE_ITEM.PROJECT_ID.eq(BASE_PROJECT.PROJECT_ID))
                .where(ENDORSE_ITEM.ENDORSE_ID.in(endorseIds))
                .and(BASE_PROJECT.PROJECT_STATUS.eq(EndorseTermEnum.VALID_FLAG.effective.name()))
                .fetchInto(EndorseItemBo.class);
    }

    /**
     * 根据保单ID及项目编码查询保全明细信息
     *
     * @param policyId      保单ID
     * @param projectCode   项目编码
     * @param endorseStatus 项目状态
     * @return
     */
    @Override
    public List<EndorseItemPo> listEndorseItem(String policyId, String projectCode, String endorseStatus) {
        List<Condition> conditions = new ArrayList<>();
        conditions.add(ENDORSE_ACCEPT.APPLY_ID.eq(policyId));
        if (AssertUtils.isNotEmpty(projectCode)) {
            conditions.add(BASE_PROJECT.PROJECT_CODE.eq(projectCode));
        }
        if (AssertUtils.isNotEmpty(endorseStatus)) {
            conditions.add(ENDORSE.ENDORSE_STATUS.eq(endorseStatus));
        }
        conditions.add(ENDORSE_ACCEPT.VALID_FLAG.eq(TerminologyConfigEnum.VALID_FLAG.effective.name()));
        conditions.add(ENDORSE.VALID_FLAG.eq(TerminologyConfigEnum.VALID_FLAG.effective.name()));
        conditions.add(BASE_PROJECT.VALID_FLAG.eq(TerminologyConfigEnum.VALID_FLAG.effective.name()));
        conditions.add(ENDORSE_ITEM.VALID_FLAG.eq(TerminologyConfigEnum.VALID_FLAG.effective.name()));
        return this.getDslContext()
                .select(ENDORSE_ITEM.fields())
                .from(ENDORSE_ITEM)
                .innerJoin(ENDORSE).on(ENDORSE_ITEM.ENDORSE_ID.eq(ENDORSE.ENDORSE_ID))
                .innerJoin(ENDORSE_ACCEPT).on(ENDORSE.ENDORSE_ID.eq(ENDORSE_ACCEPT.ENDORSE_ID))
                .innerJoin(BASE_PROJECT).on(ENDORSE_ITEM.PROJECT_ID.eq(BASE_PROJECT.PROJECT_ID))
                .where(conditions)
                .fetchInto(EndorseItemPo.class);
    }
}
