package com.gclife.endorse.base.dao.impl;

import com.gclife.common.dao.base.impl.BaseDaoImpl;
import com.gclife.common.model.config.TerminologyConfigEnum;
import com.gclife.endorse.base.dao.EndorseHealthQuestionnaireRemarkBaseDao;
import com.gclife.endorse.core.jooq.tables.pojos.EndorseHealthQuestionnaireRemarkPo;
import org.springframework.stereotype.Repository;

import static com.gclife.endorse.core.jooq.tables.EndorseHealthQuestionnaireRemark.ENDORSE_HEALTH_QUESTIONNAIRE_REMARK;

/**
 * <AUTHOR>
 *         create 19-9-10
 *         description:
 */
@Repository
public class EndorseHealthQuestionnaireRemarkBaseDaoImpl extends BaseDaoImpl implements EndorseHealthQuestionnaireRemarkBaseDao {

    @Override
    public EndorseHealthQuestionnaireRemarkPo getEndorseHealthQuestionnaireRemark(String endorseId, String customerType) {
        return this.getDslContext().selectFrom(ENDORSE_HEALTH_QUESTIONNAIRE_REMARK)
                .where(ENDORSE_HEALTH_QUESTIONNAIRE_REMARK.ENDORSE_ID.eq(endorseId)
                        .and(ENDORSE_HEALTH_QUESTIONNAIRE_REMARK.CUSTOMER_TYPE.eq(customerType))
                        .and(ENDORSE_HEALTH_QUESTIONNAIRE_REMARK.VALID_FLAG.eq(TerminologyConfigEnum.VALID_FLAG.effective.name()))).fetchOneInto(EndorseHealthQuestionnaireRemarkPo.class);
    }
}