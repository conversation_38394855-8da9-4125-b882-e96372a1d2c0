package com.gclife.endorse.base.dao.group.impl;

import com.gclife.common.dao.base.impl.BaseDaoImpl;
import com.gclife.endorse.base.dao.group.GroupEndorsePaymentDaoService;
import com.gclife.endorse.base.model.bo.group.GroupPaymentEndorseBo;
import com.gclife.endorse.base.model.bo.group.GroupPaymentMethBo;
import com.gclife.endorse.base.model.config.EndorseTermEnum;
import com.gclife.endorse.core.jooq.Tables;
import org.springframework.stereotype.Repository;

import java.util.List;

import static com.gclife.endorse.core.jooq.tables.BranchEndoConfig.BRANCH_ENDO_CONFIG;
import static com.gclife.endorse.core.jooq.tables.BranchEndoConfigDetail.BRANCH_ENDO_CONFIG_DETAIL;
import static com.gclife.endorse.core.jooq.tables.ChgGroupAddSubtractInsured.CHG_GROUP_ADD_SUBTRACT_INSURED;
import static com.gclife.endorse.core.jooq.tables.Endorse.ENDORSE;
import static com.gclife.endorse.core.jooq.tables.EndorseAccept.ENDORSE_ACCEPT;
import static com.gclife.endorse.core.jooq.tables.EndorseAgent.ENDORSE_AGENT;
import static com.gclife.endorse.core.jooq.tables.EndorseApplicant.ENDORSE_APPLICANT;
import static com.gclife.endorse.core.jooq.tables.EndorseItem.ENDORSE_ITEM;

@Repository
public class GroupEndorsePaymentDaoServiceImpl extends BaseDaoImpl implements GroupEndorsePaymentDaoService {

    @Override
    public GroupPaymentEndorseBo queryGroupPaymentEndorseInfo(String endorseId) {
        return this.getDslContext()
                .select(ENDORSE_ACCEPT.ENDORSE_ID, ENDORSE_ACCEPT.APPLY_ID, ENDORSE_ACCEPT.APPLY_NO)
                .select(ENDORSE_ITEM.ENDORSE_ITEM_ID)
                .select(ENDORSE_APPLICANT.COMPANY_NAME, ENDORSE_APPLICANT.DELEGATE_NAME, ENDORSE_APPLICANT.DELEGATE_MOBILE)
                .select(CHG_GROUP_ADD_SUBTRACT_INSURED.APPLY_TYPE, CHG_GROUP_ADD_SUBTRACT_INSURED.ADD_SUBTRACT_NUMBER)
                .select(ENDORSE_AGENT.AGENT_ID, ENDORSE_AGENT.AGENT_CODE)
                .from(ENDORSE).innerJoin(ENDORSE_ACCEPT).on(ENDORSE_ACCEPT.ENDORSE_ID.eq(ENDORSE.ENDORSE_ID))
                .leftJoin(ENDORSE_ITEM).on(ENDORSE_ITEM.ENDORSE_ID.eq(ENDORSE.ENDORSE_ID))
                .leftJoin(ENDORSE_APPLICANT).on(ENDORSE_APPLICANT.ENDORSE_ITEM_ID.eq(ENDORSE_ITEM.ENDORSE_ITEM_ID))
                .leftJoin(ENDORSE_AGENT).on(ENDORSE_AGENT.ENDORSE_ITEM_ID.eq(ENDORSE_ITEM.ENDORSE_ITEM_ID))
                .leftJoin(CHG_GROUP_ADD_SUBTRACT_INSURED).on(CHG_GROUP_ADD_SUBTRACT_INSURED.ENDORSE_ITEM_ID.eq(ENDORSE_ITEM.ENDORSE_ITEM_ID))
                .where(ENDORSE.ENDORSE_ID.eq(endorseId).and(ENDORSE.VALID_FLAG.eq(EndorseTermEnum.VALID_FLAG.effective.name()))
                        .and(Tables.ENDORSE_ACCEPT.VALID_FLAG.eq(EndorseTermEnum.VALID_FLAG.effective.name()))
                        .and(Tables.ENDORSE_ACCEPT.ENDORSE_TYPE.eq(EndorseTermEnum.ENDORSE_TYPE.GROUP_POLICY.name())))
                .fetchOneInto(GroupPaymentEndorseBo.class);
    }

    @Override
    public List<GroupPaymentMethBo> queryPaymentMethod(String managerBranchId, String projectId) {
        return getDslContext()
                .select(BRANCH_ENDO_CONFIG.BRANCH_ID,BRANCH_ENDO_CONFIG.PROJECT_ID)
                .select(BRANCH_ENDO_CONFIG_DETAIL.fields())
                .from(BRANCH_ENDO_CONFIG)
                .innerJoin(BRANCH_ENDO_CONFIG_DETAIL).on(BRANCH_ENDO_CONFIG_DETAIL.ENDO_CONFIG_ID.eq(BRANCH_ENDO_CONFIG.ENDO_CONFIG_ID))
                .where(BRANCH_ENDO_CONFIG.VALID_FLAG.eq(EndorseTermEnum.VALID_FLAG.effective.name()).and(BRANCH_ENDO_CONFIG.PROJECT_ID.eq(projectId)))
//                .where(BRANCH_ENDO_CONFIG.BRANCH_ID.eq(managerBranchId).and(BRANCH_ENDO_CONFIG.PROJECT_ID.eq(projectId)))
        .fetchInto(GroupPaymentMethBo.class);
    }
}
