package com.gclife.endorse.base.dao.group;

import com.gclife.endorse.core.jooq.tables.pojos.GroupAddSubtractInsuredDeductPo;

import java.util.List;

/**
 * <AUTHOR>
 * create 2020/3/9
 * description:
 */
public interface GroupAddSubtractInsuredDeductBaseDao {

    /**
     * 查询团险增减员抵扣信息
     * @param endorseItemId
     * @return
     */
    List<GroupAddSubtractInsuredDeductPo> queryListGroupAddSubtractInsuredDeduct(String endorseItemId);
    /**
     * 查询增减被保人抵扣信息
     * @param endorseItemId 保全明细ID
     * @return
     */
    List<GroupAddSubtractInsuredDeductPo> listDeduct(String endorseItemId);

    /**
     * 查询增员替换进来的抵扣信息
     *
     * @param addInsuredId
     * @return
     */
    GroupAddSubtractInsuredDeductPo queryGroupAddSubtractInsuredDeduct(String addInsuredId);
}
