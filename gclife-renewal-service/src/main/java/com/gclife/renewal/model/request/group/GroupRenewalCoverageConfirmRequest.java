package com.gclife.renewal.model.request.group;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * create 2023/10/30 10:47
 * description:
 */
@Data
public class GroupRenewalCoverageConfirmRequest {
    @ApiModelProperty(example = "团险续保ID")
    String groupRenewalId;
    @ApiModelProperty(example = "缴费周期")
    String premiumFrequency;
    String type;

    /**
     * 投保单保费信息
     */
    private GroupRenewalPremiumRequest applyPremium;
}
