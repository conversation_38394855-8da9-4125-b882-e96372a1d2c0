package com.gclife.renewal.model.response;

import com.gclife.common.annotation.DateFormat;
import com.gclife.common.annotation.Internation;
import com.gclife.common.model.config.DateFormatPatternEnum;
import com.gclife.common.model.config.InternationCodeEnum;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 续保流程记录
 *
 * <AUTHOR>
 * @date 2018-12-10
 */
@Data
public class RenewalFlowRemarkRespone {

    @ApiModelProperty(example = "流程记录ID")
    private String renewalFlowRemarkId;
    @ApiModelProperty(example = "续保ID")
    private String renewalId;
    @ApiModelProperty(example = "续保操作类型")
    private String remarkType;
    @Internation(codeType = "REMARK_TYPE", filed = "remarkType", type = InternationCodeEnum.syscode)
    @ApiModelProperty(example = "续保操作类型 国际化")
    private String remarkTypeName;
    @ApiModelProperty(example = "续保操作用户ID")
    private String remarkUserId;
    @ApiModelProperty(example = "续保操作用户名")
    private String remarkUserName;
    @ApiModelProperty(example = "操作时间")
    private Long remarkDate;
    @DateFormat(filed = "remarkDate", pattern = DateFormatPatternEnum.FORMATE5)
    @ApiModelProperty(example = "操作时间 格式化")
    private String remarkDateFormat;
    @ApiModelProperty(example = "结果")
    private String remarkResult;
    @Internation(codeType = "REMARK_RESULT", filed = "remarkResult", type = InternationCodeEnum.syscode)
    @ApiModelProperty(example = "结果 国际化")
    private String remarkResultName;
    @ApiModelProperty(example = "备注内容")
    private String remarkContent;
}
