package com.gclife.renewal.controller.base;

import com.gclife.common.configuration.system.permissions.AutoCheckPermissions;
import com.gclife.common.controller.base.BaseController;
import com.gclife.common.model.ResultObject;
import com.gclife.renewal.service.base.RenewalInsuranceBaseService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiResponse;
import io.swagger.annotations.ApiResponses;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;

/**
 * <AUTHOR>
 * @version v1.0
 * Description:
 * @date 19-10-16
 */
@Api(tags = "续保基础接口", description = "续保基础接口")
@Controller
@RequestMapping(value = "/v1/base/renewal/insurance/")
public class RenewalInsuranceBaseController extends BaseController {
    @Autowired
    private RenewalInsuranceBaseService renewalInsuranceBaseService;

    @ApiOperation(value = "查询续保数据", notes = "根据保单ID查询正在处理(不包括完成)续保数据")
    @ApiResponses({
            @ApiResponse(code = 200, message = "请求成功")
    })
    @AutoCheckPermissions
    @GetMapping(value = "processing")
    public ResultObject listProcessingRenewalInsurance(@RequestParam(name = "policyId") String policyId) {
        return renewalInsuranceBaseService.listProcessingRenewalInsurance(policyId);
    }
}
