package com.gclife.renewal.controller.middle;

import com.alibaba.fastjson.JSON;
import com.gclife.common.annotation.ErrorTip;
import com.gclife.common.controller.base.BaseController;
import com.gclife.common.model.ResultObject;
import com.gclife.renewal.base.model.config.RenewalErrorConfigEnum;
import com.gclife.renewal.base.model.config.RenewalTermEnum;
import com.gclife.renewal.model.request.GroupRenewalRequest;
import com.gclife.renewal.model.response.GroupRenewalDashboardTaskResponse;
import com.gclife.renewal.model.response.GroupRenewalResponse;
import com.gclife.renewal.service.middle.GroupRenewalMiddleService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiResponse;
import io.swagger.annotations.ApiResponses;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

/**
 * <AUTHOR>
 * @version v1.0
 * Description: 团险续保中台接口（只提供其他微服务调用的接口，不提供页面调用接口）
 * @date 20-8-31
 */
@Api(tags = "团险续保中台接口", description = "团险续保中台接口（只提供其他微服务调用的接口，不提供页面调用接口）")
@RestController
@RequestMapping(value = "/v1/middle/group/renewal/")
public class GroupRenewalMiddleController extends BaseController {
    @Autowired
    private GroupRenewalMiddleService groupRenewalMiddleService;

    @ApiOperation(value = "生成团险续保数据", notes = "生成团险续保数据")
    @ApiResponses({
            @ApiResponse(code = 200, message = "请求成功")
    })
    @ErrorTip(classType= RenewalErrorConfigEnum.class, key = "GROUP_RENEWAL_BUSINESS_GENERATE_ERROR")
    @PostMapping(value = "generate")
    public ResultObject<GroupRenewalResponse> generateGroupRenewal(@RequestBody GroupRenewalRequest groupRenewalRequest) {
        System.out.println("===================生成团险续保数据==================="+groupRenewalRequest.getPolicyNo());
        System.out.println(JSON.toJSONString(groupRenewalRequest));
        return groupRenewalMiddleService.generateGroupRenewal(groupRenewalRequest, this.getCurrentLoginUsers(), RenewalTermEnum.RENEWAL_TYPE.GROUP_RENEWAL.name());
    }

    @ApiOperation(value = "删除团险续保数据", notes = "删除团险续保数据")
    @ApiResponses({
            @ApiResponse(code = 200, message = "请求成功")
    })
    @PostMapping(value = "delete")
    public ResultObject deleteGroupRenewal(@RequestParam(name = "policyId") String policyId,
                                           @RequestParam(name = "receivableDate") Long receivableDate) {
        System.out.println("===================删除团险续保数据==================="+policyId+"|"+receivableDate);
        return groupRenewalMiddleService.deleteGroupRenewalData(policyId, receivableDate);
    }

    @ApiOperation(value = "团险续保-业务员端仪表板", notes = "团险续保-业务员端仪表板")
    @GetMapping(value = "agent/dashboard")
    public ResultObject<GroupRenewalDashboardTaskResponse> getGroupRenewalAgentDashboard() {
        return groupRenewalMiddleService.getGroupRenewalAgentDashboard(this.getCurrentLoginUsers());
    }

}
