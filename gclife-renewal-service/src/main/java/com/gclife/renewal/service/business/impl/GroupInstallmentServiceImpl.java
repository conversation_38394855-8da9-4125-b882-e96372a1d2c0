package com.gclife.renewal.service.business.impl;

import com.alibaba.fastjson.JSON;
import com.gclife.agent.api.AgentApi;
import com.gclife.agent.model.request.AgentApplyQueryRequest;
import com.gclife.agent.model.response.AgentResponse;
import com.gclife.common.exception.RequestException;
import com.gclife.common.model.BasePageRequest;
import com.gclife.common.model.BasePageResponse;
import com.gclife.common.model.ResultObject;
import com.gclife.common.model.base.Users;
import com.gclife.common.model.config.TerminologyConfigEnum;
import com.gclife.common.model.pojo.AppRequestHeads;
import com.gclife.common.service.impl.BaseBusinessServiceImpl;
import com.gclife.common.util.AssertUtils;
import com.gclife.common.util.ClazzUtils;
import com.gclife.common.util.DateUtils;
import com.gclife.payment.api.PaymentApi;
import com.gclife.payment.api.PaymentBaseApi;
import com.gclife.payment.model.request.PaymentItemRequest;
import com.gclife.payment.model.request.PaymentRequest;
import com.gclife.payment.model.response.PaymentItemResponse;
import com.gclife.payment.model.response.PaymentResponse;
import com.gclife.platform.api.PlatformBranchApi;
import com.gclife.platform.api.PlatformConfigApi;
import com.gclife.platform.model.response.BranchResponse;
import com.gclife.platform.model.response.NotifyConfigResponse;
import com.gclife.policy.api.PolicyApi;
import com.gclife.policy.api.RenewalPolicyApi;
import com.gclife.policy.model.request.PolicyPaymentUpdateRequest;
import com.gclife.policy.model.request.PolicyUpdateRequest;
import com.gclife.policy.model.response.PaymentAuditDetailResponse;
import com.gclife.policy.model.response.PolicyResponse;
import com.gclife.policy.model.vo.PolicyApplicantVo;
import com.gclife.policy.model.vo.PolicyCoverageVo;
import com.gclife.product.model.response.property.ProductPropertySimpleResponse;
import com.gclife.renewal.base.model.bo.GroupInstallmentListBo;
import com.gclife.renewal.base.model.bo.GroupRenewalPaymentCoverageBo;
import com.gclife.renewal.base.model.bo.group.GroupRenewalBo;
import com.gclife.renewal.base.model.bo.group.GroupRenewalPaymentTransactionBo;
import com.gclife.renewal.base.model.config.RenewalErrorConfigEnum;
import com.gclife.renewal.base.model.config.RenewalTermEnum;
import com.gclife.renewal.base.model.feign.payment.PaymentBusinessDataReqFc;
import com.gclife.renewal.base.service.GroupRenewalBaseService;
import com.gclife.renewal.base.service.GroupRenewalCoverageService;
import com.gclife.renewal.base.service.PaymentCallbackDataBaseService;
import com.gclife.renewal.base.service.group.*;
import com.gclife.renewal.core.jooq.tables.pojos.*;
import com.gclife.renewal.model.request.ApplyPaymentNotifyRequest;
import com.gclife.renewal.model.request.ReceivableRequest;
import com.gclife.renewal.model.request.RenewalImmediatePaymentRequest;
import com.gclife.renewal.model.request.RenewalPaymentItemRequest;
import com.gclife.renewal.model.response.*;
import com.gclife.renewal.model.response.group.GroupInstallmentListResponse;
import com.gclife.renewal.service.business.GroupInstallmentService;
import com.gclife.renewal.service.business.GroupInvoiceService;
import com.gclife.renewal.validate.transform.GroupInstallmentDataTransfer;
import com.gclife.renewal.validate.transform.GroupRenewalTransData;
import lombok.extern.slf4j.Slf4j;
import org.modelmapper.TypeToken;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.PlatformTransactionManager;
import org.springframework.transaction.TransactionStatus;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.support.DefaultTransactionDefinition;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

import static com.gclife.renewal.base.model.config.RenewalErrorConfigEnum.*;

/**
 * <AUTHOR>
 * create 2021/11/29 19:14
 * description:
 */
@Service
@Slf4j
public class GroupInstallmentServiceImpl extends BaseBusinessServiceImpl implements GroupInstallmentService {
    @Autowired
    private GroupRenewalAttachmentBaseService groupRenewalAttachmentBaseService;
    @Autowired
    private GroupRenewalBaseService groupRenewalBaseService;
    @Autowired
    private GroupRenewalInsuredBaseService groupRenewalInsuredBaseService;
    @Autowired
    private GroupRenewalCoverageService groupRenewalCoverageService;
    @Autowired
    private GroupRenewalCoverageBaseService groupRenewalCoverageBaseService;
    @Autowired
    private GroupRenewalPaymentTransactionBaseService paymentTransactionBaseService;
    @Autowired
    private PaymentBaseApi paymentBaseApi;
    @Autowired
    private PlatformConfigApi platformConfigApi;
    @Autowired
    private PaymentCallbackDataBaseService paymentCallbackDataBaseService;
    @Autowired
    private GroupRenewalPremiumBaseService groupRenewalPremiumBaseService;
    @Autowired
    private PolicyApi policyApi;
    @Autowired
    private GroupRenewalPaymentBaseService groupRenewalPaymentBaseService;
    @Autowired
    private RenewalPolicyApi renewalPolicyApi;
    @Autowired
    private PlatformTransactionManager platformTransactionManager;
    @Autowired
    private GroupInstallmentDataTransfer groupInstallmentDataTransfer;
    @Autowired
    private PlatformBranchApi platformBranchApi;
    @Autowired
    private AgentApi agentApi;
    @Autowired
    private GroupRenewalTransData groupRenewalTransData;
    @Autowired
    private PaymentApi paymentApi;
    @Autowired
    private GroupInvoiceService groupInvoiceService;

    /**
     * 团险续期立即支付
     *
     * @param users                   用户
     * @param appRequestHandler       请求头
     * @param immediatePaymentRequest 支付请求
     * @return RenewalImmediatePaymentResponse
     */
    @Override
    @Transactional
    public ResultObject<Map> postImmediatePayment(Users users, AppRequestHeads appRequestHandler, RenewalImmediatePaymentRequest immediatePaymentRequest) {
        // 数据验证
        AssertUtils.isNotEmpty(log, immediatePaymentRequest.getPolicyId(), RENEWAL_QUERY_POLICY_ID_IS_NOT_NULL);
        AssertUtils.isNotEmpty(log, immediatePaymentRequest.getPolicyPaymentIds(), RENEWAL_PARAMETER_POLICY_PAYMENT_ID_IS_NOT_NULL);

        List<GroupRenewalPremiumPo> groupRenewalPremiumPos = groupRenewalPremiumBaseService.listGroupRenewalPremiumByPolicyPaymentId(immediatePaymentRequest.getPolicyPaymentIds());
        AssertUtils.isNotEmpty(log, groupRenewalPremiumPos, RENEWAL_BASE_BUSINESS_RENEWAL_PREMIUM_IS_NOT_FOUND_OBJECT);

        List<String> groupRenewalIds = groupRenewalPremiumPos.stream().map(GroupRenewalPremiumPo::getGroupRenewalId).distinct().collect(Collectors.toList());
        List<GroupRenewalPo> groupRenewalPos = groupRenewalBaseService.listRenewalByRenewalIds(groupRenewalIds);

        BigDecimal receivablePremium = groupRenewalPremiumPos.stream().map(GroupRenewalPremiumPo::getPeriodTotalPremium).reduce(BigDecimal.ZERO, BigDecimal::add);

        // 查询团险续保
        AssertUtils.isNotNull(log, groupRenewalPos, RenewalErrorConfigEnum.GROUP_RENEWAL_BUSINESS_GROUP_RENEWAL_IS_NOT_FOUND_OBJECT);
        String userId = users.getUserId();
        groupRenewalPos.forEach(groupRenewalPo -> {
            if (!RenewalTermEnum.RENEWAL_STATUS.PAYMENT.name().equals(groupRenewalPo.getGroupRenewalStatus())) {
                throwsException(RenewalErrorConfigEnum.GROUP_RENEWAL_BUSINESS_STATUS_ERROR);
            }
            groupRenewalPo.setPaymentDate(DateUtils.getCurrentTime());
            groupRenewalBaseService.saveGroupRenewal(groupRenewalPo, userId);
        });

        // 查询团险续保支付事务
        List<GroupRenewalPaymentTransactionBo> renewalPaymentTransactionPos = paymentTransactionBaseService.listGroupRenewalPaymentTransactionPo(groupRenewalPremiumPos.get(0).getGroupRenewalId());
        if (AssertUtils.isNotEmpty(renewalPaymentTransactionPos)) {
            renewalPaymentTransactionPos.forEach(paymentTransactionBo -> {
                if (RenewalTermEnum.PAYMENT_STATUS.PAYMENT_FINISHED.name().equals(paymentTransactionBo.getPaymentStatus())) {
                    // 支付状态为“支付完成”，不允许重复支付
                    throw new RequestException(RENEWAL_BASE_BUSINESS_REPEAT_PAYMENT_ERROR);
                }
                List<String> remarkPaymentMethods = Arrays.asList(RenewalTermEnum.PAYMENT_METHODS.WING_OFFLINE.name(), RenewalTermEnum.PAYMENT_METHODS.ABA_PAYMENTS.name());
                final boolean isInvalid = (RenewalTermEnum.PAYMENT_STATUS.PAYMENT_WAITTING.name().equals(paymentTransactionBo.getPaymentStatus()) &&
                        !remarkPaymentMethods.contains(paymentTransactionBo.getRenewalPaymentTransactionItem().get(0).getPaymentMethodCode())) ||
                        RenewalTermEnum.PAYMENT_STATUS.PAYMENT_FAILED.name().equals(paymentTransactionBo.getPaymentStatus()) ||
                        RenewalTermEnum.PAYMENT_STATUS.PAYMENT_INVALID.name().equals(paymentTransactionBo.getPaymentStatus());
                if (isInvalid) {
                    paymentTransactionBo.setPaymentStatus(RenewalTermEnum.PAYMENT_STATUS.PAYMENT_INVALID.name());
                    paymentTransactionBo.setValidFlag(TerminologyConfigEnum.VALID_FLAG.invalid.name());
                    List<GroupRenewalPaymentTransactionItemPo> originPaymentTransactionItemPos = paymentTransactionBaseService.listPaymentTransactionItem(paymentTransactionBo.getPaymentTransactionId());
                    if (AssertUtils.isNotEmpty(originPaymentTransactionItemPos)) {
                        originPaymentTransactionItemPos.forEach(paymentTransactionItem -> {
                            paymentTransactionItem.setValidFlag(TerminologyConfigEnum.VALID_FLAG.invalid.name());
                            paymentTransactionItem.setPaymentStatus(RenewalTermEnum.PAYMENT_STATUS.PAYMENT_INVALID.name());
                        });
                    }
                    // 更新支付事务
                    paymentTransactionBaseService.savePaymentTransaction(paymentTransactionBo, userId);
                    paymentTransactionBaseService.updatePaymentTransactionItem(originPaymentTransactionItemPos, userId);
                    // 作废财务中心数据
                    ResultObject<Void> voidResultObject = paymentApi.updatePaymentStatus(paymentTransactionBo.getPaymentId(), RenewalTermEnum.PAYMENT_STATUS.PAYMENT_INVALID.name());
                    paymentTransactionBaseService.deleteGroupRenewalTransactionRelation(paymentTransactionBo.getPaymentTransactionId());
                }
            });
        }

        // 保存支付事务
        GroupRenewalPaymentTransactionPo newPaymentTransactionPo = new GroupRenewalPaymentTransactionPo();
        newPaymentTransactionPo.setPaymentAmount(receivablePremium);
        newPaymentTransactionPo.setPaymentDate(DateUtils.getCurrentTime());
        paymentTransactionBaseService.savePaymentTransaction(newPaymentTransactionPo, userId);

        // 发起支付所需参数
        PaymentRequest paymentRequest = new PaymentRequest();
        paymentRequest.setUserId(userId);
        paymentRequest.setBranchId(groupRenewalPos.get(0).getBranchId());
        paymentRequest.setDeviceChannelId(appRequestHandler.getDeviceChannel());
        paymentRequest.setBusinessId(newPaymentTransactionPo.getPaymentTransactionId());
        paymentRequest.setBusinessNo(groupRenewalPos.get(0).getRenewalPolicyNo());
        paymentRequest.setBusinessType(RenewalTermEnum.PAYMENT_BUSINESS_TYPE.GROUP_INSTALLMENT.name());
        paymentRequest.setDuePayAmount(receivablePremium);
        paymentRequest.setCurrency("USD");
        // 回调接口路径
        ResultObject<NotifyConfigResponse> notifyConfig = platformConfigApi.getNotifyConfig(RenewalTermEnum.BUSINESS_TYPE.GROUP_INSTALLMENT.name());
        AssertUtils.isResultObjectDataNull(log, notifyConfig);
        paymentRequest.setPaymentHandler(notifyConfig.getData().getNotifyUrl());
        // 查询新契约投保单ID
        ResultObject<PolicyResponse> policyObject = policyApi.queryInitialPolicy(immediatePaymentRequest.getPolicyId());
        AssertUtils.isResultObjectDataNull(log, policyObject);
        paymentRequest.setInitialApplyId(policyObject.getData().getApplyId());

        // 支付状态
        String paymentStatus = RenewalTermEnum.PAYMENT_STATUS.PAYMENT_WAITTING.name();
        List<PaymentItemRequest> paymentItemRequests = new ArrayList<>();
        for (RenewalPaymentItemRequest renewalPaymentItemRequest : immediatePaymentRequest.getPaymentItem()) {
            PaymentItemRequest paymentItemRequest = new PaymentItemRequest();
            paymentItemRequest.setPaymentMethodCode(renewalPaymentItemRequest.getPaymentMethodCode());
            //若存在支付方式，且为银行转账和支票，则是上传凭证，需要加上更新标识，不会重新下单，只会更新凭证
            if (RenewalTermEnum.PAYMENT_METHODS.BANK_TRANSFER.name().equals(renewalPaymentItemRequest.getPaymentMethodCode())
                    || RenewalTermEnum.PAYMENT_METHODS.CHEQUE.name().equals(renewalPaymentItemRequest.getPaymentMethodCode())) {
                paymentRequest.setPaymentStatusUpdateFlag(TerminologyConfigEnum.WHETHER.YES.name());
            }
            paymentItemRequest.setPaymentTypeCode(renewalPaymentItemRequest.getPaymentTypeCode());
            paymentItemRequest.setDuePayAmount(receivablePremium);
            paymentItemRequests.add(paymentItemRequest);
        }
        paymentRequest.setPaymentExpDate(groupRenewalPos.get(0).getGracePeriodExpDate());
        paymentRequest.setItems(paymentItemRequests);

        //冗余业务数据到支付中心
        PaymentBusinessDataReqFc paymentBusinessDataReqFc = groupInstallmentDataTransfer.transferPaymentBusinessData(receivablePremium, groupRenewalPos, groupRenewalPremiumPos);
        paymentRequest.setBusinessJson(JSON.toJSONString(paymentBusinessDataReqFc));

        // 发起支付
        log.info("团险续保发起支付:{}", JSON.toJSONString(paymentRequest));
        ResultObject<PaymentResponse> paymentRespFcResultObject = paymentBaseApi.startPayment(paymentRequest);
        log.info("团险续保支付返回:{}", JSON.toJSONString(paymentRespFcResultObject));
        AssertUtils.isResultObjectError(log, paymentRespFcResultObject);
        PaymentResponse paymentResponse = paymentRespFcResultObject.getData();

        newPaymentTransactionPo.setPaymentStatus(paymentStatus);
        newPaymentTransactionPo.setPaymentId(paymentResponse.getPaymentId());
        paymentTransactionBaseService.savePaymentTransaction(newPaymentTransactionPo, userId);

        if (AssertUtils.isNotEmpty(paymentResponse.getItems())) {
            List<GroupRenewalPaymentTransactionItemPo> paymentTransactionItemPos = new ArrayList<>();
            for (PaymentItemResponse paymentItem : paymentResponse.getItems()) {
                GroupRenewalPaymentTransactionItemPo paymentTransactionItemPo = new GroupRenewalPaymentTransactionItemPo();
                ClazzUtils.copyPropertiesIgnoreNull(paymentItem, paymentTransactionItemPo);
                paymentTransactionItemPo.setPaymentAmount(new BigDecimal(paymentItem.getDuePayAmount()));
                paymentTransactionItemPo.setPaymentTransactionId(newPaymentTransactionPo.getPaymentTransactionId());
                paymentTransactionItemPo.setPaymentStatus(paymentStatus);
                paymentTransactionItemPos.add(paymentTransactionItemPo);
            }
            paymentTransactionBaseService.addPaymentTransactionItem(paymentTransactionItemPos, userId);
        }

        paymentTransactionBaseService.deleteGroupRenewalTransactionRelation(newPaymentTransactionPo.getPaymentTransactionId());
        groupRenewalPos.forEach(groupRenewalPo -> {
            String groupRenewalId = groupRenewalPo.getGroupRenewalId();
            // 更新保费数据
            GroupRenewalPremiumPo groupRenewalPremiumPo = groupRenewalPremiumBaseService.queryGroupRenewalPremium(groupRenewalId);
            groupRenewalPremiumPo.setPremiumStatus(paymentStatus);
            groupRenewalPremiumBaseService.saveGroupRenewalPremium(groupRenewalPremiumPo, userId);

            //保存续期事物关联表
            GroupRenewalTransactionRelationPo groupRenewalTransactionRelationPo = new GroupRenewalTransactionRelationPo();
            groupRenewalTransactionRelationPo.setPaymentTransactionId(newPaymentTransactionPo.getPaymentTransactionId());
            groupRenewalTransactionRelationPo.setGroupRenewalId(groupRenewalPo.getGroupRenewalId());
            paymentTransactionBaseService.saveGroupRenewalTransactionRelationPo(groupRenewalTransactionRelationPo, userId);

//            // 更新缴费数据
//            GroupRenewalPaymentPo groupRenewalPaymentPo = groupRenewalPaymentBaseService.queryGroupRenewalPayment(groupRenewalId);
//            groupRenewalPaymentPo.setPaymentStatusCode(paymentStatus);
//            groupRenewalPaymentPo.setPaymentModeCode(immediatePaymentRequest.getPaymentItem().get(0).getPaymentMethodCode());
//            groupRenewalPaymentBaseService.saveGroupRenewalPayment(groupRenewalPaymentPo, userId);
        });

        //11.修改保单缴费信息
        PolicyPaymentUpdateRequest policyPaymentUpdateRequest = new PolicyPaymentUpdateRequest();
        policyPaymentUpdateRequest.setPaymentIdList(immediatePaymentRequest.getPolicyPaymentIds());
        policyPaymentUpdateRequest.setPaymentStatusCode(newPaymentTransactionPo.getPaymentStatus());
        policyPaymentUpdateRequest.setPaymentModeCode(immediatePaymentRequest.getPaymentItem().get(0).getPaymentMethodCode());
        policyPaymentUpdateRequest.setBusinessType(RenewalTermEnum.RENEWAL_TYPE.GROUP_INSTALLMENT.name());
        policyPaymentUpdateRequest.setAppRequestHeads(appRequestHandler);
        ResultObject resultObject1 = policyApi.updatePolicyPayment(policyPaymentUpdateRequest);
        AssertUtils.isResultObjectError(log, resultObject1);

        this.getLogger().info("=*==*==*==*==*==*==*==*==*==*==*==*==*==*==*==*==*==*=savePaymentInvoice异步调用微服务保存发票数据");
        groupInvoiceService.savePaymentInvoice(users, groupRenewalPos.get(0).getGroupRenewalId(), RenewalTermEnum.BUSINESS_TYPE.GROUP_INSTALLMENT.name(), receivablePremium);

        ResultObject<Map> resultObject = new ResultObject<>();
        if (AssertUtils.isNotEmpty(paymentResponse.getPaymentCodeNo())) {
            Map<String, String> map = new HashMap<>();
            map.put("paymentCodeNo", paymentResponse.getPaymentCodeNo());
            resultObject.setData(map);
        }
        return resultObject;
    }

    /**
     * 团险续期支付回调
     *
     * @param applyPaymentNotifyRequest 回调数据
     * @param users                     用户
     */
    @Override
    @Transactional
    public void handPaymentNotify(ApplyPaymentNotifyRequest applyPaymentNotifyRequest, Users users) {
        String userId = users.getUserId();
        AppRequestHeads appRequestHeads = new AppRequestHeads();
        appRequestHeads.setDeviceChannel(applyPaymentNotifyRequest.getDeviceChannel());
        try {
            String paymentTransactionId = applyPaymentNotifyRequest.getBusinessId();
            String paymentStatus = applyPaymentNotifyRequest.getStatus();
            Long currentTime = DateUtils.getCurrentTime();

            GroupRenewalPaymentTransactionBo paymentTransactionPo = paymentTransactionBaseService.getPaymentTransaction(paymentTransactionId);
            List<GroupRenewalPo> groupRenewalPos = paymentTransactionBaseService.listGroupRenewalByPaymentTransactionId(paymentTransactionId);
            if (!AssertUtils.isNotNull(paymentTransactionPo) || !AssertUtils.isNotEmpty(groupRenewalPos)) {
                return;
            }
            String policyId = groupRenewalPos.get(0).getPolicyId();

            Long arrivalDate = currentTime;
            if (AssertUtils.isNotNull(applyPaymentNotifyRequest.getActualPayDate())) {
                arrivalDate = applyPaymentNotifyRequest.getActualPayDate();
            }

            // 更新团险续保支付事务数据
            paymentTransactionPo.setPaymentStatus(paymentStatus);
            paymentTransactionPo.setRemark(applyPaymentNotifyRequest.getRemark());
            if (RenewalTermEnum.PAYMENT_STATUS.PAYMENT_SUCCESS.name().equals(paymentStatus)) {
                paymentTransactionPo.setArrivalDate(arrivalDate);
            }
            if (AssertUtils.isNotEmpty(applyPaymentNotifyRequest.getAmount())) {
                if (paymentTransactionPo.getPaymentAmount().compareTo(new BigDecimal(applyPaymentNotifyRequest.getAmount())) == 0) {
                    paymentTransactionPo.setMatchResult(RenewalTermEnum.YES_NO.YES.name());
                } else {
                    paymentTransactionPo.setMatchResult(RenewalTermEnum.YES_NO.NO.name());
                }
            }
            paymentTransactionBaseService.savePaymentTransaction(paymentTransactionPo, userId);

            List<GroupRenewalPaymentTransactionItemPo> paymentTransactionItemPos = paymentTransactionPo.getRenewalPaymentTransactionItem();
            if (AssertUtils.isNotEmpty(paymentTransactionItemPos)) {
                paymentTransactionItemPos.forEach(paymentTransactionItem -> {
                    paymentTransactionItem.setPaymentStatus(paymentStatus);
                    paymentTransactionItem.setPaymentMethodCode(applyPaymentNotifyRequest.getPaymentMethodCode());
                });
                paymentTransactionBaseService.updatePaymentTransactionItem(paymentTransactionItemPos, userId);
            }

            for (GroupRenewalPo groupRenewalPo : groupRenewalPos) {
                String groupRenewalId = groupRenewalPo.getGroupRenewalId();
                // 查询团险续保
                AssertUtils.isNotNull(log, groupRenewalPo, GROUP_RENEWAL_BUSINESS_GROUP_RENEWAL_IS_NOT_FOUND_OBJECT);
                if (RenewalTermEnum.PAYMENT_STATUS.PAYMENT_SUCCESS.name().equals(paymentStatus)) {
                    groupRenewalPo.setGroupRenewalStatus(RenewalTermEnum.RENEWAL_STATUS.ACTUAL_PAY.name());
                    groupRenewalPo.setArrivalDate(arrivalDate);
                }

                boolean invalidPayment = Arrays.asList(RenewalTermEnum.PAYMENT_STATUS.PAYMENT_FAILED.name(), RenewalTermEnum.PAYMENT_STATUS.PAYMENT_INVALID.name()).contains(paymentStatus) && currentTime >= groupRenewalPo.getPaymentExpDate();
                if (invalidPayment) {
                    groupRenewalPo.setGroupRenewalStatus(RenewalTermEnum.GROUP_RENEWAL_STATUS.INVALID.name());
                }
                groupRenewalBaseService.saveGroupRenewal(groupRenewalPo, userId);

                // 更新保费数据
                GroupRenewalPremiumPo groupRenewalPremiumPo = groupRenewalPremiumBaseService.queryGroupRenewalPremium(groupRenewalId);
                groupRenewalPremiumPo.setPremiumStatus(paymentStatus);
                if (RenewalTermEnum.PAYMENT_STATUS.PAYMENT_SUCCESS.name().equals(paymentStatus)) {
                    groupRenewalPremiumPo.setTotalActualPremium(groupRenewalPremiumPo.getPeriodTotalPremium());
                    groupRenewalPremiumPo.setActualPremium(groupRenewalPremiumPo.getPeriodTotalPremium());
                }
                groupRenewalPremiumBaseService.saveGroupRenewalPremium(groupRenewalPremiumPo, userId);

                // 更新险种保费数据
                List<GroupRenewalCoveragePremiumPo> coveragePremiumPos = groupRenewalPremiumBaseService.listGroupRenewalCoveragePremium(groupRenewalId);
                if (RenewalTermEnum.PAYMENT_STATUS.PAYMENT_SUCCESS.name().equals(paymentStatus)) {
                    coveragePremiumPos.forEach(coveragePremiumPo -> {
                        coveragePremiumPo.setTotalActualPremium(coveragePremiumPo.getPeriodTotalPremium());
                        coveragePremiumPo.setActualPremium(coveragePremiumPo.getPeriodTotalPremium());
                    });
                    groupRenewalPremiumBaseService.updateGroupRenewalCoveragePremium(coveragePremiumPos, userId);
                }

                // 保存收款附件
                if (AssertUtils.isNotEmpty(applyPaymentNotifyRequest.getListAttachment())) {
                    List<GroupRenewalAttachmentPo> attachmentPos = new ArrayList<>();
                    applyPaymentNotifyRequest.getListAttachment().forEach(attachmentId -> {
                        GroupRenewalAttachmentPo attachmentPo = new GroupRenewalAttachmentPo();
                        attachmentPo.setPaymentTransactionId(paymentTransactionPo.getPaymentTransactionId());
                        attachmentPo.setAttachmentId(attachmentId);
                        attachmentPo.setAttachmentTypeCode(RenewalTermEnum.GROUP_RENEWAL_ATTACHMENT_TYPE.RECEIPT_CERTIFY.name());
                        attachmentPos.add(attachmentPo);
                    });
                    groupRenewalAttachmentBaseService.addGroupRenewalAttachment(attachmentPos, userId);
                }

            }

            //缴费成功的如果保单已失效修改成保障中
            ResultObject<PolicyResponse> policyResultObject = policyApi.queryOnePolicy(policyId);
            AssertUtils.isResultObjectDataNull(log, policyResultObject);
            PolicyResponse policyResponse = policyResultObject.getData();
            String policyStatus = policyResponse.getPolicyStatus();
            if (!policyStatus.equals(RenewalTermEnum.POLICY_STATUS.POLICY_STATUS_EFFECTIVE.name())) {
                PolicyUpdateRequest policyUpdateRequest = new PolicyUpdateRequest();
                policyUpdateRequest.setPolicyId(policyId);
                policyUpdateRequest.setPolicyStatus(RenewalTermEnum.POLICY_STATUS.POLICY_STATUS_EFFECTIVE.name());
                policyUpdateRequest.setInvalidDate(null);
                policyUpdateRequest.setThoroughInvalidDate(null);
                ResultObject resultObject = policyApi.updatePolicy(policyUpdateRequest);
                AssertUtils.isResultObjectError(log, resultObject);

                // 将险种状态改为有效
                List<String> coverageIds = new ArrayList<>();
                groupRenewalPos.forEach(groupRenewalPo -> {
                    List<GroupRenewalCoveragePo> groupRenewalCoveragePos = groupRenewalCoverageService.listGroupRenewalCoverage(groupRenewalPo.getGroupRenewalId());
                    groupRenewalCoveragePos.stream()
                            .filter(groupRenewalCoveragePo -> TerminologyConfigEnum.VALID_FLAG.effective.name().equals(groupRenewalCoveragePo.getValidFlag())
                                    && RenewalTermEnum.COVERAGE_STATUS.EFFECTIVE.name().equals(groupRenewalCoveragePo.getCoverageStatus())
                                    && !coverageIds.contains(groupRenewalCoveragePo.getCoverageId()))
                            .forEach(renewalCoveragePo -> coverageIds.add(renewalCoveragePo.getCoverageId()));
                });
                ResultObject resultObject2 = renewalPolicyApi.updateCoverageEffective(coverageIds);
                AssertUtils.isResultObjectError(log, resultObject2);
            }

            if (!RenewalTermEnum.PAYMENT_STATUS.PAYMENT_INVALID.name().equals(applyPaymentNotifyRequest.getStatus())) {
                List<String> paymentIdList = groupRenewalPos.stream().map(GroupRenewalPo::getPolicyPaymentId).collect(Collectors.toList());
                PolicyPaymentUpdateRequest policyPaymentUpdateRequest = new PolicyPaymentUpdateRequest();
                policyPaymentUpdateRequest.setPaymentIdList(paymentIdList);
                policyPaymentUpdateRequest.setPaymentStatusCode(applyPaymentNotifyRequest.getStatus());
                policyPaymentUpdateRequest.setPaymentModeCode(applyPaymentNotifyRequest.getPaymentMethodCode());
                policyPaymentUpdateRequest.setBusinessType(RenewalTermEnum.RENEWAL_TYPE.GROUP_INSTALLMENT.name());
                policyPaymentUpdateRequest.setAppRequestHeads(appRequestHeads);
                //更新保单缴费状态并产生佣金
                ResultObject resultObject = policyApi.updatePolicyPayment(policyPaymentUpdateRequest);
                AssertUtils.isResultObjectError(log, resultObject);
            }
        } catch (Exception e) {
            e.printStackTrace();
            throwsException(RenewalErrorConfigEnum.RENEWAL_PAYMENT_HAND_PAYMENT_NOTIFY_ERROR);
        } finally {
            log.info("保存回调数据");
            PaymentCallbackDataPo paymentCallbackDataPo = new PaymentCallbackDataPo();
            ClazzUtils.copyPropertiesIgnoreNull(applyPaymentNotifyRequest, paymentCallbackDataPo);
            paymentCallbackDataPo.setBusinessType(RenewalTermEnum.BUSINESS_TYPE.GROUP_INSTALLMENT.name());
            paymentCallbackDataPo.setCallbackData(JSON.toJSONString(applyPaymentNotifyRequest));
            paymentCallbackDataBaseService.savePaymentCallbackData(paymentCallbackDataPo, userId);
        }
    }

    /**
     * 续期消息通知（催缴通知、失效通知）
     *
     * @param appRequestHeads 请求头
     * @param users           当前用户
     * @param basePageRequest 分页信息
     * @return String
     */
    @Override
    public String groupInstallmentNotify(AppRequestHeads appRequestHeads, Users users, BasePageRequest basePageRequest) {
        log.info("=================================续期消息通知 begin=====================================");
        log.info("currentPage:" + basePageRequest.getCurrentPage());
        log.info("pageSize:" + basePageRequest.getPageSize());

        // 缓存产品配置信息
        Map<String, List<ProductPropertySimpleResponse>> productPropertyMap = new HashMap<>();

        // 查询续期数据
        List<GroupRenewalBo> listGroupRenewalBo = groupRenewalBaseService.listGroupRenewalBo(basePageRequest);

        if (AssertUtils.isNotEmpty(listGroupRenewalBo)) {
            log.info("本次处理续期消息数：{}", listGroupRenewalBo.size());
            listGroupRenewalBo.forEach(groupRenewalBo -> {
                // 获取事物状态
                TransactionStatus transactionStatus = platformTransactionManager.getTransaction(new DefaultTransactionDefinition());
                try {
                    // 续期通知处理
                    groupInstallmentDataTransfer.groupInstallmentNotifyProcess(appRequestHeads, users, productPropertyMap, groupRenewalBo);
                    // 提交事物
                    platformTransactionManager.commit(transactionStatus);
                } catch (Exception e) {
                    log.error("消息处理出错：{}", groupRenewalBo.getGroupRenewalId());
                    // 事务回滚
                    platformTransactionManager.rollback(transactionStatus);
                    e.printStackTrace();
                }
            });
        }

        log.info("=================================续期消息通知 end=======================================");

        // 返回是否处理完所有续期标志
        if (AssertUtils.isNotEmpty(listGroupRenewalBo) && listGroupRenewalBo.size() == basePageRequest.getPageSize()) {
            return TerminologyConfigEnum.WHETHER.NO.name();
        }
        return TerminologyConfigEnum.WHETHER.YES.name();
    }

    /**
     * 团险续期应收列表
     *
     * @param users             用户
     * @param appRequestHandler 请求头
     * @param receivableRequest 列表请求参数
     * @return GroupInstallmentListResponse
     */
    @Override
    public ResultObject<BasePageResponse<GroupInstallmentListResponse>> loadGroupInstallmentList(Users users, AppRequestHeads appRequestHandler, ReceivableRequest receivableRequest) {
        ResultObject<BasePageResponse<GroupInstallmentListResponse>> resultObject = new ResultObject<>();
        ResultObject<List<BranchResponse>> branchResultObject = platformBranchApi.userManagerLeafBranchs();
        AssertUtils.isResultObjectError(log, branchResultObject, RenewalErrorConfigEnum.RENEWAL_QUERY_BRANCH_IS_ERROR);
        List<BranchResponse> branchResponses = branchResultObject.getData();
        List<String> branchIdList = branchResponses.stream()
                .map(BranchResponse::getBranchId)
                .filter(AssertUtils::isNotEmpty).distinct().collect(Collectors.toList());
        if (!AssertUtils.isNotEmpty(branchIdList)) {
            return resultObject;
        }
        receivableRequest.setBranchIdList(branchIdList);

        List<GroupInstallmentListBo> groupInstallmentListBos = groupRenewalBaseService.loadGroupInstallmentList(receivableRequest);
        if (!AssertUtils.isNotEmpty(groupInstallmentListBos)) {
            return resultObject;
        }
        List<GroupInstallmentListResponse> groupInstallmentListResponses = (List<GroupInstallmentListResponse>) this.converterList(groupInstallmentListBos, new TypeToken<List<GroupInstallmentListResponse>>() {
        }.getType());

        List<String> agentIdList = groupInstallmentListResponses.stream().map(GroupInstallmentListResponse::getAgentId).distinct().collect(Collectors.toList());
        AgentApplyQueryRequest agentApplyQueryRequest = new AgentApplyQueryRequest();
        agentApplyQueryRequest.setListAgentId(agentIdList);
        List<AgentResponse> agentResponseList = agentApi.agentsGet(agentApplyQueryRequest).getData();

        if (AssertUtils.isNotEmpty(groupInstallmentListResponses)) {
            groupInstallmentListResponses.forEach(groupInstallmentListResponse -> {
                //业务员信息
                if (AssertUtils.isNotEmpty(agentResponseList) && AssertUtils.isNotEmpty(groupInstallmentListResponse.getAgentId())) {
                    agentResponseList.stream().filter(agentResponse -> agentResponse.getAgentId().equals(groupInstallmentListResponse.getAgentId())).findFirst()
                            .ifPresent(agentResponse -> {
                                groupInstallmentListResponse.setAgentCode(agentResponse.getAgentCode());
                                groupInstallmentListResponse.setAgentName(agentResponse.getAgentName());
                            });
                }
                ResultObject<PolicyResponse> resultObject2 = policyApi.queryOnePolicy(groupInstallmentListResponse.getPolicyId());
                if (!AssertUtils.isResultObjectDataNull(resultObject2)) {
                    groupInstallmentListResponse.setApproveDate(resultObject2.getData().getApproveDate());
                }
            });
        }
        Integer totalLine = AssertUtils.isNotNull(groupInstallmentListBos) ? groupInstallmentListBos.get(0).getTotalLine() : null;
        BasePageResponse<GroupInstallmentListResponse> basePageResponse = BasePageResponse.getData(receivableRequest.getCurrentPage(), receivableRequest.getPageSize(), totalLine, groupInstallmentListResponses);
        resultObject.setData(basePageResponse);
        return resultObject;
    }

    /**
     * 团险续期应收明细
     *
     * @param users             用户
     * @param appRequestHandler 请求头
     * @param groupRenewalId    团险续期id
     * @return ReceivableDetailedResponse
     */
    @Override
    public ResultObject<ReceivableDetailedResponse> loadGroupInstallmentDetail(Users users, AppRequestHeads appRequestHandler, String groupRenewalId) {
        ResultObject<ReceivableDetailedResponse> resultObject = new ResultObject<>();
        GroupRenewalPo groupRenewalPo = groupRenewalBaseService.queryGroupRenewalPo(groupRenewalId);
        AssertUtils.isNotNull(log, groupRenewalPo, RenewalErrorConfigEnum.RENEWAL_QUERY_RENEWAL_IS_NOT_FOUND);
        ReceivableDetailedResponse receivableDetailedResponse = new ReceivableDetailedResponse();
        receivableDetailedResponse.setPolicyNo(groupRenewalPo.getPolicyNo());
        receivableDetailedResponse.setSalesBranchId(groupRenewalPo.getBranchId());
        String policyId = groupRenewalPo.getPolicyId();
        receivableDetailedResponse.setPolicyId(policyId);

        // 查询代理人信息
        AgentResponse agentResponse = agentApi.agentByIdGet(groupRenewalPo.getAgentId()).getData();
        if (AssertUtils.isNotNull(agentResponse)) {
            receivableDetailedResponse.setAgentCode(agentResponse.getAgentCode());
            receivableDetailedResponse.setAgentName(agentResponse.getAgentName());
            receivableDetailedResponse.setAgentMobile(agentResponse.getMobile());
        }

        PolicyApplicantVo policyApplicantVo = policyApi.queryOnePolicyApplicant(policyId).getData();
        if (AssertUtils.isNotNull(policyApplicantVo)) {
            receivableDetailedResponse.setCompanyName(policyApplicantVo.getCompanyName());
            receivableDetailedResponse.setDelegateName(policyApplicantVo.getDelegateName());
            receivableDetailedResponse.setDelegateMobile(policyApplicantVo.getDelegateMobile());
        }

        List<GroupRenewalInsuredPo> insuredBos = groupRenewalInsuredBaseService.listGroupRenewalInsured(groupRenewalId);
        receivableDetailedResponse.setInsuredNum(insuredBos.size());

        ResultObject<PolicyResponse> resultObject2 = policyApi.queryOnePolicy(policyId);
        AssertUtils.isResultObjectDataNull(log, resultObject2, RenewalErrorConfigEnum.RENEWAL_QUERY_POLICY_IS_ERROR);
        PolicyResponse policyResponse = resultObject2.getData();
        receivableDetailedResponse.setApproveDate(policyResponse.getApproveDate());
        receivableDetailedResponse.setEffectiveDate(policyResponse.getEffectiveDate());
        receivableDetailedResponse.setApplyNo(policyResponse.getApplyNo());
        receivableDetailedResponse.setPolicyStatus(policyResponse.getPolicyStatus());


        //应收详情
        ReceivableResponse receivableDetail = new ReceivableResponse();
        //查询续期险种缴费信息
        ResultObject<List<PolicyCoverageVo>> listPolicyCoverage = policyApi.listPolicyCoverage(policyId);
        AssertUtils.isResultObjectError(log, listPolicyCoverage);
        List<RenewalCoveragePremiumResponse> renewalCoveragePremiumResponseList = new ArrayList<>();
        List<ReceivableCoverageResponse> receivableCoverages = new ArrayList<>();
        List<ReceivableCoverageResponse> actualCoverages = new ArrayList<>();

        List<GroupRenewalCoveragePo> groupRenewalCoveragePos = groupRenewalCoverageService.listGroupRenewalCoverage(groupRenewalPo.getGroupRenewalId());
        if (AssertUtils.isNotEmpty(groupRenewalCoveragePos)) {
            List<GroupRenewalPaymentCoverageBo> groupRenewalPaymentCoverageBos = groupInstallmentDataTransfer.getGroupRenewalPaymentCoverageBos(groupRenewalId, groupRenewalCoveragePos);


            if (AssertUtils.isNotEmpty(groupRenewalPaymentCoverageBos)) {
                // 按产品分组
                Map<String, List<GroupRenewalPaymentCoverageBo>> productMap = groupRenewalPaymentCoverageBos.stream().collect(Collectors.groupingBy(GroupRenewalPaymentCoverageBo::getProductId));
                for (String productId : productMap.keySet()) {
                    List<GroupRenewalPaymentCoverageBo> groupRenewalPaymentCoverageBos1 = productMap.get(productId);
                    if (AssertUtils.isNotEmpty(groupRenewalPaymentCoverageBos)) {
                        GroupRenewalPaymentCoverageBo groupRenewalPaymentCoverageBo = groupRenewalPaymentCoverageBos1.get(0);
                        RenewalCoveragePremiumResponse renewalCoveragePremiumResponse = (RenewalCoveragePremiumResponse) this.converterObject(groupRenewalPaymentCoverageBo, RenewalCoveragePremiumResponse.class);
                        renewalCoveragePremiumResponse.setProductId(productId);
                        renewalCoveragePremiumResponse.setPremiumFrequency(groupRenewalPaymentCoverageBo.getPremiumFrequency());
                        renewalCoveragePremiumResponse.setPrimaryFlag(groupRenewalPaymentCoverageBo.getPrimaryFlag());
                        renewalCoveragePremiumResponse.setProductName(groupRenewalPaymentCoverageBo.getProductName());
                        renewalCoveragePremiumResponse.setProductCode(groupRenewalPaymentCoverageBo.getProductCode());
                        if (AssertUtils.isNotNull(groupRenewalPaymentCoverageBo.getAmount())) {
                            renewalCoveragePremiumResponse.setTotalAmount(groupRenewalPaymentCoverageBo.getAmount().toString());
                        }
                        renewalCoveragePremiumResponse.setCurrencyCode(policyResponse.getCurrencyCode());
                        renewalCoveragePremiumResponseList.add(renewalCoveragePremiumResponse);

                        //应收明细
                        ReceivableCoverageResponse receivable = new ReceivableCoverageResponse();
                        ClazzUtils.copyPropertiesIgnoreNull(groupRenewalPaymentCoverageBo, receivable);
                        receivable.setOriginalPremium(groupRenewalPaymentCoverageBo.getOriginalPremium());
                        receivableCoverages.add(receivable);
                        if (RenewalTermEnum.RENEWAL_STATUS.ACTUAL_PAY.name().equals(groupRenewalPo.getGroupRenewalStatus())) {
                            //已续保的续期附加险才出现在实收明细中
                            //实收明细
                            ReceivableCoverageResponse actual = new ReceivableCoverageResponse();
                            ClazzUtils.copyPropertiesIgnoreNull(groupRenewalPaymentCoverageBo, actual);
                            actualCoverages.add(actual);
                        }
                    }
                }
            }
        }
        receivableDetailedResponse.setRenewalCoveragePremiumResponses(renewalCoveragePremiumResponseList);
        receivableDetail.setReceivableCoverages(receivableCoverages);
        receivableDetail.setActualCoverages(actualCoverages);
        receivableDetail.setRenewalDate(groupRenewalPo.getReceivableDate());
        GroupRenewalPremiumPo groupRenewalPremiumPo = groupRenewalPremiumBaseService.queryGroupRenewalPremium(groupRenewalId);
        receivableDetail.setReceivablePremium(groupRenewalPremiumPo.getPeriodTotalPremium());
        receivableDetail.setRenewalStatus(groupRenewalPo.getGroupRenewalStatus());
        receivableDetail.setGracePeriodExpDate(groupRenewalPo.getGracePeriodExpDate());
        //已实收
        if (RenewalTermEnum.RENEWAL_STATUS.ACTUAL_PAY.name().equals(groupRenewalPo.getGroupRenewalStatus())) {
            List<GroupRenewalPaymentTransactionBo> renewalPaymentTransactionPos = paymentTransactionBaseService.listGroupRenewalPaymentTransactionPo(groupRenewalId);
            if (AssertUtils.isNotEmpty(renewalPaymentTransactionPos)) {
                renewalPaymentTransactionPos.stream().filter(renewalPaymentTransactionBo -> RenewalTermEnum.PAYMENT_STATUS.PAYMENT_SUCCESS.name().equals(renewalPaymentTransactionBo.getPaymentStatus()))
                        .findFirst().ifPresent(renewalPaymentTransactionBo -> {
                    GroupRenewalPaymentTransactionItemPo groupRenewalPaymentTransactionItemPo = renewalPaymentTransactionBo.getRenewalPaymentTransactionItem().get(0);
                    receivableDetail.setPaymentMethodCode(groupRenewalPaymentTransactionItemPo.getPaymentMethodCode());
                });
            }
            receivableDetail.setActualPremium(groupRenewalPremiumPo.getActualPremium());
            receivableDetail.setArrivalDate(groupRenewalPo.getPaymentDate());
            receivableDetail.setActualStatus(RenewalTermEnum.RECEIVE_STATUS.RECEIVED.name());
        }
        receivableDetailedResponse.setReceivableDetail(receivableDetail);
        resultObject.setData(receivableDetailedResponse);
        return resultObject;

    }

    /**
     * 删除团险续期应收记录
     *
     * @param appRequestHandler 请求头
     * @param users             用户
     * @param groupRenewalId    团险续期id
     * @return ResultObject
     */
    @Override
    @Transactional
    public ResultObject deleteGroupInstallment(AppRequestHeads appRequestHandler, Users users, String groupRenewalId) {
        // 数据验证
        AssertUtils.isNotEmpty(this.getLogger(), groupRenewalId, RENEWAL_RENEWAL_ID_IS_NOT_NULL);
        GroupRenewalPo groupRenewalPo = groupRenewalBaseService.queryGroupRenewalPo(groupRenewalId);
        AssertUtils.isNotNull(this.getLogger(), groupRenewalPo, RENEWAL_QUERY_RENEWAL_IS_NOT_FOUND);
        GroupRenewalPremiumPo groupRenewalPremiumPo = groupRenewalPremiumBaseService.queryGroupRenewalPremium(groupRenewalId);
        AssertUtils.isNotNull(this.getLogger(), groupRenewalPremiumPo, RENEWAL_QUERY_RENEWAL_PREMIUM_IS_NOT_FOUND);
        List<String> renewalStatuses = Arrays.asList(RenewalTermEnum.RENEWAL_STATUS.PAYMENT.name(),
                RenewalTermEnum.RENEWAL_STATUS.ACTUAL_PAY.name(), RenewalTermEnum.RENEWAL_STATUS.REFUND.name(), RenewalTermEnum.RENEWAL_STATUS.ARCHIVE.name());

        List<String> premiumStatuses = Arrays.asList(RenewalTermEnum.PAY_NOTIFY_STATUS.PAYMENT_INITIAL.name(),
                RenewalTermEnum.PAY_NOTIFY_STATUS.PAYMENT_WAITTING.name());

        //该保单当月续期保费已缴纳或正在缴纳,不允许删除
        if (renewalStatuses.contains(groupRenewalPo.getGroupRenewalStatus()) && !premiumStatuses.contains(groupRenewalPremiumPo.getPremiumStatus())) {
            throw new RequestException(RENEWAL_BUSINESS_QUERY_RENEWAL_ALREADY_PAID);
        }
        // 只能删除最新应收的续期数据
        List<GroupRenewalPo> groupRenewalPos = groupRenewalBaseService.getGroupRenewalsByPolicyId(groupRenewalPo.getPolicyId());
        groupRenewalPos.stream()
                .filter(renewalPo -> renewalPo.getReceivableDate() > groupRenewalPo.getReceivableDate())
                .findFirst().ifPresent(renewalPo -> {
            throw new RequestException(RENEWAL_BUSINESS_RENEWAL_DELETE_NOT_THE_LATEST_ERROR);
        });

        // 如果该期续期已发起支付，需去除该期后重新发起支付
        List<GroupRenewalPaymentTransactionBo> renewalPaymentTransactionBos = paymentTransactionBaseService.listGroupRenewalPaymentTransactionPo(groupRenewalId);
        if (AssertUtils.isNotEmpty(renewalPaymentTransactionBos)) {
            renewalPaymentTransactionBos.forEach(renewalPaymentTransactionBo -> {
                List<GroupRenewalPo> renewalPos = paymentTransactionBaseService.listGroupRenewalByPaymentTransactionId(renewalPaymentTransactionBo.getPaymentTransactionId());
                List<String> policyPaymentIds = renewalPos.stream()
                        .filter(renewalPo -> !renewalPo.getGroupRenewalId().equals(groupRenewalId))
                        .map(GroupRenewalPo::getPolicyPaymentId).collect(Collectors.toList());
                if (AssertUtils.isNotEmpty(policyPaymentIds)) {
                    // 批量续期发起支付
                    RenewalImmediatePaymentRequest immediatePaymentRequest = new RenewalImmediatePaymentRequest();
                    immediatePaymentRequest.setPolicyPaymentIds(policyPaymentIds);
                    immediatePaymentRequest.setPolicyId(groupRenewalPo.getPolicyId());
                    RenewalPaymentItemRequest renewalPaymentItemRequest = new RenewalPaymentItemRequest();
                    renewalPaymentItemRequest.setPaymentTypeCode("ACTUAL");
                    List<RenewalPaymentItemRequest> paymentItem = new ArrayList<>();
                    paymentItem.add(renewalPaymentItemRequest);
                    immediatePaymentRequest.setPaymentItem(paymentItem);
                    ResultObject<Map> responseResultObject = this.postImmediatePayment(users, appRequestHandler, immediatePaymentRequest);
                    AssertUtils.isResultObjectError(this.getLogger(), responseResultObject, RENEWAL_IMMEDIATE_PAYMENT_ERROR);
                } else {
                    // 作废财务中心数据
                    ResultObject<Void> voidResultObject = paymentApi.updatePaymentStatus(renewalPaymentTransactionBo.getPaymentId(), RenewalTermEnum.PAYMENT_STATUS.PAYMENT_INVALID.name());
                    AssertUtils.isResultObjectError(this.getLogger(), voidResultObject);
                }
            });
        }

        //若不存在续期待缴费,则修改保单操作表状态
        boolean renewalDoneFlag = true;
        List<GroupRenewalPo> collect = groupRenewalPos.stream().filter(groupRenewalPo1 -> !groupRenewalId.equals(groupRenewalPo1.getGroupRenewalId())).collect(Collectors.toList());
        if (AssertUtils.isNotEmpty(collect)) {
            renewalDoneFlag = collect.stream().anyMatch(groupRenewalPo1 -> !RenewalTermEnum.RENEWAL_STATUS.PAYMENT.name().equals(groupRenewalPo1.getGroupRenewalStatus()));
        }
        //重置保单保费表中的应缴时间
        ResultObject deleteReceivable = policyApi.deleteReceivable(groupRenewalPo.getPolicyId(), groupRenewalPo.getPolicyPaymentId(), renewalDoneFlag, RenewalTermEnum.RENEWAL_TYPE.GROUP_INSTALLMENT.name());
        AssertUtils.isResultObjectError(this.getLogger(), deleteReceivable);
        //删除应收数据
        groupRenewalBaseService.deleteGroupRenewalData(groupRenewalPo.getGroupRenewalId());
        return ResultObject.success();
    }

    /**
     * 财务详情团险续期业务数据
     *
     * @param users           用户
     * @param appRequestHeads 请求头
     * @param businessId      团险续期缴费事务ID
     * @return RenewalPaymentResponse
     */
    @Override
    public ResultObject<RenewalPaymentResponse> getGroupInstallmentPayment(Users users, AppRequestHeads appRequestHeads, String businessId) {
        ResultObject<RenewalPaymentResponse> resultObject = new ResultObject<>();
        try {
            AssertUtils.isNotEmpty(this.getLogger(), businessId, RENEWAL_PAYMENT_BUSINESS_ID_IS_NOT_NULL);
            GroupRenewalPaymentTransactionBo paymentTransaction = paymentTransactionBaseService.getPaymentTransaction(businessId);
            AssertUtils.isNotNull(getLogger(), paymentTransaction, RENEWAL_QUERY_RENEWAL_PREMIUM_IS_NOT_FOUND);
            List<GroupRenewalPo> groupRenewalPos = paymentTransactionBaseService.listGroupRenewalByPaymentTransactionId(businessId);
            AssertUtils.isNotEmpty(getLogger(), groupRenewalPos, RENEWAL_QUERY_RENEWAL_IS_NOT_FOUND);
            List<String> paymentIds = groupRenewalPos.stream().map(GroupRenewalPo::getPolicyPaymentId).collect(Collectors.toList());
            String policyId = groupRenewalPos.get(0).getPolicyId();

            //查询保单续期缴费信息
            PolicyPaymentUpdateRequest policyPaymentUpdateRequest = new PolicyPaymentUpdateRequest();
            policyPaymentUpdateRequest.setPaymentIdList(paymentIds);
            ResultObject<PaymentAuditDetailResponse> responseResultObject = policyApi.paymentAuditDetail(policyPaymentUpdateRequest);
            AssertUtils.isResultObjectDataNull(getLogger(), responseResultObject);
            RenewalPaymentResponse renewalPaymentResponse = (RenewalPaymentResponse) this.converterObject(responseResultObject.getData(), RenewalPaymentResponse.class);

            GroupRenewalPolicyPo groupRenewalPolicyPo = groupRenewalBaseService.queryGroupRenewalPolicyPo(groupRenewalPos.get(0).getGroupRenewalId());
            if (AssertUtils.isNotNull(groupRenewalPolicyPo)) {
                renewalPaymentResponse.setSalesBranchId(groupRenewalPolicyPo.getSalesBranchId());
                renewalPaymentResponse.setManagerBranchId(groupRenewalPolicyPo.getManagerBranchId());
                //查询销售机构
                if (AssertUtils.isNotEmpty(renewalPaymentResponse.getSalesBranchId())) {
                    BranchResponse branchResponse = platformBranchApi.branchGet(renewalPaymentResponse.getSalesBranchId()).getData();
                    if (AssertUtils.isNotNull(branchResponse)) {
                        renewalPaymentResponse.setSalesBranchId(branchResponse.getBranchId());
                        renewalPaymentResponse.setSalesBranchName(branchResponse.getBranchName());
                    }
                }
                //查询管理机构
                if (AssertUtils.isNotEmpty(renewalPaymentResponse.getManagerBranchId())) {
                    BranchResponse branchResponse = platformBranchApi.branchGet(renewalPaymentResponse.getManagerBranchId()).getData();
                    if (AssertUtils.isNotNull(branchResponse)) {
                        renewalPaymentResponse.setManagerBranchId(branchResponse.getManagerBranchId());
                        renewalPaymentResponse.setManagerBranchName(branchResponse.getBranchName());
                    }
                }
            }
            PolicyApplicantVo policyApplicantVo = policyApi.queryOnePolicyApplicant(policyId).getData();
            if (AssertUtils.isNotNull(policyApplicantVo)) {
                renewalPaymentResponse.setCompanyName(policyApplicantVo.getCompanyName());
                renewalPaymentResponse.setDelegateName(policyApplicantVo.getDelegateName());
                renewalPaymentResponse.setDelegateMobile(policyApplicantVo.getDelegateMobile());
            }

            List<RenewalPaymentDetailResponse> renewalPaymentDetailResponses = new ArrayList<>();
            groupRenewalPos.forEach(renewalBo -> {
                RenewalPaymentDetailResponse renewalPaymentDetailResponse = new RenewalPaymentDetailResponse();
                String groupRenewalId = renewalBo.getGroupRenewalId();
                GroupRenewalPremiumPo renewalPremiumPo = groupRenewalPremiumBaseService.queryGroupRenewalPremium(groupRenewalId);
                AssertUtils.isNotNull(this.getLogger(), renewalPremiumPo, RenewalErrorConfigEnum.RENEWAL_INSURANCE_BUSINESS_RENEWAL_PREMIUM_IS_NOT_FOUND_OBJECT);

                renewalPaymentDetailResponse.setGroupRenewalId(groupRenewalId);
                renewalPaymentDetailResponse.setPolicyYear("1");
                renewalPaymentDetailResponse.setRenewalYearMonth(DateUtils.timeStrToString(renewalBo.getReceivableDate(), DateUtils.FORMATE2));
                renewalPaymentDetailResponse.setTotalPremium(renewalPremiumPo.getPeriodTotalPremium() + "");
                if (AssertUtils.isNotNull(renewalPremiumPo.getPolicyPeriod())) {
                    renewalPaymentDetailResponse.setPaymentFrequency(renewalPremiumPo.getPolicyPeriod() + "");
                }

                // 查询险种信息
                List<PolicyCoverageResponse> listCoverage = new ArrayList<>();
                List<GroupRenewalCoveragePo> groupRenewalCoveragePos = groupRenewalCoverageService.listGroupRenewalCoverage(groupRenewalId);
                if (AssertUtils.isNotEmpty(groupRenewalCoveragePos)) {
                    renewalPaymentDetailResponse.setPremiumFrequency(groupRenewalCoveragePos.get(0).getPremiumFrequency());
                    List<GroupRenewalPaymentCoverageBo> groupRenewalPaymentCoverageBos = groupInstallmentDataTransfer.getGroupRenewalPaymentCoverageBos(groupRenewalId, groupRenewalCoveragePos);
                    listCoverage = (List<PolicyCoverageResponse>) this.converterList(
                            groupRenewalPaymentCoverageBos, new TypeToken<List<PolicyCoverageResponse>>() {
                            }.getType()
                    );
                    renewalPaymentDetailResponse.setListCoverage(listCoverage);
                }
                renewalPaymentDetailResponses.add(renewalPaymentDetailResponse);
            });
            renewalPaymentResponse.setListRenewalPayment(renewalPaymentDetailResponses);

            if (AssertUtils.isNotNull(groupRenewalPos.get(0).getReceivableDate())) {
                renewalPaymentResponse.setRenewalEndDateFormat(DateUtils.timeStrToString(groupRenewalPos.get(0).getReceivableDate() - 1, DateUtils.FORMATE5));
            }

            // 查询续期保单支付附件
            List<GroupRenewalAttachmentPo> groupRenewalAttachmentPos = groupRenewalAttachmentBaseService.listGroupInstallmentAttachment(businessId, RenewalTermEnum.GROUP_RENEWAL_ATTACHMENT_TYPE.GROUP_INSTALLMENT_PAYMENT_INSTRUMENT.name());
            if (AssertUtils.isNotEmpty(groupRenewalAttachmentPos)) {
                List<RenewalPaymentAttachmentResponse> renewalPaymentAttachmentResponses = (List<RenewalPaymentAttachmentResponse>) this.converterList(
                        groupRenewalAttachmentPos, new TypeToken<List<RenewalPaymentAttachmentResponse>>() {
                        }.getType()
                );
                renewalPaymentResponse.setListPaymentAttachment(renewalPaymentAttachmentResponses);

                //增加支付凭证上传时间，同样实收时间
                GroupRenewalAttachmentPo groupRenewalAttachmentPo = groupRenewalAttachmentPos.get(0);
                Long aLong = AssertUtils.isNotNull(groupRenewalAttachmentPo.getUpdatedDate()) ? groupRenewalAttachmentPo.getUpdatedDate() : groupRenewalAttachmentPo.getCreatedDate();
                String timeStrToString = DateUtils.timeStrToString(aLong, DateUtils.FORMATE6);
                renewalPaymentResponse.setPaymentAttachmentDateFormat(timeStrToString);

            }
            resultObject.setData(renewalPaymentResponse);
        } catch (Exception e) {
            e.printStackTrace();
            this.setResultObjectException(this.getLogger(), resultObject, e, RENEWAL_QUERY_PAYMENT_BUSINESS_DATA_ERROR);
        }
        return resultObject;
    }

    /**
     * APP端团险续期明细
     *
     * @param policyId 保单ID
     * @return RenewalAppDetailResponse
     */
    @Override
    public ResultObject<RenewalAppDetailResponse> queryGroupInstallmentAppDetail(String policyId) {
        // 参数校验
        AssertUtils.isNotEmpty(this.getLogger(), policyId, RenewalErrorConfigEnum.RENEWAL_PARAMETER_POLICY_ID_IS_NOT_NULL);

        ResultObject<RenewalAppDetailResponse> resultObject = new ResultObject<>();
        // 查待申请续保数据
        List<GroupRenewalPo> groupRenewalPos = groupRenewalBaseService.getGroupRenewalsByPolicyId(policyId);
        AssertUtils.isNotNull(this.getLogger(), groupRenewalPos, RenewalErrorConfigEnum.RENEWAL_QUERY_PAYMENT_RENEWAL_IS_NOT_FOUND);

        RenewalAppDetailResponse renewalAppDetailResponse = new RenewalAppDetailResponse();

        //有发起支付的待缴费的续期ID
        List<String> policyPaymentIds = new ArrayList<>();
        groupRenewalPos.stream().filter(groupRenewalPo -> RenewalTermEnum.RENEWAL_STATUS.PAYMENT.name().equals(groupRenewalPo.getGroupRenewalStatus()))
                .forEach(groupRenewalPo -> {
                    List<GroupRenewalPaymentTransactionBo> renewalPaymentTransactionBos = paymentTransactionBaseService.listGroupRenewalPaymentTransactionPo(groupRenewalPo.getGroupRenewalId());
                    if (AssertUtils.isNotEmpty(renewalPaymentTransactionBos)) {
                        renewalAppDetailResponse.setBusinessId(renewalPaymentTransactionBos.get(0).getPaymentTransactionId());
                        renewalAppDetailResponse.setPaymentMethodCode(renewalPaymentTransactionBos.get(0).getRenewalPaymentTransactionItem().get(0).getPaymentMethodCode());
                        renewalAppDetailResponse.setGainedDate(groupRenewalPo.getGracePeriodExpDate());
                        renewalAppDetailResponse.setPaymentStatusCode(renewalPaymentTransactionBos.get(0).getPaymentStatus());
                        renewalAppDetailResponse.setPolicyId(groupRenewalPo.getPolicyId());
                        renewalAppDetailResponse.setPolicyNo(groupRenewalPo.getPolicyNo());
                        renewalAppDetailResponse.setReceivableDate(groupRenewalPo.getReceivableDate());
                        renewalAppDetailResponse.setTotalPremium(renewalPaymentTransactionBos.get(0).getPaymentAmount());
                        policyPaymentIds.add(groupRenewalPo.getPolicyPaymentId());
                    }
                });
        renewalAppDetailResponse.setPolicyPaymentIds(policyPaymentIds);
        resultObject.setData(renewalAppDetailResponse);
        return resultObject;
    }
}
