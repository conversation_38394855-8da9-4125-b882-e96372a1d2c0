package com.gclife.platform.base.dao.impl;


import com.gclife.common.dao.base.impl.BaseDaoImpl;
import com.gclife.common.exception.RequestException;
import com.gclife.common.model.config.TerminologyConfigEnum;
import com.gclife.common.model.config.TerminologyTypeEnum;
import com.gclife.common.util.AssertUtils;
import com.gclife.platform.base.calculate.CalculateOption;
import com.gclife.platform.base.dao.BranchBaseDao;
import com.gclife.platform.base.dao.CalculateDao;
import com.gclife.platform.base.model.bo.BranchDo;
import com.gclife.platform.base.model.bo.BranchPagingDo;
import com.gclife.platform.base.model.bo.BranchTreeDo;
import com.gclife.platform.base.model.config.PlatformErrorConfigEnum;
import com.gclife.platform.base.model.vo.BranchPagingQueryVo;
import com.gclife.platform.base.model.vo.BranchQueryVo;
import com.gclife.platform.core.jooq.tables.Branch;
import com.gclife.platform.core.jooq.tables.pojos.BranchBankPo;
import com.gclife.platform.core.jooq.tables.pojos.BranchLicensePo;
import com.gclife.platform.core.jooq.tables.pojos.BranchPo;
import com.gclife.platform.core.jooq.tables.pojos.FinancialBranchPo;
import com.gclife.platform.core.jooq.tables.records.BranchRecord;
import org.jooq.Condition;
import org.jooq.SelectJoinStep;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

import static com.gclife.platform.core.jooq.Tables.*;


/**
 * <AUTHOR>
 * create 2018-05-02
 * description:　机构基础dao类
 */
@Component
public class BranchBaseDaoImpl extends BaseDaoImpl implements BranchBaseDao {

    @Autowired
    CalculateOption calculateOption;
    @Autowired
    CalculateDao calculateDao;

    /**
     * 向下查询机构树
     *
     * @param branchQueryVo 机构查询Vo
     * @return List<BranchLevelBo>
     */
    @Override
    public List<BranchTreeDo> queryBranchTree(BranchQueryVo branchQueryVo, String calculateMode) {
        List<BranchTreeDo> branchTreeDos = null;
        try {
            List<Object> objects = new ArrayList<>();
            objects = calculateOption.calculateValues(BranchTreeDo.class, BranchRecord.class, calculateMode, branchQueryVo);
            //数据转换
            if (AssertUtils.isNotEmpty(objects)) {
                branchTreeDos = objects.stream().map(o -> {
                    return (BranchTreeDo) o;
                }).collect(Collectors.toList());
            }
            //执行树查询
        } catch (Exception e) {
            e.printStackTrace();
            this.getLogger().error(PlatformErrorConfigEnum.PLATFORM_BUSINESS_BRANCH_IS_NOT_FOUND_OBJECT.getValue());
            throw new RequestException(PlatformErrorConfigEnum.PLATFORM_BUSINESS_BRANCH_IS_NOT_FOUND_OBJECT);
        }
        return branchTreeDos;
    }


    @Override
    public List<BranchTreeDo> queryBranchTreeManual(BranchQueryVo branchQueryVo, String calculateMode) {
        List<BranchTreeDo> branchTreeDos = null;
        try {
            List<Object> objects = new ArrayList<>();
            objects = calculateOption.calculateValuesManual(BranchTreeDo.class, BranchRecord.class, calculateMode, branchQueryVo);
            //数据转换
            if (AssertUtils.isNotEmpty(objects)) {
                branchTreeDos = objects.stream().map(o -> {
                    return (BranchTreeDo) o;
                }).collect(Collectors.toList());
            }
            //执行树查询
        } catch (Exception e) {
            e.printStackTrace();
            this.getLogger().error(PlatformErrorConfigEnum.PLATFORM_BUSINESS_BRANCH_IS_NOT_FOUND_OBJECT.getValue());
            throw new RequestException(PlatformErrorConfigEnum.PLATFORM_BUSINESS_BRANCH_IS_NOT_FOUND_OBJECT);
        }
        return branchTreeDos;
    }

    /**
     * 查询机构信息
     *
     * @param branchId 　机构ID
     * @return BranchPo
     */
    @Override
    public BranchPo queryOneBranchPoById(String branchId) {
        return this.getDslContext().selectFrom(BRANCH).where(BRANCH.BRANCH_ID.eq(branchId)).fetchOneInto(BranchPo.class);
    }

    /**
     * 查询机构信息
     *
     * @param branchId 　机构ID
     * @return BranchPo
     */
    @Override
    public BranchBankPo queryOneBranchBankById(String branchId) {
        return this.getDslContext().selectFrom(BRANCH_BANK).where(BRANCH_BANK.BRANCH_ID.eq(branchId)).fetchOneInto(BranchBankPo.class);
    }

    @Override
    public BranchPo queryOneTopBranchById(String branchId) {
        Branch t1 = BRANCH.as("T1");
        return this.getDslContext().
                select(t1.fields())
                .from(BRANCH)
                .innerJoin(t1).on(t1.BRANCH_ID.eq(BRANCH.BELONG_BRANCH_ID))
                .where(BRANCH.BRANCH_ID.eq(branchId)).fetchOneInto(BranchPo.class);
    }

    /**
     * 查询机构信息
     *
     * @param branchIds 　机构IDS
     * @return BranchPo
     */
    @Override
    public List<BranchPo> queryBranchPoByIds(List<String> branchIds) {
        return this.getDslContext().selectFrom(BRANCH).where(BRANCH.BRANCH_ID.in(branchIds)).fetchInto(BranchPo.class);
    }

    /**
     * 查询用户机构信息
     *
     * @param userId 　用户ID
     * @return BranchPo
     */
    @Override
    public BranchPo queryOneUserBranchPoByUserId(String userId) {
        return this.getDslContext().select(BRANCH.fields()).from(EMPLOYEE).innerJoin(BRANCH).on(EMPLOYEE.BRANCH_ID.eq(BRANCH.BRANCH_ID)).where(EMPLOYEE.USER_ID.eq(userId)).fetchOneInto(BranchPo.class);
    }

    @Override
    public List<BranchPo> queryUserOptionBranchFirstLevel(String userId) {
        return this.getDslContext().select(BRANCH.fields()).from(USER_BRANCH).innerJoin(BRANCH).on(USER_BRANCH.BRANCH_ID.eq(BRANCH.BRANCH_ID)).where(USER_BRANCH.USER_ID.eq(userId)).fetchInto(BranchPo.class);
    }

    @Override
    public List<BranchDo> queryBranchByCode(List<String> branchCodeList) {
        SelectJoinStep selectJoinStep = this.getDslContext()
                .select(BRANCH.fields())
                .from(BRANCH);
        List<Condition> conditionList = new ArrayList<>();
        conditionList.add(BRANCH.BRANCH_CODE.in(branchCodeList));
        selectJoinStep.where(conditionList);
        return selectJoinStep.fetchInto(BranchDo.class);
    }

    @Override
    public List<BranchDo> queryBranchByChannelTypeCode(String channelTypeCode) {
        SelectJoinStep selectJoinStep = this.getDslContext()
                .select(BRANCH.fields())
                .from(BRANCH);
        List<Condition> conditionList = new ArrayList<>();
        conditionList.add(BRANCH.CHANNEL_TYPE_CODE.eq(channelTypeCode));
        selectJoinStep.where(conditionList);
        return selectJoinStep.fetchInto(BranchDo.class);
    }

    @Override
    public BranchDo queryBranchByCode(String branchCode) {

        SelectJoinStep selectJoinStep = this.getDslContext()
                .select(BRANCH.fields())
                .from(BRANCH);
        List<Condition> conditionList = new ArrayList<>();
        conditionList.add(BRANCH.BRANCH_CODE.eq(branchCode));
        selectJoinStep.where(conditionList);
        return (BranchDo) selectJoinStep.fetchOneInto(BranchDo.class);
    }

    /**
     * 机构模糊查询
     *
     * @param branchPagingQueryVo 　分页对象
     * @return List<BranchPagingDo>
     */
    @Override
    public List<BranchPagingDo> queryBranchPagingFuzzy(BranchPagingQueryVo branchPagingQueryVo) {
        Branch T1 = BRANCH.as("T1");
        SelectJoinStep selectJoinStep = this.getDslContext()
                .select(BRANCH.fields())
                .select(BRANCH.BRANCH_ID.countOver().as("totalLine"))
                .select(T1.BRANCH_NAME.concat("-").concat(BRANCH.BRANCH_NAME).as("branchName"))
                .from(BRANCH)
                .leftJoin(T1).on(BRANCH.PARENT_BRANCH_ID.eq(T1.BRANCH_ID));
        List<Condition> conditionList = new ArrayList<>();
        if (AssertUtils.isNotNull(branchPagingQueryVo.getKeyword())) {
            conditionList.add(BRANCH.BRANCH_NAME.likeRegex(branchPagingQueryVo.getKeyword()));
        }
        if (AssertUtils.isNotNull(branchPagingQueryVo.getChannelTypeCode())) {
            conditionList.add(BRANCH.CHANNEL_TYPE_CODE.eq(branchPagingQueryVo.getChannelTypeCode()));
        }
        selectJoinStep.where(conditionList).and(BRANCH.BRANCH_ID.ne(TerminologyTypeEnum.ROOT.name()));
        return selectJoinStep.offset(branchPagingQueryVo.getOffset()).limit(branchPagingQueryVo.getPageSize()).fetchInto(BranchPagingDo.class);
    }

    @Override
    public BranchPo queryBranchDefaultChannel() {
        return this.getDslContext().select(BRANCH.fields()).from(BRANCH).where(BRANCH.SALES_CHANNEL_FLAG.eq("CHANNEL")).and(BRANCH.DEFAULT_SALES_CHANNEL_FLAG.eq("DEFAULT")).limit(1).fetchOneInto(BranchPo.class);
    }

    @Override
    public BranchDo queryOneBranchAndManagerById(String branchId) {
        Branch T1 = BRANCH.as("T1");
        return this.getDslContext()
                .select(BRANCH.fields())
                .select(T1.BRANCH_CODE.as("managerBranchCode"), T1.BRANCH_NAME.as("managerBranchName"))
                .from(BRANCH)
                .leftJoin(T1).on(BRANCH.MANAGER_BRANCH_ID.eq(T1.BRANCH_ID))
                .where(BRANCH.BRANCH_ID.eq(branchId)).fetchOneInto(BranchDo.class);
    }

    @Override
    public BranchLicensePo queryOneBranchLicenseById(String branchId) {
        return this.getDslContext().selectFrom(BRANCH_LICENSE).where(BRANCH_LICENSE.BRANCH_ID.eq(branchId)).orderBy(BRANCH_LICENSE.CREATED_DATE.desc()).limit(1).fetchOneInto(BranchLicensePo.class);
    }

    @Override
    public List<BranchBankPo> queryBranchBankById(List<String> branchIds) {
        return this.getDslContext().selectFrom(BRANCH_BANK).where(BRANCH_BANK.BRANCH_ID.in(branchIds)).fetchInto(BranchBankPo.class);
    }

    @Override
    public List<BranchDo> getBranchSameLevel(String branchId) {
        Branch son = BRANCH.as("son");
        Branch sons = BRANCH.as("sons");
        Branch father = BRANCH.as("father");
        return this.getDslContext()
                .select(sons.fields())
                .from(son)
                .leftJoin(father).on(father.BRANCH_ID.eq(son.PARENT_BRANCH_ID))
                .leftJoin(sons).on(sons.PARENT_BRANCH_ID.eq(father.BRANCH_ID))
                .where(son.BRANCH_ID.eq(branchId))
                .orderBy(sons.BRANCH_ID)
                .fetchInto(BranchDo.class);
    }

    @Override
    public List<FinancialBranchPo> queryFinancialBranchFuzzy(String keyword) {
        List<Condition> conditions = new ArrayList<>();
        conditions.add(FINANCIAL_BRANCH.VALID_FLAG.eq(TerminologyConfigEnum.VALID_FLAG.effective.name()));
        if (AssertUtils.isNotEmpty(keyword)) {
            conditions.add(FINANCIAL_BRANCH.NAME.trim().likeIgnoreCase("%"+keyword+"%"));
        }
        return this.getDslContext().selectFrom(FINANCIAL_BRANCH)
                .where(conditions).fetchInto(FinancialBranchPo.class);
    }
}