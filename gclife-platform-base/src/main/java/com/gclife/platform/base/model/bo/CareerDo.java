package com.gclife.platform.base.model.bo;

import com.gclife.platform.core.jooq.tables.pojos.BaseCareerPo;
import com.gclife.platform.core.jooq.tables.pojos.BranchPo;
import io.swagger.annotations.ApiModelProperty;

import java.util.List;

/**
 * <AUTHOR>
 *         create 2018-05-02
 *         description:　机构树基础数据类
 */
public class CareerDo extends BaseCareerPo{

    /**子节点的数量*/
    private String existChild;

    private String depth;

    public String getDepth() {
        return depth;
    }

    public void setDepth(String depth) {
        this.depth = depth;
    }

    public String getExistChild() {
        return existChild;
    }

    public void setExistChild(String existChild) {
        this.existChild = existChild;
    }
}
